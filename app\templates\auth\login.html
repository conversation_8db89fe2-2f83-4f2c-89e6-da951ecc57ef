<!-- app/templates/auth/login.html -->
<!DOCTYPE html>
<html lang="{{ session.get('language', 'fr') }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ t('auth.login.page_title') }}</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="{{ url_for('static', filename='images/favicon_tutoraide.png') }}">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-red: #e57373;
            --primary-red-dark: #d32f2f;
            --primary-red-light: #ffcdd2;
            --text-dark: #212529;
            --text-gray: #6c757d;
            --bg-light: #f8f9fa;
            --bg-white: #ffffff;
            --border-color: #dee2e6;
            --shadow-sm: 0 2px 4px rgba(0,0,0,0.05);
            --shadow-md: 0 4px 6px rgba(0,0,0,0.07);
            --shadow-lg: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, var(--bg-light) 0%, #e8f5e8 100%);
            min-height: 100vh;
            color: var(--text-dark);
        }
        
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Playfair Display', serif;
            color: var(--text-dark);
        }
        
        /* Login Container */
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
        }
        
        .login-card {
            background: var(--bg-white);
            border-radius: 20px;
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
            border: none;
        }
        
        /* Header */
        .login-header {
            background: linear-gradient(135deg, var(--primary-red) 0%, var(--primary-red-dark) 100%);
            color: white;
            padding: 2rem;
            text-align: center;
            position: relative;
        }
        
        .login-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><defs><radialGradient id="grad" cx="50%" cy="50%" r="50%"><stop offset="0%" style="stop-color:white;stop-opacity:0.1" /><stop offset="100%" style="stop-color:white;stop-opacity:0" /></radialGradient></defs><circle cx="20" cy="10" r="2" fill="url(%23grad)"/><circle cx="80" cy="5" r="1.5" fill="url(%23grad)"/><circle cx="50" cy="15" r="1" fill="url(%23grad)"/></svg>') repeat;
            opacity: 0.3;
        }
        
        .login-header h2 {
            margin: 0;
            font-size: 2rem;
            font-weight: 700;
            color: white;
            position: relative;
            z-index: 1;
        }
        
        .login-header p {
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
            font-size: 1rem;
            position: relative;
            z-index: 1;
        }
        
        /* Form Body */
        .login-body {
            padding: 2rem;
        }
        
        .form-label {
            font-weight: 500;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
            font-size: 1.1rem;
        }
        
        .form-control {
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 1rem 1.25rem;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            background: var(--bg-white);
        }
        
        .form-control:focus {
            border-color: var(--primary-red);
            box-shadow: 0 0 0 0.2rem rgba(229, 115, 115, 0.25);
            background: var(--bg-white);
        }
        
        .form-control.is-invalid {
            border-color: #dc3545;
        }
        
        .invalid-feedback {
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }
        
        .form-check {
            margin: 1.5rem 0;
        }
        
        .form-check-input:checked {
            background-color: var(--primary-red);
            border-color: var(--primary-red);
        }
        
        .form-check-input:focus {
            border-color: var(--primary-red);
            box-shadow: 0 0 0 0.2rem rgba(229, 115, 115, 0.25);
        }
        
        .form-check-label {
            font-size: 1.05rem;
            color: var(--text-gray);
        }
        
        /* Buttons */
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-red) 0%, var(--primary-red-dark) 100%);
            border: none;
            border-radius: 12px;
            padding: 1rem 1.5rem;
            font-weight: 500;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(229, 115, 115, 0.3);
            background: linear-gradient(135deg, var(--primary-red-dark) 0%, #b71c1c 100%);
        }
        
        .btn-primary:active {
            transform: translateY(0);
        }
        
        .btn-outline-danger {
            border: 2px solid var(--primary-red);
            color: var(--primary-red);
            border-radius: 12px;
            padding: 1rem 1.5rem;
            font-weight: 500;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            background: transparent;
        }
        
        .btn-outline-danger:hover {
            background: var(--primary-red);
            border-color: var(--primary-red);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(229, 115, 115, 0.3);
        }
        
        /* Divider */
        .divider {
            margin: 1.5rem 0;
            position: relative;
        }
        
        .divider hr {
            border-color: var(--border-color);
            margin: 0;
        }
        
        .divider span {
            background: var(--bg-white);
            color: var(--text-gray);
            font-size: 1rem;
            font-weight: 500;
        }
        
        /* Footer */
        .login-footer {
            background: var(--bg-light);
            padding: 1.5rem;
            text-align: center;
            border-top: 1px solid var(--border-color);
        }
        
        .login-footer a {
            color: var(--primary-red);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .login-footer a:hover {
            color: var(--primary-red-dark);
            text-decoration: underline;
        }
        
        .small-text {
            font-size: 1rem;
            color: var(--text-gray);
            margin-bottom: 0.5rem;
        }
        
        /* Alert Styles */
        .alert {
            border-radius: 12px;
            border: none;
            margin-bottom: 1.5rem;
        }
        
        .alert-danger {
            background: var(--primary-red-light);
            color: var(--primary-red-dark);
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
        }
        
        .alert-info {
            background: #e3f2fd;
            color: #1565c0;
        }
        
        .alert-warning {
            background: #fff8e1;
            color: #f57c00;
        }
        
        /* Responsive */
        @media (max-width: 576px) {
            .login-container {
                padding: 1rem;
            }
            
            .login-header {
                padding: 1.5rem;
            }
            
            .login-header h2 {
                font-size: 1.75rem;
            }
            
            .login-body {
                padding: 1.5rem;
            }
        }
        
        /* Loading Animation */
        .btn-loading {
            position: relative;
            pointer-events: none;
        }
        
        .btn-loading::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            margin: auto;
            border: 2px solid transparent;
            border-top-color: #ffffff;
            border-radius: 50%;
            animation: button-loading-spinner 1s ease infinite;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        
        @keyframes button-loading-spinner {
            from {
                transform: translate(-50%, -50%) rotate(0turn);
            }
            to {
                transform: translate(-50%, -50%) rotate(1turn);
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <!-- Header -->
            <div class="login-header">
                <h2>{{ t('auth.login.header') }}</h2>
                <p>{{ t('auth.login.subtitle') }}</p>
            </div>
            
            <!-- Flash Messages -->
            <div class="login-body">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                <!-- Login Form -->
                <form method="POST" action="{{ url_for('auth.login') }}" id="login-form">
                    {{ form.csrf_token }}

                    <div class="mb-3">
                        <label for="email" class="form-label">{{ t('auth.login.email_label') }}</label>
                        {{ form.email(class="form-control" + (" is-invalid" if form.email.errors else ""), placeholder=t('auth.login.email_placeholder')) }}
                        {% if form.email.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.email.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">{{ t('auth.login.password_label') }}</label>
                        {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else ""), placeholder=t('auth.login.password_placeholder')) }}
                        {% if form.password.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.password.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="form-check">
                        {{ form.remember_me(class="form-check-input") }}
                        <label for="remember_me" class="form-check-label">{{ t('auth.login.remember_me') }}</label>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary" id="login-btn">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            {{ t('auth.login.submit') }}
                        </button>
                    </div>
                </form>

                <!-- Google Sign-In (show if configured OR in test mode) -->
                {% if (config.GOOGLE_CLIENT_ID and config.GOOGLE_CLIENT_SECRET) or request.args.get('show_google') %}
                <!-- Divider -->
                <div class="divider">
                    <div class="d-flex align-items-center">
                        <hr class="flex-grow-1">
                        <span class="px-3">{{ t('auth.login.or') }}</span>
                        <hr class="flex-grow-1">
                    </div>
                </div>

                <!-- Google Sign-In -->
                <div class="d-grid">
                    <a href="{{ url_for('auth.google_login') }}" class="btn btn-outline-danger">
                        <i class="fab fa-google me-2"></i>
                        {{ t('auth.login.google_signin') }}
                    </a>
                </div>
                {% endif %}
            </div>
            
            <!-- Footer -->
            <div class="login-footer">
                <p class="small-text mb-2">
                    {{ t('auth.login.forgot_password') }} 
                    <a href="{{ url_for('auth.reset_password_request') }}">{{ t('auth.login.reset_link') }}</a>
                </p>
                
                <div class="row">
                    <div class="col-md-6">
                        <p class="small-text">
                            {{ t('auth.login.new_tecfee') }}<br>
                            <a href="{{ url_for('public.tecfee_enrollment_single_step') }}">
                                <i class="fas fa-graduation-cap me-1"></i>
                                {{ t('auth.login.enroll_here') }}
                            </a>
                        </p>
                    </div>
                    {% if config.GOOGLE_CLIENT_ID and config.GOOGLE_CLIENT_SECRET %}
                    <div class="col-md-6">
                        <p class="small-text">
                            {{ t('auth.login.new_user') }}<br>
                            <a href="{{ url_for('auth.google_signup') }}">
                                <i class="fab fa-google me-1"></i>
                                {{ t('auth.login.google_signup') }}
                            </a>
                        </p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/csrf.js') }}"></script>
    <script>
        // Form submission with loading state
        document.getElementById('login-form').addEventListener('submit', function() {
            const btn = document.getElementById('login-btn');
            btn.classList.add('btn-loading');
            btn.innerHTML = '<span style="opacity: 0;">{{ t('auth.login.submit') }}</span>';
            btn.disabled = true;
        });
    </script>
</body>
</html>