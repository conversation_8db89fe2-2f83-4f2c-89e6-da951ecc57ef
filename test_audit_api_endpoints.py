#!/usr/bin/env python3
"""
Integration tests for audit API endpoints.
Tests the enhanced /appointment/<id>/audit and /appointment/<id>/audit/summary endpoints.
"""

import os
import sys
import unittest
from datetime import datetime, timedelta
from unittest.mock import patch, <PERSON><PERSON>, <PERSON>Mock

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

# Set up Flask app context for testing
os.environ['FLASK_ENV'] = 'testing'

try:
    from app import create_app
    from flask_login import current_user
except ImportError as e:
    print(f"Warning: Could not import Flask app: {e}")
    print("Running tests with mocked Flask app...")


class TestAuditAPIEndpoints(unittest.TestCase):
    """Test cases for audit API endpoints."""
    
    @classmethod
    def setUpClass(cls):
        """Set up test environment."""
        cls.app = create_app()
        cls.app.config['TESTING'] = True
        cls.app.config['WTF_CSRF_ENABLED'] = False
        cls.client = cls.app.test_client()
        
        with cls.app.app_context():
            # Create test data
            cls._create_test_data()
    
    @classmethod
    def _create_test_data(cls):
        """Create test data for the tests."""
        try:
            # Create manager user
            cls.manager_user = User(
                email='<EMAIL>',
                password='password123',
                role='manager'
            )
            db.session.add(cls.manager_user)
            db.session.flush()
            
            cls.manager = Manager(
                user_id=cls.manager_user.id,
                first_name='Test',
                last_name='Manager',
                phone='555-0001'
            )
            db.session.add(cls.manager)
            
            # Create client user
            cls.client_user = User(
                email='<EMAIL>',
                password='password123',
                role='client'
            )
            db.session.add(cls.client_user)
            db.session.flush()
            
            cls.test_client = Client(
                user_id=cls.client_user.id,
                first_name='Test',
                last_name='Client',
                email='<EMAIL>',
                phone='555-0002',
                client_type='individual'
            )
            db.session.add(cls.test_client)
            
            # Create tutor user
            cls.tutor_user = User(
                email='<EMAIL>',
                password='password123',
                role='tutor'
            )
            db.session.add(cls.tutor_user)
            db.session.flush()
            
            cls.tutor = Tutor(
                user_id=cls.tutor_user.id,
                first_name='Test',
                last_name='Tutor',
                email='<EMAIL>',
                phone='555-0003'
            )
            db.session.add(cls.tutor)
            
            # Create service
            cls.service = Service(
                name='Math Tutoring',
                description='Basic math tutoring',
                duration_minutes=60
            )
            db.session.add(cls.service)
            db.session.flush()
            
            # Create tutor service
            cls.tutor_service = TutorService(
                tutor_id=cls.tutor.tutor_id,
                service_id=cls.service.service_id,
                client_rate=50.00,
                is_active=True
            )
            db.session.add(cls.tutor_service)
            
            # Create appointment
            start_time = datetime.now() + timedelta(days=1)
            end_time = start_time + timedelta(hours=1)
            
            cls.appointment = Appointment(
                tutor_id=cls.tutor.tutor_id,
                client_id=cls.test_client.client_id,
                tutor_service_id=cls.tutor_service.tutor_service_id,
                start_time=start_time,
                end_time=end_time,
                status='scheduled',
                notes='Test appointment'
            )
            db.session.add(cls.appointment)
            db.session.flush()
            
            # Create audit entries
            cls._create_audit_entries()
            
            db.session.commit()
            
        except Exception as e:
            db.session.rollback()
            print(f"Error creating test data: {e}")
            raise
    
    @classmethod
    def _create_audit_entries(cls):
        """Create test audit entries."""
        base_time = datetime.now() - timedelta(hours=2)
        
        # Create audit entry
        audit_entry = AppointmentAudit(
            appointment_id=cls.appointment.id,
            action='create',
            action_description='Appointment created',
            user_id=cls.manager_user.id,
            user_role='manager',
            user_email='<EMAIL>',
            timestamp=base_time,
            old_values=None,
            new_values={
                'tutor_id': cls.tutor.id,
                'client_id': cls.test_client.id,
                'status': 'scheduled',
                'start_time': cls.appointment.start_time.isoformat(),
                'end_time': cls.appointment.end_time.isoformat()
            },
            changes_summary='Initial appointment creation',
            notes='Created via test'
        )
        db.session.add(audit_entry)
        
        # Update audit entry
        update_entry = AppointmentAudit(
            appointment_id=cls.appointment.id,
            action='update',
            action_description='Appointment updated',
            user_id=cls.manager_user.id,
            user_role='manager',
            user_email='<EMAIL>',
            timestamp=base_time + timedelta(minutes=30),
            old_values={'status': 'scheduled'},
            new_values={'status': 'confirmed'},
            changes_summary='Status changed to confirmed',
            notes='Updated via test'
        )
        db.session.add(update_entry)
    
    def setUp(self):
        """Set up for each test."""
        self.app_context = self.app.app_context()
        self.app_context.push()
    
    def tearDown(self):
        """Clean up after each test."""
        self.app_context.pop()
    
    def _login_as_manager(self):
        """Helper to login as manager."""
        with self.client.session_transaction() as sess:
            sess['_user_id'] = str(self.manager_user.id)
            sess['_fresh'] = True
    
    def _login_as_client(self):
        """Helper to login as client."""
        with self.client.session_transaction() as sess:
            sess['_user_id'] = str(self.client_user.id)
            sess['_fresh'] = True
    
    def test_audit_endpoint_manager_access_success(self):
        """Test that managers can successfully access audit endpoint."""
        self._login_as_manager()
        
        response = self.client.get(f'/api/appointment/{self.appointment.id}/audit')
        
        self.assertEqual(response.status_code, 200)
        data = response.get_json()
        
        # Check response structure
        self.assertIn('success', data)
        self.assertTrue(data['success'])
        self.assertIn('appointment', data)
        self.assertIn('audit_entries', data)
        self.assertIn('pagination', data)
        self.assertIn('meta', data)
        
        # Check appointment data
        appointment_data = data['appointment']
        self.assertEqual(appointment_data['id'], self.appointment.id)
        self.assertIn('client_name', appointment_data)
        self.assertIn('tutor_name', appointment_data)
        self.assertIn('status', appointment_data)
        
        # Check audit entries
        self.assertIsInstance(data['audit_entries'], list)
        self.assertGreater(len(data['audit_entries']), 0)
        
        # Check pagination
        pagination = data['pagination']
        self.assertIn('current_page', pagination)
        self.assertIn('total_entries', pagination)
        self.assertIn('total_pages', pagination)
        
        # Check meta information
        meta = data['meta']
        self.assertIn('timezone', meta)
        self.assertEqual(meta['timezone'], 'EST')
        self.assertIn('request_timestamp', meta)
    
    def test_audit_endpoint_unauthorized_access(self):
        """Test that non-managers cannot access audit endpoint."""
        self._login_as_client()
        
        response = self.client.get(f'/api/appointment/{self.appointment.id}/audit')
        
        self.assertEqual(response.status_code, 403)
        data = response.get_json()
        
        self.assertIn('error', data)
        self.assertEqual(data['error'], 'Unauthorized')
        self.assertIn('message', data)
    
    def test_audit_endpoint_nonexistent_appointment(self):
        """Test audit endpoint with non-existent appointment."""
        self._login_as_manager()
        
        response = self.client.get('/api/appointment/99999/audit')
        
        self.assertEqual(response.status_code, 404)
        data = response.get_json()
        
        self.assertIn('error', data)
        self.assertEqual(data['error'], 'Appointment not found')
    
    def test_audit_endpoint_pagination(self):
        """Test audit endpoint pagination parameters."""
        self._login_as_manager()
        
        # Test with custom pagination
        response = self.client.get(f'/api/appointment/{self.appointment.id}/audit?page=1&per_page=5')
        
        self.assertEqual(response.status_code, 200)
        data = response.get_json()
        
        pagination = data['pagination']
        self.assertEqual(pagination['current_page'], 1)
        self.assertEqual(pagination['per_page'], 5)
        
        # Test with invalid pagination (should default)
        response = self.client.get(f'/api/appointment/{self.appointment.id}/audit?page=-1&per_page=200')
        
        self.assertEqual(response.status_code, 200)
        data = response.get_json()
        
        pagination = data['pagination']
        self.assertEqual(pagination['current_page'], 1)  # Should default to 1
        self.assertEqual(pagination['per_page'], 20)     # Should default to 20
    
    def test_audit_summary_endpoint_success(self):
        """Test audit summary endpoint success."""
        self._login_as_manager()
        
        response = self.client.get(f'/api/appointment/{self.appointment.id}/audit/summary')
        
        self.assertEqual(response.status_code, 200)
        data = response.get_json()
        
        # Check response structure
        self.assertIn('success', data)
        self.assertTrue(data['success'])
        self.assertIn('appointment_id', data)
        self.assertIn('appointment_info', data)
        self.assertIn('audit_summary', data)
        self.assertIn('meta', data)
        
        # Check appointment info
        appointment_info = data['appointment_info']
        self.assertIn('client_name', appointment_info)
        self.assertIn('tutor_name', appointment_info)
        self.assertIn('status', appointment_info)
        
        # Check audit summary
        audit_summary = data['audit_summary']
        self.assertIn('has_audit_trail', audit_summary)
        self.assertIn('total_entries', audit_summary)
        
        # Check meta information
        meta = data['meta']
        self.assertIn('timezone', meta)
        self.assertEqual(meta['timezone'], 'EST')
    
    def test_audit_summary_endpoint_unauthorized(self):
        """Test audit summary endpoint unauthorized access."""
        self._login_as_client()
        
        response = self.client.get(f'/api/appointment/{self.appointment.id}/audit/summary')
        
        self.assertEqual(response.status_code, 403)
        data = response.get_json()
        
        self.assertIn('error', data)
        self.assertEqual(data['error'], 'Unauthorized')
    
    def test_audit_summary_endpoint_nonexistent_appointment(self):
        """Test audit summary endpoint with non-existent appointment."""
        self._login_as_manager()
        
        response = self.client.get('/api/appointment/99999/audit/summary')
        
        self.assertEqual(response.status_code, 404)
        data = response.get_json()
        
        self.assertIn('error', data)
        self.assertEqual(data['error'], 'Appointment not found')
    
    def test_audit_endpoint_with_dependant(self):
        """Test audit endpoint with appointment that has a dependant."""
        # This test would require creating a dependant, but we'll mock it for now
        self._login_as_manager()
        
        response = self.client.get(f'/api/appointment/{self.appointment.id}/audit')
        
        self.assertEqual(response.status_code, 200)
        data = response.get_json()
        
        # Check that is_for_dependant is properly set
        appointment_data = data['appointment']
        self.assertIn('is_for_dependant', appointment_data)
        self.assertFalse(appointment_data['is_for_dependant'])  # Our test appointment doesn't have a dependant
    
    def test_audit_entries_formatting(self):
        """Test that audit entries are properly formatted with EST timezone."""
        self._login_as_manager()
        
        response = self.client.get(f'/api/appointment/{self.appointment.id}/audit')
        
        self.assertEqual(response.status_code, 200)
        data = response.get_json()
        
        audit_entries = data['audit_entries']
        self.assertGreater(len(audit_entries), 0)
        
        # Check first entry formatting
        entry = audit_entries[0]
        required_fields = [
            'id', 'appointment_id', 'action', 'action_description',
            'action_icon', 'action_color', 'user_name', 'user_role',
            'timestamp_est', 'changes_summary', 'has_changes'
        ]
        
        for field in required_fields:
            self.assertIn(field, entry, f"Missing field: {field}")
        
        # Check that EST timestamp is formatted
        self.assertIsNotNone(entry['timestamp_est'])
        self.assertIsInstance(entry['timestamp_est'], str)
    
    @patch('app.services.audit_service.AuditService.get_appointment_audit_history')
    def test_audit_endpoint_service_error_handling(self, mock_audit_service):
        """Test audit endpoint error handling when service fails."""
        self._login_as_manager()
        
        # Mock service to raise an exception
        mock_audit_service.side_effect = Exception("Service error")
        
        response = self.client.get(f'/api/appointment/{self.appointment.id}/audit')
        
        self.assertEqual(response.status_code, 500)
        data = response.get_json()
        
        self.assertIn('error', data)
        self.assertEqual(data['error'], 'Failed to load audit history')
        self.assertIn('message', data)
    
    @patch('app.services.audit_service.AuditService.get_audit_summary')
    def test_audit_summary_endpoint_service_error_handling(self, mock_audit_service):
        """Test audit summary endpoint error handling when service fails."""
        self._login_as_manager()
        
        # Mock service to raise an exception
        mock_audit_service.side_effect = Exception("Service error")
        
        response = self.client.get(f'/api/appointment/{self.appointment.id}/audit/summary')
        
        self.assertEqual(response.status_code, 500)
        data = response.get_json()
        
        self.assertIn('error', data)
        self.assertEqual(data['error'], 'Failed to load audit summary')
        self.assertIn('message', data)
    
    def test_audit_endpoint_no_login(self):
        """Test audit endpoint without login."""
        response = self.client.get(f'/api/appointment/{self.appointment.id}/audit')
        
        # Should redirect to login or return 401
        self.assertIn(response.status_code, [302, 401])
    
    def test_audit_summary_endpoint_no_login(self):
        """Test audit summary endpoint without login."""
        response = self.client.get(f'/api/appointment/{self.appointment.id}/audit/summary')
        
        # Should redirect to login or return 401
        self.assertIn(response.status_code, [302, 401])


def run_tests():
    """Run the audit API endpoint tests."""
    print("=" * 60)
    print("AUDIT API ENDPOINTS INTEGRATION TESTS")
    print("=" * 60)
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestAuditAPIEndpoints)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    if result.wasSuccessful():
        print("✅ All audit API endpoint tests passed!")
        print(f"   Tests run: {result.testsRun}")
        print(f"   Failures: {len(result.failures)}")
        print(f"   Errors: {len(result.errors)}")
        return True
    else:
        print("❌ Some audit API endpoint tests failed!")
        print(f"   Tests run: {result.testsRun}")
        print(f"   Failures: {len(result.failures)}")
        print(f"   Errors: {len(result.errors)}")
        
        if result.failures:
            print("\nFAILURES:")
            for test, traceback in result.failures:
                print(f"- {test}: {traceback}")
        
        if result.errors:
            print("\nERRORS:")
            for test, traceback in result.errors:
                print(f"- {test}: {traceback}")
        
        return False


if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)