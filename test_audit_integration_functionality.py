#!/usr/bin/env python3
"""
Test actual functionality of audit system integration.
"""

import sys
import os
import unittest
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

try:
    from app import create_app
    from app.extensions import db
    from app.models.appointment import Appointment
    from app.models.appointment_audit import AppointmentAudit
    from app.models.user import User
    from app.models.tutor import Tutor
    from app.models.client import Client
    from app.models.service import Service, TutorService
    from app.services.appointment_service import AppointmentService
    from app.services.audit_service import AuditService
    from flask import request
    from flask_login import current_user
except ImportError as e:
    print(f"Import error: {e}")
    print("This test requires the Flask application to be properly set up.")
    sys.exit(1)


class TestAuditIntegrationFunctionality(unittest.TestCase):
    """Test the actual functionality of audit system integration."""
    
    @classmethod
    def setUpClass(cls):
        """Set up test environment."""
        cls.app = create_app()
        cls.app.config['TESTING'] = True
        cls.app.config['WTF_CSRF_ENABLED'] = False
        cls.app_context = cls.app.app_context()
        cls.app_context.push()
        
        # Create test client
        cls.client = cls.app.test_client()
        
        print("✓ Test environment initialized")
    
    @classmethod
    def tearDownClass(cls):
        """Clean up test environment."""
        cls.app_context.pop()
        print("✓ Test environment cleaned up")
    
    def setUp(self):
        """Set up each test."""
        print(f"\n--- Running {self._testMethodName} ---")
    
    def test_audit_service_methods_exist(self):
        """Test that audit service has all required methods."""
        print("Testing audit service methods...")
        
        # Check that AuditService has required methods
        required_methods = [
            'get_appointment_audit_history',
            'format_audit_entry_for_display',
            'get_audit_summary'
        ]
        
        for method in required_methods:
            self.assertTrue(hasattr(AuditService, method), 
                          f"AuditService should have {method} method")
            print(f"✓ AuditService.{method} exists")
        
        print("✓ All required audit service methods exist")
    
    def test_audit_model_methods_exist(self):
        """Test that audit model has required methods."""
        print("Testing audit model methods...")
        
        # Check that AppointmentAudit has required methods
        required_methods = ['log_action']
        
        for method in required_methods:
            self.assertTrue(hasattr(AppointmentAudit, method), 
                          f"AppointmentAudit should have {method} method")
            print(f"✓ AppointmentAudit.{method} exists")
        
        print("✓ All required audit model methods exist")
    
    @patch('app.services.appointment_service.current_user')
    @patch('app.services.appointment_service.request')
    def test_audit_logging_in_appointment_service(self, mock_request, mock_current_user):
        """Test that appointment service calls audit logging."""
        print("Testing audit logging in appointment service...")
        
        # Mock user and request
        mock_user = MagicMock()
        mock_user.is_authenticated = True
        mock_user.id = 1
        mock_user.email = '<EMAIL>'
        mock_current_user.return_value = mock_user
        
        mock_request.remote_addr = '127.0.0.1'
        mock_request.headers = {'User-Agent': 'Test Agent'}
        
        # Test that AppointmentService imports AppointmentAudit
        import inspect
        appointment_service_source = inspect.getsource(AppointmentService)
        
        self.assertIn('AppointmentAudit', appointment_service_source,
                     "AppointmentService should import AppointmentAudit")
        
        # Check for log_action calls
        log_action_calls = appointment_service_source.count('AppointmentAudit.log_action')
        self.assertGreater(log_action_calls, 0,
                          "AppointmentService should call AppointmentAudit.log_action")
        
        print(f"✓ Found {log_action_calls} audit logging calls in AppointmentService")
    
    def test_api_endpoints_exist(self):
        """Test that audit API endpoints exist."""
        print("Testing audit API endpoints...")
        
        # Test with the test client
        with self.app.test_request_context():
            # Try to access audit endpoint (should get 401/403 without proper auth)
            response = self.client.get('/api/appointment/1/audit')
            
            # Should not get 404 (endpoint exists) but should get auth error
            self.assertNotEqual(response.status_code, 404,
                              "Audit API endpoint should exist (not return 404)")
            
            print(f"✓ Audit API endpoint exists (returned {response.status_code})")
    
    def test_manager_access_control(self):
        """Test that audit endpoints check for manager role."""
        print("Testing manager access control...")
        
        # Check API source for manager role checks
        with open('app/views/api.py', 'r', encoding='utf-8') as f:
            api_content = f.read()
        
        # Look for audit endpoint function
        audit_endpoint_start = api_content.find('def get_appointment_audit')
        self.assertNotEqual(audit_endpoint_start, -1,
                           "get_appointment_audit function should exist")
        
        # Get the function content (approximately)
        audit_endpoint_end = api_content.find('\ndef ', audit_endpoint_start + 1)
        if audit_endpoint_end == -1:
            audit_endpoint_end = len(api_content)
        
        audit_function = api_content[audit_endpoint_start:audit_endpoint_end]
        
        # Check for manager role verification
        self.assertIn('manager', audit_function.lower(),
                     "Audit endpoint should check for manager role")
        
        print("✓ Manager access control verified in audit endpoint")
    
    def test_frontend_integration(self):
        """Test that frontend components are properly integrated."""
        print("Testing frontend integration...")
        
        # Check manager schedule template
        schedule_path = 'app/templates/manager/schedule.html'
        with open(schedule_path, 'r', encoding='utf-8') as f:
            schedule_content = f.read()
        
        # Should include audit trail modal
        self.assertIn('audit_trail_modal.html', schedule_content,
                     "Manager schedule should include audit trail modal")
        
        # Should include audit trail JavaScript
        self.assertIn('audit_trail_modal.js', schedule_content,
                     "Manager schedule should include audit trail JavaScript")
        
        print("✓ Manager schedule properly integrates audit trail")
        
        # Check appointment detail template
        detail_path = 'app/templates/manager/appointment_detail.html'
        with open(detail_path, 'r', encoding='utf-8') as f:
            detail_content = f.read()
        
        # Should have audit trail button
        self.assertIn('data-audit-appointment-id', detail_content,
                     "Appointment detail should have audit trail button")
        
        print("✓ Appointment detail properly integrates audit trail")
    
    def test_audit_modal_components(self):
        """Test that audit modal components are complete."""
        print("Testing audit modal components...")
        
        # Check modal template
        modal_path = 'app/templates/components/audit_trail_modal.html'
        with open(modal_path, 'r', encoding='utf-8') as f:
            modal_content = f.read()
        
        required_elements = [
            'auditTrailModal',
            'auditTimeline',
            'auditLoadingState',
            'auditErrorState',
            'auditEmptyState'
        ]
        
        for element in required_elements:
            self.assertIn(element, modal_content,
                         f"Modal template should contain {element}")
        
        print("✓ Audit modal template contains all required elements")
        
        # Check JavaScript file
        js_path = 'app/static/js/audit_trail_modal.js'
        with open(js_path, 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        required_js_elements = [
            'AuditTrailModal',
            'showAuditTrail',
            'loadAuditData',
            'renderAuditData'
        ]
        
        for element in required_js_elements:
            self.assertIn(element, js_content,
                         f"JavaScript should contain {element}")
        
        print("✓ Audit modal JavaScript contains all required functionality")
    
    def test_timezone_service_integration(self):
        """Test that timezone service is properly integrated."""
        print("Testing timezone service integration...")
        
        try:
            from app.services.timezone_service import TimezoneService
            
            # Check required methods
            required_methods = ['format_for_display']
            
            for method in required_methods:
                self.assertTrue(hasattr(TimezoneService, method),
                              f"TimezoneService should have {method} method")
            
            print("✓ Timezone service integration verified")
            
        except ImportError:
            self.fail("TimezoneService should be importable")
    
    def test_audit_system_error_handling(self):
        """Test that audit system has proper error handling."""
        print("Testing audit system error handling...")
        
        # Check audit service for error handling
        with open('app/services/audit_service.py', 'r', encoding='utf-8') as f:
            audit_service_content = f.read()
        
        # Should have try/except blocks
        try_count = audit_service_content.count('try:')
        except_count = audit_service_content.count('except')
        
        self.assertGreater(try_count, 0, "Audit service should have error handling")
        self.assertGreater(except_count, 0, "Audit service should have exception handling")
        
        print(f"✓ Audit service has {try_count} try blocks and {except_count} except blocks")
        
        # Check JavaScript for error handling
        with open('app/static/js/audit_trail_modal.js', 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        # Should have error handling
        self.assertIn('catch', js_content, "JavaScript should have error handling")
        self.assertIn('showErrorState', js_content, "JavaScript should have error state handling")
        
        print("✓ Frontend JavaScript has proper error handling")


def main():
    """Run the audit integration functionality tests."""
    print("Testing Audit System Integration Functionality")
    print("=" * 60)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add all test methods
    test_methods = [
        'test_audit_service_methods_exist',
        'test_audit_model_methods_exist',
        'test_audit_logging_in_appointment_service',
        'test_api_endpoints_exist',
        'test_manager_access_control',
        'test_frontend_integration',
        'test_audit_modal_components',
        'test_timezone_service_integration',
        'test_audit_system_error_handling'
    ]
    
    for method in test_methods:
        test_suite.addTest(TestAuditIntegrationFunctionality(method))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
    result = runner.run(test_suite)
    
    # Print summary
    print("\n" + "="*60)
    print("FUNCTIONALITY TEST SUMMARY")
    print("="*60)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFAILURES:")
        for test, traceback in result.failures:
            print(f"- {test}")
    
    if result.errors:
        print("\nERRORS:")
        for test, traceback in result.errors:
            print(f"- {test}")
    
    success_rate = ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100) if result.testsRun > 0 else 0
    print(f"\nSuccess Rate: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("✓ Audit system integration functionality PASSED")
        return 0
    elif success_rate >= 70:
        print("⚠ Audit system integration functionality PARTIALLY PASSED")
        return 1
    else:
        print("✗ Audit system integration functionality FAILED")
        return 2


if __name__ == '__main__':
    sys.exit(main())