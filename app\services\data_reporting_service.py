#!/usr/bin/env python3
"""
Data Reporting Service for TutorAide Application
Comprehensive reporting system for data inconsistencies and manual review items
"""

from datetime import datetime, date, timedelta
from typing import Dict, List, Tuple, Any, Optional
from sqlalchemy import text, and_, or_
from sqlalchemy.exc import SQLAlchemyError
from app.extensions import db
import json
import logging

class DataReportingService:
    """Service for generating comprehensive data quality and inconsistency reports."""
    
    @staticmethod
    def generate_comprehensive_report() -> Dict[str, Any]:
        """
        Generate a comprehensive data quality report including validation,
        cleanup recommendations, and manual review items.
        """
        from app.services.data_validation_service import DataValidationService
        from app.services.data_cleanup_service import DataCleanupService
        
        report = {
            'timestamp': datetime.utcnow().isoformat(),
            'report_type': 'comprehensive_data_quality',
            'sections': {
                'validation_results': None,
                'cleanup_recommendations': None,
                'manual_review_items': None,
                'data_statistics': None,
                'trend_analysis': None
            },
            'summary': {
                'overall_health_score': 0,
                'critical_issues': 0,
                'high_priority_items': 0,
                'medium_priority_items': 0,
                'low_priority_items': 0,
                'manual_review_required': 0
            },
            'recommendations': []
        }
        
        try:
            # Run validation
            validation_results = DataValidationService.validate_all_data()
            report['sections']['validation_results'] = validation_results
            
            # Generate cleanup recommendations (dry run)
            cleanup_recommendations = DataCleanupService.cleanup_all_data(dry_run=True)
            report['sections']['cleanup_recommendations'] = cleanup_recommendations
            
            # Generate manual review items
            manual_review_items = DataReportingService._generate_manual_review_items()
            report['sections']['manual_review_items'] = manual_review_items
            
            # Generate data statistics
            data_statistics = DataReportingService._generate_data_statistics()
            report['sections']['data_statistics'] = data_statistics
            
            # Generate trend analysis
            trend_analysis = DataReportingService._generate_trend_analysis()
            report['sections']['trend_analysis'] = trend_analysis
            
            # Calculate overall summary
            DataReportingService._calculate_overall_summary(report)
            
            # Generate actionable recommendations
            DataReportingService._generate_actionable_recommendations(report)
            
        except Exception as e:
            report['error'] = f"Report generation failed: {str(e)}"
            logging.error(f"Comprehensive report generation failed: {e}")
        
        return report
    
    @staticmethod
    def _generate_manual_review_items() -> Dict[str, Any]:
        """Generate items that require manual review and cannot be automatically fixed."""
        manual_review = {
            'timestamp': datetime.utcnow().isoformat(),
            'categories': {
                'data_quality_concerns': [],
                'business_rule_violations': [],
                'potential_duplicates': [],
                'unusual_patterns': [],
                'missing_information': []
            },
            'total_items': 0,
            'high_priority_count': 0,
            'medium_priority_count': 0,
            'low_priority_count': 0
        }
        
        try:
            # Data quality concerns
            manual_review['categories']['data_quality_concerns'] = DataReportingService._find_data_quality_concerns()
            
            # Business rule violations
            manual_review['categories']['business_rule_violations'] = DataReportingService._find_business_rule_violations()
            
            # Potential duplicates
            manual_review['categories']['potential_duplicates'] = DataReportingService._find_potential_duplicates()
            
            # Unusual patterns
            manual_review['categories']['unusual_patterns'] = DataReportingService._find_unusual_patterns()
            
            # Missing information
            manual_review['categories']['missing_information'] = DataReportingService._find_missing_information()
            
            # Calculate totals
            DataReportingService._calculate_manual_review_totals(manual_review)
            
        except Exception as e:
            manual_review['error'] = f"Manual review generation failed: {str(e)}"
            logging.error(f"Manual review generation failed: {e}")
        
        return manual_review
    
    @staticmethod
    def _find_data_quality_concerns() -> List[Dict[str, Any]]:
        """Find data quality issues that need manual review."""
        concerns = []
        
        try:
            # Clients with suspicious contact information
            suspicious_contacts = db.session.execute(text("""
                SELECT client_id, first_name, last_name, email, phone
                FROM clients c
                JOIN users u ON c.user_id = u.user_id
                WHERE 
                    -- Email and phone both missing or invalid
                    (u.email IS NULL OR u.email = '' OR u.email NOT LIKE '%@%')
                    AND (c.phone IS NULL OR c.phone = '' OR LENGTH(c.phone) < 10)
            """)).fetchall()
            
            if suspicious_contacts:
                concerns.append({
                    'type': 'suspicious_contact_information',
                    'priority': 'high',
                    'description': 'Clients with missing or invalid contact information',
                    'count': len(suspicious_contacts),
                    'items': [
                        {
                            'client_id': row[0],
                            'name': f"{row[1]} {row[2]}",
                            'email': row[3],
                            'phone': row[4]
                        }
                        for row in suspicious_contacts
                    ],
                    'recommendation': 'Review and update client contact information'
                })
            
            # Tutors with unusually high or low rates
            unusual_rates = db.session.execute(text("""
                SELECT tutor_id, first_name, last_name, hourly_rate
                FROM tutors
                WHERE hourly_rate IS NOT NULL 
                AND (hourly_rate < 15 OR hourly_rate > 200)
                ORDER BY hourly_rate DESC
            """)).fetchall()
            
            if unusual_rates:
                concerns.append({
                    'type': 'unusual_tutor_rates',
                    'priority': 'medium',
                    'description': 'Tutors with unusually high or low hourly rates',
                    'count': len(unusual_rates),
                    'items': [
                        {
                            'tutor_id': row[0],
                            'name': f"{row[1]} {row[2]}",
                            'hourly_rate': float(row[3]) if row[3] else None
                        }
                        for row in unusual_rates
                    ],
                    'recommendation': 'Verify tutor hourly rates are correct'
                })
            
            # Appointments with very long durations
            long_appointments = db.session.execute(text("""
                SELECT appointment_id, start_time, end_time,
                       EXTRACT(epoch FROM (end_time - start_time))/3600 as duration_hours
                FROM appointments
                WHERE end_time - start_time > INTERVAL '6 hours'
                ORDER BY (end_time - start_time) DESC
                LIMIT 20
            """)).fetchall()
            
            if long_appointments:
                concerns.append({
                    'type': 'unusually_long_appointments',
                    'priority': 'medium',
                    'description': 'Appointments with unusually long durations (>6 hours)',
                    'count': len(long_appointments),
                    'items': [
                        {
                            'appointment_id': row[0],
                            'start_time': row[1],
                            'end_time': row[2],
                            'duration_hours': float(row[3])
                        }
                        for row in long_appointments
                    ],
                    'recommendation': 'Verify appointment durations are correct'
                })
            
        except Exception as e:
            concerns.append({
                'type': 'error',
                'priority': 'high',
                'description': f'Error finding data quality concerns: {str(e)}'
            })
        
        return concerns
    
    @staticmethod
    def _find_business_rule_violations() -> List[Dict[str, Any]]:
        """Find violations of business rules that need manual review."""
        violations = []
        
        try:
            # Appointments scheduled on weekends
            weekend_appointments = db.session.execute(text("""
                SELECT appointment_id, start_time, 
                       EXTRACT(dow FROM start_time) as day_of_week
                FROM appointments
                WHERE EXTRACT(dow FROM start_time) IN (0, 6)  -- Sunday = 0, Saturday = 6
                AND status NOT IN ('cancelled', 'no-show')
                AND start_time > CURRENT_TIMESTAMP - INTERVAL '30 days'
                ORDER BY start_time DESC
                LIMIT 50
            """)).fetchall()
            
            if weekend_appointments:
                violations.append({
                    'type': 'weekend_appointments',
                    'priority': 'low',
                    'description': 'Appointments scheduled on weekends',
                    'count': len(weekend_appointments),
                    'items': [
                        {
                            'appointment_id': row[0],
                            'start_time': row[1],
                            'day_of_week': 'Sunday' if row[2] == 0 else 'Saturday'
                        }
                        for row in weekend_appointments
                    ],
                    'recommendation': 'Verify weekend appointments are intentional'
                })
            
            # Clients with excessive appointment frequency
            frequent_clients = db.session.execute(text("""
                SELECT c.client_id, c.first_name, c.last_name, COUNT(a.appointment_id) as appointment_count
                FROM clients c
                JOIN appointments a ON c.client_id = a.client_id
                WHERE a.start_time > CURRENT_DATE - INTERVAL '30 days'
                GROUP BY c.client_id, c.first_name, c.last_name
                HAVING COUNT(a.appointment_id) > 20
                ORDER BY COUNT(a.appointment_id) DESC
            """)).fetchall()
            
            if frequent_clients:
                violations.append({
                    'type': 'excessive_appointment_frequency',
                    'priority': 'medium',
                    'description': 'Clients with more than 20 appointments in the last 30 days',
                    'count': len(frequent_clients),
                    'items': [
                        {
                            'client_id': row[0],
                            'name': f"{row[1]} {row[2]}",
                            'appointment_count': row[3]
                        }
                        for row in frequent_clients
                    ],
                    'recommendation': 'Review high-frequency clients for billing accuracy'
                })
            
            # Tutors with overlapping appointments (potential double-booking)
            overlapping_tutors = db.session.execute(text("""
                SELECT t.tutor_id, t.first_name, t.last_name, COUNT(*) as overlap_count
                FROM tutors t
                JOIN appointments a1 ON t.tutor_id = a1.tutor_id
                JOIN appointments a2 ON t.tutor_id = a2.tutor_id
                WHERE a1.appointment_id < a2.appointment_id
                AND a1.status NOT IN ('cancelled', 'no-show')
                AND a2.status NOT IN ('cancelled', 'no-show')
                AND (a1.start_time, a1.end_time) OVERLAPS (a2.start_time, a2.end_time)
                AND a1.start_time > CURRENT_DATE - INTERVAL '30 days'
                GROUP BY t.tutor_id, t.first_name, t.last_name
                ORDER BY COUNT(*) DESC
            """)).fetchall()
            
            if overlapping_tutors:
                violations.append({
                    'type': 'tutor_double_booking',
                    'priority': 'high',
                    'description': 'Tutors with overlapping appointments (potential double-booking)',
                    'count': len(overlapping_tutors),
                    'items': [
                        {
                            'tutor_id': row[0],
                            'name': f"{row[1]} {row[2]}",
                            'overlap_count': row[3]
                        }
                        for row in overlapping_tutors
                    ],
                    'recommendation': 'Review and resolve scheduling conflicts'
                })
            
        except Exception as e:
            violations.append({
                'type': 'error',
                'priority': 'high',
                'description': f'Error finding business rule violations: {str(e)}'
            })
        
        return violations
    
    @staticmethod
    def _find_potential_duplicates() -> List[Dict[str, Any]]:
        """Find potential duplicate records that need manual review."""
        duplicates = []
        
        try:
            # Potential duplicate clients (same name and similar contact info)
            duplicate_clients = db.session.execute(text("""
                SELECT c1.client_id as client1_id, c1.first_name as name1, c1.last_name as lastname1,
                       c2.client_id as client2_id, c2.first_name as name2, c2.last_name as lastname2,
                       u1.email as email1, u2.email as email2
                FROM clients c1
                JOIN users u1 ON c1.user_id = u1.user_id
                JOIN clients c2 ON c1.client_id < c2.client_id
                JOIN users u2 ON c2.user_id = u2.user_id
                WHERE LOWER(c1.first_name) = LOWER(c2.first_name)
                AND LOWER(c1.last_name) = LOWER(c2.last_name)
                AND (
                    LOWER(u1.email) = LOWER(u2.email)
                    OR (c1.phone IS NOT NULL AND c2.phone IS NOT NULL AND c1.phone = c2.phone)
                )
                LIMIT 20
            """)).fetchall()
            
            if duplicate_clients:
                duplicates.append({
                    'type': 'potential_duplicate_clients',
                    'priority': 'high',
                    'description': 'Clients with identical names and contact information',
                    'count': len(duplicate_clients),
                    'items': [
                        {
                            'client1_id': row[0],
                            'client1_name': f"{row[1]} {row[2]}",
                            'client1_email': row[6],
                            'client2_id': row[3],
                            'client2_name': f"{row[4]} {row[5]}",
                            'client2_email': row[7]
                        }
                        for row in duplicate_clients
                    ],
                    'recommendation': 'Review and merge duplicate client records if confirmed'
                })
            
            # Potential duplicate tutors
            duplicate_tutors = db.session.execute(text("""
                SELECT t1.tutor_id as tutor1_id, t1.first_name as name1, t1.last_name as lastname1,
                       t2.tutor_id as tutor2_id, t2.first_name as name2, t2.last_name as lastname2,
                       u1.email as email1, u2.email as email2
                FROM tutors t1
                JOIN users u1 ON t1.user_id = u1.user_id
                JOIN tutors t2 ON t1.tutor_id < t2.tutor_id
                JOIN users u2 ON t2.user_id = u2.user_id
                WHERE LOWER(t1.first_name) = LOWER(t2.first_name)
                AND LOWER(t1.last_name) = LOWER(t2.last_name)
                AND LOWER(u1.email) = LOWER(u2.email)
                LIMIT 10
            """)).fetchall()
            
            if duplicate_tutors:
                duplicates.append({
                    'type': 'potential_duplicate_tutors',
                    'priority': 'high',
                    'description': 'Tutors with identical names and email addresses',
                    'count': len(duplicate_tutors),
                    'items': [
                        {
                            'tutor1_id': row[0],
                            'tutor1_name': f"{row[1]} {row[2]}",
                            'tutor1_email': row[6],
                            'tutor2_id': row[3],
                            'tutor2_name': f"{row[4]} {row[5]}",
                            'tutor2_email': row[7]
                        }
                        for row in duplicate_tutors
                    ],
                    'recommendation': 'Review and merge duplicate tutor records if confirmed'
                })
            
        except Exception as e:
            duplicates.append({
                'type': 'error',
                'priority': 'high',
                'description': f'Error finding potential duplicates: {str(e)}'
            })
        
        return duplicates
    
    @staticmethod
    def _find_unusual_patterns() -> List[Dict[str, Any]]:
        """Find unusual data patterns that might indicate issues."""
        patterns = []
        
        try:
            # Clients with no appointments in the last 6 months
            inactive_clients = db.session.execute(text("""
                SELECT c.client_id, c.first_name, c.last_name, 
                       MAX(a.start_time) as last_appointment
                FROM clients c
                LEFT JOIN appointments a ON c.client_id = a.client_id
                GROUP BY c.client_id, c.first_name, c.last_name
                HAVING MAX(a.start_time) < CURRENT_DATE - INTERVAL '6 months' 
                   OR MAX(a.start_time) IS NULL
                ORDER BY MAX(a.start_time) ASC NULLS FIRST
                LIMIT 50
            """)).fetchall()
            
            if inactive_clients:
                patterns.append({
                    'type': 'inactive_clients',
                    'priority': 'low',
                    'description': 'Clients with no appointments in the last 6 months',
                    'count': len(inactive_clients),
                    'items': [
                        {
                            'client_id': row[0],
                            'name': f"{row[1]} {row[2]}",
                            'last_appointment': row[3]
                        }
                        for row in inactive_clients
                    ],
                    'recommendation': 'Consider archiving or following up with inactive clients'
                })
            
            # Tutors with very few appointments
            underutilized_tutors = db.session.execute(text("""
                SELECT t.tutor_id, t.first_name, t.last_name, 
                       COUNT(a.appointment_id) as appointment_count
                FROM tutors t
                LEFT JOIN appointments a ON t.tutor_id = a.tutor_id 
                    AND a.start_time > CURRENT_DATE - INTERVAL '3 months'
                WHERE t.is_active = TRUE
                GROUP BY t.tutor_id, t.first_name, t.last_name
                HAVING COUNT(a.appointment_id) < 5
                ORDER BY COUNT(a.appointment_id) ASC
            """)).fetchall()
            
            if underutilized_tutors:
                patterns.append({
                    'type': 'underutilized_tutors',
                    'priority': 'medium',
                    'description': 'Active tutors with fewer than 5 appointments in the last 3 months',
                    'count': len(underutilized_tutors),
                    'items': [
                        {
                            'tutor_id': row[0],
                            'name': f"{row[1]} {row[2]}",
                            'appointment_count': row[3]
                        }
                        for row in underutilized_tutors
                    ],
                    'recommendation': 'Review tutor utilization and consider marketing or training'
                })
            
        except Exception as e:
            patterns.append({
                'type': 'error',
                'priority': 'high',
                'description': f'Error finding unusual patterns: {str(e)}'
            })
        
        return patterns
    
    @staticmethod
    def _find_missing_information() -> List[Dict[str, Any]]:
        """Find records with missing critical information."""
        missing_info = []
        
        try:
            # Clients missing emergency contact information
            missing_emergency = db.session.execute(text("""
                SELECT client_id, first_name, last_name
                FROM clients
                WHERE (emergency_contact_name IS NULL OR emergency_contact_name = '')
                   OR (emergency_contact_phone IS NULL OR emergency_contact_phone = '')
                ORDER BY insert_date DESC
                LIMIT 50
            """)).fetchall()
            
            if missing_emergency:
                missing_info.append({
                    'type': 'missing_emergency_contact',
                    'priority': 'medium',
                    'description': 'Clients missing emergency contact information',
                    'count': len(missing_emergency),
                    'items': [
                        {
                            'client_id': row[0],
                            'name': f"{row[1]} {row[2]}"
                        }
                        for row in missing_emergency
                    ],
                    'recommendation': 'Collect emergency contact information for safety'
                })
            
            # Tutors missing qualifications or bio
            missing_tutor_info = db.session.execute(text("""
                SELECT tutor_id, first_name, last_name
                FROM tutors
                WHERE (qualifications IS NULL OR qualifications = '')
                   OR (bio IS NULL OR bio = '')
                ORDER BY insert_date DESC
                LIMIT 30
            """)).fetchall()
            
            if missing_tutor_info:
                missing_info.append({
                    'type': 'missing_tutor_profile',
                    'priority': 'low',
                    'description': 'Tutors missing qualifications or bio information',
                    'count': len(missing_tutor_info),
                    'items': [
                        {
                            'tutor_id': row[0],
                            'name': f"{row[1]} {row[2]}"
                        }
                        for row in missing_tutor_info
                    ],
                    'recommendation': 'Complete tutor profiles for better client matching'
                })
            
        except Exception as e:
            missing_info.append({
                'type': 'error',
                'priority': 'high',
                'description': f'Error finding missing information: {str(e)}'
            })
        
        return missing_info
    
    @staticmethod
    def _generate_data_statistics() -> Dict[str, Any]:
        """Generate comprehensive data statistics."""
        statistics = {
            'timestamp': datetime.utcnow().isoformat(),
            'table_counts': {},
            'growth_metrics': {},
            'utilization_metrics': {},
            'quality_metrics': {}
        }
        
        try:
            # Table counts
            tables = ['users', 'clients', 'tutors', 'dependants', 'appointments', 
                     'appointment_recurring_schedules', 'subscriptions', 'invoices', 
                     'services', 'tutor_services']
            
            for table in tables:
                count_query = text(f"SELECT COUNT(*) FROM {table}")
                count = db.session.execute(count_query).scalar()
                statistics['table_counts'][table] = count
            
            # Growth metrics (last 30 days)
            growth_queries = {
                'new_clients': "SELECT COUNT(*) FROM clients WHERE insert_date > CURRENT_DATE - INTERVAL '30 days'",
                'new_appointments': "SELECT COUNT(*) FROM appointments WHERE insert_date > CURRENT_DATE - INTERVAL '30 days'",
                'new_tutors': "SELECT COUNT(*) FROM tutors WHERE insert_date > CURRENT_DATE - INTERVAL '30 days'"
            }
            
            for metric, query in growth_queries.items():
                count = db.session.execute(text(query)).scalar()
                statistics['growth_metrics'][metric] = count
            
            # Utilization metrics
            utilization_queries = {
                'active_tutors': "SELECT COUNT(*) FROM tutors WHERE is_active = TRUE",
                'appointments_this_month': "SELECT COUNT(*) FROM appointments WHERE start_time >= DATE_TRUNC('month', CURRENT_DATE)",
                'completed_appointments_this_month': "SELECT COUNT(*) FROM appointments WHERE status = 'completed' AND start_time >= DATE_TRUNC('month', CURRENT_DATE)",
                'active_subscriptions': "SELECT COUNT(*) FROM subscriptions WHERE status = 'active'"
            }
            
            for metric, query in utilization_queries.items():
                count = db.session.execute(text(query)).scalar()
                statistics['utilization_metrics'][metric] = count
            
            # Quality metrics
            quality_queries = {
                'users_with_verified_email': "SELECT COUNT(*) FROM users WHERE email_verified = TRUE",
                'clients_with_phone': "SELECT COUNT(*) FROM clients WHERE phone IS NOT NULL AND phone != ''",
                'tutors_with_bio': "SELECT COUNT(*) FROM tutors WHERE bio IS NOT NULL AND bio != ''",
                'appointments_with_notes': "SELECT COUNT(*) FROM appointments WHERE notes IS NOT NULL AND notes != ''"
            }
            
            for metric, query in quality_queries.items():
                count = db.session.execute(text(query)).scalar()
                statistics['quality_metrics'][metric] = count
            
        except Exception as e:
            statistics['error'] = f"Statistics generation failed: {str(e)}"
            logging.error(f"Statistics generation failed: {e}")
        
        return statistics
    
    @staticmethod
    def _generate_trend_analysis() -> Dict[str, Any]:
        """Generate trend analysis for the last 6 months."""
        trends = {
            'timestamp': datetime.utcnow().isoformat(),
            'appointment_trends': [],
            'client_growth': [],
            'tutor_utilization': [],
            'revenue_trends': []
        }
        
        try:
            # Appointment trends by month
            appointment_trends = db.session.execute(text("""
                SELECT DATE_TRUNC('month', start_time) as month,
                       COUNT(*) as total_appointments,
                       COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_appointments,
                       COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled_appointments
                FROM appointments
                WHERE start_time >= CURRENT_DATE - INTERVAL '6 months'
                GROUP BY DATE_TRUNC('month', start_time)
                ORDER BY month
            """)).fetchall()
            
            trends['appointment_trends'] = [
                {
                    'month': row[0],
                    'total_appointments': row[1],
                    'completed_appointments': row[2],
                    'cancelled_appointments': row[3],
                    'completion_rate': round((row[2] / row[1] * 100) if row[1] > 0 else 0, 2)
                }
                for row in appointment_trends
            ]
            
            # Client growth by month
            client_growth = db.session.execute(text("""
                SELECT DATE_TRUNC('month', insert_date) as month,
                       COUNT(*) as new_clients
                FROM clients
                WHERE insert_date >= CURRENT_DATE - INTERVAL '6 months'
                GROUP BY DATE_TRUNC('month', insert_date)
                ORDER BY month
            """)).fetchall()
            
            trends['client_growth'] = [
                {
                    'month': row[0],
                    'new_clients': row[1]
                }
                for row in client_growth
            ]
            
        except Exception as e:
            trends['error'] = f"Trend analysis failed: {str(e)}"
            logging.error(f"Trend analysis failed: {e}")
        
        return trends
    
    @staticmethod
    def _calculate_manual_review_totals(manual_review: Dict[str, Any]) -> None:
        """Calculate totals for manual review items."""
        total_items = 0
        high_priority = 0
        medium_priority = 0
        low_priority = 0
        
        for category_name, category_items in manual_review['categories'].items():
            for item in category_items:
                if item.get('type') != 'error':
                    total_items += item.get('count', 0)
                    
                    priority = item.get('priority', 'low')
                    if priority == 'high':
                        high_priority += item.get('count', 0)
                    elif priority == 'medium':
                        medium_priority += item.get('count', 0)
                    else:
                        low_priority += item.get('count', 0)
        
        manual_review.update({
            'total_items': total_items,
            'high_priority_count': high_priority,
            'medium_priority_count': medium_priority,
            'low_priority_count': low_priority
        })
    
    @staticmethod
    def _calculate_overall_summary(report: Dict[str, Any]) -> None:
        """Calculate overall summary statistics for the comprehensive report."""
        validation_results = report['sections']['validation_results']
        cleanup_recommendations = report['sections']['cleanup_recommendations']
        manual_review_items = report['sections']['manual_review_items']
        
        # Count critical issues
        critical_issues = validation_results['summary']['critical_issues']
        
        # Count priority items
        high_priority = manual_review_items['high_priority_count']
        medium_priority = manual_review_items['medium_priority_count']
        low_priority = manual_review_items['low_priority_count']
        
        # Calculate health score (0-100)
        total_checks = validation_results['summary']['total_checks']
        passed_checks = validation_results['summary']['passed_checks']
        health_score = round((passed_checks / total_checks * 100) if total_checks > 0 else 0, 1)
        
        # Adjust health score based on critical issues
        if critical_issues > 0:
            health_score = max(0, health_score - (critical_issues * 10))
        
        report['summary'].update({
            'overall_health_score': health_score,
            'critical_issues': critical_issues,
            'high_priority_items': high_priority,
            'medium_priority_items': medium_priority,
            'low_priority_items': low_priority,
            'manual_review_required': manual_review_items['total_items']
        })
    
    @staticmethod
    def _generate_actionable_recommendations(report: Dict[str, Any]) -> None:
        """Generate actionable recommendations based on the report findings."""
        recommendations = []
        
        summary = report['summary']
        
        # Health score recommendations
        if summary['overall_health_score'] < 70:
            recommendations.append({
                'priority': 'high',
                'category': 'data_quality',
                'title': 'Improve Overall Data Quality',
                'description': f"Data health score is {summary['overall_health_score']}%. Focus on resolving critical issues first.",
                'actions': [
                    'Run automated cleanup procedures',
                    'Address all critical validation issues',
                    'Implement data validation at input level'
                ]
            })
        
        # Critical issues recommendations
        if summary['critical_issues'] > 0:
            recommendations.append({
                'priority': 'critical',
                'category': 'data_integrity',
                'title': 'Resolve Critical Data Issues',
                'description': f"Found {summary['critical_issues']} critical data integrity issues.",
                'actions': [
                    'Review and fix foreign key violations',
                    'Resolve orphaned records',
                    'Validate data consistency'
                ]
            })
        
        # Manual review recommendations
        if summary['manual_review_required'] > 50:
            recommendations.append({
                'priority': 'medium',
                'category': 'manual_review',
                'title': 'Schedule Manual Data Review',
                'description': f"{summary['manual_review_required']} items require manual review.",
                'actions': [
                    'Prioritize high-priority manual review items',
                    'Assign data steward for regular reviews',
                    'Create data quality monitoring dashboard'
                ]
            })
        
        # Cleanup recommendations
        cleanup_summary = report['sections']['cleanup_recommendations']['summary']
        if cleanup_summary['records_affected'] > 0:
            recommendations.append({
                'priority': 'medium',
                'category': 'data_cleanup',
                'title': 'Run Automated Data Cleanup',
                'description': f"{cleanup_summary['records_affected']} records can be automatically cleaned up.",
                'actions': [
                    'Review cleanup recommendations',
                    'Run cleanup procedures in test environment first',
                    'Schedule regular cleanup maintenance'
                ]
            })
        
        report['recommendations'] = recommendations
    
    @staticmethod
    def export_report_to_json(report: Dict[str, Any], filename: Optional[str] = None) -> str:
        """Export report to JSON file."""
        if filename is None:
            timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
            filename = f"data_quality_report_{timestamp}.json"
        
        try:
            with open(filename, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            return filename
        except Exception as e:
            logging.error(f"Failed to export report to JSON: {e}")
            raise
    
    @staticmethod
    def generate_summary_report() -> Dict[str, Any]:
        """Generate a concise summary report for quick review."""
        from app.services.data_validation_service import DataValidationService
        
        summary_report = {
            'timestamp': datetime.utcnow().isoformat(),
            'report_type': 'summary',
            'health_check': {
                'overall_status': 'unknown',
                'critical_issues': 0,
                'warnings': 0,
                'recommendations_count': 0
            },
            'key_metrics': {},
            'top_issues': [],
            'next_actions': []
        }
        
        try:
            # Run quick validation
            validation_results = DataValidationService.validate_all_data()
            
            # Determine overall status
            critical_issues = validation_results['summary']['critical_issues']
            warnings = validation_results['summary']['warnings']
            
            if critical_issues > 0:
                overall_status = 'critical'
            elif warnings > 10:
                overall_status = 'warning'
            elif validation_results['summary']['passed_checks'] / validation_results['summary']['total_checks'] > 0.9:
                overall_status = 'good'
            else:
                overall_status = 'needs_attention'
            
            summary_report['health_check'].update({
                'overall_status': overall_status,
                'critical_issues': critical_issues,
                'warnings': warnings,
                'recommendations_count': len(validation_results.get('recommendations', []))
            })
            
            # Key metrics
            summary_report['key_metrics'] = {
                'total_validation_checks': validation_results['summary']['total_checks'],
                'passed_checks': validation_results['summary']['passed_checks'],
                'failed_checks': validation_results['summary']['failed_checks'],
                'health_score': round((validation_results['summary']['passed_checks'] / validation_results['summary']['total_checks'] * 100), 1)
            }
            
            # Top issues (first 5 critical issues)
            top_issues = []
            for table_name, table_results in validation_results['results'].items():
                for issue in table_results['issues']:
                    if issue.get('severity') == 'critical':
                        top_issues.append({
                            'table': table_name,
                            'type': issue['type'],
                            'count': issue.get('count', 1),
                            'severity': issue['severity']
                        })
                        if len(top_issues) >= 5:
                            break
                if len(top_issues) >= 5:
                    break
            
            summary_report['top_issues'] = top_issues
            
            # Next actions
            if critical_issues > 0:
                summary_report['next_actions'].append('Address critical data integrity issues immediately')
            if warnings > 20:
                summary_report['next_actions'].append('Review and resolve data quality warnings')
            if overall_status in ['critical', 'warning']:
                summary_report['next_actions'].append('Run comprehensive data cleanup procedures')
            
            if not summary_report['next_actions']:
                summary_report['next_actions'].append('Continue regular data quality monitoring')
            
        except Exception as e:
            summary_report['error'] = f"Summary report generation failed: {str(e)}"
            logging.error(f"Summary report generation failed: {e}")
        
        return summary_report