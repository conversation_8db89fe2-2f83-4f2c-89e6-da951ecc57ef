#!/usr/bin/env python3
"""
Apply the recurring appointments dependant_id migration
Run this from the project root directory
"""
import os
import sys

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def run_migration():
    """Apply the migration to add dependant_id to recurring_appointments"""
    try:
        from app import create_app
        from app.extensions import db
        
        app = create_app()
        
        with app.app_context():
            # Check if column already exists
            try:
                result = db.session.execute(db.text("SELECT dependant_id FROM recurring_appointments LIMIT 1"))
                print("✓ Column 'dependant_id' already exists in recurring_appointments table")
                return True
            except Exception:
                # Column doesn't exist, we need to add it
                pass
            
            print("Adding dependant_id column to recurring_appointments table...")
            
            # Add the dependant_id column
            db.session.execute(db.text("""
                ALTER TABLE recurring_appointments 
                ADD COLUMN dependant_id INTEGER REFERENCES dependants(id)
            """))
            
            print("✓ Added dependant_id column")
            
            # Add index for better performance
            try:
                db.session.execute(db.text("""
                    CREATE INDEX IF NOT EXISTS idx_recurring_appointments_dependant_id 
                    ON recurring_appointments(dependant_id)
                """))
                print("✓ Added index on dependant_id")
            except Exception as e:
                print(f"⚠ Index creation failed (may already exist): {e}")
            
            db.session.commit()
            print("✅ Migration completed successfully!")
            return True
            
    except ImportError as e:
        print(f"❌ Could not import application modules: {e}")
        print("Make sure you're running this from the project root directory")
        return False
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        try:
            db.session.rollback()
        except:
            pass
        return False

if __name__ == '__main__':
    print("Applying recurring appointments migration...")
    success = run_migration()
    
    if success:
        print("\n🎉 Migration applied successfully!")
        print("You can now create recurring appointments for dependants.")
    else:
        print("\n💥 Migration failed. Please check the error messages above.")
    
    sys.exit(0 if success else 1)
