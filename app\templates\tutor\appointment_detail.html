<!-- app/templates/tutor/appointment_detail.html -->
{% extends "base.html" %}

{% block title %}{{ t('tutor.appointment_detail.page_title') }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">{{ t('tutor.appointment_detail.title') }}</h2>
    </div>
    <div class="col-md-4 text-end">
        <div class="mb-2">
            <a href="{{ url_for('tutor.schedule') }}" class="btn btn-outline-primary me-2">
                <i class="fas fa-calendar-alt"></i> {{ t('tutor.appointment_detail.schedule') }}
            </a>
            <a href="{{ url_for('tutor.update_appointment', id=appointment.id) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> {{ t('tutor.appointment_detail.update') }}
            </a>
        </div>
        {% if appointment.status == 'scheduled' %}
        <form method="POST" action="{{ url_for('tutor.take_attendance', id=appointment.id) }}" class="d-inline">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            <button type="submit" class="btn btn-success btn-lg w-100">
                <i class="fas fa-check-circle"></i> {{ t('tutor.appointment_detail.take_attendance') }}
            </button>
        </form>
        {% endif %}
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <!-- Appointment Details Card -->
        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">{{ t('tutor.appointment_detail.appointment_information') }}</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <h6 class="mb-2">{{ t('tutor.appointment_detail.date_time') }}</h6>
                        <p>
                            {{ appointment.start_time.strftime('%A, %B %d, %Y') }}<br>
                            {{ appointment.start_time.strftime('%I:%M %p') }} - {{ appointment.end_time.strftime('%I:%M %p') }}
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="mb-2">{{ t('tutor.appointment_detail.status') }}</h6>
                        <p>
                            <span class="badge bg-{{ 'primary' if appointment.status == 'scheduled' else 'success' if appointment.status == 'completed' else 'danger' if appointment.status == 'cancelled' else 'warning' }} p-2">
                                {{ appointment.status | capitalize }}
                            </span>
                        </p>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <h6 class="mb-2">
                            {% if appointment.dependant_id %}
                                {{ t('tutor.appointment_detail.student') }}
                            {% else %}
                                {{ t('tutor.appointment_detail.client') }}
                            {% endif %}
                        </h6>
                        <p>
                            {% if appointment.dependant_id and appointment.dependant %}
                                {{ appointment.dependant.first_name }} {{ appointment.dependant.last_name }}
                                <br>
                                <small class="text-muted">
                                    {{ t('tutor.appointment_detail.client') }}: {{ appointment.client.first_name }} {{ appointment.client.last_name }}
                                </small>
                            {% else %}
                                {{ appointment.client.first_name }} {{ appointment.client.last_name }}
                            {% endif %}
                            <a href="{{ url_for('tutor.view_client', id=appointment.client.id) }}" class="ms-2 text-decoration-none">
                                <i class="fas fa-external-link-alt"></i>
                            </a>
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="mb-2">{{ t('tutor.appointment_detail.duration') }}</h6>
                        <p>{{ appointment.duration_minutes }} {{ t('tutor.appointment_detail.minutes') }}</p>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <h6 class="mb-2">{{ t('tutor.appointment_detail.service') }}</h6>
                        <p>
                            {% if appointment.tutor_service and appointment.tutor_service.service %}
                                {{ appointment.tutor_service.service.name }}
                            {% else %}
                                N/A
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="mb-2">{{ t('tutor.appointment_detail.tutor_rate') }}</h6>
                        <p>
                            {% if appointment.tutor_service %}
                                ${{ "%.2f"|format(appointment.tutor_service.tutor_rate) }} {{ t('tutor.appointment_detail.per_hour') }}
                            {% else %}
                                N/A
                            {% endif %}
                        </p>
                    </div>
                </div>

                {% if appointment.notes %}
                    <div class="row">
                        <div class="col-12">
                            <h6 class="mb-2">{{ t('tutor.appointment_detail.notes') }}</h6>
                            <p class="text-muted">{{ appointment.notes }}</p>
                        </div>
                    </div>
                {% endif %}

                <!-- Attendance Confirmation Section -->
                {% if appointment.status == 'awaiting_confirmation' or appointment.status == 'awaiting_confirm' %}
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="alert alert-warning">
                            <i class="fas fa-clock"></i> {{ t('tutor.appointment_detail.attendance_confirmation') }}
                        </div>
                        <form method="POST" action="{{ url_for('tutor.confirm_attendance', id=appointment.id) }}" class="d-inline">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            <input type="hidden" name="confirmation" value="attended">
                            <button type="submit" class="btn btn-success me-2">
                                <i class="fas fa-check-circle"></i> {{ t('tutor.appointment_detail.session_attended') }}
                            </button>
                        </form>
                        <form method="POST" action="{{ url_for('tutor.confirm_attendance', id=appointment.id) }}" class="d-inline">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            <input type="hidden" name="confirmation" value="cancelled_with_notice">
                            <button type="submit" class="btn btn-warning me-2">
                                <i class="fas fa-exclamation-circle"></i> {{ t('tutor.appointment_detail.cancelled_with_notice') }}
                            </button>
                        </form>
                        <form method="POST" action="{{ url_for('tutor.confirm_attendance', id=appointment.id) }}" class="d-inline">
                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                            <input type="hidden" name="confirmation" value="cancelled_without_notice">
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-times-circle"></i> {{ t('tutor.appointment_detail.cancelled_without_notice') }}
                            </button>
                        </form>
                    </div>
                </div>
                {% endif %}
            </div>
            <div class="card-footer">
                <a href="{{ url_for('tutor.update_appointment', id=appointment.id) }}" class="btn btn-primary">
                    <i class="fas fa-edit"></i> {{ t('tutor.appointment_detail.update_status_notes') }}
                </a>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Client Details Card -->
        <div class="card shadow mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">{{ t('tutor.appointment_detail.client_information') }}</h5>
            </div>
            <div class="card-body">
                <h6>{{ appointment.client.first_name }} {{ appointment.client.last_name }}</h6>

                {% if appointment.client.client_type == 'individual' and appointment.client.individual_clients %}
                    {% if appointment.client.individual_clients.date_of_birth %}
                        <div class="mb-3">
                            <h6 class="text-muted mb-2">{{ t('tutor.appointment_detail.age') }}</h6>
                            <p>{{ appointment.client.individual_clients.age }} {{ t('tutor.appointment_detail.years') }}</p>
                        </div>
                    {% endif %}

                    {% if appointment.client.individual_clients.notes %}
                        <div class="mb-3">
                            <h6 class="text-muted mb-2">{{ t('tutor.appointment_detail.notes') }}</h6>
                            <p>{{ appointment.client.individual_clients.notes }}</p>
                        </div>
                    {% endif %}
                {% endif %}

                <a href="{{ url_for('tutor.view_client', id=appointment.client.id) }}" class="btn btn-outline-primary w-100">
                    <i class="fas fa-user"></i> {{ t('tutor.appointment_detail.view_full_profile') }}
                </a>
            </div>
        </div>

        <!-- Session History Card -->
        <div class="card shadow">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">{{ t('tutor.appointment_detail.session_history') }}</h5>
            </div>
            <div class="card-body">
                <h6 class="mb-3">{{ t('tutor.appointment_detail.recent_sessions') }}</h6>

                {% set past_sessions = appointment.client.appointments.filter(
                    Appointment.tutor_id == appointment.tutor_id,
                    Appointment.id != appointment.id,
                    Appointment.status.in_(['completed', 'no-show']),
                    Appointment.start_time < appointment.start_time
                ).order_by(Appointment.start_time.desc()).limit(5).all() %}

                {% if past_sessions %}
                    <ul class="list-group list-group-flush">
                        {% for session in past_sessions %}
                            <li class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">{{ session.start_time.strftime('%Y-%m-%d') }}</h6>
                                    <small class="badge bg-{{ 'success' if session.status == 'completed' else 'warning' }}">
                                        {{ session.status | capitalize }}
                                    </small>
                                </div>
                                <p class="mb-1">
                                    {{ session.start_time.strftime('%I:%M %p') }} - {{ session.end_time.strftime('%I:%M %p') }}
                                </p>
                                {% if session.notes %}
                                    <small class="text-muted">{{ session.notes | truncate(100) }}</small>
                                {% endif %}
                                <div class="mt-2">
                                    <a href="{{ url_for('tutor.view_appointment', id=session.id) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i> {{ t('tutor.appointment_detail.view') }}
                                    </a>
                                </div>
                            </li>
                        {% endfor %}
                    </ul>
                {% else %}
                    <p class="text-muted">{{ t('tutor.appointment_detail.no_previous_sessions') }}</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}