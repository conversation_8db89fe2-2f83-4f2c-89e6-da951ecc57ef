# Stripe Direct Bank Payouts for Tutor Payments

## Overview

This implementation allows managers to process tutor payments directly to their Canadian bank accounts using Stripe's payout functionality. The system creates secure, one-time bank account tokens and processes direct EFT transfers without requiring tutors to create Stripe accounts.

## Features

- **Direct Bank Transfers**: Payments are sent directly to tutors' encrypted bank account information
- **Canadian EFT Support**: Properly formatted routing numbers for Canadian banking
- **Batch Processing**: Process multiple payments simultaneously
- **Audit Trail**: All payouts are tracked with Stripe payout IDs
- **Error Handling**: Comprehensive error handling with detailed feedback
- **Security**: Bank account tokens are created on-demand and never stored

## Database Changes

### New Column: `tutor_payments.stripe_payout_id`
- **Type**: VARCHAR(255), nullable
- **Purpose**: Stores Stripe payout reference ID for tracking
- **Index**: Added for faster lookups

## Implementation Details

### 1. StripeService Enhancements

#### `format_canadian_routing_number(transit_number, institution_number)`
- Formats Canadian bank routing numbers for Stripe
- Format: 0 + institution (3 digits) + transit (5 digits)
- Example: Transit 12345, Institution 001 → *********

#### `create_bank_account_token(account_number, routing_number, account_holder_name)`
- Creates one-time bank account tokens for Canadian EFT
- Tokens are used immediately and not stored
- Returns Stripe token object or None on error

#### `create_payout(amount, bank_account_token, description, metadata)`
- Creates Stripe payout to bank account
- Converts amount to cents (CAD currency)
- Includes metadata for tracking

### 2. TutorPaymentService Enhancements

#### `process_payment(payment_id, use_stripe_payout=True)`
- Enhanced to support Stripe payouts
- Returns tuple: (payment, message)
- Handles rollback on errors

#### `_process_stripe_payout(payment)`
- Private method for Stripe payout processing
- Decrypts tutor bank information
- Creates bank token and processes payout
- Stores payout ID in payment record

#### `process_multiple_payments(payment_ids, use_stripe_payout=True)`
- Batch processing with detailed results
- Returns success/failure counts and details
- Handles individual payment errors gracefully

### 3. Manager Interface Updates

#### Enhanced Payment Processing
- Added Stripe payout option checkbox (default: enabled)
- Displays Stripe payout IDs for completed payments
- Improved error messaging and success feedback
- Audit logging for all processed payments

## Security Considerations

1. **Encryption**: All bank account information remains encrypted in the database
2. **Token Security**: Bank account tokens are created on-demand and never stored
3. **Audit Trail**: All payout activities are logged with payment IDs (no bank details)
4. **Error Handling**: Bank information is never exposed in error messages

## Usage

### Processing Individual Payments
1. Navigate to Manager → Tutor Payments
2. Click "Process" button for individual payment
3. Confirm with Stripe payout option enabled
4. Payment is processed and Stripe payout ID is stored

### Batch Processing
1. Select multiple pending payments using checkboxes
2. Click "Process Selected Payments"
3. Review summary and confirm with Stripe option
4. All selected payments are processed simultaneously

### Viewing Results
- Paid payments show Stripe payout ID (truncated for display)
- Success/error messages provide detailed feedback
- Audit logs contain full processing details

## Error Scenarios

### Missing Bank Information
- Error: "Missing bank information for tutor [Name]"
- Resolution: Tutor must complete banking information in profile

### Invalid Bank Details
- Error: "Failed to create bank account token"
- Resolution: Verify bank account information is correct

### Stripe API Errors
- Error: "Stripe payout error: [details]"
- Resolution: Check Stripe dashboard and API status

### Network/Connection Issues
- Error: "Error processing payment: [details]"
- Resolution: Retry processing, payment status remains unchanged

## Configuration Requirements

### Environment Variables
```bash
STRIPE_SECRET_KEY=sk_test_...  # Stripe secret key
ENCRYPTION_KEY=...             # 32-byte encryption key for bank data
```

### Stripe Account Setup
- Canadian payouts must be enabled
- Sufficient balance for payouts
- Webhook endpoints configured (optional)

## Testing

### Test Scenarios
1. **Single Payment**: Process one payment with valid bank info
2. **Batch Processing**: Process multiple payments simultaneously
3. **Missing Bank Info**: Attempt to process payment without bank details
4. **Invalid Bank Info**: Test with incorrect routing/account numbers
5. **Stripe Errors**: Test with insufficient balance or API errors

### Verification
- Check Stripe Dashboard for payout records
- Verify payment status updates in database
- Confirm audit logs contain proper information
- Test error handling and rollback scenarios

## Monitoring

### Key Metrics
- Payout success rate
- Processing time per payment
- Error frequency by type
- Total payout volume

### Logging
- All successful payouts logged with payment ID and amount
- Errors logged with payment ID (no sensitive data)
- Audit trail for manager actions

## Future Enhancements

1. **Webhook Integration**: Handle Stripe payout status updates
2. **Retry Logic**: Automatic retry for failed payouts
3. **Reporting**: Detailed payout reports and analytics
4. **Multi-Currency**: Support for other currencies beyond CAD
5. **Scheduling**: Automated payout processing on schedule
