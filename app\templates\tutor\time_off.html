{% extends 'base.html' %}

{% block title %}Time-Off Requests{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1>Manage Time-Off Requests</h1>
    <p class="lead">Request time off and view your request history.</p>

    <div class="row mt-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Your Time-Off Requests</h5>
                </div>
                <div class="card-body">
                    {% if requests %}
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Dates</th>
                                    <th>Duration</th>
                                    <th>Reason</th>
                                    <th>Status</th>
                                    <th>Manager Notes</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for request in requests %}
                                <tr>
                                    <td>{{ request.start_date.strftime('%Y-%m-%d') }} to {{ request.end_date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ request.duration_days }} day{% if request.duration_days != 1 %}s{% endif %}</td>
                                    <td>{{ request.reason }}</td>
                                    <td>
                                        {% if request.is_pending %}
                                            <span class="badge bg-warning">Pending</span>
                                        {% elif request.is_approved %}
                                            <span class="badge bg-success">Approved</span>
                                        {% elif request.is_rejected %}
                                            <span class="badge bg-danger">Rejected</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ request.manager_notes or '-' }}</td>
                                    <td>
                                        {% if request.is_pending %}
                                            <form method="POST" action="{{ url_for('tutor.cancel_time_off_request', id=request.id) }}" class="d-inline">
                                                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                                <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to cancel this time-off request?')">Cancel</button>
                                            </form>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    {% else %}
                        <div class="alert alert-info">
                            You haven't made any time-off requests yet. Use the form to submit a request.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Request Time Off</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('tutor.time_off_requests') }}">
                        {{ form.csrf_token }}
                        <div class="mb-3">
                            {{ form.start_date.label(class="form-label") }}
                            {{ form.start_date(class="form-control" + (" is-invalid" if form.start_date.errors else ""), type="date") }}
                            {% if form.start_date.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.start_date.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="mb-3">
                            {{ form.end_date.label(class="form-label") }}
                            {{ form.end_date(class="form-control" + (" is-invalid" if form.end_date.errors else ""), type="date") }}
                            {% if form.end_date.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.end_date.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="mb-3">
                            {{ form.reason.label(class="form-label") }}
                            {{ form.reason(class="form-control" + (" is-invalid" if form.reason.errors else ""), rows=3) }}
                            {% if form.reason.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.reason.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">Please provide a brief explanation for your time-off request.</div>
                        </div>
                        <button type="submit" class="btn btn-primary">Submit Request</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
