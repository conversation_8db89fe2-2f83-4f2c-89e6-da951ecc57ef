#!/usr/bin/env python3

from app import create_app
from app.extensions import db
from app.models.appointment import Appointment
from sqlalchemy import text

app = create_app()
with app.app_context():
    print('Testing updated Appointment model...')
    
    try:
        # Test basic model functionality
        print('\n1. Testing model import and basic functionality...')
        
        # Test that we can query appointments (should work even if empty)
        appointments = Appointment.query.all()
        print(f'✓ Successfully queried appointments: {len(appointments)} found')
        
        # Test model properties
        if appointments:
            apt = appointments[0]
            print(f'✓ Appointment ID: {apt.appointment_id}')
            print(f'✓ Duration calculation: {apt.calculated_duration_minutes} minutes')
            print(f'✓ Is from recurring: {apt.is_generated_from_recurring}')
            print(f'✓ Status properties work: scheduled={apt.is_scheduled}, completed={apt.is_completed}')
        
        # Test that the model schema matches database
        print('\n2. Testing model schema alignment...')
        
        # Check that all model columns exist in database
        query = "SELECT column_name FROM information_schema.columns WHERE table_name = 'appointments'"
        result = db.session.execute(text(query))
        db_columns = {row[0] for row in result}
        
        # Get model columns (excluding relationships)
        model_columns = set()
        for column in Appointment.__table__.columns:
            model_columns.add(column.name)
        
        print(f'Database columns: {sorted(db_columns)}')
        print(f'Model columns: {sorted(model_columns)}')
        
        # Check for mismatches
        missing_in_db = model_columns - db_columns
        missing_in_model = db_columns - model_columns
        
        if missing_in_db:
            print(f'⚠ Columns in model but not in database: {missing_in_db}')
        else:
            print('✓ All model columns exist in database')
            
        if missing_in_model:
            print(f'⚠ Columns in database but not in model: {missing_in_model}')
        else:
            print('✓ All database columns are represented in model')
        
        # Test that recurring template functionality is removed
        print('\n3. Testing that recurring template functionality is removed...')
        
        # These attributes should not exist anymore
        removed_attributes = [
            'is_recurring', 'frequency', 'day_of_week', 'week_of_month',
            'pattern_start_date', 'pattern_end_date', 'pattern_occurrences',
            'last_generated_date', 'recurring_template_id'
        ]
        
        for attr in removed_attributes:
            if hasattr(Appointment, attr):
                print(f'⚠ Warning: {attr} still exists in model')
            else:
                print(f'✓ {attr} successfully removed')
        
        # These methods should not exist anymore
        removed_methods = [
            'is_recurring_template', 'get_next_occurrence', 'generate_next_appointment'
        ]
        
        for method in removed_methods:
            if hasattr(Appointment, method):
                print(f'⚠ Warning: {method} still exists in model')
            else:
                print(f'✓ {method} successfully removed')
        
        print('\n✓ Appointment model cleanup completed successfully!')
        
    except Exception as e:
        print(f'✗ Error testing appointment model: {e}')
        import traceback
        traceback.print_exc()