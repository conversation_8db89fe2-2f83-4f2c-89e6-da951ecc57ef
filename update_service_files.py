import re
import os

def update_file(file_path):
    try:
        # Try UTF-8 first
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
    except UnicodeDecodeError:
        try:
            # Try Latin-1 as fallback
            with open(file_path, 'r', encoding='latin-1') as file:
                content = file.read()
        except Exception as e:
            print(f"Error reading {file_path}: {e}")
            return
    
    # Replace user.id with user.user_id
    content = re.sub(r'user\.id\b', 'user.user_id', content)
    
    # Replace client.id with client.client_id
    content = re.sub(r'client\.id\b', 'client.client_id', content)
    
    # Replace subscription.id with subscription.subscription_id
    content = re.sub(r'subscription\.id\b', 'subscription.subscription_id', content)
    
    # Replace appointment.id with appointment.appointment_id
    content = re.sub(r'appointment\.id\b', 'appointment.appointment_id', content)
    
    # Replace Appointment.id with Appointment.appointment_id
    content = re.sub(r'Appointment\.id\b', 'Appointment.appointment_id', content)
    
    # Replace ModuleSession.appointment_id == Appointment.id with ModuleSession.appointment_id == Appointment.appointment_id
    content = re.sub(r'ModuleSession\.appointment_id == Appointment\.id', 
                    r'ModuleSession.appointment_id == Appointment.appointment_id', content)
    
    # Replace ModuleSession.module_progress_id == ModuleProgress.id with ModuleSession.module_progress_id == ModuleProgress.progress_id
    content = re.sub(r'ModuleSession\.module_progress_id == ModuleProgress\.id', 
                    r'ModuleSession.module_progress_id == ModuleProgress.progress_id', content)
    
    # Replace ModuleProgress.module_id == ProgramModule.id with ModuleProgress.module_id == ProgramModule.module_id
    content = re.sub(r'ModuleProgress\.module_id == ProgramModule\.id', 
                    r'ModuleProgress.module_id == ProgramModule.module_id', content)
    
    # Replace InvoiceItem.id == None with InvoiceItem.item_id == None
    content = re.sub(r'InvoiceItem\.id == None', 
                    r'InvoiceItem.item_id == None', content)
    
    # Replace tecfee_program.id with tecfee_program.program_id
    content = re.sub(r'tecfee_program\.id\b', 'tecfee_program.program_id', content)
    
    # Replace GroupSession.id with GroupSession.group_session_id
    content = re.sub(r'GroupSession\.id\b', 'GroupSession.group_session_id', content)
    
    # Replace session.add_participant(enrollment_id=enrollment.id) with session.add_participant(enrollment_id=enrollment.enrollment_id)
    content = re.sub(r'session\.add_participant\(enrollment_id=enrollment\.id\)', 
                    r'session.add_participant(enrollment_id=enrollment.enrollment_id)', content)
    
    # Replace session.remove_participant(enrollment_id=enrollment.id) with session.remove_participant(enrollment_id=enrollment.enrollment_id)
    content = re.sub(r'session\.remove_participant\(enrollment_id=enrollment\.id\)', 
                    r'session.remove_participant(enrollment_id=enrollment.enrollment_id)', content)
    
    try:
        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(content)
        print(f"Updated {file_path}")
    except Exception as e:
        print(f"Error writing {file_path}: {e}")

# Update specific service files that need changes
service_files = [
    'app/services/user_service.py',
    'app/services/subscription_service.py',
    'app/services/program_service.py',
    'app/services/invoice_service.py',
    'app/services/group_session_service.py',
    'app/services/appointment_service.py'
]

for file_path in service_files:
    if os.path.exists(file_path):
        update_file(file_path)
    else:
        print(f"File not found: {file_path}")