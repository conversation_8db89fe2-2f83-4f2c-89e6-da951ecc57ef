import sys
import os
import secrets
import string
from datetime import datetime

# Add the current directory to the path so we can import the app
sys.path.append(os.path.abspath(os.path.dirname(__file__)))

# Import the app and models
from app import create_app
from app.extensions import db
from app.models.user import User

def update_manager_password(email):
    """Update the password for an existing manager user"""
    
    # Generate a secure random password
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    password = ''.join(secrets.choice(alphabet) for i in range(16))
    
    # Create the app context
    app = create_app()
    
    with app.app_context():
        # Find the user
        user = User.query.filter_by(email=email).first()
        if not user:
            print(f"User with email {email} not found.")
            return None
        
        # Update the password with the new hashing method
        user.set_password(password)
        db.session.commit()
        
        print(f"Password updated successfully for {email}!")
        print(f"New password: {password}")
        
        return password

if __name__ == "__main__":
    email = "<EMAIL>"
    update_manager_password(email)