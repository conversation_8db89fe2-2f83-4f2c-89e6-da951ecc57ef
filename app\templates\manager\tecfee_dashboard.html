{% extends "base.html" %}

{% block title %}{{ t('manager.tecfee.title_full') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="h3 mb-3">
                <i class="fas fa-graduation-cap text-primary"></i>
                {{ t('manager.tecfee.title') }}
            </h1>
            <p class="text-muted">{{ program.description }}</p>
        </div>
        <div class="col-md-4 text-end">
            <div class="btn-group" role="group">
                <a href="{{ url_for('manager.tecfee_enroll_client') }}" class="btn btn-primary">
                    <i class="fas fa-user-plus"></i> {{ t('manager.tecfee.enroll_client') }}
                </a>
                <a href="{{ url_for('manager.create_tecfee_group_session') }}" class="btn btn-success">
                    <i class="fas fa-calendar-plus"></i> {{ t('manager.tecfee.create_session') }}
                </a>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ total_enrollments }}</h4>
                            <p class="card-text">Total Enrollments</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ active_enrollments }}</h4>
                            <p class="card-text">Active Enrollments</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ upcoming_sessions|length }}</h4>
                            <p class="card-text">Upcoming Sessions</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-alt fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ program.total_modules }}</h4>
                            <p class="card-text">Program Modules</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-book fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('manager.tecfee_enrollments') }}" class="btn btn-outline-primary btn-lg w-100">
                                <i class="fas fa-list-alt d-block mb-2"></i>
                                View All Enrollments
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('manager.tecfee_group_sessions') }}" class="btn btn-outline-success btn-lg w-100">
                                <i class="fas fa-users-cog d-block mb-2"></i>
                                Manage Group Sessions
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('public.tecfee_enrollment_single_step') }}" class="btn btn-outline-info btn-lg w-100" target="_blank">
                                <i class="fas fa-external-link-alt d-block mb-2"></i>
                                View Public Page
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('manager.dashboard') }}" class="btn btn-outline-secondary btn-lg w-100">
                                <i class="fas fa-arrow-left d-block mb-2"></i>
                                Back to Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Upcoming Sessions -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Upcoming Group Sessions</h5>
                    <a href="{{ url_for('manager.tecfee_group_sessions') }}" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    {% if upcoming_sessions %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Module</th>
                                    <th>Date & Time</th>
                                    <th>Tutor</th>
                                    <th>Participants</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for session in upcoming_sessions %}
                                <tr>
                                    <td>
                                        <strong>Module {{ session.module.module_order }}</strong><br>
                                        <small class="text-muted">{{ session.module.name }}</small>
                                    </td>
                                    <td>
                                        {{ session.session_date.strftime('%Y-%m-%d') }}<br>
                                        <small class="text-muted">{{ session.start_time.strftime('%H:%M') }} - {{ session.end_time.strftime('%H:%M') }}</small>
                                    </td>
                                    <td>{{ session.tutor.first_name }} {{ session.tutor.last_name }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ session.current_participants }}/{{ session.max_participants }}</span>
                                        {% if session.current_participants < 4 %}
                                        <br><small class="text-warning">Need {{ 4 - session.current_participants }} more</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if session.status == 'scheduled' %}
                                        <span class="badge bg-warning">Scheduled</span>
                                        {% elif session.status == 'confirmed' %}
                                        <span class="badge bg-success">Confirmed</span>
                                        {% elif session.status == 'cancelled' %}
                                        <span class="badge bg-danger">Cancelled</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('manager.view_tecfee_group_session', id=session.id) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No upcoming sessions scheduled.</p>
                        <a href="{{ url_for('manager.create_tecfee_group_session') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Create First Session
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Enrollments -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Recent Enrollments</h5>
                    <a href="{{ url_for('manager.tecfee_enrollments') }}" class="btn btn-sm btn-outline-primary">View All</a>
                </div>
                <div class="card-body">
                    {% if recent_enrollments %}
                    {% for enrollment in recent_enrollments %}
                    <div class="d-flex justify-content-between align-items-center mb-3 pb-3 border-bottom">
                        <div>
                            <strong>{{ enrollment.client.first_name }} {{ enrollment.client.last_name }}</strong><br>
                            <small class="text-muted">{{ enrollment.enrollment_date.strftime('%Y-%m-%d') }}</small>
                        </div>
                        <div>
                            {% if enrollment.status == 'active' %}
                            <span class="badge bg-success">Active</span>
                            {% elif enrollment.status == 'completed' %}
                            <span class="badge bg-primary">Completed</span>
                            {% elif enrollment.status == 'withdrawn' %}
                            <span class="badge bg-danger">Withdrawn</span>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-user-plus fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No enrollments yet.</p>
                        <a href="{{ url_for('manager.tecfee_enroll_client') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Enroll First Client
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Program Modules Overview -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Program Modules</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for module in program.modules %}
                        <div class="col-md-4 mb-3">
                            <div class="card border-left-primary">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                        <h6 class="card-title mb-0">Module {{ module.module_order }}</h6>
                                        <span class="badge bg-primary">{{ module.duration_minutes }}min</span>
                                    </div>
                                    <p class="card-text">{{ module.name }}</p>
                                    <small class="text-muted">{{ module.description }}</small>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 4px solid #007bff !important;
}
</style>
{% endblock %}
