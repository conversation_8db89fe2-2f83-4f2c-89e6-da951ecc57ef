# app/models/dependant.py
from datetime import datetime
from app.extensions import db

class Dependant(db.Model):
    """Simple model for dependants - no polymorphic inheritance complexity."""
    __tablename__ = 'dependants'

    dependant_id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>ey('users.user_id'), nullable=True)  # Optional login account
    first_name = db.Column(db.String(100), nullable=False)
    last_name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(255), nullable=True)  # Optional
    password_hash = db.Column(db.String(255), nullable=True)  # Optional
    phone = db.Column(db.String(20), nullable=True)
    address = db.Column(db.Text, nullable=True)  # Legacy field - kept for migration purposes

    # Structured address fields
    civic_number = db.Column(db.String(20), nullable=True)
    street = db.Column(db.String(255), nullable=True)
    city = db.Column(db.String(100), nullable=True)
    postal_code = db.Column(db.String(10), nullable=True)
    province = db.Column(db.String(50), default='Quebec', nullable=True)
    country = db.Column(db.String(50), default='Canada', nullable=True)

    # Simple fields for dependant-specific info
    date_of_birth = db.Column(db.Date, nullable=True)
    school_grade = db.Column(db.String(50), nullable=True)
    notes = db.Column(db.Text, nullable=True)

    # Status fields
    is_active = db.Column(db.Boolean, default=True, nullable=False)
    preferred_language = db.Column(db.String(2), default='en', nullable=True)

    # Timestamps
    insert_date = db.Column(db.DateTime, default=datetime.utcnow)
    modification_date = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    def __repr__(self):
        return f'<Dependant {self.dependant_id} {self.first_name} {self.last_name}>'

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

    @property
    def age(self):
        if self.date_of_birth:
            today = datetime.today()
            return today.year - self.date_of_birth.year - ((today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day))
        return None

    @property
    def grade_display(self):
        """Get the display name for the school grade."""
        grade_mapping = {
            'accueil': 'Accueil',
            'primaire_1': 'Primaire 1',
            'primaire_2': 'Primaire 2',
            'primaire_3': 'Primaire 3',
            'primaire_4': 'Primaire 4',
            'primaire_5': 'Primaire 5',
            'primaire_6': 'Primaire 6',
            'secondaire_1': 'Secondaire 1',
            'secondaire_2': 'Secondaire 2',
            'secondaire_3': 'Secondaire 3',
            'secondaire_4': 'Secondaire 4',
            'secondaire_5': 'Secondaire 5',
            'cegep': 'CÉGEP',
            'universite_1': 'Université 1ère année',
            'universite_2_3': 'Université 2e-3e année',
            'adulte': 'Adulte',
            'autre': 'Autre'
        }
        return grade_mapping.get(self.school_grade, self.school_grade or 'Not specified')

    @property
    def formatted_address(self):
        """Get formatted address from structured fields."""
        address_parts = []

        if self.civic_number and self.street:
            address_parts.append(f"{self.civic_number} {self.street}")
        elif self.street:
            address_parts.append(self.street)

        if self.city:
            address_parts.append(self.city)

        if self.province:
            address_parts.append(self.province)

        if self.postal_code:
            address_parts.append(self.postal_code)

        if self.country:
            address_parts.append(self.country)

        return ", ".join(address_parts) if address_parts else None


class DependantRelationship(db.Model):
    """Model for tracking relationships between clients and dependants."""
    __tablename__ = 'dependant_relationships'

    relationship_id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('clients.client_id', ondelete='CASCADE'), nullable=False)
    dependant_id = db.Column(db.Integer, db.ForeignKey('dependants.dependant_id', ondelete='CASCADE'), nullable=False)
    relationship_type = db.Column(db.String(50), nullable=False)  # 'child', 'student', 'employee', 'dependent', etc.
    is_primary_contact = db.Column(db.Boolean, default=False)  # Is this the primary contact for the dependant?
    can_schedule = db.Column(db.Boolean, default=True)
    can_receive_updates = db.Column(db.Boolean, default=True)
    insert_date = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    client = db.relationship('Client', backref='dependant_relationships')
    dependant = db.relationship('Dependant', backref='client_relationships')

    __table_args__ = (
        db.UniqueConstraint('client_id', 'dependant_id', 'relationship_type', name='dependant_relationship_unique'),
    )

    def __repr__(self):
        return f'<DependantRelationship Client:{self.client_id} -> Dependant:{self.dependant_id} ({self.relationship_type})>'
