<!-- app/templates/manager/client_detail.html -->
{% extends "base.html" %}

{% block title %}Client Details - TutorAide Inc.{% endblock %}

{% block content %}
<div class="page-header fade-in-up">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>{{ client.first_name }} {{ client.last_name }}</h1>
            <div class="mt-2">
                <span class="badge bg-primary me-2">
                    {{ client.client_type|capitalize }}
                </span>
                {% if client.is_suspended %}
                    <span class="badge bg-danger">Suspended</span>
                {% else %}
                    <span class="badge bg-success">Active</span>
                {% endif %}
            </div>
        </div>
        <div class="action-buttons">
            <a href="{{ url_for('manager.clients_list') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Clients
            </a>
            <a href="{{ url_for('manager.edit_client', id=client.id) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- Client Information -->
    <div class="col-md-4 mb-4">
        <div class="card h-100 fade-in-up">
            <div class="card-header">
                <h5 class="mb-0">Client Information</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6 class="text-muted mb-1">Email</h6>
                    <p>
                        {% if client.user %}
                            {{ client.user.email }}
                        {% else %}
                            {{ client.email }}
                        {% endif %}
                    </p>
                </div>

                <div class="mb-3">
                    <h6 class="text-muted mb-1">Phone</h6>
                    <p>{{ client.phone }}</p>
                </div>

                {% if client.address %}
                    <div class="mb-3">
                        <h6 class="text-muted mb-1">Address</h6>
                        <p>{{ client.address|nl2br }}</p>
                    </div>
                {% endif %}

                {% if client.stripe_customer_id %}
                    <div class="mb-3">
                        <h6 class="text-muted mb-1">Stripe Customer ID</h6>
                        <p>{{ client.stripe_customer_id }}</p>
                    </div>
                {% endif %}

                {% if client.client_type == 'individual' and client.individual_clients %}
                    {% if client.individual_clients.date_of_birth %}
                        <div class="mb-3">
                            <h6 class="text-muted mb-1">Date of Birth</h6>
                            <p>{{ client.individual_clients.date_of_birth.strftime('%Y-%m-%d') }} ({{ client.individual_clients.age }} years)</p>
                        </div>
                    {% endif %}

                    {% if client.individual_clients.notes %}
                        <div class="mb-3">
                            <h6 class="text-muted mb-1">Notes</h6>
                            <p>{{ client.individual_clients.notes|nl2br }}</p>
                        </div>
                    {% endif %}
                {% endif %}

                {% if client.client_type == 'institutional' and client.institutional_clients %}
                    <div class="mb-3">
                        <h6 class="text-muted mb-1">Institution Name</h6>
                        <p>{{ client.institutional_clients.institution_name }}</p>
                    </div>

                    {% if client.institutional_clients.institution_type %}
                        <div class="mb-3">
                            <h6 class="text-muted mb-1">Institution Type</h6>
                            <p>{{ client.institutional_clients.institution_type }}</p>
                        </div>
                    {% endif %}

                    {% if client.institutional_clients.contact_person %}
                        <div class="mb-3">
                            <h6 class="text-muted mb-1">Contact Person</h6>
                            <p>{{ client.institutional_clients.contact_person }}</p>
                        </div>
                    {% endif %}

                    {% if client.institutional_clients.contract_start_date and client.institutional_clients.contract_end_date %}
                        <div class="mb-3">
                            <h6 class="text-muted mb-1">Contract Period</h6>
                            <p>{{ client.institutional_clients.contract_start_date.strftime('%Y-%m-%d') }} to {{ client.institutional_clients.contract_end_date.strftime('%Y-%m-%d') }}</p>
                            {% if client.institutional_clients.is_contract_active %}
                                <span class="badge bg-success">Active Contract</span>
                            {% else %}
                                <span class="badge bg-danger">Expired Contract</span>
                            {% endif %}
                        </div>
                    {% endif %}
                {% endif %}

                <div class="mb-3">
                    <h6 class="text-muted mb-1">Member Since</h6>
                    <p>{{ client.insert_date.strftime('%Y-%m-%d') }}</p>
                </div>

                {% if client.is_suspended %}
                    <div class="alert alert-danger">
                        <h6 class="alert-heading">Account Suspended</h6>
                        <p><strong>Reason:</strong> {{ client.suspension_reason }}</p>
                        <p><strong>Suspended on:</strong> {{ client.suspended_at.strftime('%Y-%m-%d') }}</p>
                        <a href="{{ url_for('manager.unsuspend_client', id=client.id) }}" class="btn btn-sm btn-success">
                            <i class="fas fa-unlock"></i> Unsuspend Account
                        </a>
                    </div>
                {% else %}
                    <a href="#" class="btn btn-sm btn-danger suspend-client" data-bs-toggle="modal" data-bs-target="#suspendModal" data-client-id="{{ client.id }}" data-client-name="{{ client.first_name }} {{ client.last_name }}">
                        <i class="fas fa-ban"></i> Suspend Account
                    </a>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Dependants Information -->
    <div class="col-md-8 mb-4">
        <div class="card shadow h-100">
            <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Dependants</h5>
                <a href="{{ url_for('manager.edit_client', id=client.id) }}" class="btn btn-sm btn-light">
                    <i class="fas fa-plus"></i> Manage Dependants
                </a>
            </div>
            <div class="card-body">
                {% if dependant_relationships %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Relationship</th>
                                    <th>Grade</th>
                                    <th>Primary</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for relationship in dependant_relationships %}
                                    <tr>
                                        <td>
                                            <a href="{{ url_for('manager.view_dependant', id=relationship.dependant.id) }}">
                                                {{ relationship.dependant.first_name }} {{ relationship.dependant.last_name }}
                                            </a>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ relationship.relationship_type }}</span>
                                        </td>
                                        <td>
                                            {% if relationship.dependant.school_grade %}
                                                <span class="badge bg-info">{{ relationship.dependant.school_grade }}</span>
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if relationship.is_primary %}
                                                <i class="fas fa-star text-warning" title="Primary Contact"></i>
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if relationship.dependant.is_active %}
                                                <span class="badge bg-success">Active</span>
                                            {% else %}
                                                <span class="badge bg-danger">Inactive</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{{ url_for('manager.view_dependant', id=relationship.dependant.id) }}" class="btn btn-sm btn-primary" title="View dependant">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('manager.edit_dependant', id=relationship.dependant.id) }}" class="btn btn-sm btn-outline-primary" title="Edit dependant">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center p-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <p>No dependants found.</p>
                        <a href="{{ url_for('manager.edit_client', id=client.id) }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add Dependants
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Program Enrollments -->
    <div class="col-md-6 mb-4">
        <div class="card shadow h-100">
            <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Program Enrollments</h5>
                <a href="{{ url_for('manager.enroll_client', id=client.id) }}" class="btn btn-sm btn-light">
                    <i class="fas fa-plus"></i> Enroll in Program
                </a>
            </div>
            <div class="card-body">
                {% if client.enrollments.count() > 0 %}
                    <div class="list-group">
                        {% for enrollment in client.enrollments %}
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">{{ enrollment.program.name }}</h6>
                                    <small>{{ enrollment.start_date.strftime('%b %d, %Y') }}</small>
                                </div>
                                <p class="mb-1">Status: <span class="badge bg-{{ 'success' if enrollment.status == 'completed' else 'primary' if enrollment.status == 'active' else 'warning' }}">{{ enrollment.status|capitalize }}</span></p>
                                <div class="progress mt-2" style="height: 10px;">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: {{ enrollment.completion_percentage or 0 }}%" aria-valuenow="{{ enrollment.completion_percentage or 0 }}" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <small class="d-block text-end mt-2">{{ (enrollment.completion_percentage or 0)|round|int }}% Complete</small>
                                <div class="mt-2">
                                    <a href="{{ url_for('manager.view_enrollment', id=enrollment.id) }}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i> View Details
                                    </a>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center p-4">
                        <i class="fas fa-graduation-cap fa-3x text-muted mb-3"></i>
                        <p>No program enrollments found.</p>
                        <a href="{{ url_for('manager.enroll_client', id=client.id) }}" class="btn btn-success">
                            <i class="fas fa-plus"></i> Enroll in Program
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Recent Appointments -->
    <div class="col-md-6 mb-4">
        <div class="card shadow h-100">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Recent Appointments</h5>
                <a href="{{ url_for('manager.client_appointments', id=client.id) }}" class="btn btn-sm btn-light">
                    <i class="fas fa-calendar"></i> View All
                </a>
            </div>
            <div class="card-body">
                {% if recent_appointments %}
                    <div class="list-group">
                        {% for appointment in recent_appointments %}
                            <a href="{{ url_for('manager.view_appointment', id=appointment.id) }}" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">
                                        {% if appointment.is_program_session and appointment.module_session %}
                                            {{ appointment.module_session.module_progress.module.program.name }} -
                                            {{ appointment.module_session.module_progress.module.name }}
                                        {% else %}
                                            {{ appointment.tutor_service.service.name }}
                                        {% endif %}
                                    </h6>
                                    <small>{{ appointment.start_time.strftime('%b %d, %Y') }}</small>
                                </div>
                                <p class="mb-1">
                                    <i class="fas fa-chalkboard-teacher me-1"></i>
                                    {{ appointment.tutor.first_name }} {{ appointment.tutor.last_name }}
                                </p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="badge bg-{{ 'primary' if appointment.status == 'scheduled' else 'success' if appointment.status == 'completed' else 'danger' if appointment.status == 'cancelled' else 'warning' }}">
                                        {{ appointment.status|capitalize }}
                                    </span>
                                    <span>
                                        {{ appointment.start_time.strftime('%I:%M %p') }} - {{ appointment.end_time.strftime('%I:%M %p') }}
                                    </span>
                                </div>
                            </a>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center p-4">
                        <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                        <p>No recent appointments found.</p>
                        <a href="{{ url_for('manager.schedule_appointment', client_id=client.id) }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Schedule Appointment
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Suspend Client Modal -->
<div class="modal fade" id="suspendModal" tabindex="-1" aria-labelledby="suspendModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="suspendModalLabel">Suspend Client</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="suspendForm" method="POST" action="{{ url_for('manager.suspend_client', id=client.id) }}">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="modal-body">
                    <p>Are you sure you want to suspend <strong>{{ client.first_name }} {{ client.last_name }}</strong>?</p>
                    <p>This will prevent the client from scheduling new appointments and accessing their account.</p>

                    <div class="mb-3">
                        <label for="suspension_reason" class="form-label">Reason for suspension</label>
                        <textarea class="form-control" id="suspension_reason" name="suspension_reason" rows="3" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Suspend Client</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
