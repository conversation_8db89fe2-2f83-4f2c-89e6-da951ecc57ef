#!/usr/bin/env python3
"""
Development Database Recreation Script
Drops all tables and recreates them from the updated schema.sql
"""
import psycopg2
import os
from dotenv import load_dotenv

def recreate_database():
    load_dotenv()
    
    print("=== Development Database Recreation ===")
    print("⚠️  This will DROP ALL TABLES and recreate from schema.sql")
    print("⚠️  All existing data will be LOST!")
    
    # Connect to database
    database_url = os.getenv('DATABASE_URL')
    if not database_url:
        raise ValueError("DATABASE_URL environment variable not found")
    
    conn = psycopg2.connect(database_url)
    
    try:
        with conn.cursor() as cur:
            print("\n1. Dropping all existing tables...")
            
            # Drop all tables (cascade to handle foreign key dependencies)
            cur.execute("""
                DROP SCHEMA public CASCADE;
                CREATE SCHEMA public;
                GRANT ALL ON SCHEMA public TO postgres;
                GRANT ALL ON SCHEMA public TO public;
            """)
            conn.commit()
            print("   ✓ All tables dropped successfully")
            
            print("\n2. Reading updated schema.sql...")
            with open('app/schema.sql', 'r', encoding='utf-8') as f:
                schema_sql = f.read()
            print("   ✓ Schema file loaded")
            
            print("\n3. Creating tables from updated schema...")
            cur.execute(schema_sql)
            conn.commit()
            print("   ✓ All tables created successfully")
            
            print("\n4. Verifying new table structure...")
            
            # Verify appointment_recurring_schedules table exists
            cur.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_name = 'appointment_recurring_schedules'
            """)
            recurring_table = cur.fetchone()
            print(f"   ✓ appointment_recurring_schedules table: {'EXISTS' if recurring_table else 'MISSING'}")
            
            # Verify foreign key column in appointments
            cur.execute("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'appointments' 
                AND column_name = 'recurring_schedule_id'
            """)
            fk_column = cur.fetchone()
            print(f"   ✓ recurring_schedule_id column in appointments: {'EXISTS' if fk_column else 'MISSING'}")
            
            # Count total tables
            cur.execute("""
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_type = 'BASE TABLE'
            """)
            table_count = cur.fetchone()[0]
            print(f"   ✓ Total tables created: {table_count}")
            
            # Count indexes on appointment_recurring_schedules
            cur.execute("""
                SELECT COUNT(*) 
                FROM pg_indexes 
                WHERE tablename = 'appointment_recurring_schedules'
            """)
            index_count = cur.fetchone()[0]
            print(f"   ✓ Indexes on appointment_recurring_schedules: {index_count}")
            
    except Exception as e:
        print(f"✗ Database recreation failed: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()
    
    print("\n🎉 Database recreation completed successfully!")
    print("\n=== Summary ===")
    print("✅ Old tables dropped")
    print("✅ New schema applied")
    print("✅ appointment_recurring_schedules table created")
    print("✅ Foreign key relationship established")
    print("✅ All indexes and constraints applied")
    print("✅ Default data inserted")
    
    return True

if __name__ == "__main__":
    recreate_database()