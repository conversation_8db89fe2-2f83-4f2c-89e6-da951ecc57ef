# Appointment System Context

This document provides essential context about the existing appointment management system for development work.

## Current System Architecture

### Technology Stack:
- **Backend**: Python Flask with SQLAlchemy ORM
- **Database**: SQL database with migration support
- **Authentication**: Google Sign-in integration
- **Frontend**: HTML templates with static CSS/JS
- **Internationalization**: Flask-Babel for multi-language support
- **Deployment**: Heroku-ready with Procfile

### Key Components:
- **Models**: Database entities for appointments, users, recurring patterns
- **Views**: Route handlers for web endpoints
- **Forms**: WTForms for user input validation
- **Services**: Business logic layer
- **Tasks**: Background job processing
- **Migrations**: Database schema versioning

## Current Features

### Appointment Management:
- Basic appointment creation and management
- Recurring appointment support (recently fixed)
- Appointment status tracking
- Google Calendar integration capabilities

### User Management:
- Google authentication
- Multi-language support
- User roles and permissions

### Known Issues Recently Addressed:
- Appointment status inconsistencies (fixed)
- Recurring appointment bugs (resolved)
- Google authentication button styling

## Development Guidelines

### Code Organization:
- Follow Flask application factory pattern
- Use blueprints for route organization
- Separate business logic into services
- Keep models focused on data representation

### Database Practices:
- Always create migrations for schema changes
- Use descriptive migration names
- Test migrations both up and down
- Consider data migration needs

### Authentication & Authorization:
- Leverage existing Google auth integration
- Implement role-based access control
- Validate user permissions at route level
- Consider session management

### Internationalization:
- Use Flask-Babel for all user-facing text
- Extract strings for translation
- Test with multiple locales
- Consider RTL language support

### Testing Approach:
- Write unit tests for business logic
- Integration tests for critical workflows
- Test authentication flows
- Validate database constraints

## File Structure Patterns

```
app/
├── models/          # Database models
├── views/           # Route handlers (blueprints)
├── forms/           # WTForms definitions
├── services/        # Business logic
├── tasks/           # Background jobs
├── templates/       # Jinja2 templates
├── static/          # CSS, JS, images
├── locales/         # Translation files
└── migrations/      # Database migrations
```

## Integration Points

### External Services:
- Google OAuth for authentication
- Google Calendar (potential integration)
- Email services for notifications
- Payment processing (if applicable)

### Internal Dependencies:
- Database connection management
- Session handling
- Error logging and monitoring
- Background task processing