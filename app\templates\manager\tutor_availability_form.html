{% extends 'base.html' %}

{% block title %}Edit Tutor Availability{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1>Edit Availability for {{ tutor.full_name }}</h1>
            <p class="lead">Update the tutor's availability slot.</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('manager_tutor_profile.tutor_availability', tutor_id=tutor.id) }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Availability
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Edit Availability</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('manager_tutor_profile.tutor_availability', tutor_id=tutor.id) }}">
                        {{ form.csrf_token }}
                        {{ form.id }}
                        <div class="mb-3">
                            {{ form.day_of_week.label(class="form-label") }}
                            {{ form.day_of_week(class="form-select") }}
                        </div>
                        <div class="mb-3">
                            {{ form.start_time.label(class="form-label") }}
                            {{ form.start_time(class="form-control", type="time") }}
                            <small class="text-muted">Use 24-hour format (e.g., 14:00 for 2:00 PM)</small>
                        </div>
                        <div class="mb-3">
                            {{ form.end_time.label(class="form-label") }}
                            {{ form.end_time(class="form-control" + (" is-invalid" if form.end_time.errors else ""), type="time") }}
                            <small class="text-muted">Use 24-hour format (e.g., 16:00 for 4:00 PM)</small>
                            {% if form.end_time.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.end_time.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="mb-3 form-check">
                            {{ form.is_active(class="form-check-input") }}
                            {{ form.is_active.label(class="form-check-label") }}
                        </div>
                        <button type="submit" class="btn btn-primary">Update Availability</button>
                        <a href="{{ url_for('manager_tutor_profile.tutor_availability', tutor_id=tutor.id) }}" class="btn btn-secondary">Cancel</a>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
