#!/usr/bin/env python3
"""
Test script for recurring appointment generation logic.
This script tests the creation and generation of recurring appointments.
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

from datetime import datetime, date, time, timedelta
from app import create_app
from app.extensions import db
from app.models.appointment import Appointment
from app.models.appointment_recurring_schedule import AppointmentRecurringSchedule
from app.services.recurring_appointment_service import RecurringAppointmentService
from app.services.scheduling_service import SchedulingService

def test_recurring_schedule_validation():
    """Test recurring schedule parameter validation."""
    print("Testing recurring schedule validation...")
    
    # Test valid weekly schedule
    valid_weekly = {
        'tutor_id': 1,
        'client_id': 1,
        'tutor_service_id': 1,
        'start_time': time(10, 0),
        'duration_minutes': 60,
        'frequency': 'weekly',
        'day_of_week': 1,  # Tuesday
        'pattern_start_date': date.today() + timedelta(days=1)
    }
    
    is_valid, errors = RecurringAppointmentService.validate_recurring_schedule_parameters(valid_weekly)
    assert is_valid, f"Valid weekly schedule should pass validation: {errors}"
    print("✓ Valid weekly schedule passes validation")
    
    # Test invalid frequency
    invalid_freq = valid_weekly.copy()
    invalid_freq['frequency'] = 'invalid'
    
    is_valid, errors = RecurringAppointmentService.validate_recurring_schedule_parameters(invalid_freq)
    assert not is_valid, "Invalid frequency should fail validation"
    assert any('frequency' in error.lower() for error in errors), "Should have frequency error"
    print("✓ Invalid frequency fails validation")
    
    # Test missing required field
    missing_field = valid_weekly.copy()
    del missing_field['tutor_id']
    
    is_valid, errors = RecurringAppointmentService.validate_recurring_schedule_parameters(missing_field)
    assert not is_valid, "Missing required field should fail validation"
    assert any('tutor_id' in error for error in errors), "Should have tutor_id error"
    print("✓ Missing required field fails validation")
    
    # Test valid monthly schedule
    valid_monthly = {
        'tutor_id': 1,
        'client_id': 1,
        'tutor_service_id': 1,
        'start_time': time(14, 30),
        'duration_minutes': 90,
        'frequency': 'monthly',
        'day_of_week': 2,  # Wednesday
        'week_of_month': 2,  # Second week
        'pattern_start_date': date.today() + timedelta(days=1)
    }
    
    is_valid, errors = RecurringAppointmentService.validate_recurring_schedule_parameters(valid_monthly)
    assert is_valid, f"Valid monthly schedule should pass validation: {errors}"
    print("✓ Valid monthly schedule passes validation")
    
    # Test invalid day of week
    invalid_dow = valid_weekly.copy()
    invalid_dow['day_of_week'] = 7  # Invalid (should be 0-6)
    
    is_valid, errors = RecurringAppointmentService.validate_recurring_schedule_parameters(invalid_dow)
    assert not is_valid, "Invalid day_of_week should fail validation"
    print("✓ Invalid day_of_week fails validation")
    
    print("All validation tests passed!\n")

def test_appointment_generation_patterns():
    """Test appointment generation for different recurring patterns."""
    print("Testing appointment generation patterns...")
    
    # Test weekly pattern
    start_date = date(2024, 1, 1)  # Monday
    weekly_schedule = AppointmentRecurringSchedule(
        id=1,
        tutor_id=1,
        client_id=1,
        tutor_service_id=1,
        start_time=time(10, 0),
        duration_minutes=60,
        frequency='weekly',
        day_of_week=1,  # Tuesday
        pattern_start_date=start_date,
        is_active=True
    )
    
    # Test weekly occurrence calculation
    next_occurrence = weekly_schedule.get_next_occurrence(start_date)
    expected_date = date(2024, 1, 2)  # First Tuesday after Monday start
    assert next_occurrence == expected_date, f"Expected {expected_date}, got {next_occurrence}"
    print("✓ Weekly pattern calculates correct next occurrence")
    
    # Test biweekly pattern
    biweekly_schedule = AppointmentRecurringSchedule(
        id=2,
        tutor_id=1,
        client_id=1,
        tutor_service_id=1,
        start_time=time(14, 0),
        duration_minutes=60,
        frequency='biweekly',
        day_of_week=3,  # Thursday
        pattern_start_date=date(2024, 1, 4),  # First Thursday
        is_active=True
    )
    
    # Test biweekly occurrence calculation
    next_occurrence = biweekly_schedule.get_next_occurrence(date(2024, 1, 5))
    expected_date = date(2024, 1, 18)  # Two weeks later
    assert next_occurrence == expected_date, f"Expected {expected_date}, got {next_occurrence}"
    print("✓ Biweekly pattern calculates correct next occurrence")
    
    # Test monthly pattern
    monthly_schedule = AppointmentRecurringSchedule(
        id=3,
        tutor_id=1,
        client_id=1,
        tutor_service_id=1,
        start_time=time(16, 0),
        duration_minutes=60,
        frequency='monthly',
        day_of_week=0,  # Monday
        week_of_month=2,  # Second week
        pattern_start_date=date(2024, 1, 8),  # Second Monday of January
        is_active=True
    )
    
    # Test monthly occurrence calculation
    next_occurrence = monthly_schedule.get_next_occurrence(date(2024, 1, 9))
    expected_date = date(2024, 2, 12)  # Second Monday of February
    assert next_occurrence == expected_date, f"Expected {expected_date}, got {next_occurrence}"
    print("✓ Monthly pattern calculates correct next occurrence")
    
    print("All pattern generation tests passed!\n")

def test_schedule_boundary_conditions():
    """Test schedule boundary conditions and edge cases."""
    print("Testing schedule boundary conditions...")
    
    # Test end date boundary
    schedule_with_end = AppointmentRecurringSchedule(
        id=4,
        tutor_id=1,
        client_id=1,
        tutor_service_id=1,
        start_time=time(10, 0),
        duration_minutes=60,
        frequency='weekly',
        day_of_week=1,  # Tuesday
        pattern_start_date=date(2024, 1, 1),
        pattern_end_date=date(2024, 1, 15),  # End after 2 weeks
        is_active=True
    )
    
    # Should return None after end date
    next_occurrence = schedule_with_end.get_next_occurrence(date(2024, 1, 16))
    assert next_occurrence is None, "Should return None after pattern end date"
    print("✓ Respects pattern end date boundary")
    
    # Test inactive schedule
    inactive_schedule = AppointmentRecurringSchedule(
        id=5,
        tutor_id=1,
        client_id=1,
        tutor_service_id=1,
        start_time=time(10, 0),
        duration_minutes=60,
        frequency='weekly',
        day_of_week=1,
        pattern_start_date=date(2024, 1, 1),
        is_active=False
    )
    
    next_occurrence = inactive_schedule.get_next_occurrence(date(2024, 1, 1))
    assert next_occurrence is None, "Inactive schedule should return None"
    print("✓ Inactive schedules return None for next occurrence")
    
    # Test before start date
    future_schedule = AppointmentRecurringSchedule(
        id=6,
        tutor_id=1,
        client_id=1,
        tutor_service_id=1,
        start_time=time(10, 0),
        duration_minutes=60,
        frequency='weekly',
        day_of_week=1,
        pattern_start_date=date(2024, 6, 1),  # Future start date
        is_active=True
    )
    
    next_occurrence = future_schedule.get_next_occurrence(date(2024, 1, 1))
    assert next_occurrence == date(2024, 6, 1), "Should return start date when before pattern start"
    print("✓ Returns start date when querying before pattern start")
    
    print("All boundary condition tests passed!\n")

def test_appointment_timing_validation():
    """Test appointment timing validation logic."""
    print("Testing appointment timing validation...")
    
    # Test valid timing
    start_time = datetime.now() + timedelta(hours=2)
    end_time = start_time + timedelta(hours=1)
    
    is_valid, errors = SchedulingService.validate_appointment_timing(start_time, end_time)
    assert is_valid, f"Valid timing should pass: {errors}"
    print("✓ Valid appointment timing passes validation")
    
    # Test past appointment
    past_start = datetime.now() - timedelta(hours=1)
    past_end = past_start + timedelta(hours=1)
    
    is_valid, errors = SchedulingService.validate_appointment_timing(past_start, past_end)
    assert not is_valid, "Past appointments should fail validation"
    assert any('past' in error.lower() for error in errors), "Should have past date error"
    print("✓ Past appointments fail validation")
    
    # Test invalid duration (too short)
    short_end = start_time + timedelta(minutes=10)
    
    is_valid, errors = SchedulingService.validate_appointment_timing(start_time, short_end)
    assert not is_valid, "Too short appointments should fail validation"
    assert any('15 minutes' in error for error in errors), "Should have minimum duration error"
    print("✓ Too short appointments fail validation")
    
    # Test invalid duration (too long)
    long_end = start_time + timedelta(hours=10)
    
    is_valid, errors = SchedulingService.validate_appointment_timing(start_time, long_end)
    assert not is_valid, "Too long appointments should fail validation"
    assert any('8 hours' in error for error in errors), "Should have maximum duration error"
    print("✓ Too long appointments fail validation")
    
    # Test business hours validation
    early_start = datetime.now().replace(hour=6, minute=0, second=0, microsecond=0) + timedelta(days=1)
    early_end = early_start + timedelta(hours=1)
    
    is_valid, errors = SchedulingService.validate_appointment_timing(early_start, early_end)
    assert not is_valid, "Appointments outside business hours should fail validation"
    print("✓ Appointments outside business hours fail validation")
    
    print("All timing validation tests passed!\n")

def test_schedule_generation_workflow():
    """Test the complete schedule generation workflow."""
    print("Testing complete schedule generation workflow...")
    
    # Test schedule data
    schedule_data = {
        'tutor_id': 1,
        'client_id': 1,
        'tutor_service_id': 1,
        'start_time': time(10, 0),
        'duration_minutes': 60,
        'frequency': 'weekly',
        'day_of_week': 1,  # Tuesday
        'pattern_start_date': date.today() + timedelta(days=1),
        'pattern_end_date': date.today() + timedelta(days=30),
        'default_status': 'scheduled',
        'transport_fee': 10.00,
        'is_subscription_based': False
    }
    
    # Test validation of complete workflow data
    is_valid, errors = RecurringAppointmentService.validate_recurring_schedule_parameters(schedule_data)
    assert is_valid, f"Complete schedule data should be valid: {errors}"
    print("✓ Complete schedule data passes validation")
    
    # Test pattern matching logic
    test_schedule = AppointmentRecurringSchedule(
        tutor_id=schedule_data['tutor_id'],
        client_id=schedule_data['client_id'],
        tutor_service_id=schedule_data['tutor_service_id'],
        start_time=schedule_data['start_time'],
        duration_minutes=schedule_data['duration_minutes'],
        frequency=schedule_data['frequency'],
        day_of_week=schedule_data['day_of_week'],
        pattern_start_date=schedule_data['pattern_start_date'],
        pattern_end_date=schedule_data['pattern_end_date'],
        is_active=True
    )
    
    # Test pattern matching for different dates
    start_date = schedule_data['pattern_start_date']
    
    # Find the first Tuesday after start date
    days_ahead = (1 - start_date.weekday()) % 7
    if days_ahead == 0:
        days_ahead = 7  # If today is Tuesday, get next Tuesday
    first_tuesday = start_date + timedelta(days=days_ahead)
    
    should_generate = RecurringAppointmentService._should_generate_appointment_on_date(
        test_schedule, first_tuesday
    )
    assert should_generate, f"Should generate appointment on first Tuesday ({first_tuesday})"
    print("✓ Pattern matching correctly identifies generation dates")
    
    # Test that it doesn't generate on wrong days
    wrong_day = first_tuesday + timedelta(days=1)  # Wednesday
    should_not_generate = RecurringAppointmentService._should_generate_appointment_on_date(
        test_schedule, wrong_day
    )
    assert not should_not_generate, f"Should not generate appointment on Wednesday ({wrong_day})"
    print("✓ Pattern matching correctly rejects wrong dates")
    
    print("All workflow tests passed!\n")

def main():
    """Run all tests."""
    print("Starting Recurring Appointment Generation Tests")
    print("=" * 50)
    
    try:
        # Create Flask app context for testing
        app = create_app()
        with app.app_context():
            test_recurring_schedule_validation()
            test_appointment_generation_patterns()
            test_schedule_boundary_conditions()
            test_appointment_timing_validation()
            test_schedule_generation_workflow()
            
            print("=" * 50)
            print("🎉 ALL TESTS PASSED! 🎉")
            print("Recurring appointment generation logic is working correctly.")
            
    except AssertionError as e:
        print(f"❌ TEST FAILED: {e}")
        return 1
    except Exception as e:
        print(f"❌ UNEXPECTED ERROR: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())