#!/usr/bin/env python3
"""
Test script to verify Task 4 models work with database operations.
Tests actual database operations with the updated models.
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

def test_database_operations():
    """Test that models work correctly with database operations."""
    try:
        from app import create_app
        from app.extensions import db
        from app.models.program import Program, ProgramPricing, GroupSession, GroupSessionParticipant, Enrollment
        from app.models.tutor import Tu<PERSON>
        from app.models.client import Client
        from datetime import datetime, date, time
        
        # Create application context
        app = create_app()
        with app.app_context():
            print("✓ Application context created successfully")
            
            # Test that we can query the models (this tests the primary key configuration)
            try:
                # Test ProgramPricing query
                pricing_count = ProgramPricing.query.count()
                print(f"✓ ProgramPricing query successful (found {pricing_count} records)")
                
                # Test GroupSession query
                session_count = GroupSession.query.count()
                print(f"✓ GroupSession query successful (found {session_count} records)")
                
                # Test GroupSessionParticipant query
                participant_count = GroupSessionParticipant.query.count()
                print(f"✓ GroupSessionParticipant query successful (found {participant_count} records)")
                
            except Exception as e:
                print(f"✗ Database query failed: {e}")
                return False
            
            # Test foreign key relationships by attempting joins
            try:
                # Test ProgramPricing -> Program relationship
                pricing_with_program = db.session.query(ProgramPricing).join(Program).first()
                if pricing_with_program:
                    print("✓ ProgramPricing -> Program join successful")
                else:
                    print("✓ ProgramPricing -> Program join syntax valid (no data)")
                
                # Test GroupSession -> Program relationship
                session_with_program = db.session.query(GroupSession).join(Program).first()
                if session_with_program:
                    print("✓ GroupSession -> Program join successful")
                else:
                    print("✓ GroupSession -> Program join syntax valid (no data)")
                
                # Test GroupSessionParticipant -> GroupSession relationship
                participant_with_session = db.session.query(GroupSessionParticipant).join(GroupSession).first()
                if participant_with_session:
                    print("✓ GroupSessionParticipant -> GroupSession join successful")
                else:
                    print("✓ GroupSessionParticipant -> GroupSession join syntax valid (no data)")
                
            except Exception as e:
                print(f"✗ Join query failed: {e}")
                return False
            
            print("✓ All database operations completed successfully")
            return True
            
    except Exception as e:
        print(f"✗ Database test failed: {e}")
        return False

def main():
    """Run database operation tests for Task 4."""
    print("Testing Task 4: Database Operations")
    print("=" * 40)
    
    if test_database_operations():
        print("=" * 40)
        print("✓ Task 4 database operations are WORKING")
        print("All models can perform database operations correctly")
        return True
    else:
        print("=" * 40)
        print("✗ Task 4 database operations have issues")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)