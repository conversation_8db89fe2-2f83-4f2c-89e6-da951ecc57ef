@echo off
REM PostgreSQL Migration Script for TECFÉE Prospect System
REM Make sure to update the database connection details below

REM Database connection details - UPDATE THESE WITH YOUR ACTUAL VALUES
set DB_HOST=localhost
set DB_PORT=5432
set DB_NAME=your_database_name
set DB_USER=your_username
set DB_PASSWORD=your_password

echo Running TECFÉE Prospect System Migrations...
echo ==============================================

REM Check if psql is available
where psql >nul 2>nul
if %errorlevel% neq 0 (
    echo Error: psql command not found. Please install PostgreSQL client tools.
    pause
    exit /b 1
)

REM Set PGPASSWORD environment variable to avoid password prompt
set PGPASSWORD=%DB_PASSWORD%

echo 1. Running main migration (tables, indexes, triggers)...
psql -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -f "app/migrations/complete_prospect_and_tecfee_migration.sql"

if %errorlevel% neq 0 (
    echo X Main migration failed
    pause
    exit /b 1
)
echo ✓ Main migration completed successfully

echo.
echo 2. Inserting TECFÉE program data...
psql -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -f "app/migrations/insert_tecfee_program_data.sql"

if %errorlevel% neq 0 (
    echo X TECFÉE program data insertion failed
    pause
    exit /b 1
)
echo ✓ TECFÉE program data inserted successfully

echo.
echo ==============================================
echo Migration completed successfully!
echo.
echo You can now:
echo 1. Start your Flask application
echo 2. Visit /public/tecfee/enrollment to test prospect registration
echo 3. Access manager views to manage prospects and TECFÉE program
echo.
echo Note: Make sure your .env file has the correct database credentials.
pause
