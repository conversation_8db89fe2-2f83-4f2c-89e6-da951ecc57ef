#!/usr/bin/env python3
"""
Test script to verify that all program-related models are correctly configured
with descriptive primary key names and proper foreign key references.
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

def test_program_models():
    """Test that all program-related models can be imported and have correct primary keys."""
    
    try:
        # Import the models
        from app.models.program import (
            Program, ProgramModule, Enrollment, ModuleProgress, 
            ModuleSession, ProgramPricing, GroupSession, GroupSessionParticipant
        )
        
        print("✅ All program-related models imported successfully")
        
        # Test primary key names
        models_and_expected_pk = [
            (Program, 'program_id'),
            (ProgramModule, 'module_id'),
            (Enrollment, 'enrollment_id'),
            (ModuleProgress, 'progress_id'),
            (ModuleSession, 'session_id'),
            (ProgramPricing, 'pricing_id'),
            (GroupSession, 'group_session_id'),
            (GroupSessionParticipant, 'participant_id')
        ]
        
        print("\n🔍 Checking primary key names:")
        for model_class, expected_pk in models_and_expected_pk:
            # Get the primary key column name
            pk_columns = [col.name for col in model_class.__table__.primary_key.columns]
            if len(pk_columns) == 1 and pk_columns[0] == expected_pk:
                print(f"✅ {model_class.__name__}: {expected_pk}")
            else:
                print(f"❌ {model_class.__name__}: Expected {expected_pk}, got {pk_columns}")
                return False
        
        # Test foreign key references
        print("\n🔍 Checking foreign key references:")
        
        # Check ProgramModule foreign keys
        program_module_fks = {col.name: list(col.foreign_keys)[0].target_fullname 
                             for col in ProgramModule.__table__.columns 
                             if col.foreign_keys}
        expected_program_module_fks = {'program_id': 'programs.program_id'}
        if program_module_fks == expected_program_module_fks:
            print("✅ ProgramModule foreign keys correct")
        else:
            print(f"❌ ProgramModule foreign keys: Expected {expected_program_module_fks}, got {program_module_fks}")
            return False
        
        # Check Enrollment foreign keys
        enrollment_fks = {col.name: list(col.foreign_keys)[0].target_fullname 
                         for col in Enrollment.__table__.columns 
                         if col.foreign_keys}
        expected_enrollment_fks = {
            'client_id': 'clients.client_id',
            'program_id': 'programs.program_id'
        }
        if enrollment_fks == expected_enrollment_fks:
            print("✅ Enrollment foreign keys correct")
        else:
            print(f"❌ Enrollment foreign keys: Expected {expected_enrollment_fks}, got {enrollment_fks}")
            return False
        
        # Check ModuleProgress foreign keys
        module_progress_fks = {col.name: list(col.foreign_keys)[0].target_fullname 
                              for col in ModuleProgress.__table__.columns 
                              if col.foreign_keys}
        expected_module_progress_fks = {
            'enrollment_id': 'enrollments.enrollment_id',
            'module_id': 'program_modules.module_id'
        }
        if module_progress_fks == expected_module_progress_fks:
            print("✅ ModuleProgress foreign keys correct")
        else:
            print(f"❌ ModuleProgress foreign keys: Expected {expected_module_progress_fks}, got {module_progress_fks}")
            return False
        
        # Check ModuleSession foreign keys
        module_session_fks = {col.name: list(col.foreign_keys)[0].target_fullname 
                             for col in ModuleSession.__table__.columns 
                             if col.foreign_keys}
        expected_module_session_fks = {
            'module_progress_id': 'module_progress.progress_id',
            'appointment_id': 'appointments.appointment_id'
        }
        if module_session_fks == expected_module_session_fks:
            print("✅ ModuleSession foreign keys correct")
        else:
            print(f"❌ ModuleSession foreign keys: Expected {expected_module_session_fks}, got {module_session_fks}")
            return False
        
        # Check GroupSession foreign keys
        group_session_fks = {col.name: list(col.foreign_keys)[0].target_fullname 
                            for col in GroupSession.__table__.columns 
                            if col.foreign_keys}
        expected_group_session_fks = {
            'program_id': 'programs.program_id',
            'module_id': 'program_modules.module_id',
            'tutor_id': 'tutors.tutor_id'
        }
        if group_session_fks == expected_group_session_fks:
            print("✅ GroupSession foreign keys correct")
        else:
            print(f"❌ GroupSession foreign keys: Expected {expected_group_session_fks}, got {group_session_fks}")
            return False
        
        # Check GroupSessionParticipant foreign keys
        participant_fks = {col.name: list(col.foreign_keys)[0].target_fullname 
                          for col in GroupSessionParticipant.__table__.columns 
                          if col.foreign_keys}
        expected_participant_fks = {
            'group_session_id': 'group_sessions.group_session_id',
            'enrollment_id': 'enrollments.enrollment_id'
        }
        if participant_fks == expected_participant_fks:
            print("✅ GroupSessionParticipant foreign keys correct")
        else:
            print(f"❌ GroupSessionParticipant foreign keys: Expected {expected_participant_fks}, got {participant_fks}")
            return False
        
        print("\n🎉 All program-related models are correctly configured!")
        print("✅ All primary keys use descriptive names")
        print("✅ All foreign key references are correct")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = test_program_models()
    sys.exit(0 if success else 1)