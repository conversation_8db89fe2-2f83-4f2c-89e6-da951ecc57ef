# app/models/tutor_service_rate.py
from datetime import datetime
from app.extensions import db

class TutorServiceRate(db.Model):
    __tablename__ = 'tutor_service_rates'
    
    rate_id = db.Column(db.Integer, primary_key=True)
    tutor_id = db.Column(db.<PERSON><PERSON>, db.<PERSON><PERSON>('tutors.tutor_id', ondelete='CASCADE'), nullable=True)
    service_id = db.Column(db.Integer, db.<PERSON>ey('services.service_id', ondelete='CASCADE'), nullable=True)
    rate = db.Column(db.Numeric(8, 2), nullable=False)  # Rate for the service
    effective_date = db.Column(db.Date, nullable=False)  # When the rate becomes effective
    end_date = db.Column(db.Date, nullable=True)  # When the rate expires (if applicable)
    is_active = db.Column(db.<PERSON>, default=True)
    created_by = db.Column(db.Inte<PERSON>, db.<PERSON><PERSON>ey('users.user_id'), nullable=True)
    insert_date = db.Column(db.DateTime, default=datetime.utcnow)
    modification_date = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    tutor = db.relationship('Tutor', backref='service_rates')
    service = db.relationship('Service', backref='tutor_rates')
    
    __table_args__ = (db.UniqueConstraint('tutor_id', 'service_id', name='_tutor_service_rate_uc'),)
    
    def __repr__(self):
        return f'<TutorServiceRate tutor_id={self.tutor_id} service_id={self.service_id}>'
    
    @property
    def display_name(self):
        """Return a display name with rate information."""
        service_name = self.service.name if self.service else "Unknown Service"
        return f"{service_name} (${self.rate}/hr)"
