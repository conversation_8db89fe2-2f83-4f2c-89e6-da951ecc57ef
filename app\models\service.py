# app/models/service.py
from datetime import datetime
from app.extensions import db

class Service(db.Model):
    __tablename__ = 'services'

    service_id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text, nullable=True)
    base_rate = db.Column(db.Numeric(8, 2), nullable=True)
    duration_minutes = db.Column(db.Integer, default=60)
    is_active = db.Column(db.<PERSON><PERSON><PERSON>, default=True)
    insert_date = db.Column(db.DateTime, default=datetime.utcnow)
    modification_date = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    tutor_services = db.relationship('TutorService', backref='service', lazy='dynamic', cascade='all, delete-orphan')

    def __repr__(self):
        return f'<Service {self.name}>'

class TutorService(db.Model):
    __tablename__ = 'tutor_services'

    tutor_service_id = db.Column(db.Integer, primary_key=True)
    tutor_id = db.Column(db.Integer, db.ForeignKey('tutors.tutor_id'), nullable=False)
    service_id = db.Column(db.Integer, db.ForeignKey('services.service_id'), nullable=False)
    custom_rate = db.Column(db.Numeric(8, 2), nullable=True)  # Custom rate for this tutor-service combination
    transport_fee = db.Column(db.Numeric(8, 2), default=0.00)  # Transport fee for this service
    is_active = db.Column(db.Boolean, default=True)
    insert_date = db.Column(db.DateTime, default=datetime.utcnow)
    modification_date = db.Column(db.DateTime, default=datetime.utcnow)

    # Appointments linked to this tutor-service combination
    appointments = db.relationship('Appointment', lazy='dynamic')

    __table_args__ = (db.UniqueConstraint('tutor_id', 'service_id', name='_tutor_service_uc'),)

    def __repr__(self):
        return f'<TutorService tutor_id={self.tutor_id} service_id={self.service_id}>'

    @property
    def display_name(self):
        """Return a display name that includes transport fee info."""
        service_name = self.service.name if self.service else "Unknown Service"
        rate = self.custom_rate if self.custom_rate else (self.service.base_rate if self.service else 0)
        base_display = f"{service_name} (${rate}/hr)"

        if self.transport_fee and float(self.transport_fee) > 0:
            return f"{base_display} + ${self.transport_fee} transport"
        return base_display