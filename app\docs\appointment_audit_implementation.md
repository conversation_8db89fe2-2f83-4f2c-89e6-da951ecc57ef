# Appointment Audit Implementation

## Overview
This document describes the implementation of an audit table for appointments that tracks who created, modified, updated, and deleted appointments. Only managers can view this audit log.

## Implementation Details

### 1. Database Migration
- **File**: `app/migrations/add_appointment_audit_table.sql`
- Creates the `appointment_audit` table with the following columns:
  - `appointment_id`: Reference to the appointment
  - `action`: Type of action (create, update, delete, cancel)
  - `user_id`, `user_role`, `user_email`: Who made the change
  - `timestamp`: When the change was made
  - `old_values`, `new_values`: JSON fields storing complete state before/after
  - Specific fields for quick queries (status, tutor_id, client_id, start_time changes)
  - `ip_address`, `user_agent`: Additional tracking information
  - `notes`: Optional notes about the change
- Adds audit tracking fields to appointments table:
  - `created_by`: User who created the appointment
  - `modified_by`: User who last modified the appointment
  - `deleted_by`: User who deleted the appointment (for soft deletes)
  - `deleted_at`: Timestamp of deletion (for soft deletes)
- Creates database trigger to automatically log changes to appointment_audit table

### 2. Model Implementation
- **File**: `app/models/appointment_audit.py`
- `AppointmentAudit` model with:
  - Relationships to User and Appointment models
  - Properties for human-readable action descriptions
  - `changes_summary` property that summarizes what changed
  - Class method `log_action()` for creating audit entries

- **Updated**: `app/models/appointment.py`
  - Added audit tracking fields (created_by, modified_by, deleted_by, deleted_at)

### 3. Service Layer Updates
- **File**: `app/services/appointment_service.py`
- Updated to track audit information:
  - `create_regular_appointment_from_form()`: Sets created_by and logs creation
  - `update_appointment_status()`: Tracks status changes and logs updates
  - `update_appointment_from_form()`: Tracks all field changes and logs updates
  - `create_recurring_appointment_from_form()`: Sets created_by for recurring templates
- All methods now capture:
  - Current user from Flask-Login
  - IP address and user agent from request
  - Old and new values for changes

### 4. Frontend Implementation

#### Manager Views
- **File**: `app/views/manager.py`
- Added two new routes:
  - `/appointment-audit`: Main audit log view with filtering and pagination
  - `/appointment/<id>/audit`: Audit history for a specific appointment

#### Templates
- **File**: `app/templates/manager/appointment_audit.html`
  - Main audit log list view with:
    - Filtering by appointment ID, user, and action type
    - Pagination support
    - Links to view appointment details and full history

- **File**: `app/templates/manager/appointment_audit_detail.html`
  - Detailed audit history for a specific appointment with:
    - Current appointment details
    - Timeline view of all changes
    - Detailed change comparison tables
    - Visual indicators for different action types

- **Updated**: `app/templates/manager/appointment_detail.html`
  - Added "Audit Log" button to view appointment history

- **Updated**: `app/templates/base.html`
  - Added "Appointment Audit Log" link in the Reports section of manager sidebar

### 5. Translations
- **Updated**: `app/locales/en/common.json`
  - Added "appointment_audit": "Appointment Audit Log"

- **Updated**: `app/locales/fr/common.json`
  - Added "appointment_audit": "Journal d'audit des rendez-vous"

## How It Works

### Automatic Tracking
1. When an appointment is created:
   - `created_by` is set to current user
   - Audit entry with action='create' is logged
   - Database trigger also creates audit record

2. When an appointment is updated:
   - `modified_by` is set to current user
   - Audit entry with action='update' is logged
   - Old and new values are captured for comparison

3. When an appointment is cancelled:
   - `modified_by` is set to current user
   - Audit entry with action='cancel' is logged

### Manual Audit Logging
The service layer explicitly calls `AppointmentAudit.log_action()` to ensure:
- User context is properly captured
- IP address and user agent are recorded
- Application-level logic is preserved

### Database Trigger Backup
The PostgreSQL trigger ensures that even direct database changes are captured in the audit log.

## Security Considerations

1. **Access Control**: Only managers can view audit logs
2. **Data Privacy**: Audit logs contain sensitive information and should be handled accordingly
3. **Retention**: Consider implementing audit log retention policies
4. **Performance**: Audit table has proper indexes for efficient querying

## Usage

### For Managers
1. Navigate to Reports > Appointment Audit Log in the sidebar
2. Use filters to find specific audit entries
3. Click on appointment ID to view full appointment details
4. Click "View History" to see complete change timeline for an appointment

### For Developers
```python
# Audit logging is automatic when using service methods
appointment = AppointmentService.create_appointment_from_form(form)

# Manual audit logging if needed
from app.models.appointment_audit import AppointmentAudit
AppointmentAudit.log_action(
    appointment_id=appointment.id,
    action='custom_action',
    user=current_user,
    old_appointment=old_state,
    new_appointment=new_state,
    notes='Custom action performed'
)
```

## Future Enhancements

1. **Export Functionality**: Add ability to export audit logs to CSV/Excel
2. **Advanced Filtering**: Add date range filters, multi-select filters
3. **Audit Reports**: Generate summary reports of changes over time
4. **Email Notifications**: Notify managers of critical changes
5. **Bulk Operations Tracking**: Better support for tracking bulk updates
6. **API Access**: RESTful API endpoints for audit log access