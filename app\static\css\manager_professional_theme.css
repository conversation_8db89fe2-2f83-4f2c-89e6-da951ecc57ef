/* Clean Minimalist Manager Interface Theme */
/* White, <PERSON>, Black with Red Highlights */

/* ===== COLOR VARIABLES ===== */
:root {
    /* Primary Colors */
    --primary-white: #ffffff;
    --primary-black: #000000;
    --primary-red: #e57373;
    --primary-red-dark: #d32f2f;
    --primary-red-light: #ffcdd2;
    --primary-red-pastel: #f8bbd9;

    /* Gray Scale */
    --light-gray: #f8f9fa;
    --medium-gray: #6c757d;
    --dark-gray: #495057;
    --border-gray: #dee2e6;
    --text-gray: #212529;

    /* Functional Colors - Soft Red Tones */
    --success-red: #e8a5a5;
    --warning-red: #f4a5a5;
    --info-red: #e57373;
    --danger-red: #e57373;

    /* Shadows & Effects */
    --soft-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    --card-shadow: 0 6px 25px rgba(0, 0, 0, 0.06);
    --hover-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);

    /* Typography */
    --font-elegant: 'Playfair Display', serif;
    --font-modern: 'Inter', sans-serif;
    --font-accent: 'Inter', sans-serif;
}

/* ===== GOOGLE FONTS IMPORT ===== */
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600&family=Crimson+Text:wght@400;600&display=swap');

/* ===== GLOBAL FONT SIZE INCREASE ===== */
body {
    font-size: 1.1rem;
    line-height: 1.6;
}

/* Increase font size for all text elements */
p, span, div, li, a {
    font-size: 1.05rem;
}

/* Ensure headings are properly sized */
h1 { font-size: 2.2rem; }
h2 { font-size: 1.9rem; }
h3 { font-size: 1.6rem; }
h4 { font-size: 1.4rem; }
h5 { font-size: 1.2rem; }
h6 { font-size: 1.1rem; }

/* ===== GLOBAL MANAGER INTERFACE STYLING ===== */
.manager-interface {
    font-family: var(--font-modern);
    background: var(--light-gray);
    min-height: 100vh;
    font-size: 1.1rem;
}

/* ===== NAVIGATION ENHANCEMENTS ===== */
.navbar-dark.bg-primary {
    background: var(--primary-black) !important;
    box-shadow: var(--soft-shadow);
    border-bottom: 1px solid var(--border-gray);
}

.navbar-brand {
    font-family: var(--font-elegant);
    font-weight: 600;
    color: var(--primary-white) !important;
    text-shadow: none;
}

/* ===== SIDEBAR STYLING ===== */
.sidebar {
    background: var(--primary-white);
    border-right: 1px solid var(--border-gray);
    box-shadow: var(--soft-shadow);
}

.sidebar .nav-link {
    color: var(--text-gray);
    border-radius: 20px;
    margin: 4px 8px;
    padding: 12px 16px;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 1.05rem;
}

.sidebar .nav-link:hover {
    background: var(--light-gray);
    color: var(--text-gray);
    transform: translateX(2px);
    box-shadow: var(--soft-shadow);
}

.sidebar .nav-link.active {
    background: var(--primary-red);
    color: var(--primary-white);
    box-shadow: var(--card-shadow);
}

/* ===== MAIN CONTENT AREA ===== */
.main-content {
    background: var(--light-gray);
    min-height: 100vh;
    padding: 24px;
}

/* ===== CARD COMPONENTS ===== */
.card {
    border: 1px solid var(--border-gray);
    border-radius: 20px;
    box-shadow: var(--card-shadow);
    background: var(--primary-white);
    overflow: hidden;
    transition: all 0.3s ease;
    margin-bottom: 24px;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--hover-shadow);
}

.card-header {
    background: var(--light-gray) !important;
    border-bottom: 1px solid var(--border-gray);
    padding: 20px 24px;
    color: var(--text-gray) !important;
    font-family: var(--font-elegant);
    font-weight: 600;
    font-size: 1.25rem;
}

.card-body {
    padding: 24px;
    background: var(--primary-white);
}

/* ===== BUTTONS ===== */
.btn {
    font-size: 1.05rem;
    padding: 12px 20px;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-sm {
    font-size: 1rem !important;
    padding: 8px 16px !important;
    border-radius: 20px !important;
}

.btn-primary {
    background: var(--primary-red);
    border: none;
    border-radius: 25px;
    padding: 12px 24px;
    font-weight: 500;
    color: var(--primary-white);
    box-shadow: var(--soft-shadow);
    transition: all 0.3s ease;
    font-size: 1.05rem;
}

.btn-primary:hover {
    background: var(--primary-red-dark);
    transform: translateY(-2px);
    box-shadow: var(--hover-shadow);
}

.btn-success {
    background: var(--success-red);
    border: none;
    border-radius: 25px;
    padding: 12px 24px;
    font-weight: 500;
    color: var(--primary-white);
    box-shadow: var(--soft-shadow);
    font-size: 1.05rem;
}

.btn-success:hover {
    background: #d49999;
    transform: translateY(-2px);
    box-shadow: var(--hover-shadow);
}

.btn-outline-primary {
    border: 2px solid var(--primary-red);
    color: var(--primary-red);
    border-radius: 25px;
    padding: 10px 22px;
    font-weight: 500;
    background: transparent;
    transition: all 0.3s ease;
    font-size: 1.05rem;
}

.btn-outline-primary:hover {
    background: var(--primary-red);
    border-color: var(--primary-red);
    color: var(--primary-white);
    transform: translateY(-2px);
}

.btn-secondary {
    background: var(--medium-gray);
    border: none;
    border-radius: 25px;
    color: var(--primary-white);
    box-shadow: var(--soft-shadow);
}

.btn-secondary:hover {
    background: var(--dark-gray);
    transform: translateY(-2px);
    box-shadow: var(--hover-shadow);
}

.btn-danger {
    background: var(--primary-red-dark);
    border: none;
    border-radius: 25px;
    color: var(--primary-white);
    box-shadow: var(--soft-shadow);
}

.btn-danger:hover {
    background: #c62828;
    transform: translateY(-2px);
    box-shadow: var(--hover-shadow);
}

/* ===== BUTTON FOCUS STATES ===== */
/* Override Bootstrap's default blue focus states */
.btn:focus,
.btn:focus-visible,
.btn:active:focus {
    outline: none !important;
    box-shadow: 0 0 0 0.2rem rgba(229, 115, 115, 0.25) !important;
}

.btn-primary:focus,
.btn-primary:focus-visible,
.btn-primary:active:focus {
    background: var(--primary-red) !important;
    border-color: var(--primary-red) !important;
    box-shadow: 0 0 0 0.2rem rgba(229, 115, 115, 0.25) !important;
}

.btn-success:focus,
.btn-success:focus-visible,
.btn-success:active:focus {
    background: var(--success-red) !important;
    border-color: var(--success-red) !important;
    box-shadow: 0 0 0 0.2rem rgba(232, 165, 165, 0.25) !important;
}

.btn-danger:focus,
.btn-danger:focus-visible,
.btn-danger:active:focus {
    background: var(--primary-red-dark) !important;
    border-color: var(--primary-red-dark) !important;
    box-shadow: 0 0 0 0.2rem rgba(211, 47, 47, 0.25) !important;
}

.btn-secondary:focus,
.btn-secondary:focus-visible,
.btn-secondary:active:focus {
    background: var(--medium-gray) !important;
    border-color: var(--medium-gray) !important;
    box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.25) !important;
}

.btn-outline-primary:focus,
.btn-outline-primary:focus-visible,
.btn-outline-primary:active:focus {
    color: var(--primary-red) !important;
    border-color: var(--primary-red) !important;
    box-shadow: 0 0 0 0.2rem rgba(229, 115, 115, 0.25) !important;
}

/* ===== FORM ELEMENTS ===== */
.form-control {
    border: 1px solid var(--border-gray);
    border-radius: 15px;
    padding: 14px 18px;
    background: var(--primary-white);
    transition: all 0.3s ease;
    font-family: var(--font-modern);
    font-size: 1.05rem;
}

.form-control:focus {
    border-color: var(--primary-red);
    box-shadow: 0 0 0 0.2rem rgba(229, 115, 115, 0.15);
    background: var(--primary-white);
}

.form-label {
    font-weight: 500;
    color: var(--text-gray);
    margin-bottom: 8px;
    font-family: var(--font-modern);
    font-size: 1.05rem;
}

.form-select {
    border: 1px solid var(--border-gray);
    border-radius: 15px;
    padding: 14px 18px;
    background: var(--primary-white);
    transition: all 0.3s ease;
    font-family: var(--font-modern);
    font-size: 1.05rem;
}

.form-select:focus {
    border-color: var(--primary-red);
    box-shadow: 0 0 0 0.2rem rgba(229, 115, 115, 0.15);
}

/* ===== ALERTS ===== */
.alert {
    border: 1px solid var(--border-gray);
    border-radius: 15px;
    padding: 16px 20px;
    margin-bottom: 20px;
    font-size: 1.05rem;
}

.alert-success {
    background: var(--primary-red-light);
    color: var(--primary-red-dark);
    border-color: var(--primary-red-pastel);
    border-left: 4px solid var(--success-red);
}

.alert-info {
    background: var(--primary-red-light);
    color: var(--primary-red-dark);
    border-color: var(--primary-red-pastel);
    border-left: 4px solid var(--info-red);
}

.alert-warning {
    background: var(--primary-red-light);
    color: var(--primary-red-dark);
    border-color: var(--primary-red-pastel);
    border-left: 4px solid var(--warning-red);
}

.alert-danger {
    background: var(--primary-red-light);
    color: var(--primary-red-dark);
    border-color: var(--primary-red-pastel);
    border-left: 4px solid var(--danger-red);
}

/* ===== TABLES ===== */
.table {
    background: var(--primary-white);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-gray);
    margin-bottom: 0;
}

.table thead th {
    background: var(--light-gray);
    color: var(--text-gray);
    border: none;
    padding: 16px;
    font-weight: 600;
    font-family: var(--font-elegant);
    font-size: 1.05rem;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background: var(--light-gray);
}

.table tbody td {
    padding: 16px;
    border-color: var(--border-gray);
    vertical-align: middle;
    font-size: 1.05rem;
}

/* ===== BADGES ===== */
.badge {
    border-radius: 20px;
    padding: 6px 12px;
    font-weight: 500;
    font-size: 0.9rem;
}

.badge.bg-success {
    background: var(--success-red) !important;
}

.badge.bg-warning {
    background: var(--warning-red) !important;
    color: var(--primary-white) !important;
}

.badge.bg-danger {
    background: var(--danger-red) !important;
}

.badge.bg-primary {
    background: var(--primary-red) !important;
}

.badge.bg-secondary {
    background: var(--medium-gray) !important;
}

/* ===== DASHBOARD SPECIFIC ===== */
.dashboard-welcome {
    background: var(--primary-white);
    border: 1px solid var(--border-gray);
    border-radius: 20px;
    padding: 32px;
    margin-bottom: 32px;
    box-shadow: var(--card-shadow);
}

.dashboard-welcome h2 {
    font-family: var(--font-elegant);
    color: var(--text-gray);
    margin-bottom: 8px;
}

.dashboard-welcome p {
    color: var(--medium-gray);
    font-size: 1.2rem;
}

/* ===== STATISTICS CARDS ===== */
.stat-card {
    background: var(--primary-white);
    border: 1px solid var(--border-gray);
    border-radius: 20px;
    padding: 24px;
    text-align: center;
    box-shadow: var(--card-shadow);
    transition: all 0.3s ease;
    margin-bottom: 24px;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--hover-shadow);
}

.stat-card .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    font-family: var(--font-elegant);
    color: var(--primary-red);
}

.stat-card .stat-label {
    color: var(--medium-gray);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 1rem;
}

/* ===== PAGE HEADERS ===== */
.page-header {
    background: var(--primary-white);
    border: 1px solid var(--border-gray);
    border-radius: 20px;
    padding: 24px 32px;
    margin-bottom: 24px;
    box-shadow: var(--card-shadow);
}

.page-header h1 {
    font-family: var(--font-elegant);
    color: var(--text-gray);
    margin-bottom: 8px;
}

.page-header p {
    color: var(--medium-gray);
    margin-bottom: 0;
}

/* ===== LIST ITEMS ===== */
.list-group-item {
    border: 1px solid var(--border-gray);
    background: var(--primary-white);
    padding: 16px 20px;
    transition: all 0.3s ease;
}

.list-group-item:hover {
    background: var(--light-gray);
    transform: translateY(-1px);
    box-shadow: var(--soft-shadow);
}

.list-group-item:first-child {
    border-top-left-radius: 15px;
    border-top-right-radius: 15px;
}

.list-group-item:last-child {
    border-bottom-left-radius: 15px;
    border-bottom-right-radius: 15px;
}

/* ===== SCHEDULE SPECIFIC ===== */
.schedule-slot {
    background: var(--primary-white);
    border: 1px solid var(--border-gray);
    border-radius: 15px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: var(--card-shadow);
    transition: all 0.3s ease;
}

.schedule-slot:hover {
    transform: translateY(-2px);
    box-shadow: var(--hover-shadow);
}

.schedule-slot.booked {
    border-left: 4px solid var(--primary-red);
    background: var(--primary-red-light);
}

.schedule-slot.available {
    border-left: 4px solid var(--success-red);
}

/* ===== SEARCH AND FILTERS ===== */
.search-box {
    background: var(--primary-white);
    border: 1px solid var(--border-gray);
    border-radius: 20px;
    padding: 20px;
    margin-bottom: 24px;
    box-shadow: var(--card-shadow);
}

.search-box .form-control {
    border-radius: 25px;
    padding: 12px 20px;
}

/* ===== BREADCRUMBS ===== */
.breadcrumb {
    background: var(--primary-white);
    border: 1px solid var(--border-gray);
    border-radius: 20px;
    padding: 16px 24px;
    margin-bottom: 24px;
    box-shadow: var(--card-shadow);
}

.breadcrumb-item a {
    color: var(--primary-red);
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: var(--primary-red-dark);
}

.breadcrumb-item.active {
    color: var(--text-gray);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .card {
        border-radius: 16px;
        margin-bottom: 16px;
    }

    .card-header {
        padding: 16px 20px;
        font-size: 1rem;
    }

    .card-body {
        padding: 20px;
    }

    .btn {
        border-radius: 20px;
        padding: 10px 20px;
    }

    .dashboard-welcome {
        padding: 24px;
        margin-bottom: 24px;
        border-radius: 16px;
    }

    .page-header {
        padding: 20px 24px;
        border-radius: 16px;
    }

    .main-content {
        padding: 16px;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* ===== CUSTOM SCROLLBAR ===== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-gray);
}

::-webkit-scrollbar-thumb {
    background: var(--medium-gray);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-red);
}

/* ===== ACTION BUTTONS GROUP ===== */
.action-buttons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.action-buttons .btn {
    margin-bottom: 8px;
}

/* ===== PAGINATION ===== */
.pagination .page-link {
    border: 1px solid var(--border-gray);
    color: var(--primary-red);
    padding: 12px 16px;
    border-radius: 20px;
    margin: 0 4px;
}

.pagination .page-link:hover {
    background: var(--primary-red);
    color: var(--primary-white);
    border-color: var(--primary-red);
}

.pagination .page-item.active .page-link {
    background: var(--primary-red);
    border-color: var(--primary-red);
}

/* ===== MODAL OVERRIDES ===== */
.modal-content {
    border-radius: 20px;
    border: 1px solid var(--border-gray);
    box-shadow: var(--hover-shadow);
}

.modal-header {
    background: var(--light-gray);
    border-bottom: 1px solid var(--border-gray);
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
}

.modal-footer {
    border-top: 1px solid var(--border-gray);
    border-bottom-left-radius: 20px;
    border-bottom-right-radius: 20px;
}

/* Fix for form controls in modals - ensure text is visible */
.modal-content .form-control,
.modal-content .form-select,
.modal-content select,
.modal-content input,
.modal-content textarea {
    color: var(--text-gray) !important;
    background-color: var(--primary-white) !important;
}

.modal-content .form-control:focus,
.modal-content .form-select:focus {
    color: var(--text-gray) !important;
    background-color: var(--primary-white) !important;
}

/* Dropdown menu styling for searchable inputs */
.modal-content .dropdown-menu {
    background-color: var(--primary-white);
    border: 1px solid var(--border-gray);
    color: var(--text-gray);
}

.modal-content .dropdown-item {
    color: var(--text-gray);
    padding: 8px 16px;
}

.modal-content .dropdown-item:hover {
    background-color: var(--light-gray);
    color: var(--text-gray);
}

/* ===== STATUS INDICATORS ===== */
.status-active {
    color: var(--success-red) !important;
    font-weight: 600;
}

.status-inactive {
    color: var(--medium-gray) !important;
    font-weight: 600;
}

.status-pending {
    color: var(--warning-red) !important;
    font-weight: 600;
}

.status-cancelled {
    color: var(--danger-red) !important;
    font-weight: 600;
}

/* ===== INFO BUTTON OVERRIDES ===== */
/* Override all btn-info and bg-info classes to use light gray instead of blue */
.manager-interface .btn-info {
    background-color: var(--medium-gray) !important;
    border-color: var(--medium-gray) !important;
    color: var(--primary-white) !important;
}

.manager-interface .btn-info:hover {
    background-color: var(--dark-gray) !important;
    border-color: var(--dark-gray) !important;
    color: var(--primary-white) !important;
    transform: translateY(-2px);
    box-shadow: var(--hover-shadow);
}

.manager-interface .btn-info:focus {
    background-color: var(--dark-gray) !important;
    border-color: var(--dark-gray) !important;
    color: var(--primary-white) !important;
    box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.25) !important;
}

.manager-interface .btn-info:active {
    background-color: var(--dark-gray) !important;
    border-color: var(--dark-gray) !important;
    color: var(--primary-white) !important;
}

.manager-interface .bg-info {
    background-color: var(--medium-gray) !important;
}

.manager-interface .badge.bg-info {
    background-color: var(--medium-gray) !important;
    color: var(--primary-white) !important;
}

.manager-interface .alert-info {
    background-color: #f8f9fa !important;
    border-color: var(--border-gray) !important;
    color: var(--text-gray) !important;
    border-left: 4px solid var(--medium-gray) !important;
}