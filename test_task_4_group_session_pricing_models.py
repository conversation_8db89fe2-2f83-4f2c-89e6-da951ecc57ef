#!/usr/bin/env python3
"""
Test script to verify Task 4: Group Session and Pricing Models
Tests that ProgramPricing, GroupSession, and GroupSessionParticipant models
use correct primary key names and foreign key references.
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

def test_model_imports():
    """Test that all models can be imported without errors."""
    try:
        from app.models.program import ProgramPricing, GroupSession, GroupSessionParticipant
        print("✓ All models imported successfully")
        return True
    except Exception as e:
        print(f"✗ Model import failed: {e}")
        return False

def test_primary_key_names():
    """Test that models use correct primary key names."""
    try:
        from app.models.program import ProgramPricing, GroupSession, GroupSessionParticipant
        
        # Test ProgramPricing primary key
        pricing_pk = ProgramPricing.__table__.primary_key.columns.keys()[0]
        assert pricing_pk == 'pricing_id', f"Expected 'pricing_id', got '{pricing_pk}'"
        print("✓ ProgramPricing uses 'pricing_id' as primary key")
        
        # Test GroupSession primary key
        session_pk = GroupSession.__table__.primary_key.columns.keys()[0]
        assert session_pk == 'group_session_id', f"Expected 'group_session_id', got '{session_pk}'"
        print("✓ GroupSession uses 'group_session_id' as primary key")
        
        # Test GroupSessionParticipant primary key
        participant_pk = GroupSessionParticipant.__table__.primary_key.columns.keys()[0]
        assert participant_pk == 'participant_id', f"Expected 'participant_id', got '{participant_pk}'"
        print("✓ GroupSessionParticipant uses 'participant_id' as primary key")
        
        return True
    except Exception as e:
        print(f"✗ Primary key test failed: {e}")
        return False

def test_foreign_key_references():
    """Test that foreign key references use correct column names."""
    try:
        from app.models.program import ProgramPricing, GroupSession, GroupSessionParticipant
        
        # Test ProgramPricing foreign keys
        pricing_fks = {fk.column.key: fk.target_fullname for fk in ProgramPricing.__table__.foreign_keys}
        expected_pricing_fks = {'program_id': 'programs.program_id'}
        assert pricing_fks == expected_pricing_fks, f"Expected {expected_pricing_fks}, got {pricing_fks}"
        print("✓ ProgramPricing foreign keys reference correct columns")
        
        # Test GroupSession foreign keys
        session_fks = {fk.column.key: fk.target_fullname for fk in GroupSession.__table__.foreign_keys}
        expected_session_fks = {
            'program_id': 'programs.program_id',
            'module_id': 'program_modules.module_id',
            'tutor_id': 'tutors.tutor_id'
        }
        assert session_fks == expected_session_fks, f"Expected {expected_session_fks}, got {session_fks}"
        print("✓ GroupSession foreign keys reference correct columns")
        
        # Test GroupSessionParticipant foreign keys
        participant_fks = {fk.column.key: fk.target_fullname for fk in GroupSessionParticipant.__table__.foreign_keys}
        expected_participant_fks = {
            'group_session_id': 'group_sessions.group_session_id',
            'enrollment_id': 'enrollments.enrollment_id'
        }
        assert participant_fks == expected_participant_fks, f"Expected {expected_participant_fks}, got {participant_fks}"
        print("✓ GroupSessionParticipant foreign keys reference correct columns")
        
        return True
    except Exception as e:
        print(f"✗ Foreign key test failed: {e}")
        return False

def test_model_relationships():
    """Test that model relationships are properly defined."""
    try:
        from app.models.program import ProgramPricing, GroupSession, GroupSessionParticipant
        
        # Test ProgramPricing relationships
        assert hasattr(ProgramPricing, 'program'), "ProgramPricing should have 'program' relationship"
        print("✓ ProgramPricing has correct relationships")
        
        # Test GroupSession relationships
        assert hasattr(GroupSession, 'program'), "GroupSession should have 'program' relationship"
        assert hasattr(GroupSession, 'module'), "GroupSession should have 'module' relationship"
        assert hasattr(GroupSession, 'tutor'), "GroupSession should have 'tutor' relationship"
        assert hasattr(GroupSession, 'participants'), "GroupSession should have 'participants' relationship"
        print("✓ GroupSession has correct relationships")
        
        # Test GroupSessionParticipant relationships
        assert hasattr(GroupSessionParticipant, 'enrollment'), "GroupSessionParticipant should have 'enrollment' relationship"
        # The group_session relationship is created via backref from GroupSession.participants
        print("✓ GroupSessionParticipant has correct relationships (backref creates group_session)")
        
        return True
    except Exception as e:
        print(f"✗ Relationship test failed: {e}")
        return False

def main():
    """Run all tests for Task 4."""
    print("Testing Task 4: Group Session and Pricing Models")
    print("=" * 50)
    
    tests = [
        test_model_imports,
        test_primary_key_names,
        test_foreign_key_references,
        test_model_relationships
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ Task 4 implementation is CORRECT")
        print("All models use descriptive primary keys and correct foreign key references")
        return True
    else:
        print("✗ Task 4 implementation has issues")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)