# app/utils/db_connection.py
"""
Database connection validation and management utilities.
Provides comprehensive database connectivity testing and environment validation.
"""
import os
import sys
import logging
from typing import Dict, List, Tuple, Optional
from urllib.parse import urlparse
import sqlalchemy
from sqlalchemy import create_engine, text, inspect
from sqlalchemy.exc import SQLAlchemyError, OperationalError, DatabaseError
from flask import current_app
from app.extensions import db

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatabaseConnectionError(Exception):
    """Custom exception for database connection issues."""
    pass

class EnvironmentValidationError(Exception):
    """Custom exception for environment configuration issues."""
    pass

class DatabaseConnectionManager:
    """Manages database connection validation and testing."""
    
    REQUIRED_ENV_VARS = [
        'DATABASE_URL',
        'SECRET_KEY'
    ]
    
    OPTIONAL_ENV_VARS = [
        'FLASK_ENV',
        'FLASK_CONFIG',
        'SQLALCHEMY_ENGINE_OPTIONS'
    ]
    
    def __init__(self):
        self.connection_status = {
            'connected': False,
            'database_url': None,
            'database_name': None,
            'host': None,
            'port': None,
            'username': None,
            'error': None,
            'schema_valid': False,
            'tables_count': 0
        }
    
    def validate_environment_variables(self) -> Dict[str, any]:
        """
        Validate that all required environment variables are present and valid.
        
        Returns:
            Dict containing validation results and any issues found
        """
        validation_result = {
            'valid': True,
            'missing_required': [],
            'missing_optional': [],
            'invalid_values': [],
            'recommendations': []
        }
        
        # Check required environment variables
        for var in self.REQUIRED_ENV_VARS:
            value = os.environ.get(var)
            if not value:
                validation_result['missing_required'].append(var)
                validation_result['valid'] = False
            elif var == 'DATABASE_URL':
                # Validate DATABASE_URL format
                if not self._validate_database_url(value):
                    validation_result['invalid_values'].append({
                        'variable': var,
                        'issue': 'Invalid database URL format'
                    })
                    validation_result['valid'] = False
        
        # Check optional environment variables
        for var in self.OPTIONAL_ENV_VARS:
            if not os.environ.get(var):
                validation_result['missing_optional'].append(var)
        
        # Add recommendations
        if validation_result['missing_required']:
            validation_result['recommendations'].append(
                "Set missing required environment variables in your .env file"
            )
        
        if 'FLASK_ENV' not in os.environ:
            validation_result['recommendations'].append(
                "Set FLASK_ENV to 'development', 'testing', or 'production'"
            )
        
        return validation_result
    
    def _validate_database_url(self, url: str) -> bool:
        """
        Validate database URL format.
        
        Args:
            url: Database URL to validate
            
        Returns:
            True if URL format is valid, False otherwise
        """
        try:
            parsed = urlparse(url)
            return (
                parsed.scheme in ['postgresql', 'postgres'] and
                parsed.hostname and
                parsed.username and
                parsed.password and
                parsed.path and len(parsed.path) > 1  # Database name
            )
        except Exception:
            return False
    
    def test_database_connection(self, database_url: str = None) -> Dict[str, any]:
        """
        Test database connectivity and gather connection information.
        
        Args:
            database_url: Optional database URL to test. If None, uses environment variable.
            
        Returns:
            Dict containing connection test results
        """
        if not database_url:
            database_url = os.environ.get('DATABASE_URL')
        
        if not database_url:
            raise EnvironmentValidationError("DATABASE_URL environment variable not set")
        
        # Handle SQLAlchemy 1.4+ compatibility with postgres:// URLs
        if database_url.startswith('postgres://'):
            database_url = database_url.replace('postgres://', 'postgresql://', 1)
        
        try:
            # Parse URL for connection info
            parsed_url = urlparse(database_url)
            self.connection_status.update({
                'database_url': database_url,
                'database_name': parsed_url.path[1:] if parsed_url.path else None,
                'host': parsed_url.hostname,
                'port': parsed_url.port,
                'username': parsed_url.username
            })
            
            # Create engine with connection pooling settings
            engine = create_engine(
                database_url,
                pool_pre_ping=True,
                pool_recycle=300,
                pool_timeout=30,
                pool_size=5,
                max_overflow=10
            )
            
            # Test basic connectivity
            with engine.connect() as connection:
                # Test basic query
                result = connection.execute(text("SELECT 1 as test"))
                test_value = result.fetchone()[0]
                
                if test_value != 1:
                    raise DatabaseConnectionError("Basic connectivity test failed")
                
                # Test database version
                version_result = connection.execute(text("SELECT version()"))
                db_version = version_result.fetchone()[0]
                logger.info(f"Connected to database: {db_version}")
                
                # Check if we can access schema information
                inspector = inspect(engine)
                tables = inspector.get_table_names()
                
                self.connection_status.update({
                    'connected': True,
                    'schema_valid': True,
                    'tables_count': len(tables),
                    'error': None
                })
                
                logger.info(f"Database connection successful. Found {len(tables)} tables.")
                
        except OperationalError as e:
            error_msg = self._format_operational_error(str(e))
            self.connection_status.update({
                'connected': False,
                'error': error_msg,
                'schema_valid': False
            })
            logger.error(f"Database connection failed: {error_msg}")
            raise DatabaseConnectionError(error_msg)
            
        except DatabaseError as e:
            error_msg = f"Database error: {str(e)}"
            self.connection_status.update({
                'connected': False,
                'error': error_msg,
                'schema_valid': False
            })
            logger.error(error_msg)
            raise DatabaseConnectionError(error_msg)
            
        except Exception as e:
            error_msg = f"Unexpected error: {str(e)}"
            self.connection_status.update({
                'connected': False,
                'error': error_msg,
                'schema_valid': False
            })
            logger.error(error_msg)
            raise DatabaseConnectionError(error_msg)
        
        return self.connection_status
    
    def _format_operational_error(self, error_str: str) -> str:
        """
        Format operational errors with helpful messages.
        
        Args:
            error_str: Raw error string from SQLAlchemy
            
        Returns:
            Formatted, user-friendly error message
        """
        error_lower = error_str.lower()
        
        if 'connection refused' in error_lower:
            return (
                "Database connection refused. Please check:\n"
                "- Database server is running\n"
                "- Host and port are correct\n"
                "- Network connectivity to database server"
            )
        elif 'authentication failed' in error_lower or 'password authentication failed' in error_lower:
            return (
                "Database authentication failed. Please check:\n"
                "- Username and password are correct\n"
                "- User has permission to access the database\n"
                "- Database credentials in .env file"
            )
        elif 'database' in error_lower and 'does not exist' in error_lower:
            return (
                "Database does not exist. Please check:\n"
                "- Database name is correct in DATABASE_URL\n"
                "- Database has been created on the server\n"
                "- User has permission to access the database"
            )
        elif 'timeout' in error_lower:
            return (
                "Database connection timeout. Please check:\n"
                "- Database server is responsive\n"
                "- Network latency to database server\n"
                "- Connection pool settings"
            )
        elif 'ssl' in error_lower:
            return (
                "SSL connection issue. Please check:\n"
                "- SSL requirements for your database\n"
                "- SSL certificates are valid\n"
                "- Connection string SSL parameters"
            )
        else:
            return f"Database connection error: {error_str}"
    
    def validate_flask_app_database(self, app=None) -> Dict[str, any]:
        """
        Validate database connection within Flask application context.
        
        Args:
            app: Flask application instance. If None, uses current_app.
            
        Returns:
            Dict containing validation results
        """
        if app is None:
            app = current_app
        
        validation_result = {
            'app_configured': False,
            'database_uri': None,
            'connection_test': None,
            'error': None
        }
        
        try:
            # Check if app has database configuration
            database_uri = app.config.get('SQLALCHEMY_DATABASE_URI')
            if not database_uri:
                validation_result['error'] = "SQLALCHEMY_DATABASE_URI not configured in Flask app"
                return validation_result
            
            validation_result.update({
                'app_configured': True,
                'database_uri': database_uri
            })
            
            # Test connection using Flask-SQLAlchemy
            with app.app_context():
                # Test basic database query
                result = db.engine.execute(text("SELECT 1 as test"))
                test_value = result.fetchone()[0]
                
                if test_value == 1:
                    validation_result['connection_test'] = 'success'
                else:
                    validation_result['connection_test'] = 'failed'
                    validation_result['error'] = 'Basic query test failed'
                    
        except Exception as e:
            validation_result.update({
                'connection_test': 'failed',
                'error': str(e)
            })
        
        return validation_result
    
    def get_connection_status(self) -> Dict[str, any]:
        """Get current connection status."""
        return self.connection_status.copy()
    
    def generate_connection_report(self) -> str:
        """
        Generate a comprehensive connection status report.
        
        Returns:
            Formatted string report of connection status
        """
        report_lines = [
            "=== Database Connection Report ===",
            f"Connected: {'Yes' if self.connection_status['connected'] else 'No'}",
        ]
        
        if self.connection_status['connected']:
            report_lines.extend([
                f"Database: {self.connection_status['database_name']}",
                f"Host: {self.connection_status['host']}",
                f"Port: {self.connection_status['port']}",
                f"Username: {self.connection_status['username']}",
                f"Tables Found: {self.connection_status['tables_count']}",
                f"Schema Valid: {'Yes' if self.connection_status['schema_valid'] else 'No'}"
            ])
        else:
            report_lines.append(f"Error: {self.connection_status['error']}")
        
        return "\n".join(report_lines)

# Convenience functions for easy access
def validate_environment() -> Dict[str, any]:
    """Validate environment variables."""
    manager = DatabaseConnectionManager()
    return manager.validate_environment_variables()

def test_database_connection(database_url: str = None) -> Dict[str, any]:
    """Test database connection."""
    manager = DatabaseConnectionManager()
    return manager.test_database_connection(database_url)

def get_connection_status() -> Dict[str, any]:
    """Get current connection status."""
    manager = DatabaseConnectionManager()
    return manager.get_connection_status()