# app/forms/client_forms.py
from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, PasswordField, TextAreaField, DateField, SubmitField, SelectField, BooleanField
from wtforms.validators import Optional, Length, EqualTo, DataRequired, Email

class ClientProfileForm(FlaskForm):
    first_name = StringField('First Name', validators=[DataRequired()])
    last_name = StringField('Last Name', validators=[DataRequired()])
    phone = StringField('Phone Number', validators=[DataRequired()])
    email = StringField('Email', validators=[Optional(), Email()])
    address = TextAreaField('Address', validators=[Optional()])
    preferred_language = SelectField('Preferred Language', choices=[
        ('en', 'English'),
        ('fr', 'Français')
    ], validators=[Optional()])
    password = PasswordField('New Password', validators=[
        Optional(),
        Length(min=8, message='Password must be at least 8 characters long.')
    ])
    confirm_password = PasswordField('Confirm New Password', validators=[
        Optional(),
        EqualTo('password', message='Passwords must match.')
    ])
    submit = SubmitField('Update Profile')

class IndividualClientForm(FlaskForm):
    first_name = StringField('First Name', validators=[DataRequired()])
    last_name = StringField('Last Name', validators=[DataRequired()])
    date_of_birth = DateField('Date of Birth', validators=[Optional()])
    school_grade = StringField('School Grade', validators=[Optional()])
    notes = TextAreaField('Notes', validators=[Optional()])
    submit = SubmitField('Save')

class ClientRelationshipForm(FlaskForm):
    client_id = SelectField('Client', validators=[DataRequired()], coerce=int)
    related_client_id = SelectField('Related Client', validators=[DataRequired()], coerce=int)
    relationship_type = SelectField('Relationship Type', choices=[
        ('parent', 'Parent'),
        ('guardian', 'Guardian'),
        ('employee', 'Employee'),
        ('student', 'Student'),
        ('other', 'Other')
    ], validators=[DataRequired()])
    is_primary = BooleanField('Primary Relationship', default=False)
    submit = SubmitField('Save Relationship')

class ManagerClientRelationshipForm(FlaskForm):
    related_client_id = SelectField('Select Dependant', validators=[DataRequired()], coerce=int)
    relationship_type = SelectField('Relationship Type', choices=[
        ('child', 'Child'),
        ('student', 'Student'),
        ('employee', 'Employee'),
        ('dependent', 'Dependent'),
        ('other', 'Other')
    ], validators=[DataRequired()])
    is_primary = BooleanField('Primary Contact for this Dependant', default=False)
    submit = SubmitField('Save Relationship')
