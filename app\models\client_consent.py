# app/models/client_consent.py
from datetime import datetime
from app.extensions import db

class ClientConsent(db.Model):
    """Model for tracking client consent to terms of service."""
    __tablename__ = 'client_consents'

    consent_id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.<PERSON>Key('clients.client_id', ondelete='CASCADE'), nullable=False)
    tos_version = db.Column(db.String(50), nullable=False)

    # Mandatory consent
    mandatory_accepted_at = db.Column(db.DateTime, nullable=False, default=datetime.utcnow)
    mandatory_ip = db.Column(db.String(45), nullable=True)

    # Optional consent
    optional_consent = db.Column(db.<PERSON>, nullable=False, default=False)
    optional_updated_at = db.Column(db.DateTime, nullable=True)
    optional_ip = db.Column(db.String(45), nullable=True)

    # For backward compatibility
    ip_address = db.Column(db.String(50), nullable=True)
    insert_date = db.Column(db.DateTime, default=datetime.utcnow)
    modification_date = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationship
    client = db.relationship('Client', backref=db.backref('consent', uselist=False))

    def __repr__(self):
        return f'<ClientConsent client_id={self.client_id} tos_version={self.tos_version} optional_consent={self.optional_consent}>'

    @classmethod
    def create_or_update(cls, client_id, tos_version, mandatory=True, optional=False, ip=None):
        """Create or update a client consent record."""
        consent = cls.query.filter_by(client_id=client_id).first()
        
        if consent:
            # Update existing consent
            if mandatory:
                consent.tos_version = tos_version
                consent.mandatory_accepted_at = datetime.utcnow()
                consent.mandatory_ip = ip
            
            # Only update optional consent if explicitly provided
            if optional is not None:
                consent.optional_consent = optional
                consent.optional_updated_at = datetime.utcnow()
                consent.optional_ip = ip
            
            # For backward compatibility
            consent.ip_address = ip
            consent.modification_date = datetime.utcnow()
        else:
            # Create new consent record
            consent = cls(
                client_id=client_id,
                tos_version=tos_version,
                mandatory_accepted_at=datetime.utcnow(),
                mandatory_ip=ip,
                optional_consent=optional,
                ip_address=ip
            )
            
            if optional:
                consent.optional_updated_at = datetime.utcnow()
                consent.optional_ip = ip
            
            db.session.add(consent)
        
        db.session.commit()
        return consent

    @classmethod
    def has_mandatory_consent(cls, client_id):
        """Check if a client has provided mandatory consent."""
        return cls.query.filter_by(client_id=client_id).first() is not None

    @classmethod
    def has_optional_consent(cls, client_id):
        """Check if a client has provided optional consent."""
        consent = cls.query.filter_by(client_id=client_id).first()
        return consent is not None and consent.optional_consent
