
# app/models/user.py
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta
import uuid
from app.extensions import db

class User(UserMixin, db.Model):
    __tablename__ = 'users'

    user_id = db.Column(db.Integer, primary_key=True)
    email = db.Column(db.String(255), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(20), nullable=False)
    is_active = db.Column(db.<PERSON>, default=True)
    email_verified = db.Column(db.<PERSON>, default=False)
    verification_token = db.Column(db.String(255), nullable=True)
    verification_token_expires = db.Column(db.DateTime, nullable=True)
    google_id = db.Column(db.String(255), nullable=True, unique=True)
    auth_provider = db.Column(db.String(50), default='local')  # 'local', 'google'
    reset_token = db.Column(db.String(255), nullable=True)
    reset_token_expires = db.Column(db.DateTime, nullable=True)
    last_login = db.Column(db.DateTime, nullable=True)
    insert_date = db.Column(db.DateTime, default=datetime.utcnow)
    modification_date = db.Column(db.DateTime, default=datetime.utcnow)
    
    def get_id(self):
        """Override the get_id method from UserMixin to use user_id instead of id."""
        return str(self.user_id)

    # Relationships
    manager = db.relationship('Manager', backref='user', uselist=False, cascade='all, delete-orphan')
    client = db.relationship('Client', backref='user', uselist=False, cascade='all, delete-orphan')
    tutor = db.relationship('Tutor', backref='user', uselist=False, cascade='all, delete-orphan')

    def __init__(self, email, password, role, google_id=None, auth_provider='local'):
        self.email = email
        if password:
            self.set_password(password)
        self.role = role
        self.google_id = google_id
        self.auth_provider = auth_provider

        # Google users have pre-verified emails
        if auth_provider == 'google':
            self.email_verified = True

    def set_password(self, password):
        self.password_hash = generate_password_hash(password, method='pbkdf2:sha256')

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def generate_reset_token(self):
        self.reset_token = str(uuid.uuid4())
        self.reset_token_expires = datetime.utcnow() + timedelta(hours=24)
        return self.reset_token

    def verify_reset_token(self, token):
        if self.reset_token != token:
            return False
        if self.reset_token_expires < datetime.utcnow():
            return False
        return True

    def clear_reset_token(self):
        self.reset_token = None
        self.reset_token_expires = None

    def generate_verification_token(self):
        """Generate a verification token for email verification."""
        self.verification_token = str(uuid.uuid4())
        self.verification_token_expires = datetime.utcnow() + timedelta(hours=24)
        return self.verification_token

    def verify_email_token(self, token):
        """Verify the email verification token."""
        if self.verification_token != token:
            return False
        if self.verification_token_expires < datetime.utcnow():
            return False
        return True

    def confirm_email(self):
        """Mark email as verified and clear verification token."""
        self.email_verified = True
        self.verification_token = None
        self.verification_token_expires = None

    def __repr__(self):
        return f'<User {self.email}>'