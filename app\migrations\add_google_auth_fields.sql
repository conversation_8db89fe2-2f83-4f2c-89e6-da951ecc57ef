-- Add Google Authentication Fields to Users Table
-- This script adds the necessary fields for Google OAuth authentication
-- Run this script on the production database to enable Google Sign-In

-- Step 1: Add Google authentication fields to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS google_id VARCHAR(255) UNIQUE,
ADD COLUMN IF NOT EXISTS auth_provider VARCHAR(50) DEFAULT 'local';

-- Step 2: Create index for Google ID lookups
CREATE INDEX IF NOT EXISTS idx_users_google_id ON users(google_id);
CREATE INDEX IF NOT EXISTS idx_users_auth_provider ON users(auth_provider);

-- Step 3: Update existing users to have auth_provider = 'local'
UPDATE users 
SET auth_provider = 'local' 
WHERE auth_provider IS NULL;

-- Step 4: Make password_hash nullable for Google users (they don't need passwords)
ALTER TABLE users ALTER COLUMN password_hash DROP NOT NULL;

-- Step 5: Add constraint to ensure either password_hash OR google_id is present
-- (Users must have either local authentication or Google authentication)
ALTER TABLE users 
ADD CONSTRAINT users_auth_check 
CHECK (
    (auth_provider = 'local' AND password_hash IS NOT NULL) OR 
    (auth_provider = 'google' AND google_id IS NOT NULL)
);

-- Step 6: Verify the changes
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'users' 
AND column_name IN ('google_id', 'auth_provider', 'password_hash')
ORDER BY column_name;

-- Step 7: Show current authentication provider distribution
SELECT 
    auth_provider,
    COUNT(*) as user_count,
    COUNT(CASE WHEN email_verified = TRUE THEN 1 END) as verified_count
FROM users 
GROUP BY auth_provider
ORDER BY auth_provider;

-- Expected result:
-- - google_id: VARCHAR(255) column for storing Google user IDs
-- - auth_provider: VARCHAR(50) column with default 'local'
-- - password_hash: Now nullable for Google users
-- - Constraint ensures users have either local or Google authentication
-- - Indexes created for efficient lookups
