# Implementation Plan

- [x] 1. Update Service and TutorService Models





  - Modify Service model to use `service_id` as primary key instead of `id`
  - Update TutorService model to use `tutor_service_id` as primary key
  - Fix foreign key references in TutorService to point to correct primary keys
  - Update all relationship definitions to use correct column names
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 2.1, 2.2, 3.1, 3.2_

- [x] 2. Update Payment and Availability Models





















  - Modify TutorPayment model to use `payment_id` as primary key instead of `id`
  - Update TutorAvailability model to use `availability_id` as primary key
  - Update TimeOff model to use `time_off_id` as primary key
  - Fix all foreign key references to point to correct primary keys
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 2.1, 2.2, 3.1, 3.2_

-


-



- [x] 3. Update Program-Related Models

























  - Modify Program model to use `program_id` as primary key instead of `id`
  - Update ProgramModule model to use `module_id` as primary key
  - Update Enrollment model to use `enrollment_id` as primary key
  - Update ModuleProgress model to use `progress_id` as primary key
  - Update ModuleSession model to use `session_id` as primary key
  - Fix all foreign key references between program-related models
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 2.1, 2.2, 3.1, 3.2_

-

- [x] 4. Update Group Session and Pricing Models


































  - Modify ProgramPricing model to use `pricing_id` as primary key instead of `id`
  - Update GroupSession model to use `group_session_id` as primary key
  - Update GroupSessionParticipant model to use `participant_id` as primary key
  - Fix all foreign key references to use correct primary key names
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 2.1, 2.2, 3.1, 3.2_

- [x] 5. Update Invoice and Notification Models




























  - Modify Invoice model to use `invoice_id` as primary key instead of `id`
  - Update InvoiceItem model to use `item_id` as primary key
  - Update Notification model to use `notification_id` as primary key
  - Update InvoiceGenerationSettings model to use `setting_id` as primary key
  - Fix all foreign key references to point to correct primary keys
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 2.1, 2.2, 3.1, 3.2_

- [x] 6. Update Consent and Audit Models























  - Modify ClientConsent model to use `consent_id` as primary key instead of `id`
  - Update AppointmentAudit model to use `audit_id` as primary key
  - Fix foreign key references to use correct column names
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 2.1, 2.2, 3.1, 3.2_

- [x] 7. Update TutorServiceRate Model









  - Fix TutorServiceRate model foreign key references to use correct primary key names
  - Update tutor_id foreign key to reference `tutors.tutor_id`
  - Update service_id foreign key to reference `services.service_id`
  - Test model relationships work correctly
  - _Requirements: 2.1, 2.2, 2.3, 3.1, 3.2_

- [x] 8. Update Database Schema File














































  - Modify main `schema.sql` file to use descriptive primary key names for all tables
  - Update all foreign key constraint definitions to reference correct primary keys
  - Ensure all table relationships are properly defined with correct column names
  - Validate schema consistency and foreign key integrity
  - _Requirements: 1.1, 1.2, 1.3, 4.1, 4.2, 4.4_

- [x] 9. Recreate Database with Updated Schema




















  - Drop existing database tables using development approach
  - Recreate database from updated schema.sql file
  - Verify all tables are created with correct primary key names
  - Test database connectivity and basic operations
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 5.4_

- [x] 10. Test Model Configuration and Relationships






  - Import all updated models and verify no configuration errors
  - Test SQLAlchemy relationships work correctly with new column names
  - Validate foreign key constraints are properly enforced
  - Run basic CRUD operations on all models to ensure functionality
  - _Requirements: 3.3, 3.4, 3.5, 5.1, 5.2, 5.3_

- [x] 11. Update Any Remaining Code References






  - Search for any hardcoded column name references in views, services, or other code
  - Update any raw SQL queries to use new column names
  - Fix any remaining foreign key references in code
  - Test all updated code paths work correctly
  - _Requirements: 5.1, 5.2, 5.3, 6.1, 6.2_

- [ ] 12. Comprehensive Application Testing
  - Start the application and verify it loads without SQLAlchemy configuration errors
  - Test user authentication and basic workflows
  - Validate all database operations work correctly
  - Test API endpoints that depend on database relationships
  - Verify no functionality regressions from the naming changes
  - _Requirements: 5.1, 5.2, 5.3, 5.4, 5.5, 6.3, 6.4, 6.5_