#!/usr/bin/env python3
"""
Test script to verify Service and TutorService model updates.
"""

import os
import sys

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_model_imports():
    """Test that models can be imported without errors."""
    print("Testing model imports...")
    
    try:
        from app.models.service import Service, TutorService
        print("✅ Service and TutorService models imported successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to import models: {e}")
        return False

def test_model_attributes():
    """Test that models have the correct primary key attributes."""
    print("Testing model attributes...")
    
    try:
        from app.models.service import Service, TutorService
        
        # Test Service model
        service_columns = [col.name for col in Service.__table__.columns]
        if 'service_id' in service_columns:
            print("✅ Service model has service_id primary key")
        else:
            print("❌ Service model missing service_id primary key")
            return False
            
        # Test TutorService model
        tutor_service_columns = [col.name for col in TutorService.__table__.columns]
        if 'tutor_service_id' in tutor_service_columns:
            print("✅ TutorService model has tutor_service_id primary key")
        else:
            print("❌ TutorService model missing tutor_service_id primary key")
            return False
            
        return True
    except Exception as e:
        print(f"❌ Failed to test model attributes: {e}")
        return False

def test_foreign_key_references():
    """Test that foreign key references are correct."""
    print("Testing foreign key references...")
    
    try:
        from app.models.service import TutorService
        
        # Check foreign key references
        tutor_id_fk = None
        service_id_fk = None
        
        for col in TutorService.__table__.columns:
            if col.name == 'tutor_id' and col.foreign_keys:
                tutor_id_fk = list(col.foreign_keys)[0]
            elif col.name == 'service_id' and col.foreign_keys:
                service_id_fk = list(col.foreign_keys)[0]
        
        # Check tutor_id foreign key
        if tutor_id_fk and str(tutor_id_fk.column) == 'tutors.tutor_id':
            print("✅ TutorService.tutor_id references tutors.tutor_id correctly")
        else:
            print(f"❌ TutorService.tutor_id foreign key incorrect: {tutor_id_fk.column if tutor_id_fk else 'None'}")
            return False
            
        # Check service_id foreign key
        if service_id_fk and str(service_id_fk.column) == 'services.service_id':
            print("✅ TutorService.service_id references services.service_id correctly")
        else:
            print(f"❌ TutorService.service_id foreign key incorrect: {service_id_fk.column if service_id_fk else 'None'}")
            return False
            
        return True
    except Exception as e:
        print(f"❌ Failed to test foreign key references: {e}")
        return False

def main():
    """Run all tests."""
    print("=" * 60)
    print("SERVICE MODEL UPDATES VERIFICATION")
    print("=" * 60)
    
    tests = [
        test_model_imports,
        test_model_attributes,
        test_foreign_key_references
    ]
    
    all_passed = True
    for test in tests:
        if not test():
            all_passed = False
        print()
    
    print("=" * 60)
    if all_passed:
        print("✅ All Service model update tests passed!")
    else:
        print("❌ Some Service model update tests failed!")
    print("=" * 60)
    
    return all_passed

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)