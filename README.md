# TutorAide Management System

A comprehensive web application for managing tutoring programs, appointments, clients, services, and invoices with Stripe payment integration.

## Features

- **User Role System**
  - **Managers**: Full system access to manage programs, appointments, users, and invoices
  - **Tutors**: Access to their schedule and client information
  - **Clients**: Manage their profiles, view appointments, and pay invoices

- **Client Management**
  - Support for both individual and institutional clients
  - Flexible client relationships (parent-child, institution-member)
  - Complete client profiles with customizable fields

- **Program Management**
  - Structured tutoring programs with multiple modules
  - Progress tracking through program modules
  - Support for sequential and non-sequential learning paths
  - Program-specific appointment scheduling

- **Appointment Management**
  - Interactive calendar views (day, week, month)
  - Appointment scheduling with conflict detection
  - Business hours constraints (8 AM to 9 PM EST)
  - Status tracking (scheduled, completed, cancelled, no-show)
  - Integration with program modules for structured learning

- **Service Management**
  - Customizable tutoring services with pricing
  - Tutor-specific rates for services
  - Service integration with program modules

- **Invoice System**
  - Automated invoice generation for completed appointments
  - Advance invoicing for subscription packages
  - Daily invoice generation for completed sessions
  - Client-level invoice aggregation and grouping
  - Unpaid invoice tracking with sorting by amount
  - Secure payment processing through Stripe integration

## Technology Stack

- **Backend**: Python with Flask framework
- **Database**: PostgreSQL
- **Frontend**: HTML, CSS (Bootstrap), JavaScript
- **Authentication**: Flask-Login
- **Forms**: Flask-WTF
- **Payment Processing**: Stripe API

## Data Model

### Client Management
- **Clients**: Base table for all client types with common fields
- **Individual Clients**: Extension for individual clients (students, parents, adult learners)
- **Institutional Clients**: Extension for organizational clients (schools, companies)
- **Client Relationships**: Flexible mapping between clients (parent-child, institution-member)

### Program Management
- **Programs**: Defines structured tutoring programs offered
- **Program Modules**: Individual components/sessions within programs
- **Enrollments**: Tracks client enrollment in programs
- **Module Progress**: Tracks client progress through program modules
- **Module Sessions**: Links program modules to appointments

### Appointment System
- **Appointments**: Scheduled tutoring sessions
- **Recurring Appointments**: Templates for generating regular appointments
- **Tutors & Services**: Service providers and available tutoring services
- **Tutor Services**: Junction between tutors and services with pricing

### Financial Management
- **Invoices & Invoice Items**: Billing records for services
- **Subscriptions**: Purchased plans for ongoing tutoring
- **Payments**: Records of financial transactions

## Installation

### Prerequisites

- Python 3.9+
- PostgreSQL
- Stripe account

### Setup

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/tutoring-appointment-system.git
   cd tutoring-appointment-system
   ```

2. Create and activate a virtual environment:
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

4. Create a `.env` file in the project root with the following variables (replace with your values):
   ```
   FLASK_APP=run.py
   FLASK_ENV=development
   SECRET_KEY=your-secret-key-change-in-production
   DATABASE_URL=postgresql://username:password@localhost/tutoring_app
   STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
   STRIPE_SECRET_KEY=your-stripe-secret-key
   STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret
   MAIL_SERVER=smtp.example.com
   MAIL_PORT=587
   MAIL_USE_TLS=true
   MAIL_USERNAME=<EMAIL>
   MAIL_PASSWORD=your-email-password
   MAIL_DEFAULT_SENDER=Your Name <<EMAIL>>
   TIMEZONE=America/New_York
   ```

5. Initialize and migrate the database:
   ```
   flask db init
   flask db migrate -m "Initial migration"
   flask db upgrade
   ```

6. Create an admin user:
   ```
   flask create-admin
   ```

7. (Optional) Create sample data for testing:
   ```
   flask create-sample-data
   ```

## Running the Application

1. Start the development server:
   ```
   flask run
   ```

2. Access the application at http://localhost:5000

## Deployment

The application is configured for deployment on Railway:

1. Create a new project on Railway
2. Connect your GitHub repository
3. Add a PostgreSQL database service
4. Configure environment variables in the Railway dashboard
5. Railway will automatically deploy your application

## Usage

### Manager Portal

- Manage tutors, clients (individual and institutional), and services
- Create and manage structured tutoring programs
- Track client progress through program modules
- Schedule and track appointments
- Generate and manage invoices
- View unpaid invoices grouped by client and sorted by amount
- Receive notifications for expired subscriptions and program milestones
- View reports and analytics

### Tutor Portal

- View and update appointments
- Access client information and notes
- Track client progress through program modules
- Manage personal profile
- Submit module completion reports

### Client Portal

- View and manage personal profiles
- For individual clients: manage personal profiles and progress
- For institutional clients: manage member accounts
- Track progress through enrolled programs
- Check scheduled appointments
- Pay invoices securely

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Bootstrap for the frontend styling
- Flask community for the excellent web framework
- Stripe for payment processing capabilities