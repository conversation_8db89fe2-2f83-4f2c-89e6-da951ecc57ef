<!-- app/templates/tutor/schedule.html -->
{% extends "base.html" %}

{% block title %}{{ t('tutor.schedule.page_title') }}{% endblock %}

{% block styles %}
<style>
    .calendar-container {
        overflow-x: auto;
    }
    .calendar-header {
        position: sticky;
        top: 0;
        background-color: #fff;
        z-index: 100;
    }
    .time-column {
        width: 80px;
        min-width: 80px;
    }
    .day-column {
        width: 200px;
        min-width: 200px;
    }
    .appointment {
        position: absolute;
        width: calc(100% - 10px);
        border-radius: 4px;
        padding: 5px;
        overflow: hidden;
        cursor: pointer;
        color: white;
    }
    .appointment.status-scheduled {
        background-color: #0d6efd;
    }
    .appointment.status-completed {
        background-color: #198754;
    }
    .appointment.status-cancelled {
        background-color: #dc3545;
        text-decoration: line-through;
    }
    .appointment.status-no-show {
        background-color: #fd7e14;
    }
    .appointment.status-awaiting_confirmation {
        background-color: #6f42c1;
        border: 2px solid #ffc107;
    }
    .time-slot {
        height: 60px;
        border-top: 1px solid #ddd;
        position: relative;
    }
    .day-header {
        height: 30px;
        text-align: center;
        border-bottom: 1px solid #ddd;
        font-weight: bold;
    }
    .current-time-indicator {
        position: absolute;
        height: 2px;
        background-color: red;
        width: 100%;
        z-index: 50;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">{{ t('tutor.schedule.title') }}</h2>
    </div>
</div>

<!-- Calendar Controls -->
<div class="row mb-4">
    <div class="col-md-4">
        <!-- View Type Selector -->
        <div class="btn-group" role="group">
            <a href="{{ url_for('tutor.schedule', view='day', start_date=start_date.strftime('%Y-%m-%d')) }}"
               class="btn btn-outline-primary {{ 'active' if view_type == 'day' else '' }}">{{ t('tutor.schedule.view_types.day') }}</a>
            <a href="{{ url_for('tutor.schedule', view='week', start_date=start_date.strftime('%Y-%m-%d')) }}"
               class="btn btn-outline-primary {{ 'active' if view_type == 'week' else '' }}">{{ t('tutor.schedule.view_types.week') }}</a>
            <a href="{{ url_for('tutor.schedule', view='month', start_date=start_date.strftime('%Y-%m-%d')) }}"
               class="btn btn-outline-primary {{ 'active' if view_type == 'month' else '' }}">{{ t('tutor.schedule.view_types.month') }}</a>
        </div>
    </div>
    <div class="col-md-4 text-center">
        <!-- Date Navigation -->
        <div class="btn-group" role="group">
            <a href="{{ url_for('tutor.schedule', view=view_type, start_date=prev_date.strftime('%Y-%m-%d')) }}"
               class="btn btn-outline-secondary">
                <i class="fas fa-chevron-left"></i>
            </a>
            <button class="btn btn-outline-secondary" id="current-date-btn">
                {% if view_type == 'day' %}
                    {{ start_date.strftime('%B %d, %Y') }}
                {% elif view_type == 'week' %}
                    {{ t('tutor.schedule.view_types.week_of') }} {{ start_date.strftime('%B %d, %Y') }}
                {% elif view_type == 'month' %}
                    {{ start_date.strftime('%B %Y') }}
                {% endif %}
            </button>
            <a href="{{ url_for('tutor.schedule', view=view_type, start_date=next_date.strftime('%Y-%m-%d')) }}"
               class="btn btn-outline-secondary">
                <i class="fas fa-chevron-right"></i>
            </a>
        </div>
    </div>
    <div class="col-md-4 text-end">
        <!-- Client Filter -->
        <select class="form-select" id="client-filter">
            <option value="">{{ t('tutor.schedule.filter.all_clients') }}</option>
            {% for client in clients %}
                <option value="{{ client.id }}" {% if request.args.get('client_id') == client.id|string %}selected{% endif %}>
                    {{ client.first_name }} {{ client.last_name }}
                </option>
            {% endfor %}
        </select>
    </div>
</div>

<!-- Calendar View -->
<div class="card shadow">
    <div class="card-body p-0">
        <div class="calendar-container" id="calendar-container">
            {% if view_type == 'day' %}
                <!-- Day View -->
                <table class="table table-bordered m-0">
                    <thead class="calendar-header">
                        <tr>
                            <th class="time-column"></th>
                            <th class="day-column">{{ start_date.strftime('%A, %B %d') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for hour in range(8, 22) %}
                            <tr>
                                <td class="time-column">
                                    {{ '{0}{1}'.format(hour % 12 if hour % 12 else 12, 'AM' if hour < 12 else 'PM') }}
                                </td>
                                <td class="time-slot" data-hour="{{ hour }}"></td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% elif view_type == 'week' %}
                <!-- Week View -->
                <table class="table table-bordered m-0">
                    <thead class="calendar-header">
                        <tr>
                            <th class="time-column"></th>
                            {% for i in range(7) %}
                                {% set day = start_date + timedelta(days=i) %}
                                <th class="text-center {{ 'bg-light' if day.date() == now.date() else '' }}">
                                    {{ day.strftime('%a') }}<br>
                                    {{ day.strftime('%m/%d') }}
                                </th>
                            {% endfor %}
                        </tr>
                    </thead>
                    <tbody>
                        {% for hour in range(8, 22) %}
                            <tr>
                                <td class="time-column">
                                    {{ '{0}{1}'.format(hour % 12 if hour % 12 else 12, 'AM' if hour < 12 else 'PM') }}
                                </td>
                                {% for i in range(7) %}
                                    <td class="time-slot" data-day="{{ i }}" data-hour="{{ hour }}"></td>
                                {% endfor %}
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% elif view_type == 'month' %}
                <!-- Month View -->
                <table class="table table-bordered m-0">
                    <thead class="calendar-header">
                        <tr>
                            <th class="text-center">{{ t('tutor.schedule.days.sunday') }}</th>
                            <th class="text-center">{{ t('tutor.schedule.days.monday') }}</th>
                            <th class="text-center">{{ t('tutor.schedule.days.tuesday') }}</th>
                            <th class="text-center">{{ t('tutor.schedule.days.wednesday') }}</th>
                            <th class="text-center">{{ t('tutor.schedule.days.thursday') }}</th>
                            <th class="text-center">{{ t('tutor.schedule.days.friday') }}</th>
                            <th class="text-center">{{ t('tutor.schedule.days.saturday') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% set first_day = start_date %}
                        {% set last_day = next_date - timedelta(days=1) %}
                        {% set first_day_weekday = first_day.weekday() %}
                        {% set days_in_month = (last_day - first_day).days + 1 %}
                        {% set total_weeks = (days_in_month + first_day_weekday + 6) // 7 %}

                        {% for week in range(total_weeks) %}
                            <tr style="height: 120px;">
                                {% for weekday in range(7) %}
                                    {% set day_offset = week * 7 + weekday - first_day_weekday %}
                                    {% set current_day = first_day + timedelta(days=day_offset) %}

                                    {% if current_day.month == first_day.month %}
                                        <td class="position-relative {{ 'bg-light' if current_day.date() == now.date() else '' }}" data-date="{{ current_day.strftime('%Y-%m-%d') }}">
                                            <div class="d-flex justify-content-between mb-2">
                                                <span class="fw-bold">{{ current_day.day }}</span>
                                                <a href="{{ url_for('tutor.schedule', view='day', start_date=current_day.strftime('%Y-%m-%d')) }}"
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-calendar-day"></i>
                                                </a>
                                            </div>
                                            <div class="appointment-container" style="height: 90px; overflow-y: auto;"></div>
                                        </td>
                                    {% else %}
                                        <td class="bg-light text-muted">
                                            <span class="fw-light">{{ current_day.day }}</span>
                                        </td>
                                    {% endif %}
                                {% endfor %}
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% endif %}
        </div>
    </div>
</div>

<!-- Appointment Details Modal -->
<div class="modal fade" id="appointmentModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ t('tutor.schedule.modal.appointment_details') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">{{ t('tutor.schedule.modal.loading') }}</span>
                    </div>
                </div>
                <div id="appointmentDetails" style="display: none;">
                    <div class="mb-3">
                        <label class="fw-bold">{{ t('tutor.schedule.modal.client') }}</label>
                        <span id="appointmentClient"></span>
                    </div>
                    <div class="mb-3">
                        <label class="fw-bold">{{ t('tutor.schedule.modal.service') }}</label>
                        <span id="appointmentService"></span>
                    </div>
                    <div class="mb-3">
                        <label class="fw-bold">{{ t('tutor.schedule.modal.date_time') }}</label>
                        <span id="appointmentDateTime"></span>
                    </div>
                    <div class="mb-3">
                        <label class="fw-bold">{{ t('tutor.schedule.modal.status') }}</label>
                        <span id="appointmentStatus"></span>
                    </div>
                    <div class="mb-3">
                        <label class="fw-bold">{{ t('tutor.schedule.modal.notes') }}</label>
                        <p id="appointmentNotes" class="text-muted"></p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ t('tutor.schedule.modal.close') }}</button>
                <a href="#" id="viewAppointmentBtn" class="btn btn-primary">{{ t('tutor.schedule.modal.view_details') }}</a>
                <a href="#" id="updateAppointmentBtn" class="btn btn-success">{{ t('tutor.schedule.modal.update') }}</a>
            </div>
        </div>
    </div>
</div>

<!-- Date Picker Modal -->
<div class="modal fade" id="datePickerModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ t('tutor.schedule.date_picker.select_date') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="date" id="datePicker" class="form-control">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ t('tutor.schedule.modal.close') }}</button>
                <button type="button" class="btn btn-primary" id="goToDateBtn">{{ t('tutor.schedule.date_picker.go_to_date') }}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Calendar data from backend
        const calendarData = {{ calendar_data | tojson }};
        const viewType = '{{ view_type }}';

        // Populate calendar with appointments
        renderCalendar(calendarData);

        // Show appointment modal when clicking on an appointment
        document.querySelectorAll('.appointment').forEach(function(appointment) {
            appointment.addEventListener('click', function() {
                const appointmentId = this.dataset.id;
                showAppointmentDetails(appointmentId);
            });
        });

        // Date picker functionality
        document.getElementById('current-date-btn').addEventListener('click', function() {
            const datePickerModal = new bootstrap.Modal(document.getElementById('datePickerModal'));
            datePickerModal.show();
        });

        document.getElementById('goToDateBtn').addEventListener('click', function() {
            const date = document.getElementById('datePicker').value;
            if (date) {
                window.location.href = "{{ url_for('tutor.schedule', view=view_type) }}" + "&start_date=" + date;
            }
        });

        // Client filter functionality
        document.getElementById('client-filter').addEventListener('change', function() {
            const clientId = this.value;
            if (clientId) {
                window.location.href = "{{ url_for('tutor.schedule', view=view_type, start_date=start_date.strftime('%Y-%m-%d')) }}" + "&client_id=" + clientId;
            } else {
                window.location.href = "{{ url_for('tutor.schedule', view=view_type, start_date=start_date.strftime('%Y-%m-%d')) }}";
            }
        });

        // Mark current time on the calendar
        if (viewType === 'day' || viewType === 'week') {
            markCurrentTime();
        }
    });

    function renderCalendar(data) {
        const appointments = data.appointments;
        const viewType = data.view_type;

        if (viewType === 'day') {
            appointments.forEach(function(appointment) {
                const startTime = new Date(appointment.start_time);
                const endTime = new Date(appointment.end_time);

                // Calculate position and height
                const startHour = startTime.getHours() + startTime.getMinutes() / 60;
                const endHour = endTime.getHours() + endTime.getMinutes() / 60;
                const duration = endHour - startHour;

                // Create appointment element
                const appointmentEl = document.createElement('div');
                appointmentEl.className = `appointment status-${appointment.status}`;
                appointmentEl.dataset.id = appointment.id;
                appointmentEl.innerHTML = `
                    <div class="fw-bold">${appointment.client_name}</div>
                    <div class="small">${appointment.service_name}</div>
                    <div class="small">${formatTime(startTime)} - ${formatTime(endTime)}</div>
                `;

                // Place in hour slot
                const hourCells = document.querySelectorAll(`td[data-hour="${Math.floor(startHour)}"]`);

                if (hourCells.length > 0) {
                    appointmentEl.style.top = `${(startHour - Math.floor(startHour)) * 60}px`;
                    appointmentEl.style.height = `${duration * 60}px`;
                    hourCells[0].appendChild(appointmentEl);
                }
            });
        } else if (viewType === 'week') {
            appointments.forEach(function(appointment) {
                const startTime = new Date(appointment.start_time);
                const endTime = new Date(appointment.end_time);

                // Calculate position and height
                const startHour = startTime.getHours() + startTime.getMinutes() / 60;
                const endHour = endTime.getHours() + endTime.getMinutes() / 60;
                const duration = endHour - startHour;
                const dayOfWeek = startTime.getDay();

                // Create appointment element
                const appointmentEl = document.createElement('div');
                appointmentEl.className = `appointment status-${appointment.status}`;
                appointmentEl.dataset.id = appointment.id;
                appointmentEl.innerHTML = `
                    <div class="fw-bold">${appointment.client_name}</div>
                    <div class="small">${appointment.service_name}</div>
                    <div class="small">${formatTime(startTime)} - ${formatTime(endTime)}</div>
                `;

                // Place in day/hour slot
                const cell = document.querySelector(`td[data-day="${dayOfWeek}"][data-hour="${Math.floor(startHour)}"]`);

                if (cell) {
                    appointmentEl.style.top = `${(startHour - Math.floor(startHour)) * 60}px`;
                    appointmentEl.style.height = `${duration * 60}px`;
                    cell.appendChild(appointmentEl);
                }
            });
        } else if (viewType === 'month') {
            appointments.forEach(function(appointment) {
                const startTime = new Date(appointment.start_time);
                const dateStr = startTime.toISOString().split('T')[0];
                const cell = document.querySelector(`td[data-date="${dateStr}"]`);

                if (cell) {
                    const container = cell.querySelector('.appointment-container');
                    const appointmentEl = document.createElement('div');
                    appointmentEl.className = `p-1 mb-1 rounded status-${appointment.status}`;
                    appointmentEl.dataset.id = appointment.id;
                    appointmentEl.innerHTML = `
                        <small>${formatTime(startTime)}: ${appointment.client_name}</small>
                    `;
                    container.appendChild(appointmentEl);
                }
            });
        }
    }

    function showAppointmentDetails(appointmentId) {
        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('appointmentModal'));
        modal.show();

        // Hide details, show spinner
        document.getElementById('appointmentDetails').style.display = 'none';
        document.querySelector('#appointmentModal .spinner-border').style.display = 'block';

        // Set up buttons
        document.getElementById('viewAppointmentBtn').href = `/tutor/appointments/${appointmentId}`;
        document.getElementById('updateAppointmentBtn').href = `/tutor/appointments/${appointmentId}/update`;

        // Fetch appointment details
        fetch(`/api/appointment/${appointmentId}`)
            .then(response => response.json())
            .then(data => {
                document.getElementById('appointmentClient').textContent = data.client.name;
                document.getElementById('appointmentService').textContent = data.service.name;
                document.getElementById('appointmentDateTime').textContent =
                    `${formatDateTime(new Date(data.start_time))} - ${formatTime(new Date(data.end_time))}`;

                // Format status
                const statusEl = document.getElementById('appointmentStatus');
                statusEl.textContent = data.status.charAt(0).toUpperCase() + data.status.slice(1);
                statusEl.className = '';
                statusEl.classList.add(`text-${getStatusColor(data.status)}`);

                // Notes
                document.getElementById('appointmentNotes').textContent = data.notes || '{{ t('tutor.schedule.modal.no_notes') }}';

                // Hide spinner, show details
                document.querySelector('#appointmentModal .spinner-border').style.display = 'none';
                document.getElementById('appointmentDetails').style.display = 'block';
            })
            .catch(error => {
                console.error('Error fetching appointment details:', error);
                document.querySelector('#appointmentModal .spinner-border').style.display = 'none';
                document.getElementById('appointmentDetails').innerHTML = '<div class="alert alert-danger">Error loading appointment details.</div>';
            });
    }

    function markCurrentTime() {
        const now = new Date();
        const currentHour = now.getHours();
        const currentMinutes = now.getMinutes();

        // Only mark time if within business hours (8AM-9PM)
        if (currentHour >= 8 && currentHour < 21) {
            if ('{{ view_type }}' === 'day') {
                const hourCell = document.querySelector(`td[data-hour="${currentHour}"]`);
                if (hourCell) {
                    const indicator = document.createElement('div');
                    indicator.className = 'current-time-indicator';
                    indicator.style.top = `${currentMinutes}px`;
                    hourCell.appendChild(indicator);
                }
            } else if ('{{ view_type }}' === 'week') {
                const dayOfWeek = now.getDay();
                const hourCell = document.querySelector(`td[data-day="${dayOfWeek}"][data-hour="${currentHour}"]`);
                if (hourCell) {
                    const indicator = document.createElement('div');
                    indicator.className = 'current-time-indicator';
                    indicator.style.top = `${currentMinutes}px`;
                    hourCell.appendChild(indicator);
                }
            }
        }
    }

    function formatTime(date) {
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }

    function formatDateTime(date) {
        return date.toLocaleDateString() + ' ' + formatTime(date);
    }

    function getStatusColor(status) {
        switch (status) {
            case 'completed': return 'success';
            case 'scheduled': return 'primary';
            case 'cancelled': return 'danger';
            case 'no-show': return 'warning';
            case 'awaiting_confirmation': return 'info';
            default: return 'secondary';
        }
    }
</script>
{% endblock %}