#!/usr/bin/env python3

from app import create_app
from app.extensions import db
from sqlalchemy import text

app = create_app()
with app.app_context():
    print('Checking database schema for referenced tables...')
    
    tables_to_check = ['clients', 'tutors', 'dependants', 'tutor_services', 'subscriptions', 'users', 'appointment_recurring_schedules']
    
    for table in tables_to_check:
        print(f'\n{table.upper()} table columns:')
        query = f"SELECT column_name, data_type FROM information_schema.columns WHERE table_name = '{table}' ORDER BY ordinal_position"
        try:
            result = db.session.execute(text(query))
            columns = result.fetchall()
            for col in columns:
                print(f'  {col[0]}: {col[1]}')
        except Exception as e:
            print(f'  Error: {e}')
    
    print('\nChecking foreign key constraints in appointments table...')
    query = """
    SELECT 
        tc.constraint_name,
        kcu.column_name,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name
    FROM information_schema.table_constraints AS tc
    JOIN information_schema.key_column_usage AS kcu
        ON tc.constraint_name = kcu.constraint_name
    JOIN information_schema.constraint_column_usage AS ccu
        ON ccu.constraint_name = tc.constraint_name
    WHERE tc.constraint_type = 'FOREIGN KEY' 
        AND tc.table_name = 'appointments'
    """
    
    try:
        result = db.session.execute(text(query))
        fks = result.fetchall()
        for fk in fks:
            print(f'  {fk[1]} -> {fk[2]}.{fk[3]}')
    except Exception as e:
        print(f'  Error: {e}')