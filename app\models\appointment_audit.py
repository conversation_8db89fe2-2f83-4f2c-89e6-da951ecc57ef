# app/models/appointment_audit.py
from datetime import datetime
from app.extensions import db
from sqlalchemy.dialects.postgresql import INET, JSONB

class AppointmentAudit(db.Model):
    __tablename__ = 'appointment_audit'

    audit_id = db.Column(db.Integer, primary_key=True)
    appointment_id = db.Column(db.Integer, nullable=False)
    action = db.Column(db.String(20), nullable=False)  # create, update, delete, cancel
    user_id = db.Column(db.Integer, db.ForeignKey('users.user_id'), nullable=True)
    user_role = db.Column(db.String(50), nullable=True)
    user_email = db.Column(db.String(255), nullable=True)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    
    # JSON fields for storing old and new values
    old_values = db.Column(JSONB)
    new_values = db.Column(JSONB)
    
    # Specific fields for quick querying
    old_status = db.Column(db.String(50))
    new_status = db.Column(db.String(50))
    old_tutor_id = db.Column(db.Integer)
    new_tutor_id = db.Column(db.Integer)
    old_client_id = db.Column(db.Integer)
    new_client_id = db.Column(db.Integer)
    old_start_time = db.Column(db.DateTime)
    new_start_time = db.Column(db.DateTime)
    
    # Additional context
    ip_address = db.Column(INET)
    user_agent = db.Column(db.Text)
    notes = db.Column(db.Text)
    
    # Relationships
    user = db.relationship('User', foreign_keys=[user_id])
    
    def __repr__(self):
        return f'<AppointmentAudit {self.audit_id} appointment={self.appointment_id} action={self.action}>'
    
    @property
    def appointment(self):
        """Get the associated appointment if it still exists."""
        from app.models.appointment import Appointment
        return Appointment.query.get(self.appointment_id)
    
    @property
    def action_description(self):
        """Get a human-readable description of the action."""
        descriptions = {
            'create': 'Created',
            'update': 'Updated',
            'delete': 'Deleted',
            'cancel': 'Cancelled'
        }
        return descriptions.get(self.action, self.action.title())
    
    @property
    def changes_summary(self):
        """Get a summary of what changed."""
        if self.action == 'create':
            return "Appointment created"
        elif self.action == 'cancel':
            return "Appointment cancelled"
        elif self.action == 'delete':
            return "Appointment deleted"
        elif self.action == 'update' and self.old_values and self.new_values:
            changes = []
            
            # Check specific important fields
            if self.old_status != self.new_status:
                changes.append(f"Status: {self.old_status} → {self.new_status}")
            
            if self.old_tutor_id != self.new_tutor_id:
                changes.append("Tutor changed")
            
            if self.old_client_id != self.new_client_id:
                changes.append("Client changed")
            
            if self.old_start_time != self.new_start_time:
                changes.append("Time changed")
            
            # Check other fields from JSON
            old_json = self.old_values or {}
            new_json = self.new_values or {}
            
            for key in ['notes', 'transport_fee', 'duration_minutes']:
                if old_json.get(key) != new_json.get(key):
                    changes.append(f"{key.replace('_', ' ').title()} changed")
            
            return ", ".join(changes) if changes else "Minor changes"
        
        return "Unknown change"
    
    @classmethod
    def log_action(cls, appointment_id, action, user=None, old_appointment=None, 
                   new_appointment=None, ip_address=None, user_agent=None, notes=None):
        """Create an audit log entry."""
        audit = cls(
            appointment_id=appointment_id,
            action=action,
            user_id=user.user_id if user else None,
            user_role=user.role if user else None,
            user_email=user.email if user else None,
            ip_address=ip_address,
            user_agent=user_agent,
            notes=notes
        )
        
        if old_appointment:
            audit.old_values = {
                'status': old_appointment.status,
                'tutor_id': old_appointment.tutor_id,
                'client_id': old_appointment.client_id,
                'dependant_id': old_appointment.dependant_id,
                'start_time': old_appointment.start_time.isoformat() if old_appointment.start_time else None,
                'end_time': old_appointment.end_time.isoformat() if old_appointment.end_time else None,
                'notes': old_appointment.notes,
                'transport_fee': float(old_appointment.transport_fee) if old_appointment.transport_fee else None
            }
            audit.old_status = old_appointment.status
            audit.old_tutor_id = old_appointment.tutor_id
            audit.old_client_id = old_appointment.client_id
            audit.old_start_time = old_appointment.start_time
        
        if new_appointment:
            audit.new_values = {
                'status': new_appointment.status,
                'tutor_id': new_appointment.tutor_id,
                'client_id': new_appointment.client_id,
                'dependant_id': new_appointment.dependant_id,
                'start_time': new_appointment.start_time.isoformat() if new_appointment.start_time else None,
                'end_time': new_appointment.end_time.isoformat() if new_appointment.end_time else None,
                'notes': new_appointment.notes,
                'transport_fee': float(new_appointment.transport_fee) if new_appointment.transport_fee else None
            }
            audit.new_status = new_appointment.status
            audit.new_tutor_id = new_appointment.tutor_id
            audit.new_client_id = new_appointment.client_id
            audit.new_start_time = new_appointment.start_time
        
        db.session.add(audit)
        return audit