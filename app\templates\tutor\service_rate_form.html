{% extends 'base.html' %}

{% block title %}Edit Service Rate{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1>Edit Service Rate</h1>
    <p class="lead">Update your rate for {{ rate.service.name }}.</p>

    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Edit Service Rate</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('tutor.service_rates') }}">
                        {{ form.csrf_token }}
                        {{ form.id }}
                        <div class="mb-3">
                            {{ form.service_id.label(class="form-label") }}
                            {{ form.service_id(class="form-select", disabled=true) }}
                        </div>
                        <div class="mb-3">
                            {{ form.tutor_rate.label(class="form-label") }}
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                {{ form.tutor_rate(class="form-control", readonly=true) }}
                            </div>
                            <div class="form-text">Tutor rate is set by management.</div>
                        </div>
                        <div class="mb-3">
                            {{ form.client_rate.label(class="form-label") }}
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                {{ form.client_rate(class="form-control") }}
                            </div>
                        </div>
                        <div class="mb-3">
                            {{ form.transport_fee.label(class="form-label") }}
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                {{ form.transport_fee(class="form-control") }}
                            </div>
                        </div>
                        <div class="mb-3">
                            {{ form.transport_fee_description.label(class="form-label") }}
                            {{ form.transport_fee_description(class="form-control") }}
                        </div>
                        <div class="mb-3 form-check">
                            {{ form.is_active(class="form-check-input") }}
                            {{ form.is_active.label(class="form-check-label") }}
                        </div>
                        <button type="submit" class="btn btn-primary">Update Service Rate</button>
                        <a href="{{ url_for('tutor.service_rates') }}" class="btn btn-secondary">Cancel</a>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
