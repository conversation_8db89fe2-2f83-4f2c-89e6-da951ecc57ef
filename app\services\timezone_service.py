# app/services/timezone_service.py
from datetime import datetime, timezone
from flask import current_app
import pytz


class TimezoneService:
    """Service for handling timezone conversions, specifically UTC to Eastern Time."""
    
    @staticmethod
    def get_eastern_timezone():
        """Get the Eastern timezone object (handles EST/EDT automatically)."""
        timezone_name = current_app.config.get('TIMEZONE', 'America/New_York')
        return pytz.timezone(timezone_name)
    
    @staticmethod
    def utc_to_eastern(utc_datetime):
        """
        Convert UTC datetime to Eastern Time (EST/EDT).
        
        Args:
            utc_datetime (datetime): UTC datetime object (naive or timezone-aware)
            
        Returns:
            datetime: Eastern timezone datetime object
            
        Raises:
            ValueError: If input is not a datetime object
        """
        if not isinstance(utc_datetime, datetime):
            raise ValueError("Input must be a datetime object")
        
        # If datetime is naive, assume it's UTC
        if utc_datetime.tzinfo is None:
            utc_datetime = utc_datetime.replace(tzinfo=timezone.utc)
        
        # Convert to Eastern timezone
        eastern_tz = TimezoneService.get_eastern_timezone()
        return utc_datetime.astimezone(eastern_tz)
    
    @staticmethod
    def eastern_to_utc(eastern_datetime):
        """
        Convert Eastern Time datetime to UTC.
        
        Args:
            eastern_datetime (datetime): Eastern timezone datetime object
            
        Returns:
            datetime: UTC timezone datetime object
            
        Raises:
            ValueError: If input is not a datetime object
        """
        if not isinstance(eastern_datetime, datetime):
            raise ValueError("Input must be a datetime object")
        
        # If datetime is naive, assume it's Eastern
        if eastern_datetime.tzinfo is None:
            eastern_tz = TimezoneService.get_eastern_timezone()
            eastern_datetime = eastern_tz.localize(eastern_datetime)
        
        # Convert to UTC
        return eastern_datetime.astimezone(timezone.utc)
    
    @staticmethod
    def format_eastern_datetime(utc_datetime, format_string='%Y-%m-%d %I:%M %p EST'):
        """
        Format UTC datetime as Eastern Time string.
        
        Args:
            utc_datetime (datetime): UTC datetime object
            format_string (str): strftime format string
            
        Returns:
            str: Formatted Eastern time string
        """
        if not utc_datetime:
            return ""
        
        eastern_dt = TimezoneService.utc_to_eastern(utc_datetime)
        
        # Determine if we're in EST or EDT
        is_dst = eastern_dt.dst().total_seconds() > 0
        timezone_abbr = 'EDT' if is_dst else 'EST'
        
        # Replace EST/EDT in format string with actual timezone
        format_string = format_string.replace('EST', timezone_abbr)
        
        return eastern_dt.strftime(format_string)
    
    @staticmethod
    def format_eastern_date(utc_datetime, format_string='%Y-%m-%d'):
        """
        Format UTC datetime as Eastern Time date string.
        
        Args:
            utc_datetime (datetime): UTC datetime object
            format_string (str): strftime format string for date
            
        Returns:
            str: Formatted Eastern date string
        """
        if not utc_datetime:
            return ""
        
        eastern_dt = TimezoneService.utc_to_eastern(utc_datetime)
        return eastern_dt.strftime(format_string)
    
    @staticmethod
    def format_eastern_time(utc_datetime, format_string='%I:%M %p'):
        """
        Format UTC datetime as Eastern Time time string.
        
        Args:
            utc_datetime (datetime): UTC datetime object
            format_string (str): strftime format string for time
            
        Returns:
            str: Formatted Eastern time string
        """
        if not utc_datetime:
            return ""
        
        eastern_dt = TimezoneService.utc_to_eastern(utc_datetime)
        return eastern_dt.strftime(format_string)
    
    @staticmethod
    def get_current_eastern_time():
        """
        Get current datetime in Eastern timezone.
        
        Returns:
            datetime: Current Eastern timezone datetime
        """
        utc_now = datetime.now(timezone.utc)
        return TimezoneService.utc_to_eastern(utc_now)
    
    @staticmethod
    def is_dst(utc_datetime=None):
        """
        Check if Daylight Saving Time is in effect for Eastern timezone.
        
        Args:
            utc_datetime (datetime, optional): UTC datetime to check. 
                                             If None, uses current time.
            
        Returns:
            bool: True if DST is in effect, False otherwise
        """
        if utc_datetime is None:
            utc_datetime = datetime.now(timezone.utc)
        
        eastern_dt = TimezoneService.utc_to_eastern(utc_datetime)
        return eastern_dt.dst().total_seconds() > 0
    
    @staticmethod
    def get_timezone_offset(utc_datetime=None):
        """
        Get the current timezone offset from UTC for Eastern timezone.
        
        Args:
            utc_datetime (datetime, optional): UTC datetime to check offset for.
                                             If None, uses current time.
            
        Returns:
            int: Offset in hours from UTC (negative for Eastern time)
        """
        if utc_datetime is None:
            utc_datetime = datetime.now(timezone.utc)
        
        eastern_dt = TimezoneService.utc_to_eastern(utc_datetime)
        offset_seconds = eastern_dt.utcoffset().total_seconds()
        return int(offset_seconds / 3600)
    
    @staticmethod
    def format_for_display(utc_datetime, include_timezone=True, long_format=False):
        """
        Format UTC datetime for user display in Eastern time.
        
        Args:
            utc_datetime (datetime): UTC datetime object
            include_timezone (bool): Whether to include timezone abbreviation
            long_format (bool): Whether to use long format with day name
            
        Returns:
            str: Formatted datetime string for display
        """
        if not utc_datetime:
            return ""
        
        eastern_dt = TimezoneService.utc_to_eastern(utc_datetime)
        
        if long_format:
            # Format: Monday, January 15, 2024 at 2:30 PM EST
            date_part = eastern_dt.strftime("%A, %B %d, %Y")
            time_part = eastern_dt.strftime("%I:%M %p")
            
            if include_timezone:
                is_dst = eastern_dt.dst().total_seconds() > 0
                timezone_abbr = 'EDT' if is_dst else 'EST'
                return f"{date_part} at {time_part} {timezone_abbr}"
            else:
                return f"{date_part} at {time_part}"
        else:
            # Format: Jan 15, 2024 2:30 PM EST
            if include_timezone:
                is_dst = eastern_dt.dst().total_seconds() > 0
                timezone_abbr = 'EDT' if is_dst else 'EST'
                return eastern_dt.strftime(f"%b %d, %Y %I:%M %p {timezone_abbr}")
            else:
                return eastern_dt.strftime("%b %d, %Y %I:%M %p")