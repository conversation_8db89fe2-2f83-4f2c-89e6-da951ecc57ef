#!/usr/bin/env python3
"""
Simple test for audit service core functionality without Flask context.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timezone

# Test the core formatting functions directly
def test_format_field_value():
    """Test field value formatting without Flask context."""
    print("Testing field value formatting...")
    
    # Import the service class
    from app.services.audit_service import AuditService
    
    test_cases = [
        ('status', 'scheduled', 'Scheduled'),
        ('status', 'confirmed', 'Confirmed'),
        ('duration_minutes', 90, '1h 30m'),
        ('duration_minutes', 60, '1h'),
        ('duration_minutes', 45, '45m'),
        ('transport_fee', 25.50, '$25.50'),
        ('notes', '', 'Empty'),
        ('notes', None, 'Not set'),
        ('tutor_id', 123, 'ID 123'),  # Without database lookup
        ('client_id', 456, 'ID 456')  # Without database lookup
    ]
    
    for field, value, expected_pattern in test_cases:
        try:
            formatted = AuditService._format_field_value(field, value)
            print(f"✓ {field}: {repr(value)} → {formatted}")
            
            # Basic validation
            if field == 'status' and value:
                assert formatted == value.replace('_', ' ').title()
            elif field == 'duration_minutes' and value:
                assert 'h' in formatted or 'm' in formatted
            elif field == 'transport_fee' and value:
                assert formatted.startswith('$')
            elif value is None:
                assert formatted == 'Not set'
            elif value == '':
                assert formatted == 'Empty'
                
        except Exception as e:
            print(f"⚠ Error formatting {field}: {e}")
    
    print("Field value formatting test passed!\n")

def test_get_field_display_name():
    """Test field display name mapping."""
    print("Testing field display names...")
    
    from app.services.audit_service import AuditService
    
    test_fields = {
        'status': 'Status',
        'tutor_id': 'Tutor',
        'client_id': 'Client',
        'dependant_id': 'Dependant',
        'start_time': 'Start Time',
        'end_time': 'End Time',
        'notes': 'Notes',
        'transport_fee': 'Transport Fee',
        'duration_minutes': 'Duration',
        'unknown_field': 'Unknown Field'
    }
    
    for field, expected in test_fields.items():
        display_name = AuditService._get_field_display_name(field)
        print(f"✓ {field} → {display_name}")
        assert display_name == expected, f"Expected {expected}, got {display_name}"
    
    print("Field display names test passed!\n")

def test_get_action_display_info():
    """Test action display information."""
    print("Testing action display information...")
    
    from app.services.audit_service import AuditService
    
    expected_actions = {
        'create': {'icon': '➕', 'color': 'green'},
        'update': {'icon': '📝', 'color': 'blue'},
        'delete': {'icon': '🗑️', 'color': 'red'},
        'cancel': {'icon': '❌', 'color': 'orange'},
        'unknown': {'icon': '📝', 'color': 'gray'}
    }
    
    for action, expected in expected_actions.items():
        info = AuditService._get_action_display_info(action)
        print(f"✓ {action}: {info}")
        assert info['icon'] == expected['icon'], f"Wrong icon for {action}"
        assert info['color'] == expected['color'], f"Wrong color for {action}"
    
    print("Action display info test passed!\n")

def test_changes_detail_structure():
    """Test the structure of changes detail formatting."""
    print("Testing changes detail structure...")
    
    from app.services.audit_service import AuditService
    
    # Mock audit entry for testing
    class MockAuditEntry:
        def __init__(self, action, old_values=None, new_values=None):
            self.action = action
            self.old_values = old_values or {}
            self.new_values = new_values or {}
    
    # Test create action
    create_entry = MockAuditEntry(
        action='create',
        new_values={
            'status': 'scheduled',
            'notes': 'Initial appointment',
            'duration_minutes': 60
        }
    )
    
    create_changes = AuditService._format_changes_detail(create_entry)
    print(f"✓ Create changes count: {len(create_changes)}")
    
    for change in create_changes:
        print(f"  - {change['field_display']}: {change['new_value_display']} (type: {change['change_type']})")
        assert change['change_type'] == 'created'
        assert 'field_display' in change
        assert 'new_value_display' in change
    
    # Test update action
    update_entry = MockAuditEntry(
        action='update',
        old_values={
            'status': 'scheduled',
            'notes': ''
        },
        new_values={
            'status': 'confirmed',
            'notes': 'Client confirmed'
        }
    )
    
    update_changes = AuditService._format_changes_detail(update_entry)
    print(f"✓ Update changes count: {len(update_changes)}")
    
    for change in update_changes:
        print(f"  - {change['field_display']}: {change['old_value_display']} → {change['new_value_display']} (type: {change['change_type']})")
        assert change['change_type'] == 'updated'
        assert 'old_value_display' in change
        assert 'new_value_display' in change
    
    print("Changes detail structure test passed!\n")

if __name__ == '__main__':
    print("=== Simple Audit Service Test ===\n")
    
    try:
        test_format_field_value()
        test_get_field_display_name()
        test_get_action_display_info()
        test_changes_detail_structure()
        
        print("🎉 All simple tests passed! Core audit service functionality is working correctly.")
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)