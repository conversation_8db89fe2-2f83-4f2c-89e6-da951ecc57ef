-- Migration: Fix Tutor Table Schema to Match SQLAlchemy Model
-- Date: 2025-07-17
-- Description: Updates the tutors table structure to match the SQLAlchemy model expectations

-- Step 1: Add the new columns that the SQLAlchemy model expects
DO $$ 
BEGIN
    -- Add street_address column (combines civic_number and street)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'tutors' 
                   AND column_name = 'street_address') THEN
        ALTER TABLE tutors ADD COLUMN street_address TEXT;
        
        -- Migrate existing data: combine civic_number and street into street_address
        UPDATE tutors 
        SET street_address = CASE 
            WHEN civic_number IS NOT NULL AND street IS NOT NULL THEN 
                CONCAT(civic_number, ' ', street)
            WHEN civic_number IS NOT NULL THEN 
                civic_number
            WHEN street IS NOT NULL THEN 
                street
            ELSE NULL
        END;
        
        RAISE NOTICE 'Added street_address column and migrated data';
    END IF;
    
    -- Add zip_code column (rename from postal_code)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'tutors' 
                   AND column_name = 'zip_code') THEN
        ALTER TABLE tutors ADD COLUMN zip_code VARCHAR(20);
        
        -- Migrate existing data from postal_code to zip_code
        UPDATE tutors SET zip_code = postal_code WHERE postal_code IS NOT NULL;
        
        RAISE NOTICE 'Added zip_code column and migrated data from postal_code';
    END IF;
    
    -- Add birthdate column (rename from date_of_birth)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'tutors' 
                   AND column_name = 'birthdate') THEN
        ALTER TABLE tutors ADD COLUMN birthdate DATE;
        
        -- Migrate existing data from date_of_birth to birthdate
        UPDATE tutors SET birthdate = date_of_birth WHERE date_of_birth IS NOT NULL;
        
        RAISE NOTICE 'Added birthdate column and migrated data from date_of_birth';
    END IF;
    
    -- Add bank_transit_number_encrypted column (rename from bank_transit_number)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'tutors' 
                   AND column_name = 'bank_transit_number_encrypted') THEN
        ALTER TABLE tutors ADD COLUMN bank_transit_number_encrypted VARCHAR(255);
        
        -- Note: bank_transit_number was not encrypted, so we don't migrate the data
        -- The application will handle re-entering this information
        
        RAISE NOTICE 'Added bank_transit_number_encrypted column';
    END IF;
    
    -- Add bank_institution_number_encrypted column (rename from bank_institution_number)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'tutors' 
                   AND column_name = 'bank_institution_number_encrypted') THEN
        ALTER TABLE tutors ADD COLUMN bank_institution_number_encrypted VARCHAR(255);
        
        -- Note: bank_institution_number was not encrypted, so we don't migrate the data
        -- The application will handle re-entering this information
        
        RAISE NOTICE 'Added bank_institution_number_encrypted column';
    END IF;
    
    -- Rename bank_account_number_encrypted if it doesn't exist but bank_account_number does
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'tutors' 
                   AND column_name = 'bank_account_number_encrypted') 
       AND EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'tutors' 
                   AND column_name = 'bank_account_number') THEN
        ALTER TABLE tutors RENAME COLUMN bank_account_number TO bank_account_number_encrypted;
        ALTER TABLE tutors ALTER COLUMN bank_account_number_encrypted TYPE VARCHAR(255);
        
        RAISE NOTICE 'Renamed bank_account_number to bank_account_number_encrypted';
    ELSIF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                      WHERE table_name = 'tutors' 
                      AND column_name = 'bank_account_number_encrypted') THEN
        ALTER TABLE tutors ADD COLUMN bank_account_number_encrypted VARCHAR(255);
        
        RAISE NOTICE 'Added bank_account_number_encrypted column';
    END IF;
END $$;

-- Step 2: Update column types to match SQLAlchemy model expectations
DO $$
BEGIN
    -- Ensure phone column allows NULL (SQLAlchemy model has nullable=False but we'll keep it flexible)
    -- Update city column type
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'tutors' 
               AND column_name = 'city' 
               AND character_maximum_length != 100) THEN
        ALTER TABLE tutors ALTER COLUMN city TYPE VARCHAR(100);
        RAISE NOTICE 'Updated city column type to VARCHAR(100)';
    END IF;
    
    -- Update province column type
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'tutors' 
               AND column_name = 'province' 
               AND character_maximum_length != 100) THEN
        ALTER TABLE tutors ALTER COLUMN province TYPE VARCHAR(100);
        RAISE NOTICE 'Updated province column type to VARCHAR(100)';
    END IF;
    
    -- Update country column type
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'tutors' 
               AND column_name = 'country' 
               AND character_maximum_length != 100) THEN
        ALTER TABLE tutors ALTER COLUMN country TYPE VARCHAR(100);
        RAISE NOTICE 'Updated country column type to VARCHAR(100)';
    END IF;
END $$;

-- Step 3: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_tutors_user_id ON tutors(user_id);
CREATE INDEX IF NOT EXISTS idx_tutors_is_active ON tutors(is_active);
CREATE INDEX IF NOT EXISTS idx_tutors_city ON tutors(city);
CREATE INDEX IF NOT EXISTS idx_tutors_province ON tutors(province);

-- Step 4: Verify the migration
DO $$
DECLARE
    missing_columns TEXT[] := ARRAY[]::TEXT[];
    col_name TEXT;
BEGIN
    -- Check for required columns
    FOR col_name IN SELECT unnest(ARRAY['street_address', 'zip_code', 'birthdate', 
                                        'bank_transit_number_encrypted', 
                                        'bank_institution_number_encrypted', 
                                        'bank_account_number_encrypted']) LOOP
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                       WHERE table_name = 'tutors' 
                       AND column_name = col_name) THEN
            missing_columns := array_append(missing_columns, col_name);
        END IF;
    END LOOP;
    
    IF array_length(missing_columns, 1) > 0 THEN
        RAISE EXCEPTION 'Migration incomplete. Missing columns: %', array_to_string(missing_columns, ', ');
    ELSE
        RAISE NOTICE 'Migration completed successfully. All required columns are present.';
    END IF;
END $$;

-- Step 5: Display current table structure
SELECT
    column_name,
    data_type,
    character_maximum_length,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'tutors'
ORDER BY ordinal_position;

-- ========================================
-- DEPENDANTS TABLE SCHEMA FIX
-- ========================================

-- Step 6: Fix dependants table schema to match SQLAlchemy model
DO $$
BEGIN
    -- Add user_id column (optional login account for dependants)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'dependants'
                   AND column_name = 'user_id') THEN
        ALTER TABLE dependants ADD COLUMN user_id INTEGER REFERENCES users(user_id) ON DELETE SET NULL;
        RAISE NOTICE 'Added user_id column to dependants table';
    END IF;

    -- Add email column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'dependants'
                   AND column_name = 'email') THEN
        ALTER TABLE dependants ADD COLUMN email VARCHAR(255);
        RAISE NOTICE 'Added email column to dependants table';
    END IF;

    -- Add password_hash column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'dependants'
                   AND column_name = 'password_hash') THEN
        ALTER TABLE dependants ADD COLUMN password_hash VARCHAR(255);
        RAISE NOTICE 'Added password_hash column to dependants table';
    END IF;

    -- Add phone column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'dependants'
                   AND column_name = 'phone') THEN
        ALTER TABLE dependants ADD COLUMN phone VARCHAR(20);
        RAISE NOTICE 'Added phone column to dependants table';
    END IF;

    -- Add address column (legacy field)
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'dependants'
                   AND column_name = 'address') THEN
        ALTER TABLE dependants ADD COLUMN address TEXT;
        RAISE NOTICE 'Added address column to dependants table';
    END IF;

    -- Add civic_number column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'dependants'
                   AND column_name = 'civic_number') THEN
        ALTER TABLE dependants ADD COLUMN civic_number VARCHAR(20);
        RAISE NOTICE 'Added civic_number column to dependants table';
    END IF;

    -- Add street column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'dependants'
                   AND column_name = 'street') THEN
        ALTER TABLE dependants ADD COLUMN street VARCHAR(255);
        RAISE NOTICE 'Added street column to dependants table';
    END IF;

    -- Add city column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'dependants'
                   AND column_name = 'city') THEN
        ALTER TABLE dependants ADD COLUMN city VARCHAR(100);
        RAISE NOTICE 'Added city column to dependants table';
    END IF;

    -- Add postal_code column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'dependants'
                   AND column_name = 'postal_code') THEN
        ALTER TABLE dependants ADD COLUMN postal_code VARCHAR(10);
        RAISE NOTICE 'Added postal_code column to dependants table';
    END IF;

    -- Add province column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'dependants'
                   AND column_name = 'province') THEN
        ALTER TABLE dependants ADD COLUMN province VARCHAR(50) DEFAULT 'Quebec';
        RAISE NOTICE 'Added province column to dependants table';
    END IF;

    -- Add country column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'dependants'
                   AND column_name = 'country') THEN
        ALTER TABLE dependants ADD COLUMN country VARCHAR(50) DEFAULT 'Canada';
        RAISE NOTICE 'Added country column to dependants table';
    END IF;

    -- Rename grade_level to school_grade if needed
    IF EXISTS (SELECT 1 FROM information_schema.columns
               WHERE table_name = 'dependants'
               AND column_name = 'grade_level')
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns
                       WHERE table_name = 'dependants'
                       AND column_name = 'school_grade') THEN
        ALTER TABLE dependants RENAME COLUMN grade_level TO school_grade;
        RAISE NOTICE 'Renamed grade_level to school_grade in dependants table';
    ELSIF NOT EXISTS (SELECT 1 FROM information_schema.columns
                      WHERE table_name = 'dependants'
                      AND column_name = 'school_grade') THEN
        ALTER TABLE dependants ADD COLUMN school_grade VARCHAR(50);
        RAISE NOTICE 'Added school_grade column to dependants table';
    END IF;

    -- Add is_active column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'dependants'
                   AND column_name = 'is_active') THEN
        ALTER TABLE dependants ADD COLUMN is_active BOOLEAN DEFAULT TRUE NOT NULL;
        RAISE NOTICE 'Added is_active column to dependants table';
    END IF;

    -- Add preferred_language column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'dependants'
                   AND column_name = 'preferred_language') THEN
        ALTER TABLE dependants ADD COLUMN preferred_language VARCHAR(2) DEFAULT 'en';
        RAISE NOTICE 'Added preferred_language column to dependants table';
    END IF;

    -- Remove school_name column if it exists (not in SQLAlchemy model)
    IF EXISTS (SELECT 1 FROM information_schema.columns
               WHERE table_name = 'dependants'
               AND column_name = 'school_name') THEN
        -- Don't drop it, just note it for manual cleanup later
        RAISE NOTICE 'Note: school_name column exists but is not in SQLAlchemy model - consider manual cleanup';
    END IF;

    -- Remove special_needs column if it exists (not in SQLAlchemy model)
    IF EXISTS (SELECT 1 FROM information_schema.columns
               WHERE table_name = 'dependants'
               AND column_name = 'special_needs') THEN
        -- Don't drop it, just note it for manual cleanup later
        RAISE NOTICE 'Note: special_needs column exists but is not in SQLAlchemy model - consider manual cleanup';
    END IF;
END $$;

-- Step 7: Create indexes for dependants table
CREATE INDEX IF NOT EXISTS idx_dependants_user_id ON dependants(user_id);
CREATE INDEX IF NOT EXISTS idx_dependants_is_active ON dependants(is_active);
CREATE INDEX IF NOT EXISTS idx_dependants_email ON dependants(email);

-- Step 8: Verify dependants table migration
DO $$
DECLARE
    missing_dependant_columns TEXT[] := ARRAY[]::TEXT[];
    col_name TEXT;
BEGIN
    -- Check for required columns in dependants table
    FOR col_name IN SELECT unnest(ARRAY['user_id', 'email', 'password_hash', 'phone', 'address',
                                        'civic_number', 'street', 'city', 'postal_code', 'province',
                                        'country', 'school_grade', 'is_active', 'preferred_language']) LOOP
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                       WHERE table_name = 'dependants'
                       AND column_name = col_name) THEN
            missing_dependant_columns := array_append(missing_dependant_columns, col_name);
        END IF;
    END LOOP;

    IF array_length(missing_dependant_columns, 1) > 0 THEN
        RAISE EXCEPTION 'Dependants table migration incomplete. Missing columns: %', array_to_string(missing_dependant_columns, ', ');
    ELSE
        RAISE NOTICE 'Dependants table migration completed successfully. All required columns are present.';
    END IF;
END $$;

-- Step 9: Display dependants table structure
SELECT
    'dependants' as table_name,
    column_name,
    data_type,
    character_maximum_length,
    is_nullable,
    column_default
FROM information_schema.columns
WHERE table_name = 'dependants'
ORDER BY ordinal_position;
