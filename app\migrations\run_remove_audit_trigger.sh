#!/bin/bash

# Script to remove the appointment audit trigger
# This fixes the duplicate audit log entries issue

echo "Removing appointment audit trigger to fix duplicate entries..."
echo "=============================================="

# Source the database configuration from .env if it exists
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
fi

# Database connection from environment variables
DB_HOST="${DATABASE_HOST:-localhost}"
DB_PORT="${DATABASE_PORT:-5432}"
DB_NAME="${DATABASE_NAME}"
DB_USER="${DATABASE_USER}"
DB_PASSWORD="${DATABASE_PASSWORD}"

# Check if required variables are set
if [ -z "$DB_NAME" ] || [ -z "$DB_USER" ]; then
    echo "Error: Database credentials not found in environment variables."
    echo "Please set DATABASE_NAME and DATABASE_USER in your .env file."
    exit 1
fi

# Set PGPASSWORD environment variable
export PGPASSWORD="$DB_PASSWORD"

# Run the migration
psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f "remove_appointment_audit_trigger.sql"

if [ $? -eq 0 ]; then
    echo "✓ Audit trigger removed successfully"
    echo ""
    echo "The duplicate audit log entries issue has been fixed."
    echo "Future appointment changes will only create one audit entry."
else
    echo "✗ Failed to remove audit trigger"
    exit 1
fi