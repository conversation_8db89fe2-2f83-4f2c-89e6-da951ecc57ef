"""
CLI commands for managing participant counts in group sessions.
"""
import click
from flask.cli import with_appcontext
from app.extensions import db
from app.models.program import GroupSession, GroupSessionParticipant


@click.command('fix-participant-counts')
@with_appcontext
def fix_participant_counts_command():
    """Fix participant counts for all group sessions."""
    click.echo('Fixing participant counts for all group sessions...')
    
    # Get all group sessions
    sessions = GroupSession.query.all()
    click.echo(f'Found {len(sessions)} group sessions')
    
    fixed_count = 0
    
    for session in sessions:
        # Get actual participant count from database
        actual_count = GroupSessionParticipant.query.filter_by(
            group_session_id=session.id
        ).filter(
            GroupSessionParticipant.attendance_status.in_(['registered', 'attended'])
        ).count()
        
        # Get count from database column directly
        db_column_count = object.__getattribute__(session, 'current_participants')
        
        click.echo(f'Session {session.id}: DB column={db_column_count}, Actual={actual_count}')
        
        # If they don't match, update the database column
        if actual_count != db_column_count:
            click.echo(f'  Updating from {db_column_count} to {actual_count}')
            object.__setattr__(session, 'current_participants', actual_count)
            fixed_count += 1
    
    if fixed_count > 0:
        db.session.commit()
        click.echo(f'Fixed {fixed_count} group sessions')
    else:
        click.echo('All participant counts are already correct')


@click.command('check-participant-counts')
@with_appcontext
def check_participant_counts_command():
    """Check participant counts for all group sessions."""
    click.echo('Checking participant counts for all group sessions...')
    
    # Get all group sessions
    sessions = GroupSession.query.all()
    click.echo(f'Found {len(sessions)} group sessions')
    
    for session in sessions:
        # Get actual participant count from database
        actual_count = GroupSessionParticipant.query.filter_by(
            group_session_id=session.id
        ).filter(
            GroupSessionParticipant.attendance_status.in_(['registered', 'attended'])
        ).count()
        
        # Get count from model property
        try:
            model_count = session.current_participants_count
        except Exception as e:
            model_count = f"ERROR: {e}"
        
        # Get count from database column directly
        db_column_count = object.__getattribute__(session, 'current_participants')
        
        click.echo(f'Session {session.id}:')
        click.echo(f'  - Actual participants in DB: {actual_count}')
        click.echo(f'  - Model property count: {model_count}')
        click.echo(f'  - DB column value: {db_column_count}')
        
        # Show participants
        participants = GroupSessionParticipant.query.filter_by(
            group_session_id=session.id
        ).all()
        
        if participants:
            click.echo(f'  - Participants:')
            for p in participants:
                click.echo(f'    * ID {p.id}: enrollment_id={p.enrollment_id}, status={p.attendance_status}')
        else:
            click.echo(f'  - No participants found')
        
        click.echo()
