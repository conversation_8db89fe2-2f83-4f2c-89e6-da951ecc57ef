-- Quick fix for User model id references
-- This adds an 'id' property to the users table that mirrors user_id
-- This is a temporary fix to maintain compatibility with existing code

-- Add an 'id' column that mirrors user_id for backward compatibility
ALTER TABLE users ADD COLUMN IF NOT EXISTS id INTEGER;

-- Update the id column to match user_id for all existing records
UPDATE users SET id = user_id WHERE id IS NULL OR id != user_id;

-- Create a trigger to keep id and user_id in sync
CREATE OR REPLACE FUNCTION sync_user_id()
RETURNS TRIGGER AS $$
BEGIN
    -- When user_id changes, update id to match
    IF TG_OP = 'INSERT' OR (TG_OP = 'UPDATE' AND NEW.user_id != OLD.user_id) THEN
        NEW.id = NEW.user_id;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Drop the trigger if it exists and recreate it
DROP TRIGGER IF EXISTS sync_user_id_trigger ON users;
CREATE TRIGGER sync_user_id_trigger
    BEFORE INSERT OR UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION sync_user_id();

-- Verify the fix
SELECT 'USER ID SYNC VERIFICATION:' as info;
SELECT user_id, id, email, role 
FROM users 
WHERE user_id != id OR id IS NULL
LIMIT 5;

SELECT 'USER ID FIX COMPLETED!' as status,
       'The id column now mirrors user_id for backward compatibility.' as message;
