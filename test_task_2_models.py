#!/usr/bin/env python3
"""
Test script for Task 2: Payment and Availability Models
Tests that all models can be imported and have correct primary key naming.
"""

import sys
import os

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_model_imports():
    """Test that all models can be imported without errors."""
    try:
        from models.tutor_payment import TutorPayment
        from models.tutor_availability import TutorAvailability
        from models.time_off import TimeOff
        print("✓ All models imported successfully")
        return True
    except Exception as e:
        print(f"✗ Model import failed: {e}")
        return False

def test_primary_key_names():
    """Test that models use correct descriptive primary key names."""
    try:
        from models.tutor_payment import TutorPayment
        from models.tutor_availability import TutorAvailability
        from models.time_off import TimeOff
        
        # Check TutorPayment primary key
        payment_pk = TutorPayment.__table__.primary_key.columns.keys()[0]
        assert payment_pk == 'payment_id', f"Expected 'payment_id', got '{payment_pk}'"
        print("✓ TutorPayment uses 'payment_id' as primary key")
        
        # Check TutorAvailability primary key
        availability_pk = TutorAvailability.__table__.primary_key.columns.keys()[0]
        assert availability_pk == 'availability_id', f"Expected 'availability_id', got '{availability_pk}'"
        print("✓ TutorAvailability uses 'availability_id' as primary key")
        
        # Check TimeOff primary key
        timeoff_pk = TimeOff.__table__.primary_key.columns.keys()[0]
        assert timeoff_pk == 'time_off_id', f"Expected 'time_off_id', got '{timeoff_pk}'"
        print("✓ TimeOff uses 'time_off_id' as primary key")
        
        return True
    except Exception as e:
        print(f"✗ Primary key test failed: {e}")
        return False

def test_foreign_key_references():
    """Test that foreign key references point to correct primary keys."""
    try:
        from models.tutor_payment import TutorPayment
        from models.tutor_availability import TutorAvailability
        from models.time_off import TimeOff
        
        # Check TutorPayment foreign keys
        payment_fks = {col.name: str(col.foreign_keys) for col in TutorPayment.__table__.columns if col.foreign_keys}
        print(f"✓ TutorPayment foreign keys: {payment_fks}")
        
        # Check TutorAvailability foreign keys
        availability_fks = {col.name: str(col.foreign_keys) for col in TutorAvailability.__table__.columns if col.foreign_keys}
        print(f"✓ TutorAvailability foreign keys: {availability_fks}")
        
        # Check TimeOff foreign keys
        timeoff_fks = {col.name: str(col.foreign_keys) for col in TimeOff.__table__.columns if col.foreign_keys}
        print(f"✓ TimeOff foreign keys: {timeoff_fks}")
        
        return True
    except Exception as e:
        print(f"✗ Foreign key test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("Testing Task 2: Payment and Availability Models")
    print("=" * 50)
    
    tests = [
        test_model_imports,
        test_primary_key_names,
        test_foreign_key_references
    ]
    
    results = []
    for test in tests:
        print(f"\nRunning {test.__name__}...")
        results.append(test())
    
    print("\n" + "=" * 50)
    if all(results):
        print("✓ All tests passed! Task 2 models are correctly configured.")
        return True
    else:
        print("✗ Some tests failed. Please check the output above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)