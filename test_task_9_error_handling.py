#!/usr/bin/env python3
"""
Test script for Task 9: Comprehensive Error Handling and User Feedback
Tests all aspects of error handling in the audit system.
"""

import sys
import os
import json
import time
import requests
from datetime import datetime

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_health_check_endpoint():
    """Test the health check endpoint for network connectivity verification."""
    print("🔍 Testing health check endpoint...")
    
    try:
        # Test GET request
        response = requests.get('http://localhost:5000/api/health-check', timeout=5)
        print(f"   GET /api/health-check: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Status: {data.get('status')}")
            print(f"   Database: {data.get('database')}")
            print("   ✅ Health check endpoint working")
        else:
            print(f"   ❌ Health check failed with status {response.status_code}")
            
        # Test HEAD request (used by JavaScript)
        head_response = requests.head('http://localhost:5000/api/health-check', timeout=5)
        print(f"   HEAD /api/health-check: {head_response.status_code}")
        
        return response.status_code == 200
        
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Health check endpoint error: {e}")
        return False

def test_audit_api_error_handling():
    """Test audit API error handling for various scenarios."""
    print("🔍 Testing audit API error handling...")
    
    test_cases = [
        {
            'name': 'Non-existent appointment',
            'url': 'http://localhost:5000/api/appointment/99999/audit',
            'expected_status': 404
        },
        {
            'name': 'Invalid appointment ID',
            'url': 'http://localhost:5000/api/appointment/invalid/audit',
            'expected_status': 404
        },
        {
            'name': 'Invalid pagination parameters',
            'url': 'http://localhost:5000/api/appointment/1/audit?page=-1&per_page=1000',
            'expected_status': [200, 400]  # Should handle gracefully
        }
    ]
    
    success_count = 0
    
    for test_case in test_cases:
        try:
            print(f"   Testing: {test_case['name']}")
            response = requests.get(test_case['url'], timeout=10)
            
            expected = test_case['expected_status']
            if isinstance(expected, list):
                success = response.status_code in expected
            else:
                success = response.status_code == expected
                
            if success:
                print(f"   ✅ {test_case['name']}: {response.status_code}")
                success_count += 1
                
                # Check error response structure
                if response.status_code >= 400:
                    try:
                        error_data = response.json()
                        if 'error' in error_data and 'message' in error_data:
                            print(f"      Error structure: ✅")
                        else:
                            print(f"      Error structure: ❌ Missing required fields")
                    except:
                        print(f"      Error structure: ❌ Invalid JSON")
            else:
                print(f"   ❌ {test_case['name']}: Expected {expected}, got {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"   ❌ {test_case['name']}: Request failed - {e}")
    
    print(f"   Passed {success_count}/{len(test_cases)} error handling tests")
    return success_count == len(test_cases)

def test_audit_service_error_handling():
    """Test audit service error handling and fallback mechanisms."""
    print("🔍 Testing audit service error handling...")
    
    try:
        from app.services.audit_service import AuditService
        from app.models.appointment_audit import AppointmentAudit
        
        # Test with invalid appointment ID
        print("   Testing invalid appointment ID...")
        result = AuditService.get_appointment_audit_history(-1, 1, 20, False)
        
        if isinstance(result, dict) and 'error_info' in result:
            print("   ✅ Invalid appointment ID handled gracefully")
        else:
            print("   ❌ Invalid appointment ID not handled properly")
            
        # Test cache functionality
        print("   Testing cache functionality...")
        cache_stats = AuditService.get_cache_stats()
        
        if isinstance(cache_stats, dict):
            print("   ✅ Cache stats accessible")
            print(f"      Cache size: {cache_stats.get('cache_size', 0)}")
        else:
            print("   ❌ Cache stats not accessible")
            
        # Test cache clearing
        print("   Testing cache clearing...")
        AuditService.clear_cache()
        print("   ✅ Cache clearing completed")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ Cannot import audit service: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Audit service error: {e}")
        return False

def test_javascript_error_handling():
    """Test JavaScript error handling by checking the file structure."""
    print("🔍 Testing JavaScript error handling implementation...")
    
    js_file_path = 'app/static/js/audit_trail_modal.js'
    
    if not os.path.exists(js_file_path):
        print(f"   ❌ JavaScript file not found: {js_file_path}")
        return False
    
    try:
        with open(js_file_path, 'r', encoding='utf-8') as f:
            js_content = f.read()
        
        # Check for key error handling methods
        required_methods = [
            'showErrorState',
            'hideErrorState',
            'retryLoadAuditData',
            'logError',
            'showUserNotification',
            'executeWithErrorBoundary',
            'handleRecoverableError',
            'checkNetworkConnectivity',
            'showNetworkStatus',
            'hideNetworkStatus'
        ]
        
        found_methods = []
        missing_methods = []
        
        for method in required_methods:
            if method in js_content:
                found_methods.append(method)
            else:
                missing_methods.append(method)
        
        print(f"   Found {len(found_methods)}/{len(required_methods)} error handling methods")
        
        if found_methods:
            print("   ✅ Found methods:", ', '.join(found_methods))
        
        if missing_methods:
            print("   ❌ Missing methods:", ', '.join(missing_methods))
        
        # Check for error handling patterns
        error_patterns = [
            'try {',
            'catch (',
            'throw new Error',
            'console.error',
            'console.warn'
        ]
        
        pattern_counts = {}
        for pattern in error_patterns:
            count = js_content.count(pattern)
            pattern_counts[pattern] = count
            
        print("   Error handling patterns:")
        for pattern, count in pattern_counts.items():
            print(f"      {pattern}: {count} occurrences")
        
        # Check for specific error handling features
        features = [
            ('Network connectivity check', 'checkNetworkConnectivity'),
            ('Retry mechanism', 'retryLoadAuditData'),
            ('User notifications', 'showUserNotification'),
            ('Error logging', 'logError'),
            ('Fallback displays', 'showPartialDataFallback'),
            ('Corrupted data handling', 'showCorruptedDataFallback')
        ]
        
        feature_results = []
        for feature_name, feature_code in features:
            if feature_code in js_content:
                feature_results.append(f"✅ {feature_name}")
            else:
                feature_results.append(f"❌ {feature_name}")
        
        print("   Error handling features:")
        for result in feature_results:
            print(f"      {result}")
        
        success_rate = len(found_methods) / len(required_methods)
        return success_rate >= 0.8  # 80% of methods should be present
        
    except Exception as e:
        print(f"   ❌ Error reading JavaScript file: {e}")
        return False

def test_modal_template_error_elements():
    """Test that the modal template has all required error handling elements."""
    print("🔍 Testing modal template error handling elements...")
    
    template_path = 'app/templates/components/audit_trail_modal.html'
    
    if not os.path.exists(template_path):
        print(f"   ❌ Template file not found: {template_path}")
        return False
    
    try:
        with open(template_path, 'r', encoding='utf-8') as f:
            template_content = f.read()
        
        # Check for required error handling elements
        required_elements = [
            'auditErrorState',
            'networkStatusIndicator',
            'auditLoadingState',
            'auditEmptyState',
            'error-title',
            'error-message',
            'error-code',
            'retry-button',
            'error-details-toggle'
        ]
        
        found_elements = []
        missing_elements = []
        
        for element in required_elements:
            if element in template_content:
                found_elements.append(element)
            else:
                missing_elements.append(element)
        
        print(f"   Found {len(found_elements)}/{len(required_elements)} error handling elements")
        
        if found_elements:
            print("   ✅ Found elements:", ', '.join(found_elements))
        
        if missing_elements:
            print("   ❌ Missing elements:", ', '.join(missing_elements))
        
        # Check for Bootstrap alert classes
        alert_classes = ['alert-danger', 'alert-warning', 'alert-info']
        for alert_class in alert_classes:
            if alert_class in template_content:
                print(f"   ✅ {alert_class} styling present")
            else:
                print(f"   ❌ {alert_class} styling missing")
        
        success_rate = len(found_elements) / len(required_elements)
        return success_rate >= 0.9  # 90% of elements should be present
        
    except Exception as e:
        print(f"   ❌ Error reading template file: {e}")
        return False

def generate_error_handling_report():
    """Generate a comprehensive report of error handling implementation."""
    print("📊 Generating Error Handling Implementation Report...")
    
    report = {
        'timestamp': datetime.now().isoformat(),
        'test_results': {},
        'summary': {},
        'recommendations': []
    }
    
    # Run all tests
    tests = [
        ('Health Check Endpoint', test_health_check_endpoint),
        ('Audit API Error Handling', test_audit_api_error_handling),
        ('Audit Service Error Handling', test_audit_service_error_handling),
        ('JavaScript Error Handling', test_javascript_error_handling),
        ('Modal Template Error Elements', test_modal_template_error_elements)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_function in tests:
        print(f"\n{'='*60}")
        print(f"Running: {test_name}")
        print('='*60)
        
        try:
            result = test_function()
            report['test_results'][test_name] = {
                'passed': result,
                'timestamp': datetime.now().isoformat()
            }
            
            if result:
                passed_tests += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
            report['test_results'][test_name] = {
                'passed': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    # Generate summary
    success_rate = (passed_tests / total_tests) * 100
    report['summary'] = {
        'total_tests': total_tests,
        'passed_tests': passed_tests,
        'failed_tests': total_tests - passed_tests,
        'success_rate': success_rate
    }
    
    # Generate recommendations
    if success_rate < 100:
        report['recommendations'].append("Review failed tests and implement missing error handling features")
    
    if success_rate >= 80:
        report['recommendations'].append("Error handling implementation is mostly complete")
    else:
        report['recommendations'].append("Significant error handling improvements needed")
    
    # Save report
    report_filename = f'audit_error_handling_test_results.json'
    with open(report_filename, 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n{'='*60}")
    print("📊 ERROR HANDLING TEST SUMMARY")
    print('='*60)
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Success Rate: {success_rate:.1f}%")
    print(f"Report saved to: {report_filename}")
    
    if success_rate >= 80:
        print("🎉 Error handling implementation is satisfactory!")
    else:
        print("⚠️  Error handling implementation needs improvement.")
    
    return success_rate >= 80

if __name__ == '__main__':
    print("🚀 Starting Task 9 Error Handling Tests")
    print("="*60)
    
    success = generate_error_handling_report()
    
    if success:
        print("\n✅ Task 9 implementation appears to be working correctly!")
        sys.exit(0)
    else:
        print("\n❌ Task 9 implementation has issues that need to be addressed.")
        sys.exit(1)