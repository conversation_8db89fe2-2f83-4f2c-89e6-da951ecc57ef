{% extends "base.html" %}

{% block title %}{{ t('manager.tecfee.group_sessions.title_full') }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="h3 mb-3">
                <i class="fas fa-users-cog text-primary"></i>
                {{ t('manager.tecfee.group_sessions.title') }}
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('manager.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('manager.tecfee_dashboard') }}">TECFÉE</a></li>
                    <li class="breadcrumb-item active">Group Sessions</li>
                </ol>
            </nav>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('manager.create_tecfee_group_session') }}" class="btn btn-primary">
                <i class="fas fa-calendar-plus"></i> {{ t('manager.tecfee.group_sessions.create_new_session', 'Create New Session') }}
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="status" class="form-label">Status</label>
                            <select name="status" id="status" class="form-select">
                                <option value="all" {% if current_status == 'all' %}selected{% endif %}>All Statuses</option>
                                <option value="scheduled" {% if current_status == 'scheduled' %}selected{% endif %}>Scheduled</option>
                                <option value="confirmed" {% if current_status == 'confirmed' %}selected{% endif %}>Confirmed</option>
                                <option value="completed" {% if current_status == 'completed' %}selected{% endif %}>Completed</option>
                                <option value="cancelled" {% if current_status == 'cancelled' %}selected{% endif %}>Cancelled</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="tutor_id" class="form-label">Tutor</label>
                            <select name="tutor_id" id="tutor_id" class="form-select">
                                <option value="">All Tutors</option>
                                {% for tutor in tutors %}
                                <option value="{{ tutor.id }}" {% if current_tutor_id == tutor.id|string %}selected{% endif %}>
                                    {{ tutor.first_name }} {{ tutor.last_name }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-outline-primary me-2">
                                <i class="fas fa-filter"></i> Filter
                            </button>
                            <a href="{{ url_for('manager.tecfee_group_sessions') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Sessions Table -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        Group Sessions
                        <span class="badge bg-secondary">{{ sessions|length }}</span>
                    </h5>
                    <div class="btn-group btn-group-sm">
                        <button type="button" class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-download"></i> Export
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-file-csv"></i> Export CSV</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-calendar"></i> Export Calendar</a></li>
                        </ul>
                    </div>
                </div>
                <div class="card-body">
                    {% if sessions %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Module</th>
                                    <th>Date & Time</th>
                                    <th>Tutor</th>
                                    <th>Participants</th>
                                    <th>Status</th>
                                    <th>Payment</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for session in sessions %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="badge bg-primary me-2">{{ session.module.module_order }}</div>
                                            <div>
                                                <strong>{{ session.module.name }}</strong><br>
                                                <small class="text-muted">{{ session.module.duration_minutes }} minutes</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <strong>{{ session.session_date.strftime('%Y-%m-%d') }}</strong><br>
                                        <small class="text-muted">{{ session.start_time.strftime('%H:%M') }} - {{ session.end_time.strftime('%H:%M') }}</small>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-info text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                {{ session.tutor.first_name[0] }}{{ session.tutor.last_name[0] }}
                                            </div>
                                            <div>
                                                {{ session.tutor.first_name }} {{ session.tutor.last_name }}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <span class="badge bg-info me-2">{{ session.current_participants_count }}/{{ session.max_participants }}</span>
                                            <div class="progress flex-grow-1" style="height: 8px;">
                                                <div class="progress-bar" role="progressbar"
                                                     style="width: {{ (session.current_participants_count / session.max_participants * 100) if session.max_participants > 0 else 0 }}%">
                                                </div>
                                            </div>
                                        </div>
                                        {% if session.current_participants_count < 4 %}
                                        <small class="text-warning">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            Need {{ 4 - session.current_participants_count }} more
                                        </small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if session.status == 'scheduled' %}
                                        <span class="badge bg-warning">Scheduled</span>
                                        {% elif session.status == 'confirmed' %}
                                        <span class="badge bg-success">Confirmed</span>
                                        {% elif session.status == 'completed' %}
                                        <span class="badge bg-primary">Completed</span>
                                        {% elif session.status == 'cancelled' %}
                                        <span class="badge bg-danger">Cancelled</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <strong>${{ "%.2f"|format(session.total_tutor_payment) }}</strong><br>
                                        <small class="text-muted">${{ "%.2f"|format(session.tutor_payment_rate) }}/student</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('manager.view_tecfee_group_session', id=session.id) }}"
                                               class="btn btn-outline-primary" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if session.status == 'scheduled' and session.current_participants >= 4 %}
                                            <button type="button" class="btn btn-outline-success"
                                                    onclick="confirmSession({{ session.id }})" title="Confirm Session">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            {% endif %}
                                            {% if session.status == 'confirmed' %}
                                            <form method="POST" action="{{ url_for('manager.complete_tecfee_group_session', id=session.id) }}"
                                                  style="display: inline;" onsubmit="return confirm('Mark this session as completed?')">
                                                <button type="submit" class="btn btn-outline-primary" title="Complete Session">
                                                    <i class="fas fa-flag-checkered"></i>
                                                </button>
                                            </form>
                                            {% endif %}
                                            {% if session.status in ['scheduled', 'confirmed'] %}
                                            <button type="button" class="btn btn-outline-danger"
                                                    onclick="cancelSession({{ session.id }})" title="Cancel Session">
                                                <i class="fas fa-times"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No group sessions found</h5>
                        <p class="text-muted">
                            {% if current_status != 'all' or current_tutor_id %}
                            No sessions found matching your filters.
                            {% else %}
                            No group sessions have been created yet.
                            {% endif %}
                        </p>
                        <a href="{{ url_for('manager.create_tecfee_group_session') }}" class="btn btn-primary">
                            <i class="fas fa-calendar-plus"></i> Create First Session
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    {% if sessions %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">Session Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        {% set scheduled_count = sessions | selectattr('status', 'equalto', 'scheduled') | list | length %}
                        {% set confirmed_count = sessions | selectattr('status', 'equalto', 'confirmed') | list | length %}
                        {% set completed_count = sessions | selectattr('status', 'equalto', 'completed') | list | length %}
                        {% set cancelled_count = sessions | selectattr('status', 'equalto', 'cancelled') | list | length %}

                        <div class="col-md-3">
                            <div class="border rounded p-3">
                                <h4 class="text-warning">{{ scheduled_count }}</h4>
                                <small class="text-muted">Scheduled</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border rounded p-3">
                                <h4 class="text-success">{{ confirmed_count }}</h4>
                                <small class="text-muted">Confirmed</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border rounded p-3">
                                <h4 class="text-primary">{{ completed_count }}</h4>
                                <small class="text-muted">Completed</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border rounded p-3">
                                <h4 class="text-danger">{{ cancelled_count }}</h4>
                                <small class="text-muted">Cancelled</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<style>
.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 12px;
    font-weight: bold;
}
</style>

<script>
function confirmSession(sessionId) {
    if (confirm('Confirm this group session? This will notify all participants.')) {
        fetch(`/manager/tecfee/group-sessions/${sessionId}/confirm`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error confirming session: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while confirming the session.');
        });
    }
}

function cancelSession(sessionId) {
    if (confirm('Cancel this group session? This action cannot be undone and will notify all participants.')) {
        fetch(`/manager/tecfee/group-sessions/${sessionId}/cancel`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error cancelling session: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while cancelling the session.');
        });
    }
}
</script>
{% endblock %}
