-- Migration: Remove legacy recurring fields from appointments table
-- This migration removes the embedded recurring fields that are now handled by appointment_recurring_schedules

-- Remove legacy recurring fields from appointments table
ALTER TABLE appointments DROP COLUMN IF EXISTS is_recurring;
ALTER TABLE appointments DROP COLUMN IF EXISTS recurrence_pattern;
ALTER TABLE appointments DROP COLUMN IF EXISTS recurrence_end_date;

-- Ensure the recurring_schedule_id foreign key constraint exists
-- (This should already exist from the schema, but we'll make sure)
DO $$
BEGIN
    -- Check if the foreign key constraint exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_appointments_recurring_schedule'
        AND table_name = 'appointments'
    ) THEN
        -- Add the foreign key constraint if it doesn't exist
        ALTER TABLE appointments ADD CONSTRAINT fk_appointments_recurring_schedule 
            FOREIGN KEY (recurring_schedule_id) REFERENCES appointment_recurring_schedules(schedule_id) ON DELETE SET NULL;
    END IF;
END $$;

-- Ensure the index exists for the foreign key
CREATE INDEX IF NOT EXISTS idx_appointments_recurring_schedule_id ON appointments(recurring_schedule_id);