-- Fix Dependants Table Date of Birth Constraint
-- This script fixes the NOT NULL constraint on date_of_birth to allow NULL values

-- ========================================
-- DEPENDANTS TABLE DATE_OF_BIRTH FIX
-- ========================================

-- Check current constraint and modify if needed
DO $$ 
BEGIN
    -- Check if date_of_birth column exists and has NOT NULL constraint
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'dependants' 
               AND column_name = 'date_of_birth' 
               AND is_nullable = 'NO') THEN
        
        -- Remove NOT NULL constraint from date_of_birth
        ALTER TABLE dependants ALTER COLUMN date_of_birth DROP NOT NULL;
        
        RAISE NOTICE 'Removed NOT NULL constraint from dependants.date_of_birth column';
    ELSE
        RAISE NOTICE 'dependants.date_of_birth column already allows NULL values';
    END IF;
    
    -- Also ensure other optional fields allow NULL
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'dependants' 
               AND column_name = 'email' 
               AND is_nullable = 'NO') THEN
        ALTER TABLE dependants ALTER COLUMN email DROP NOT NULL;
        RAISE NOTICE 'Removed NOT NULL constraint from dependants.email column';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'dependants' 
               AND column_name = 'phone' 
               AND is_nullable = 'NO') THEN
        ALTER TABLE dependants ALTER COLUMN phone DROP NOT NULL;
        RAISE NOTICE 'Removed NOT NULL constraint from dependants.phone column';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'dependants' 
               AND column_name = 'school_grade' 
               AND is_nullable = 'NO') THEN
        ALTER TABLE dependants ALTER COLUMN school_grade DROP NOT NULL;
        RAISE NOTICE 'Removed NOT NULL constraint from dependants.school_grade column';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'dependants' 
               AND column_name = 'notes' 
               AND is_nullable = 'NO') THEN
        ALTER TABLE dependants ALTER COLUMN notes DROP NOT NULL;
        RAISE NOTICE 'Removed NOT NULL constraint from dependants.notes column';
    END IF;
    
    -- Ensure address fields allow NULL
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'dependants' 
               AND column_name = 'civic_number' 
               AND is_nullable = 'NO') THEN
        ALTER TABLE dependants ALTER COLUMN civic_number DROP NOT NULL;
        RAISE NOTICE 'Removed NOT NULL constraint from dependants.civic_number column';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'dependants' 
               AND column_name = 'street' 
               AND is_nullable = 'NO') THEN
        ALTER TABLE dependants ALTER COLUMN street DROP NOT NULL;
        RAISE NOTICE 'Removed NOT NULL constraint from dependants.street column';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'dependants' 
               AND column_name = 'city' 
               AND is_nullable = 'NO') THEN
        ALTER TABLE dependants ALTER COLUMN city DROP NOT NULL;
        RAISE NOTICE 'Removed NOT NULL constraint from dependants.city column';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'dependants' 
               AND column_name = 'postal_code' 
               AND is_nullable = 'NO') THEN
        ALTER TABLE dependants ALTER COLUMN postal_code DROP NOT NULL;
        RAISE NOTICE 'Removed NOT NULL constraint from dependants.postal_code column';
    END IF;
    
END $$;

-- ========================================
-- VERIFICATION QUERIES
-- ========================================

-- Check dependants table structure
SELECT 'DEPENDANTS TABLE COLUMNS:' as info;
SELECT 
    column_name,
    data_type,
    character_maximum_length,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'dependants' 
AND column_name IN ('date_of_birth', 'email', 'phone', 'school_grade', 'notes',
                    'civic_number', 'street', 'city', 'postal_code')
ORDER BY column_name;

-- Show current dependants data (if any)
SELECT 'CURRENT DEPENDANTS:' as info;
SELECT dependant_id, first_name, last_name, date_of_birth, school_grade, is_active
FROM dependants
ORDER BY dependant_id
LIMIT 10;

-- Final success message
SELECT 'DEPENDANTS DATE_OF_BIRTH FIX COMPLETED!' as status,
       'The dependants table now allows NULL values for optional fields.' as message,
       'You can now create dependants without requiring a date of birth.' as next_step;
