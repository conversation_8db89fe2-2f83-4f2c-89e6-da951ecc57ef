<!-- app/templates/client/payment_success.html -->
{% extends "base.html" %}

{% block title %}{{ t('payment.success') }} - TutorAide Inc.{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-body text-center py-5">
                <div class="mb-4">
                    <i class="fas fa-check-circle text-success" style="font-size: 5rem;"></i>
                </div>
                <h2 class="mb-3">{{ t('payment.success_title') }}</h2>
                <p class="lead mb-4">{{ t('payment.success_message') }}</p>
                <p class="mb-4">{{ t('payment.invoice_number') }}: <strong>#{{ invoice.id }}</strong></p>
                <p class="mb-4">{{ t('payment.amount') }}: <strong>${{ "%.2f"|format(invoice.total_amount) }}</strong></p>
                <p class="mb-4">{{ t('payment.date') }}: <strong>{{ invoice.paid_date.strftime('%Y-%m-%d %H:%M') }}</strong></p>
                
                {% if invoice.stripe_payment_intent_id %}
                    <p class="text-muted small mb-4">{{ t('payment.transaction_id') }}: {{ invoice.stripe_payment_intent_id }}</p>
                {% endif %}
                
                <div class="mt-4">
                    <a href="{{ url_for('client.view_invoice', id=invoice.id) }}" class="btn btn-primary">
                        <i class="fas fa-file-invoice"></i> {{ t('payment.view_invoice') }}
                    </a>
                    <a href="{{ url_for('client.dashboard') }}" class="btn btn-outline-secondary ms-2">
                        <i class="fas fa-home"></i> {{ t('payment.back_to_dashboard') }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
