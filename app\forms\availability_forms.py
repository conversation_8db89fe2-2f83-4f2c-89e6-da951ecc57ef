# app/forms/availability_forms.py
from flask_wtf import FlaskForm
from wtforms import StringField, SelectField, TimeField, BooleanField, SubmitField, HiddenField
from wtforms.validators import DataRequired, Optional, ValidationError

class TutorAvailabilityForm(FlaskForm):
    """Form for adding or editing a tutor's availability slot."""
    id = HiddenField('ID')
    day_of_week = SelectField('Day of Week', choices=[
        (0, 'Monday'),
        (1, 'Tuesday'),
        (2, 'Wednesday'),
        (3, 'Thursday'),
        (4, 'Friday'),
        (5, 'Saturday'),
        (6, 'Sunday')
    ], coerce=int, validators=[DataRequired()])

    # Create time choices in 30-minute increments from 8:00 AM to 9:00 PM
    time_choices = []
    for hour in range(8, 22):  # 8 AM to 9 PM
        time_choices.append((f"{hour:02d}:00", f"{hour % 12 or 12}:00 {'AM' if hour < 12 else 'PM'}"))
        if hour < 21:  # Don't add 9:30 PM
            time_choices.append((f"{hour:02d}:30", f"{hour % 12 or 12}:30 {'AM' if hour < 12 else 'PM'}"))

    start_time = SelectField('Start Time', choices=time_choices, validators=[DataRequired()])
    end_time = SelectField('End Time', choices=time_choices, validators=[DataRequired()])
    all_day = BooleanField('All Day (8:00 AM - 9:00 PM)')
    is_active = BooleanField('Active', default=True)
    submit = SubmitField('Add Availability')

    def validate_end_time(self, field):
        """Validate that end time is after start time."""
        if self.start_time.data and field.data and not self.all_day.data:
            if field.data <= self.start_time.data:
                raise ValidationError('End time must be after start time.')

class TutorServiceRateForm(FlaskForm):
    """Form for adding or editing a tutor's service rate."""
    id = HiddenField('ID')
    service_id = SelectField('Service', coerce=int, validators=[DataRequired()])
    tutor_rate = StringField('Tutor Rate ($/hr)', validators=[DataRequired()])
    client_rate = StringField('Client Rate ($/hr)', validators=[DataRequired()])
    transport_fee = StringField('Transport Fee ($)', validators=[Optional()], default='0.00')
    transport_fee_description = StringField('Transport Fee Description', validators=[Optional()])
    is_active = BooleanField('Active', default=True)
    submit = SubmitField('Save Rate')
