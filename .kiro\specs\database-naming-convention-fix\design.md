# Database Naming Convention Fix Design

## Overview

This design document outlines the systematic approach to standardize database primary key naming conventions throughout the appointment management system. The solution focuses on converting all generic `id` primary keys to descriptive names following the `{table_name_singular}_id` pattern, updating all foreign key references, and ensuring SQLAlchemy model consistency.

## Architecture

### Naming Convention Strategy
- **Primary Keys**: Use descriptive format `{table_name_singular}_id` (e.g., `service_id`, `notification_id`)
- **Foreign Keys**: Match the referenced table's primary key name exactly
- **Consistency**: All models and database schema must use identical column names
- **Self-Documentation**: Column names should clearly indicate their purpose and relationships

### Migration Approach
- **Development Environment**: Drop and recreate tables with correct schema (as per development guidelines)
- **Schema First**: Update the main `schema.sql` file as the single source of truth
- **Model Updates**: Update all SQLAlchemy models to match the new schema
- **Validation**: Comprehensive testing to ensure all relationships work correctly

## Components and Interfaces

### Database Schema Updates
The following tables need primary key renaming:

#### Tables with Generic `id` Primary Keys:
1. **services** table: `id` → `service_id`
2. **tutor_services** table: `id` → `tutor_service_id` 
3. **tutor_payments** table: `id` → `payment_id`
4. **tutor_availabilities** table: `id` → `availability_id`
5. **time_off_requests** table: `id` → `time_off_id`
6. **programs** table: `id` → `program_id`
7. **program_modules** table: `id` → `module_id`
8. **enrollments** table: `id` → `enrollment_id`
9. **module_progress** table: `id` → `progress_id`
10. **module_sessions** table: `id` → `session_id`
11. **program_pricing** table: `id` → `pricing_id`
12. **group_sessions** table: `id` → `group_session_id`
13. **group_session_participants** table: `id` → `participant_id`
14. **notifications** table: `id` → `notification_id`
15. **invoice_generation_settings** table: `id` → `setting_id`
16. **invoices** table: `id` → `invoice_id`
17. **invoice_items** table: `id` → `item_id`
18. **client_consents** table: `id` → `consent_id`
19. **appointment_audit** table: `id` → `audit_id`

### Foreign Key Reference Updates

#### Critical Foreign Key Mismatches to Fix:
1. **tutor_service_rates** table:
   - `tutor_id` references `tutors.id` → should reference `tutors.tutor_id`
   - `service_id` references `services.id` → should reference `services.service_id`

2. **tutor_payments** table:
   - `tutor_id` references `tutors.id` → should reference `tutors.tutor_id`
   - `appointment_id` references `appointments.id` → should reference `appointments.appointment_id`

3. **tutor_availabilities** table:
   - `tutor_id` references `tutors.id` → should reference `tutors.tutor_id`

4. **time_off_requests** table:
   - `tutor_id` references `tutors.id` → should reference `tutors.tutor_id`

5. **service** and **tutor_services** tables:
   - `tutor_id` references `tutors.id` → should reference `tutors.tutor_id`
   - `service_id` references `services.id` → should reference `services.service_id`

6. **notifications** table:
   - `user_id` references `users.id` → should reference `users.user_id`

7. **invoice** and related tables:
   - `client_id` references `clients.id` → should reference `clients.client_id`
   - `appointment_id` references `appointments.id` → should reference `appointments.appointment_id`

8. **program** related tables:
   - All foreign key references need to be updated to match new primary key names

9. **client_consents** table:
   - `client_id` references `clients.id` → should reference `clients.client_id`

## Data Models

### Updated Model Structure Examples

#### Service Model
```python
class Service(db.Model):
    __tablename__ = 'services'
    
    service_id = db.Column(db.Integer, primary_key=True)  # Changed from 'id'
    name = db.Column(db.String(100), nullable=False)
    # ... other fields
```

#### TutorService Model
```python
class TutorService(db.Model):
    __tablename__ = 'tutor_services'
    
    tutor_service_id = db.Column(db.Integer, primary_key=True)  # Changed from 'id'
    tutor_id = db.Column(db.Integer, db.ForeignKey('tutors.tutor_id'), nullable=False)  # Fixed reference
    service_id = db.Column(db.Integer, db.ForeignKey('services.service_id'), nullable=False)  # Fixed reference
    # ... other fields
```

#### Notification Model
```python
class Notification(db.Model):
    __tablename__ = 'notifications'
    
    notification_id = db.Column(db.Integer, primary_key=True)  # Changed from 'id'
    user_id = db.Column(db.Integer, db.ForeignKey('users.user_id'), nullable=False)  # Fixed reference
    # ... other fields
```

## Error Handling

### SQLAlchemy Configuration Errors
- **Model Validation**: Ensure all foreign key references point to existing columns
- **Relationship Mapping**: Verify all SQLAlchemy relationships use correct column names
- **Circular Dependencies**: Handle model import order to prevent configuration issues
- **Migration Safety**: Validate schema changes before applying

### Data Integrity Protection
- **Foreign Key Constraints**: Ensure all foreign key relationships remain valid
- **Referential Integrity**: Verify no orphaned records are created during migration
- **Transaction Safety**: Use database transactions for atomic schema changes
- **Rollback Capability**: Maintain ability to revert changes if issues occur

## Testing Strategy

### Model Configuration Testing
- **Import Tests**: Verify all models can be imported without errors
- **Relationship Tests**: Test all SQLAlchemy relationships work correctly
- **Query Tests**: Validate database queries use correct column names
- **Foreign Key Tests**: Ensure all foreign key constraints are properly defined

### Database Schema Testing
- **Schema Validation**: Verify database schema matches model definitions
- **Constraint Tests**: Test all foreign key constraints work correctly
- **Index Tests**: Ensure indexes are created on the correct columns
- **Migration Tests**: Validate schema changes apply correctly

### Application Integration Testing
- **Startup Tests**: Verify application starts without configuration errors
- **CRUD Operations**: Test create, read, update, delete operations work correctly
- **Relationship Queries**: Test queries that join multiple tables
- **API Endpoint Tests**: Verify all endpoints continue to work correctly

## Implementation Phases

### Phase 1: Schema Analysis and Planning
1. Audit all existing models and identify naming inconsistencies
2. Create comprehensive mapping of required changes
3. Identify all foreign key relationships that need updates
4. Plan the order of changes to minimize dependencies

### Phase 2: Schema File Updates
1. Update the main `schema.sql` file with new primary key names
2. Update all foreign key references in the schema
3. Ensure all constraints use the correct column names
4. Validate the updated schema for consistency

### Phase 3: Model Class Updates
1. Update all SQLAlchemy model classes with new primary key names
2. Fix all foreign key column references in models
3. Update all relationship definitions to use correct column names
4. Test model imports and configurations

### Phase 4: Database Recreation and Testing
1. Drop existing database tables (development environment)
2. Recreate database from updated schema
3. Test all model operations and relationships
4. Validate application startup and basic functionality

### Phase 5: Comprehensive Testing and Validation
1. Run full test suite to ensure no regressions
2. Test all user workflows end-to-end
3. Validate all API endpoints work correctly
4. Perform integration testing with external services

## Security Considerations

### Data Protection
- **Backup Strategy**: Ensure database backups exist before making changes
- **Transaction Isolation**: Use appropriate isolation levels for schema changes
- **Access Control**: Maintain proper database user permissions
- **Audit Logging**: Log all schema modification activities

### Migration Safety
- **Validation Checks**: Verify schema changes before applying
- **Rollback Procedures**: Maintain ability to revert changes
- **Testing Environment**: Test all changes in development before production
- **Change Approval**: Document and review all schema modifications

## Performance Considerations

### Database Performance
- **Index Strategy**: Ensure indexes are created on new primary key columns
- **Query Optimization**: Verify queries perform well with new column names
- **Foreign Key Performance**: Ensure foreign key constraints don't impact performance
- **Connection Pooling**: Maintain efficient database connection management

### Application Performance
- **Model Loading**: Ensure model configuration doesn't slow application startup
- **Relationship Loading**: Optimize SQLAlchemy relationship loading strategies
- **Query Efficiency**: Maintain efficient database query patterns
- **Caching Strategy**: Update any caching that depends on column names

## Monitoring and Maintenance

### Health Monitoring
- **Schema Validation**: Regular checks to ensure schema consistency
- **Relationship Integrity**: Monitor foreign key constraint violations
- **Performance Monitoring**: Track query performance after changes
- **Error Monitoring**: Watch for any naming-related errors

### Maintenance Procedures
- **Documentation Updates**: Update all documentation with new column names
- **Code Review Guidelines**: Establish standards for future naming conventions
- **Developer Training**: Ensure team understands new naming standards
- **Continuous Validation**: Regular checks to prevent naming inconsistencies

This design provides a comprehensive approach to standardizing database naming conventions while maintaining data integrity and ensuring reliable operation of the appointment management system.