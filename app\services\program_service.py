# app/services/program_service.py
from datetime import datetime, timedelta
from app.extensions import db
from app.models.program import Program, ProgramModule, Enrollment, ModuleProgress, ModuleSession
from app.models.client import Client
from app.models.appointment import Appointment
from sqlalchemy import and_, or_

class ProgramService:
    """Service class for managing programs, enrollments, and module progress."""

    @staticmethod
    def create_program(name, code, description, program_type, default_duration_minutes, 
                      total_modules, is_sequential=True, created_by=None):
        """Create a new program."""
        program = Program(
            name=name,
            code=code,
            description=description,
            program_type=program_type,
            default_duration_minutes=default_duration_minutes,
            total_modules=total_modules,
            is_sequential=is_sequential,
            created_by=created_by
        )
        db.session.add(program)
        db.session.commit()
        return program

    @staticmethod
    def add_module_to_program(program_id, name, description, module_number, duration_minutes,
                             learning_objectives=None, prerequisites=None, materials_needed=None,
                             is_required=True, service_id=None):
        """Add a module to an existing program."""
        module = ProgramModule(
            program_id=program_id,
            name=name,
            description=description,
            module_number=module_number,
            duration_minutes=duration_minutes,
            learning_objectives=learning_objectives,
            prerequisites=prerequisites,
            materials_needed=materials_needed,
            is_required=is_required,
            service_id=service_id
        )
        db.session.add(module)
        db.session.commit()
        return module

    @staticmethod
    def enroll_client(client_id, program_id, start_date=None, end_date=None, created_by=None):
        """Enroll a client in a program."""
        client = Client.query.get_or_404(client_id)
        program = Program.query.get_or_404(program_id)
        
        # Set default dates if not provided
        if not start_date:
            start_date = datetime.now().date()
        
        if not end_date and program.total_modules:
            # Estimate end date based on modules and default duration
            estimated_days = program.total_modules * 7  # Assume one module per week
            end_date = start_date + timedelta(days=estimated_days)
        
        # Create enrollment
        enrollment = Enrollment(
            client_id=client_id,
            program_id=program_id,
            enrollment_date=datetime.now().date(),
            start_date=start_date,
            end_date=end_date,
            status='active',
            created_by=created_by
        )
        db.session.add(enrollment)
        db.session.flush()  # Get enrollment ID without committing
        
        # Create module progress entries for all modules in the program
        for module in program.modules:
            module_progress = ModuleProgress(
                enrollment_id=enrollment.id,
                module_id=module.id,
                status='not_started'
            )
            db.session.add(module_progress)
        
        db.session.commit()
        return enrollment

    @staticmethod
    def schedule_module_session(module_progress_id, tutor_id, start_time, duration_minutes, 
                               notes=None, session_number=1):
        """Schedule an appointment for a module session."""
        module_progress = ModuleProgress.query.get_or_404(module_progress_id)
        enrollment = module_progress.enrollment
        module = module_progress.module
        
        # Calculate end time
        end_time = start_time + timedelta(minutes=duration_minutes)
        
        # Create appointment
        appointment = Appointment(
            tutor_id=tutor_id,
            client_id=enrollment.client_id,
            tutor_service_id=module.service_id if module.service_id else None,
            start_time=start_time,
            end_time=end_time,
            status='scheduled',
            notes=notes,
            is_program_session=True
        )
        db.session.add(appointment)
        db.session.flush()  # Get appointment ID without committing
        
        # Link appointment to module progress
        module_session = ModuleSession(
            module_progress_id=module_progress_id,
            appointment_id=appointment.appointment_id,
            session_number=session_number
        )
        db.session.add(module_session)
        
        # Update module progress status if it's the first session
        if module_progress.status == 'not_started':
            module_progress.status = 'in_progress'
            module_progress.start_date = start_time.date()
        
        db.session.commit()
        return appointment, module_session

    @staticmethod
    def complete_module(module_progress_id, score=None, feedback=None, tutor_notes=None):
        """Mark a module as completed."""
        module_progress = ModuleProgress.query.get_or_404(module_progress_id)
        
        module_progress.status = 'completed'
        module_progress.completion_date = datetime.now().date()
        if score is not None:
            module_progress.score = score
        if feedback:
            module_progress.feedback = feedback
        if tutor_notes:
            module_progress.tutor_notes = tutor_notes
        
        # Update enrollment completion percentage
        enrollment = module_progress.enrollment
        total_required_modules = enrollment.program.modules.filter_by(is_required=True).count()
        completed_required_modules = ModuleProgress.query.join(ProgramModule).filter(
            ModuleProgress.enrollment_id == enrollment.id,
            ModuleProgress.status == 'completed',
            ProgramModule.is_required == True
        ).count()
        
        if total_required_modules > 0:
            enrollment.completion_percentage = (completed_required_modules / total_required_modules) * 100
            
            # If all required modules are completed, mark enrollment as completed
            if completed_required_modules == total_required_modules:
                enrollment.status = 'completed'
        
        db.session.commit()
        return module_progress

    @staticmethod
    def get_client_programs(client_id, status=None):
        """Get all programs a client is enrolled in, optionally filtered by status."""
        query = Enrollment.query.filter_by(client_id=client_id)
        if status:
            query = query.filter_by(status=status)
        return query.all()

    @staticmethod
    def get_program_clients(program_id, status=None):
        """Get all clients enrolled in a program, optionally filtered by status."""
        query = Enrollment.query.filter_by(program_id=program_id)
        if status:
            query = query.filter_by(status=status)
        return query.all()

    @staticmethod
    def get_module_progress(enrollment_id, module_id=None):
        """Get progress for all modules in an enrollment or a specific module."""
        if module_id:
            return ModuleProgress.query.filter_by(
                enrollment_id=enrollment_id, 
                module_id=module_id
            ).first()
        return ModuleProgress.query.filter_by(enrollment_id=enrollment_id).all()

    @staticmethod
    def get_upcoming_module_sessions(client_id=None, tutor_id=None, days=7):
        """Get upcoming module sessions for a client or tutor."""
        now = datetime.now()
        end_date = now + timedelta(days=days)
        
        query = db.session.query(ModuleSession, Appointment, ModuleProgress, ProgramModule).\
            join(Appointment, ModuleSession.appointment_id == Appointment.appointment_id).\
            join(ModuleProgress, ModuleSession.module_progress_id == ModuleProgress.progress_id).\
            join(ProgramModule, ModuleProgress.module_id == ProgramModule.module_id).\
            filter(Appointment.start_time >= now, Appointment.start_time <= end_date)
        
        if client_id:
            query = query.filter(Appointment.client_id == client_id)
        
        if tutor_id:
            query = query.filter(Appointment.tutor_id == tutor_id)
        
        return query.order_by(Appointment.start_time).all()
