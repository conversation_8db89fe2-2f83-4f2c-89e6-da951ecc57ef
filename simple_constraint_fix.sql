-- Simple Fix for Appointment Audit Constraint
-- This script removes ALL check constraints on the action column and adds a new one

-- ========================================
-- REMOVE ALL ACTION CONSTRAINTS
-- ========================================

-- First, let's see what constraints exist
SELECT 'CURRENT CONSTRAINTS ON APPOINTMENT_AUDIT:' as info;
SELECT 
    tc.constraint_name,
    tc.constraint_type
FROM information_schema.table_constraints tc
WHERE tc.table_name = 'appointment_audit'
AND tc.constraint_type = 'CHECK';

-- Drop ALL check constraints on appointment_audit table
DO $$ 
DECLARE
    constraint_record RECORD;
BEGIN
    FOR constraint_record IN 
        SELECT constraint_name 
        FROM information_schema.table_constraints 
        WHERE table_name = 'appointment_audit' 
        AND constraint_type = 'CHECK'
    LOOP
        EXECUTE 'ALTER TABLE appointment_audit DROP CONSTRAINT ' || constraint_record.constraint_name;
        RAISE NOTICE 'Dropped constraint: %', constraint_record.constraint_name;
    END LOOP;
END $$;

-- ========================================
-- ADD NEW SIMPLE CONSTRAINT
-- ========================================

-- Add a new constraint that allows the values the application uses
ALTER TABLE appointment_audit 
ADD CONSTRAINT appointment_audit_action_valid 
CHECK (action IN ('create', 'update', 'delete', 'cancel', 'complete', 'reschedule'));

-- ========================================
-- VERIFICATION
-- ========================================

-- Show the new constraint
SELECT 'NEW CONSTRAINTS:' as info;
SELECT 
    tc.constraint_name,
    tc.constraint_type
FROM information_schema.table_constraints tc
WHERE tc.table_name = 'appointment_audit'
AND tc.constraint_type = 'CHECK';

-- Test that 'create' is now allowed
DO $$
BEGIN
    BEGIN
        -- This should work now
        INSERT INTO appointment_audit (appointment_id, action, timestamp) 
        VALUES (999999, 'create', NOW());
        
        -- Clean up the test record
        DELETE FROM appointment_audit WHERE appointment_id = 999999;
        
        RAISE NOTICE 'SUCCESS: action=create is now allowed!';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'ERROR: action=create still failed: %', SQLERRM;
    END;
END $$;

SELECT 'CONSTRAINT FIX COMPLETED!' as status;
