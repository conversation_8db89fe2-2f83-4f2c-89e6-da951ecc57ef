# app/tasks/enrollment_cleanup.py
from datetime import datetime, timedelta
from app import create_app
from app.extensions import db
from app.models.program import Enrollment, GroupSessionParticipant
from app.models.client import Client
from app.models.user import User
from app.utils.email import send_email
from flask import url_for
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def cleanup_abandoned_enrollments():
    """Clean up abandoned enrollments that are older than 24 hours."""
    app = create_app()
    with app.app_context():
        try:
            # Find pending enrollments older than 24 hours
            cutoff_time = datetime.utcnow() - timedelta(hours=24)
            
            expired_enrollments = Enrollment.query.filter(
                Enrollment.status == 'pending',
                Enrollment.created_at < cutoff_time
            ).all()
            
            logger.info(f"Found {len(expired_enrollments)} expired enrollments to clean up")
            
            for enrollment in expired_enrollments:
                # Remove associated group session participants
                participants = GroupSessionParticipant.query.filter_by(
                    enrollment_id=enrollment.id,
                    is_paid=False  # Only remove unpaid reservations
                ).all()
                
                for participant in participants:
                    db.session.delete(participant)
                    logger.info(f"Removed participant from session {participant.group_session_id}")
                
                # Delete the enrollment
                db.session.delete(enrollment)
                logger.info(f"Deleted enrollment {enrollment.id} for client {enrollment.client_id}")
            
            db.session.commit()
            logger.info(f"Successfully cleaned up {len(expired_enrollments)} abandoned enrollments")
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error during enrollment cleanup: {str(e)}")
            raise

def cleanup_expired_reservations():
    """Clean up expired session reservations."""
    app = create_app()
    with app.app_context():
        try:
            now = datetime.utcnow()
            
            # Find expired reservations
            expired_reservations = GroupSessionParticipant.query.filter(
                GroupSessionParticipant.is_paid == False,
                GroupSessionParticipant.reserved_until < now
            ).all()
            
            logger.info(f"Found {len(expired_reservations)} expired reservations to clean up")
            
            for reservation in expired_reservations:
                db.session.delete(reservation)
                logger.info(f"Removed expired reservation for session {reservation.group_session_id}")
            
            db.session.commit()
            logger.info(f"Successfully cleaned up {len(expired_reservations)} expired reservations")
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error during reservation cleanup: {str(e)}")
            raise

def send_incomplete_enrollment_reminders():
    """Send reminder emails for incomplete enrollments (between 2-24 hours old)."""
    app = create_app()
    with app.app_context():
        try:
            # Find pending enrollments between 2 and 24 hours old
            min_time = datetime.utcnow() - timedelta(hours=24)
            max_time = datetime.utcnow() - timedelta(hours=2)
            
            pending_enrollments = db.session.query(Enrollment, Client, User).join(
                Client, Enrollment.client_id == Client.id
            ).join(
                User, Client.user_id == User.id
            ).filter(
                Enrollment.status == 'pending',
                Enrollment.created_at >= min_time,
                Enrollment.created_at <= max_time,
                Enrollment.recovery_token == None  # Haven't sent reminder yet
            ).all()
            
            logger.info(f"Found {len(pending_enrollments)} enrollments needing reminders")
            
            for enrollment, client, user in pending_enrollments:
                # Generate recovery token
                enrollment.generate_recovery_token()
                
                # Send reminder email
                subject = "Complétez votre inscription TECFÉE"
                
                recovery_url = url_for('public.continue_tecfee_enrollment', 
                                     token=enrollment.recovery_token,
                                     _external=True)
                
                html_body = f"""
                <h2>Votre inscription TECFÉE est presque terminée!</h2>
                
                <p>Bonjour {client.first_name},</p>
                
                <p>Nous avons remarqué que vous avez commencé votre inscription au programme TECFÉE 
                mais ne l'avez pas encore complétée.</p>
                
                <p>Il vous reste seulement à:</p>
                <ul>
                    <li>Sélectionner vos sessions</li>
                    <li>Effectuer le paiement</li>
                </ul>
                
                <p><strong>Votre inscription expirera dans moins de 24 heures.</strong></p>
                
                <p style="text-align: center; margin: 30px 0;">
                    <a href="{recovery_url}" 
                       style="background: #e57373; color: white; padding: 12px 30px; 
                              text-decoration: none; border-radius: 8px; display: inline-block;">
                        Continuer mon inscription
                    </a>
                </p>
                
                <p>Si le bouton ne fonctionne pas, copiez et collez ce lien dans votre navigateur:<br>
                {recovery_url}</p>
                
                <p>Si vous avez des questions, n'hésitez pas à nous contacter.</p>
                
                <p>Cordialement,<br>
                L'équipe TutorAide</p>
                """
                
                text_body = f"""
                Votre inscription TECFÉE est presque terminée!
                
                Bonjour {client.first_name},
                
                Nous avons remarqué que vous avez commencé votre inscription au programme TECFÉE 
                mais ne l'avez pas encore complétée.
                
                Il vous reste seulement à:
                - Sélectionner vos sessions
                - Effectuer le paiement
                
                Votre inscription expirera dans moins de 24 heures.
                
                Continuer mon inscription: {recovery_url}
                
                Si vous avez des questions, n'hésitez pas à nous contacter.
                
                Cordialement,
                L'équipe TutorAide
                """
                
                send_email(
                    subject=subject,
                    recipients=[user.email],
                    text_body=text_body,
                    html_body=html_body
                )
                
                logger.info(f"Sent reminder email to {user.email} for enrollment {enrollment.id}")
            
            db.session.commit()
            logger.info(f"Successfully sent {len(pending_enrollments)} reminder emails")
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error sending enrollment reminders: {str(e)}")
            raise

def main():
    """Run all cleanup tasks."""
    logger.info("Starting enrollment cleanup tasks...")
    
    # Clean up expired reservations
    cleanup_expired_reservations()
    
    # Send reminders for incomplete enrollments
    send_incomplete_enrollment_reminders()
    
    # Clean up abandoned enrollments
    cleanup_abandoned_enrollments()
    
    logger.info("Enrollment cleanup tasks completed")

if __name__ == "__main__":
    main()