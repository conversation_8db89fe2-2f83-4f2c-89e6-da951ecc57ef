<!-- app/templates/manager/subscription_plans_list.html -->
{% extends "base.html" %}

{% block title %}{{ t('manager.subscription_plans.title_full') }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">{{ t('manager.subscription_plans.title') }}</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('manager.new_subscription_plan') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> {{ t('manager.subscription_plans.add_plan') }}
        </a>
    </div>
</div>

<!-- Subscription Plans Table -->
<div class="card shadow">
    <div class="card-body">
        {% if plans %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Duration</th>
                            <th>Max Hours</th>
                            <th>Price</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for plan in plans %}
                            <tr>
                                <td>{{ plan.name }}</td>
                                <td>{{ plan.duration_months }} month{% if plan.duration_months > 1 %}s{% endif %}</td>
                                <td>{{ plan.max_hours }} hours</td>
                                <td>${{ "%.2f"|format(plan.price) }}</td>
                                <td>
                                    <span class="badge bg-{{ 'success' if plan.is_active else 'danger' }}">
                                        {{ 'Active' if plan.is_active else 'Inactive' }}
                                    </span>
                                </td>
                                <td>
                                    <a href="{{ url_for('manager.edit_subscription_plan', id=plan.plan_id) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                    <a href="{{ url_for('manager.subscriptions_list') }}?plan_id={{ plan.id }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-users"></i> Subscribers
                                    </a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center p-5">
                <i class="fas fa-calendar-check fa-3x text-muted mb-3"></i>
                <h5>No subscription plans found</h5>
                <p class="text-muted">No subscription plans have been created yet.</p>
                <a href="{{ url_for('manager.new_subscription_plan') }}" class="btn btn-primary mt-3">
                    <i class="fas fa-plus"></i> Add Subscription Plan
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Plan Analytics Card -->
{% if plans %}
<div class="card shadow mt-4">
    <div class="card-header bg-light">
        <h5 class="mb-0">Subscription Analytics</h5>
    </div>
    <div class="card-body">
        <div class="row">
            {% for plan in plans %}
                <div class="col-md-4 mb-3">
                    <div class="card h-100">
                        <div class="card-body">
                            <h5 class="card-title">{{ plan.name }}</h5>
                            <p class="card-text">${{ "%.2f"|format(plan.price) }} / {{ plan.duration_months }} month{% if plan.duration_months > 1 %}s{% endif %}</p>
                            <p class="card-text">Max: {{ plan.max_hours }} hours</p>
                            
                            <!-- Stats: active subscriptions, total subscribers -->
                            <div class="mt-3">
                                <p class="mb-1">Active Subscriptions: {{ plan.subscriptions.filter_by(status='active').count() }}</p>
                                <p class="mb-0">Total Subscribers: {{ plan.subscriptions.count() }}</p>
                            </div>
                        </div>
                        <div class="card-footer text-center">
                            <a href="{{ url_for('manager.subscriptions_list') }}?plan_id={{ plan.id }}" class="btn btn-sm btn-outline-primary">
                                View Subscribers
                            </a>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}
{% endblock %}