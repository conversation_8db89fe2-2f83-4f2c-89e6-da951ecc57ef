-- Fix Appointment Audit Action Constraint
-- This script updates the check constraint to match what the application code expects

-- ========================================
-- FIX ACTION CHECK CONSTRAINT
-- ========================================

-- Drop the existing check constraint
DO $$ 
BEGIN
    -- Drop the constraint if it exists
    IF EXISTS (SELECT 1 FROM information_schema.table_constraints 
               WHERE table_name = 'appointment_audit' 
               AND constraint_name = 'appointment_audit_action_check') THEN
        ALTER TABLE appointment_audit DROP CONSTRAINT appointment_audit_action_check;
        RAISE NOTICE 'Dropped existing appointment_audit_action_check constraint';
    END IF;
    
    -- Also check for other possible constraint names
    IF EXISTS (SELECT 1 FROM information_schema.table_constraints 
               WHERE table_name = 'appointment_audit' 
               AND constraint_type = 'CHECK' 
               AND constraint_name LIKE '%action%') THEN
        -- Get the constraint name and drop it
        DECLARE
            constraint_name_var TEXT;
        BEGIN
            SELECT constraint_name INTO constraint_name_var
            FROM information_schema.table_constraints 
            WHERE table_name = 'appointment_audit' 
            AND constraint_type = 'CHECK' 
            AND constraint_name LIKE '%action%'
            LIMIT 1;
            
            IF constraint_name_var IS NOT NULL THEN
                EXECUTE 'ALTER TABLE appointment_audit DROP CONSTRAINT ' || constraint_name_var;
                RAISE NOTICE 'Dropped constraint: %', constraint_name_var;
            END IF;
        END;
    END IF;
END $$;

-- Add the new constraint that matches the application code
ALTER TABLE appointment_audit 
ADD CONSTRAINT appointment_audit_action_check 
CHECK (action IN ('create', 'update', 'delete', 'cancel', 'complete', 'reschedule'));

-- ========================================
-- VERIFICATION
-- ========================================

-- Check the new constraint
SELECT 'APPOINTMENT_AUDIT CONSTRAINTS:' as info;
SELECT
    tc.constraint_name,
    tc.constraint_type,
    cc.check_clause
FROM information_schema.check_constraints cc
JOIN information_schema.table_constraints tc
    ON cc.constraint_name = tc.constraint_name
WHERE tc.table_name = 'appointment_audit'
AND tc.constraint_type = 'CHECK';

-- Test the constraint with a sample insert (will be rolled back)
DO $$
BEGIN
    -- Test valid action
    BEGIN
        INSERT INTO appointment_audit (appointment_id, action, timestamp) 
        VALUES (999999, 'create', NOW());
        RAISE NOTICE 'SUCCESS: action=create is allowed';
        ROLLBACK;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'ERROR: action=create failed: %', SQLERRM;
        ROLLBACK;
    END;
    
    -- Test invalid action
    BEGIN
        INSERT INTO appointment_audit (appointment_id, action, timestamp) 
        VALUES (999999, 'invalid_action', NOW());
        RAISE NOTICE 'ERROR: invalid_action should have failed but did not';
        ROLLBACK;
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'SUCCESS: invalid_action correctly rejected: %', SQLERRM;
        ROLLBACK;
    END;
END $$;

-- Final success message
SELECT 'APPOINTMENT_AUDIT CONSTRAINT FIX COMPLETED!' as status,
       'The action constraint now allows: create, update, delete, cancel, complete, reschedule' as message,
       'You can now restart your application and create appointments.' as next_step;
