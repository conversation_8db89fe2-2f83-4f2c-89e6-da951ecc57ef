#!/usr/bin/env python3
"""
Simple verification of audit system integration status.
"""

import os
import sys

def check_file_exists(path, description):
    """Check if a file exists and report status."""
    if os.path.exists(path):
        print(f"✓ {description}")
        return True
    else:
        print(f"✗ {description}")
        return False

def check_file_contains(path, search_terms, description):
    """Check if a file contains specific terms."""
    if not os.path.exists(path):
        print(f"✗ {description} (file not found)")
        return False
    
    try:
        with open(path, 'r', encoding='utf-8') as f:
            content = f.read().lower()
        
        if isinstance(search_terms, str):
            search_terms = [search_terms]
        
        found_all = all(term.lower() in content for term in search_terms)
        
        if found_all:
            print(f"✓ {description}")
            return True
        else:
            missing = [term for term in search_terms if term.lower() not in content]
            print(f"✗ {description} (missing: {', '.join(missing)})")
            return False
    except Exception as e:
        print(f"✗ {description} (error reading file: {e})")
        return False

def main():
    """Run simple audit integration verification."""
    print("Audit System Integration Verification")
    print("=" * 50)
    
    checks = []
    
    # Check core audit components
    print("\n1. Core Audit Components:")
    checks.append(check_file_exists("app/models/appointment_audit.py", "Audit model exists"))
    checks.append(check_file_exists("app/services/audit_service.py", "Audit service exists"))
    checks.append(check_file_exists("app/services/timezone_service.py", "Timezone service exists"))
    
    # Check audit logging integration
    print("\n2. Audit Logging Integration:")
    checks.append(check_file_contains("app/services/appointment_service.py", 
                                    ["AppointmentAudit", "log_action"], 
                                    "Appointment service includes audit logging"))
    checks.append(check_file_contains("app/views/client.py", 
                                    ["AppointmentAudit", "log_action"], 
                                    "Client views include audit logging"))
    checks.append(check_file_contains("app/views/tutor.py", 
                                    ["AppointmentAudit", "log_action"], 
                                    "Tutor views include audit logging"))
    
    # Check API endpoints
    print("\n3. API Endpoints:")
    checks.append(check_file_contains("app/views/api.py", 
                                    ["get_appointment_audit", "/audit"], 
                                    "Audit API endpoints exist"))
    checks.append(check_file_contains("app/views/api.py", 
                                    ["manager", "role"], 
                                    "API includes manager access control"))
    
    # Check frontend components
    print("\n4. Frontend Components:")
    checks.append(check_file_exists("app/templates/components/audit_trail_modal.html", 
                                  "Audit trail modal template exists"))
    checks.append(check_file_exists("app/static/js/audit_trail_modal.js", 
                                  "Audit trail modal JavaScript exists"))
    checks.append(check_file_exists("app/static/css/audit_trail_modal.css", 
                                  "Audit trail modal CSS exists"))
    
    # Check manager view integration
    print("\n5. Manager View Integration:")
    checks.append(check_file_contains("app/templates/manager/schedule.html", 
                                    ["audit", "audit_trail_modal"], 
                                    "Manager schedule includes audit trail"))
    checks.append(check_file_contains("app/templates/manager/appointment_detail.html", 
                                    ["audit", "data-audit-appointment-id"], 
                                    "Appointment detail includes audit trail button"))
    
    # Calculate results
    passed = sum(checks)
    total = len(checks)
    percentage = (passed / total) * 100 if total > 0 else 0
    
    print(f"\n" + "=" * 50)
    print(f"RESULTS: {passed}/{total} checks passed ({percentage:.1f}%)")
    
    if percentage >= 90:
        print("✓ Audit system integration is COMPLETE")
        return 0
    elif percentage >= 80:
        print("⚠ Audit system integration is MOSTLY COMPLETE")
        return 0
    elif percentage >= 60:
        print("⚠ Audit system integration has SOME GAPS")
        return 1
    else:
        print("✗ Audit system integration NEEDS WORK")
        return 2

if __name__ == '__main__':
    sys.exit(main())