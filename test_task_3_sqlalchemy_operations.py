#!/usr/bin/env python3
"""
Test script to verify that program-related models work correctly with SQLAlchemy operations.
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

def test_sqlalchemy_operations():
    """Test that program-related models work with SQLAlchemy operations."""
    
    try:
        from app import create_app
        from app.extensions import db
        from app.models.program import (
            Program, ProgramModule, Enrollment, ModuleProgress, 
            ModuleSession, ProgramPricing, GroupSession, GroupSessionParticipant
        )
        
        # Create app context
        app = create_app()
        
        with app.app_context():
            print("✅ App context created successfully")
            
            # Test that we can create table metadata without errors
            try:
                # This will validate all model configurations
                db.create_all()
                print("✅ All program-related models can create tables without errors")
            except Exception as e:
                print(f"❌ Error creating tables: {e}")
                return False
            
            # Test basic query operations (without actually inserting data)
            try:
                # Test that we can create queries without errors
                program_query = Program.query.filter_by(program_id=1)
                module_query = ProgramModule.query.filter_by(module_id=1)
                enrollment_query = Enrollment.query.filter_by(enrollment_id=1)
                progress_query = ModuleProgress.query.filter_by(progress_id=1)
                session_query = ModuleSession.query.filter_by(session_id=1)
                pricing_query = ProgramPricing.query.filter_by(pricing_id=1)
                group_session_query = GroupSession.query.filter_by(group_session_id=1)
                participant_query = GroupSessionParticipant.query.filter_by(participant_id=1)
                
                print("✅ All query operations can be created without errors")
            except Exception as e:
                print(f"❌ Error creating queries: {e}")
                return False
            
            # Test relationships
            try:
                # Test that relationships are properly configured
                program = Program()
                program.program_id = 999  # Set a test ID
                
                # Test accessing relationships (this validates the relationship configuration)
                modules_rel = program.modules
                enrollments_rel = program.enrollments
                
                print("✅ All relationships are properly configured")
            except Exception as e:
                print(f"❌ Error with relationships: {e}")
                return False
        
        print("\n🎉 All SQLAlchemy operations work correctly!")
        print("✅ Models can create tables without configuration errors")
        print("✅ Queries can be constructed using descriptive primary keys")
        print("✅ Relationships are properly configured")
        return True
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = test_sqlalchemy_operations()
    sys.exit(0 if success else 1)