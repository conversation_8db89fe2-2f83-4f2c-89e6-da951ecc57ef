<!-- app/templates/client/appointments.html -->
{% extends "base.html" %}

{% block title %}{{ t('appointments.your_appointments') }} - TutorAide Inc.{% endblock %}

{% block styles %}
<style>
    /* Reset all Bootstrap tab styles */
    .nav-tabs {
        border-bottom: 1px solid #dee2e6 !important;
    }
    
    .nav-tabs .nav-item {
        margin-bottom: -1px !important;
    }
    
    .nav-tabs .nav-link {
        color: #212529 !important;
        font-weight: 500;
        border: 1px solid transparent !important;
        border-top-left-radius: 0 !important;
        border-top-right-radius: 0 !important;
        background: transparent !important;
        padding: 1rem 1.5rem !important;
        margin-bottom: 0 !important;
        position: relative !important;
        transition: all 0.3s ease;
    }
    
    .nav-tabs .nav-link::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        right: 0;
        height: 3px;
        background: transparent;
        transition: background 0.3s ease;
    }
    
    .nav-tabs .nav-link:hover {
        color: #e57373 !important;
        border-color: transparent !important;
        background: transparent !important;
    }
    
    .nav-tabs .nav-link:hover::after {
        background: transparent;
    }
    
    /* Active tab styling */
    .nav-tabs .nav-link.active,
    .nav-tabs .nav-item.show .nav-link {
        color: #e57373 !important;
        background-color: transparent !important;
        border: 1px solid transparent !important;
        border-bottom-color: transparent !important;
        font-weight: 600 !important;
    }
    
    .nav-tabs .nav-link.active::after {
        background: #e57373 !important;
    }
    
    /* Remove ALL focus states */
    .nav-tabs .nav-link:focus,
    .nav-tabs .nav-link:focus-visible,
    .nav-tabs .nav-link.active:focus,
    .nav-tabs .nav-link.active:focus-visible {
        outline: none !important;
        outline-width: 0 !important;
        box-shadow: none !important;
        border-color: transparent !important;
        background: transparent !important;
    }
    
    /* Override Bootstrap's tab-pane.active */
    .tab-content > .active {
        display: block;
    }
    
    /* Ensure no blue appears on click */
    .nav-tabs .nav-link:active,
    .nav-tabs .nav-link.active:active {
        outline: none !important;
        box-shadow: none !important;
        border-color: transparent !important;
    }
    
    .nav-tabs {
        border-bottom: 1px solid #dee2e6;
    }
    
    .card-header {
        background: #f8f9fa !important;
        padding: 0;
    }
    
    .card-header .nav-tabs {
        margin-bottom: -1px;
    }
    
    .nav-tabs .nav-item {
        margin-bottom: 0;
    }
    
    .nav-tabs .nav-link {
        padding: 1rem 1.5rem;
        margin-bottom: 0;
        border-radius: 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h2 class="mb-3">{{ t('appointments.your_appointments') }}</h2>
        <p class="text-muted">{{ t('appointments.view_and_manage') }}</p>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <ul class="nav nav-tabs card-header-tabs" id="appointmentsTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="upcoming-tab" data-bs-toggle="tab" data-bs-target="#upcoming" type="button" role="tab" aria-controls="upcoming" aria-selected="true">
                    {{ t('appointments.upcoming') }}
                    {% if upcoming_appointments %}
                        <span class="badge bg-danger ms-1">{{ upcoming_appointments|length }}</span>
                    {% endif %}
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="past-tab" data-bs-toggle="tab" data-bs-target="#past" type="button" role="tab" aria-controls="past" aria-selected="false">
                    {{ t('appointments.past') }}
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="all-tab" data-bs-toggle="tab" data-bs-target="#all" type="button" role="tab" aria-controls="all" aria-selected="false">
                    {{ t('appointments.all') }}
                </button>
            </li>
        </ul>
    </div>
    <div class="card-body">
        <div class="tab-content" id="appointmentsTabsContent">
            <!-- Upcoming Appointments Tab -->
            <div class="tab-pane fade show active" id="upcoming" role="tabpanel" aria-labelledby="upcoming-tab">
                {% if upcoming_appointments or upcoming_group_sessions %}
                    <!-- Regular Appointments -->
                    {% if upcoming_appointments %}
                        <h5 class="mb-3">{{ t('appointments.individual_sessions') }}</h5>
                        <div class="table-responsive mb-4">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>{{ t('appointments.date') }}</th>
                                        <th>{{ t('appointments.time') }}</th>
                                        <th>{{ t('appointments.client') }}</th>
                                        <th>{{ t('appointments.tutor') }}</th>
                                        <th>{{ t('appointments.service') }}</th>
                                        <th>{{ t('appointments.status') }}</th>
                                        <th>{{ t('appointments.actions') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for appointment in upcoming_appointments %}
                                        <tr>
                                            <td>{{ appointment.start_time.strftime('%Y-%m-%d') }}</td>
                                            <td>{{ appointment.start_time.strftime('%H:%M') }} - {{ appointment.end_time.strftime('%H:%M') }}</td>
                                            <td>
                                                {% if appointment.client %}
                                                    {{ appointment.client.first_name }} {{ appointment.client.last_name }}
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                            <td>{{ appointment.tutor.first_name }} {{ appointment.tutor.last_name }}</td>
                                            <td>{{ appointment.tutor_service.service.name }}</td>
                                            <td>
                                                <span class="badge bg-{{ 'success' if appointment.status == 'completed' else 'warning' if appointment.status == 'scheduled' else 'danger' }}">
                                                    {{ appointment.status | capitalize }}
                                                </span>
                                            </td>
                                            <td>
                                                <a href="{{ url_for('client.view_appointment', id=appointment.id) }}" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                {% if appointment.status == 'scheduled' %}
                                                    <a href="{{ url_for('client.cancel_appointment', id=appointment.id) }}" class="btn btn-sm btn-danger" onclick="return confirm('{{ t('appointments.confirm_cancel') }}');">
                                                        <i class="fas fa-times"></i>
                                                    </a>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% endif %}
                    
                    <!-- TECFÉE Group Sessions -->
                    {% if upcoming_group_sessions %}
                        <h5 class="mb-3">{{ t('appointments.group_sessions_tecfee') }}</h5>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>{{ t('appointments.date') }}</th>
                                        <th>{{ t('appointments.time') }}</th>
                                        <th>{{ t('appointments.program') }}</th>
                                        <th>{{ t('appointments.module') }}</th>
                                        <th>{{ t('appointments.tutor') }}</th>
                                        <th>{{ t('appointments.participants') }}</th>
                                        <th>{{ t('appointments.status') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for session in upcoming_group_sessions %}
                                        <tr>
                                            <td>{{ session.session_date.strftime('%Y-%m-%d') }}</td>
                                            <td>{{ session.start_time.strftime('%H:%M') }} - {{ session.end_time.strftime('%H:%M') }}</td>
                                            <td>{{ session.program.name }}</td>
                                            <td>
                                                {% if session.module %}
                                                    {{ t('appointments.module_number', number=session.module.module_order) }}: {{ session.module.name }}
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                            <td>{{ session.tutor.first_name }} {{ session.tutor.last_name }}</td>
                                            <td>
                                                <span class="badge bg-info">
                                                    {{ session.current_participants_count }}/{{ session.max_participants }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ 'success' if session.participation.attendance_status == 'attended' else 'warning' if session.participation.attendance_status == 'registered' else 'danger' }}">
                                                    {{ t('appointments.attendance_' + session.participation.attendance_status) }}
                                                </span>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% endif %}
                {% else %}
                    <p class="text-muted text-center my-4">{{ t('appointments.no_upcoming_appointments') }}</p>
                {% endif %}
            </div>

            <!-- Past Appointments Tab -->
            <div class="tab-pane fade" id="past" role="tabpanel" aria-labelledby="past-tab">
                {% if past_appointments or past_group_sessions %}
                    <!-- Regular Appointments -->
                    {% if past_appointments %}
                        <h5 class="mb-3">{{ t('appointments.individual_sessions') }}</h5>
                        <div class="table-responsive mb-4">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>{{ t('appointments.date') }}</th>
                                        <th>{{ t('appointments.time') }}</th>
                                        <th>{{ t('appointments.client') }}</th>
                                        <th>{{ t('appointments.tutor') }}</th>
                                        <th>{{ t('appointments.service') }}</th>
                                        <th>{{ t('appointments.status') }}</th>
                                        <th>{{ t('appointments.actions') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for appointment in past_appointments %}
                                        <tr>
                                            <td>{{ appointment.start_time.strftime('%Y-%m-%d') }}</td>
                                            <td>{{ appointment.start_time.strftime('%H:%M') }} - {{ appointment.end_time.strftime('%H:%M') }}</td>
                                            <td>
                                                {% if appointment.client %}
                                                    {{ appointment.client.first_name }} {{ appointment.client.last_name }}
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                            <td>{{ appointment.tutor.first_name }} {{ appointment.tutor.last_name }}</td>
                                            <td>{{ appointment.tutor_service.service.name }}</td>
                                            <td>
                                                <span class="badge bg-{{ 'success' if appointment.status == 'completed' else 'warning' if appointment.status == 'scheduled' else 'danger' }}">
                                                    {{ appointment.status | capitalize }}
                                                </span>
                                            </td>
                                            <td>
                                                <a href="{{ url_for('client.view_appointment', id=appointment.id) }}" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% endif %}
                    
                    <!-- TECFÉE Group Sessions -->
                    {% if past_group_sessions %}
                        <h5 class="mb-3">{{ t('appointments.group_sessions_tecfee') }}</h5>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>{{ t('appointments.date') }}</th>
                                        <th>{{ t('appointments.time') }}</th>
                                        <th>{{ t('appointments.program') }}</th>
                                        <th>{{ t('appointments.module') }}</th>
                                        <th>{{ t('appointments.tutor') }}</th>
                                        <th>{{ t('appointments.participants') }}</th>
                                        <th>{{ t('appointments.status') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for session in past_group_sessions %}
                                        <tr>
                                            <td>{{ session.session_date.strftime('%Y-%m-%d') }}</td>
                                            <td>{{ session.start_time.strftime('%H:%M') }} - {{ session.end_time.strftime('%H:%M') }}</td>
                                            <td>{{ session.program.name }}</td>
                                            <td>
                                                {% if session.module %}
                                                    {{ t('appointments.module_number', number=session.module.module_order) }}: {{ session.module.name }}
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                            <td>{{ session.tutor.first_name }} {{ session.tutor.last_name }}</td>
                                            <td>
                                                <span class="badge bg-info">
                                                    {{ session.current_participants_count }}/{{ session.max_participants }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ 'success' if session.participation.attendance_status == 'attended' else 'danger' if session.participation.attendance_status == 'absent' else 'secondary' }}">
                                                    {{ t('appointments.attendance_' + session.participation.attendance_status) }}
                                                </span>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% endif %}
                {% else %}
                    <p class="text-muted text-center my-4">{{ t('appointments.no_past_appointments') }}</p>
                {% endif %}
            </div>

            <!-- All Appointments Tab -->
            <div class="tab-pane fade" id="all" role="tabpanel" aria-labelledby="all-tab">
                {% if all_appointments or upcoming_group_sessions or past_group_sessions %}
                    <!-- Regular Appointments -->
                    {% if all_appointments %}
                        <h5 class="mb-3">{{ t('appointments.individual_sessions') }}</h5>
                        <div class="table-responsive mb-4">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>{{ t('appointments.date') }}</th>
                                        <th>{{ t('appointments.time') }}</th>
                                        <th>{{ t('appointments.client') }}</th>
                                        <th>{{ t('appointments.tutor') }}</th>
                                        <th>{{ t('appointments.service') }}</th>
                                        <th>{{ t('appointments.status') }}</th>
                                        <th>{{ t('appointments.actions') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for appointment in all_appointments %}
                                        <tr>
                                            <td>{{ appointment.start_time.strftime('%Y-%m-%d') }}</td>
                                            <td>{{ appointment.start_time.strftime('%H:%M') }} - {{ appointment.end_time.strftime('%H:%M') }}</td>
                                            <td>
                                                {% if appointment.client %}
                                                    {{ appointment.client.first_name }} {{ appointment.client.last_name }}
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                            <td>{{ appointment.tutor.first_name }} {{ appointment.tutor.last_name }}</td>
                                            <td>{{ appointment.tutor_service.service.name }}</td>
                                            <td>
                                                <span class="badge bg-{{ 'success' if appointment.status == 'completed' else 'warning' if appointment.status == 'scheduled' else 'danger' }}">
                                                    {{ appointment.status | capitalize }}
                                                </span>
                                            </td>
                                            <td>
                                                <a href="{{ url_for('client.view_appointment', id=appointment.id) }}" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                {% if appointment.status == 'scheduled' and appointment.start_time > now %}
                                                    <a href="{{ url_for('client.cancel_appointment', id=appointment.id) }}" class="btn btn-sm btn-danger" onclick="return confirm('{{ t('appointments.confirm_cancel') }}');">
                                                        <i class="fas fa-times"></i>
                                                    </a>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% endif %}
                    
                    <!-- TECFÉE Group Sessions -->
                    {% if upcoming_group_sessions or past_group_sessions %}
                        <h5 class="mb-3">{{ t('appointments.group_sessions_tecfee') }}</h5>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>{{ t('appointments.date') }}</th>
                                        <th>{{ t('appointments.time') }}</th>
                                        <th>{{ t('appointments.program') }}</th>
                                        <th>{{ t('appointments.module') }}</th>
                                        <th>{{ t('appointments.tutor') }}</th>
                                        <th>{{ t('appointments.participants') }}</th>
                                        <th>{{ t('appointments.status') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for session in upcoming_group_sessions + past_group_sessions %}
                                        <tr>
                                            <td>{{ session.session_date.strftime('%Y-%m-%d') }}</td>
                                            <td>{{ session.start_time.strftime('%H:%M') }} - {{ session.end_time.strftime('%H:%M') }}</td>
                                            <td>{{ session.program.name }}</td>
                                            <td>
                                                {% if session.module %}
                                                    {{ t('appointments.module_number', number=session.module.module_order) }}: {{ session.module.name }}
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                            <td>{{ session.tutor.first_name }} {{ session.tutor.last_name }}</td>
                                            <td>
                                                <span class="badge bg-info">
                                                    {{ session.current_participants_count }}/{{ session.max_participants }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ 'success' if session.participation.attendance_status == 'attended' else 'danger' if session.participation.attendance_status == 'absent' else 'warning' if session.participation.attendance_status == 'registered' else 'secondary' }}">
                                                    {{ t('appointments.attendance_' + session.participation.attendance_status) }}
                                                </span>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% endif %}
                {% else %}
                    <p class="text-muted text-center my-4">{{ t('appointments.no_appointments') }}</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
