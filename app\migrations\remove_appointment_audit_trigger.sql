-- Migration: Remove appointment audit trigger to prevent duplicate entries
-- Date: 2025-01-07
-- Issue: Duplicate audit log entries (one from application, one from trigger)
-- Solution: Remove database trigger and rely on application-level audit logging

-- Drop the trigger that creates duplicate audit entries
DROP TRIGGER IF EXISTS appointment_audit_trigger ON appointments;

-- Drop the associated function
DROP FUNCTION IF EXISTS audit_appointment_changes();

-- Add comment to document the change
COMMENT ON TABLE appointment_audit IS 'Audit trail for appointment changes. Managed by application code, not database triggers.';