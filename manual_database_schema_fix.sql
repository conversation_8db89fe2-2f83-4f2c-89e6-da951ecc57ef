-- Manual Database Schema Fix for Tutors and Dependants Tables
-- Run this script in your PostgreSQL database to fix the column mismatch errors
-- This script is safe to run multiple times

-- ========================================
-- TUTORS TABLE FIXES
-- ========================================

-- Add street_address column (combines civic_number and street)
ALTER TABLE tutors ADD COLUMN IF NOT EXISTS street_address TEXT;

-- Migrate existing data: combine civic_number and street into street_address
UPDATE tutors 
SET street_address = CASE 
    WHEN civic_number IS NOT NULL AND street IS NOT NULL THEN 
        CONCAT(civic_number, ' ', street)
    WHEN civic_number IS NOT NULL THEN 
        civic_number
    WHEN street IS NOT NULL THEN 
        street
    ELSE NULL
END
WHERE street_address IS NULL;

-- Add zip_code column (rename from postal_code)
ALTER TABLE tutors ADD COLUMN IF NOT EXISTS zip_code VARCHAR(20);

-- Migrate existing data from postal_code to zip_code
UPDATE tutors SET zip_code = postal_code WHERE zip_code IS NULL AND postal_code IS NOT NULL;

-- Add birthdate column (rename from date_of_birth)
ALTER TABLE tutors ADD COLUMN IF NOT EXISTS birthdate DATE;

-- Migrate existing data from date_of_birth to birthdate
UPDATE tutors SET birthdate = date_of_birth WHERE birthdate IS NULL AND date_of_birth IS NOT NULL;

-- Add encrypted banking columns
ALTER TABLE tutors ADD COLUMN IF NOT EXISTS bank_transit_number_encrypted VARCHAR(255);
ALTER TABLE tutors ADD COLUMN IF NOT EXISTS bank_institution_number_encrypted VARCHAR(255);

-- Handle bank_account_number_encrypted column
DO $$ 
BEGIN
    -- Check if bank_account_number_encrypted exists
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'tutors' 
                   AND column_name = 'bank_account_number_encrypted') THEN
        -- Check if bank_account_number exists and rename it
        IF EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'tutors' 
                   AND column_name = 'bank_account_number') THEN
            ALTER TABLE tutors RENAME COLUMN bank_account_number TO bank_account_number_encrypted;
            ALTER TABLE tutors ALTER COLUMN bank_account_number_encrypted TYPE VARCHAR(255);
        ELSE
            -- Add the column if neither exists
            ALTER TABLE tutors ADD COLUMN bank_account_number_encrypted VARCHAR(255);
        END IF;
    END IF;
END $$;

-- ========================================
-- DEPENDANTS TABLE FIXES
-- ========================================

-- Add user_id column (optional login account for dependants)
ALTER TABLE dependants ADD COLUMN IF NOT EXISTS user_id INTEGER;

-- Add foreign key constraint for user_id (if users table exists)
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                   WHERE constraint_name = 'dependants_user_id_fkey') THEN
        ALTER TABLE dependants ADD CONSTRAINT dependants_user_id_fkey 
        FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL;
    END IF;
EXCEPTION
    WHEN others THEN
        -- Ignore if users table doesn't exist or constraint already exists
        NULL;
END $$;

-- Add email column
ALTER TABLE dependants ADD COLUMN IF NOT EXISTS email VARCHAR(255);

-- Add password_hash column
ALTER TABLE dependants ADD COLUMN IF NOT EXISTS password_hash VARCHAR(255);

-- Add phone column
ALTER TABLE dependants ADD COLUMN IF NOT EXISTS phone VARCHAR(20);

-- Add address column (legacy field)
ALTER TABLE dependants ADD COLUMN IF NOT EXISTS address TEXT;

-- Add structured address columns
ALTER TABLE dependants ADD COLUMN IF NOT EXISTS civic_number VARCHAR(20);
ALTER TABLE dependants ADD COLUMN IF NOT EXISTS street VARCHAR(255);
ALTER TABLE dependants ADD COLUMN IF NOT EXISTS city VARCHAR(100);
ALTER TABLE dependants ADD COLUMN IF NOT EXISTS postal_code VARCHAR(10);
ALTER TABLE dependants ADD COLUMN IF NOT EXISTS province VARCHAR(50) DEFAULT 'Quebec';
ALTER TABLE dependants ADD COLUMN IF NOT EXISTS country VARCHAR(50) DEFAULT 'Canada';

-- Handle school_grade column (rename from grade_level if it exists)
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'dependants' 
               AND column_name = 'grade_level') 
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns 
                       WHERE table_name = 'dependants' 
                       AND column_name = 'school_grade') THEN
        ALTER TABLE dependants RENAME COLUMN grade_level TO school_grade;
    ELSIF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                      WHERE table_name = 'dependants' 
                      AND column_name = 'school_grade') THEN
        ALTER TABLE dependants ADD COLUMN school_grade VARCHAR(50);
    END IF;
END $$;

-- Add is_active column
ALTER TABLE dependants ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT TRUE;

-- Add preferred_language column
ALTER TABLE dependants ADD COLUMN IF NOT EXISTS preferred_language VARCHAR(2) DEFAULT 'en';

-- ========================================
-- CREATE INDEXES FOR PERFORMANCE
-- ========================================

-- Tutors table indexes
CREATE INDEX IF NOT EXISTS idx_tutors_user_id ON tutors(user_id);
CREATE INDEX IF NOT EXISTS idx_tutors_is_active ON tutors(is_active);
CREATE INDEX IF NOT EXISTS idx_tutors_city ON tutors(city);
CREATE INDEX IF NOT EXISTS idx_tutors_province ON tutors(province);

-- Dependants table indexes
CREATE INDEX IF NOT EXISTS idx_dependants_user_id ON dependants(user_id);
CREATE INDEX IF NOT EXISTS idx_dependants_is_active ON dependants(is_active);
CREATE INDEX IF NOT EXISTS idx_dependants_email ON dependants(email);

-- ========================================
-- VERIFICATION QUERIES
-- ========================================

-- Check tutors table structure
SELECT 'TUTORS TABLE COLUMNS:' as info;
SELECT 
    column_name,
    data_type,
    character_maximum_length,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'tutors' 
AND column_name IN ('street_address', 'zip_code', 'birthdate', 
                    'bank_transit_number_encrypted', 
                    'bank_institution_number_encrypted', 
                    'bank_account_number_encrypted')
ORDER BY column_name;

-- Check dependants table structure
SELECT 'DEPENDANTS TABLE COLUMNS:' as info;
SELECT 
    column_name,
    data_type,
    character_maximum_length,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'dependants' 
AND column_name IN ('user_id', 'email', 'password_hash', 'phone', 'address',
                    'civic_number', 'street', 'city', 'postal_code', 'province', 
                    'country', 'school_grade', 'is_active', 'preferred_language')
ORDER BY column_name;

-- Final success message
SELECT 'DATABASE SCHEMA FIX COMPLETED SUCCESSFULLY!' as status,
       'You can now restart your application.' as next_step;
