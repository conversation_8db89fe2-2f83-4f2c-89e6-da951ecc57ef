#!/usr/bin/env python3
"""
Demo script showing the audit service functionality with realistic data.
This demonstrates how the audit entry display components work with the service.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timezone
from app.services.audit_service import AuditService

# Mock audit entry class for demonstration
class MockAuditEntry:
    def __init__(self, **kwargs):
        self.id = kwargs.get('id', 1)
        self.appointment_id = kwargs.get('appointment_id', 123)
        self.action = kwargs.get('action', 'create')
        self.action_description = kwargs.get('action_description', 'Appointment Created')
        self.timestamp = kwargs.get('timestamp', datetime.now(timezone.utc))
        self.user_id = kwargs.get('user_id', 1)
        self.user_email = kwargs.get('user_email', '<EMAIL>')
        self.user_role = kwargs.get('user_role', 'manager')
        self.old_values = kwargs.get('old_values', {})
        self.new_values = kwargs.get('new_values', {})
        self.changes_summary = kwargs.get('changes_summary', '')
        self.notes = kwargs.get('notes', '')
        self.user = None

def demo_audit_entries():
    """Demonstrate different types of audit entries."""
    print("=== Audit Entry Display Components Demo ===\n")
    
    # 1. CREATE Entry
    print("1. CREATE Entry:")
    create_entry = MockAuditEntry(
        id=1,
        action='create',
        action_description='Appointment Created',
        user_email='<EMAIL>',
        user_role='manager',
        new_values={
            'client_id': 1,
            'tutor_id': 2,
            'status': 'scheduled',
            'start_time': '2024-12-15T19:30:00Z',
            'duration_minutes': 60,
            'notes': 'Initial math tutoring session'
        },
        changes_summary='Initial appointment creation with client John Doe'
    )
    
    formatted_create = AuditService.format_audit_entry_for_display(create_entry)
    print_audit_entry_summary(formatted_create)
    
    # 2. UPDATE Entry
    print("\n2. UPDATE Entry:")
    update_entry = MockAuditEntry(
        id=2,
        action='update',
        action_description='Appointment Updated',
        user_email='<EMAIL>',
        user_role='tutor',
        old_values={
            'status': 'scheduled',
            'notes': 'Initial math tutoring session',
            'start_time': '2024-12-15T19:30:00Z'
        },
        new_values={
            'status': 'confirmed',
            'notes': 'Client confirmed attendance - focus on algebra',
            'start_time': '2024-12-15T20:00:00Z'
        },
        changes_summary='Status confirmed and time adjusted by tutor'
    )
    
    formatted_update = AuditService.format_audit_entry_for_display(update_entry)
    print_audit_entry_summary(formatted_update)
    
    # 3. CANCEL Entry
    print("\n3. CANCEL Entry:")
    cancel_entry = MockAuditEntry(
        id=3,
        action='cancel',
        action_description='Appointment Cancelled',
        user_email='<EMAIL>',
        user_role='client',
        old_values={
            'status': 'confirmed',
            'notes': 'Client confirmed attendance - focus on algebra'
        },
        new_values={
            'status': 'cancelled',
            'notes': 'Cancelled due to family emergency'
        },
        changes_summary='Client cancelled due to scheduling conflict'
    )
    
    formatted_cancel = AuditService.format_audit_entry_for_display(cancel_entry)
    print_audit_entry_summary(formatted_cancel)
    
    # 4. DELETE Entry
    print("\n4. DELETE Entry:")
    delete_entry = MockAuditEntry(
        id=4,
        action='delete',
        action_description='Appointment Deleted',
        user_email='<EMAIL>',
        user_role='manager',
        old_values={
            'client_id': 1,
            'tutor_id': 2,
            'status': 'cancelled',
            'notes': 'Cancelled due to family emergency'
        },
        changes_summary='Appointment permanently removed after cancellation'
    )
    
    formatted_delete = AuditService.format_audit_entry_for_display(delete_entry)
    print_audit_entry_summary(formatted_delete)

def print_audit_entry_summary(entry):
    """Print a summary of a formatted audit entry."""
    print(f"   ID: {entry['id']}")
    print(f"   Action: {entry['action']} ({entry['action_description']})")
    print(f"   User: {entry['user_name']} ({entry['user_role']})")
    print(f"   Summary: {entry['changes_summary']}")
    print(f"   Has Changes: {entry['has_changes']}")
    print(f"   Changes Count: {len(entry['changes_detail'])}")
    
    if entry['changes_detail']:
        print("   Changes:")
        for change in entry['changes_detail']:
            if change['change_type'] == 'created':
                print(f"     + {change['field_display']}: {change['new_value_display']}")
            elif change['change_type'] == 'updated':
                print(f"     ~ {change['field_display']}: {change['old_value_display']} → {change['new_value_display']}")

def demo_field_formatting():
    """Demonstrate field value formatting."""
    print("\n=== Field Value Formatting Demo ===\n")
    
    test_values = [
        ('status', 'scheduled'),
        ('status', 'confirmed'),
        ('status', 'cancelled'),
        ('duration_minutes', 30),
        ('duration_minutes', 60),
        ('duration_minutes', 90),
        ('transport_fee', 15.50),
        ('transport_fee', 0),
        ('notes', ''),
        ('notes', None),
        ('notes', 'Client confirmed attendance'),
        ('start_time', '2024-12-15T19:30:00Z'),
        ('tutor_id', 123),
        ('client_id', 456)
    ]
    
    for field, value in test_values:
        formatted = AuditService._format_field_value(field, value)
        display_name = AuditService._get_field_display_name(field)
        print(f"{display_name}: {repr(value)} → {formatted}")

def demo_action_icons():
    """Demonstrate action icons and colors."""
    print("\n=== Action Icons and Colors Demo ===\n")
    
    actions = ['create', 'update', 'delete', 'cancel', 'read']
    
    for action in actions:
        info = AuditService._get_action_display_info(action)
        print(f"{action.upper()}: {info['icon']} ({info['color']})")

if __name__ == '__main__':
    try:
        demo_audit_entries()
        demo_field_formatting()
        demo_action_icons()
        
        print("\n🎉 Audit entry display components demo completed successfully!")
        print("\nKey Features Implemented:")
        print("✓ Individual audit entry HTML template with before/after comparison")
        print("✓ CSS styling for different action types (create, update, delete, cancel)")
        print("✓ Expandable details section for comprehensive change information")
        print("✓ User information display with proper formatting")
        print("✓ Timestamp display with EST formatting")
        print("✓ Action-specific icons and color coding")
        print("✓ Field value formatting for different data types")
        print("✓ Responsive design with mobile support")
        
    except Exception as e:
        print(f"❌ Demo failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)