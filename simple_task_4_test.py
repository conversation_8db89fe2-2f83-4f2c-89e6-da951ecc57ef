#!/usr/bin/env python3
"""
Simple test to verify Task 4 models are correctly configured.
Tests only the specific models for Task 4.
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

def test_task_4_models():
    """Test Task 4 models in isolation."""
    try:
        # Import only the models we need for Task 4
        from app.models.program import ProgramPricing, GroupSession, GroupSessionParticipant
        
        print("✓ Task 4 models imported successfully")
        
        # Check primary keys
        assert ProgramPricing.__table__.primary_key.columns.keys()[0] == 'pricing_id'
        assert GroupSession.__table__.primary_key.columns.keys()[0] == 'group_session_id'
        assert GroupSessionParticipant.__table__.primary_key.columns.keys()[0] == 'participant_id'
        print("✓ All models use correct primary key names")
        
        # Check foreign key references
        pricing_fks = {fk.column.key: fk.target_fullname for fk in ProgramPricing.__table__.foreign_keys}
        session_fks = {fk.column.key: fk.target_fullname for fk in GroupSession.__table__.foreign_keys}
        participant_fks = {fk.column.key: fk.target_fullname for fk in GroupSessionParticipant.__table__.foreign_keys}
        
        print(f"ProgramPricing foreign keys: {pricing_fks}")
        print(f"GroupSession foreign keys: {session_fks}")
        print(f"GroupSessionParticipant foreign keys: {participant_fks}")
        
        # Verify expected foreign keys
        assert pricing_fks == {'program_id': 'programs.program_id'}
        assert session_fks == {
            'program_id': 'programs.program_id',
            'module_id': 'program_modules.module_id',
            'tutor_id': 'tutors.tutor_id'
        }
        assert participant_fks == {
            'group_session_id': 'group_sessions.group_session_id',
            'enrollment_id': 'enrollments.enrollment_id'
        }
        print("✓ All foreign key references are correct")
        
        return True
        
    except Exception as e:
        print(f"✗ Task 4 model test failed: {e}")
        return False

def main():
    """Run Task 4 model tests."""
    print("Task 4: Group Session and Pricing Models - Verification")
    print("=" * 55)
    
    if test_task_4_models():
        print("=" * 55)
        print("✅ TASK 4 COMPLETED SUCCESSFULLY")
        print()
        print("Summary of changes verified:")
        print("- ProgramPricing model uses 'pricing_id' as primary key")
        print("- GroupSession model uses 'group_session_id' as primary key") 
        print("- GroupSessionParticipant model uses 'participant_id' as primary key")
        print("- All foreign key references use correct primary key names")
        print("- All models can be imported and configured without errors")
        return True
    else:
        print("=" * 55)
        print("❌ TASK 4 HAS ISSUES")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)