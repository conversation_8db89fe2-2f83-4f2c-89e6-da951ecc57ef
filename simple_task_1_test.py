#!/usr/bin/env python3
"""
Simple test for Task 1: Service and TutorService model updates.
"""

import os
import sys

def test_service_and_tutor_service_models():
    """Test Service and TutorService models specifically."""
    print("Testing Service and TutorService models...")
    
    try:
        from app import create_app
        from app.extensions import db
        from app.models.service import Service, TutorService
        
        app = create_app()
        
        with app.app_context():
            # Test Service model
            print("Testing Service model...")
            service = Service(
                name='Test Service',
                description='Test Description',
                default_price=50.00,
                duration_minutes=60
            )
            
            # Check primary key attribute
            assert hasattr(service, 'service_id'), "Service should have service_id attribute"
            print("✅ Service model has correct primary key attribute")
            
            # Test TutorService model
            print("Testing TutorService model...")
            tutor_service = TutorService(
                tutor_id=1,  # This would reference tutors.tutor_id
                service_id=1,  # This would reference services.service_id
                tutor_rate=40.00,
                client_rate=50.00
            )
            
            # Check primary key attribute
            assert hasattr(tutor_service, 'tutor_service_id'), "TutorService should have tutor_service_id attribute"
            print("✅ TutorService model has correct primary key attribute")
            
            # Test foreign key references
            tutor_id_col = None
            service_id_col = None
            
            for col in TutorService.__table__.columns:
                if col.name == 'tutor_id':
                    tutor_id_col = col
                elif col.name == 'service_id':
                    service_id_col = col
            
            # Check tutor_id foreign key
            tutor_fk = list(tutor_id_col.foreign_keys)[0] if tutor_id_col.foreign_keys else None
            assert tutor_fk and str(tutor_fk.column) == 'tutors.tutor_id', f"tutor_id should reference tutors.tutor_id, got {tutor_fk.column if tutor_fk else 'None'}"
            print("✅ TutorService.tutor_id references tutors.tutor_id correctly")
            
            # Check service_id foreign key
            service_fk = list(service_id_col.foreign_keys)[0] if service_id_col.foreign_keys else None
            assert service_fk and str(service_fk.column) == 'services.service_id', f"service_id should reference services.service_id, got {service_fk.column if service_fk else 'None'}"
            print("✅ TutorService.service_id references services.service_id correctly")
            
            print("✅ All Service and TutorService model tests passed!")
            return True
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def main():
    """Run the test."""
    print("=" * 60)
    print("TASK 1: SERVICE AND TUTORSERVICE MODEL UPDATES TEST")
    print("=" * 60)
    
    success = test_service_and_tutor_service_models()
    
    print("=" * 60)
    if success:
        print("✅ Task 1 completed successfully!")
        print("Service and TutorService models have been updated with:")
        print("- Service.service_id as primary key (instead of id)")
        print("- TutorService.tutor_service_id as primary key (instead of id)")
        print("- Correct foreign key references to tutors.tutor_id and services.service_id")
    else:
        print("❌ Task 1 failed!")
    print("=" * 60)
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)