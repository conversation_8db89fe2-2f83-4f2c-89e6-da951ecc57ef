import re

def update_file(file_path):
    with open(file_path, 'r') as file:
        content = file.read()
    
    # Replace user.id with user.user_id
    content = re.sub(r'user\.id\b', 'user.user_id', content)
    
    # Replace client.id with client.client_id
    content = re.sub(r'client\.id\b', 'client.client_id', content)
    
    # Replace Client.id with Client.client_id
    content = re.sub(r'Client\.id\b', 'Client.client_id', content)
    
    # Replace tecfee_program.id with tecfee_program.program_id
    content = re.sub(r'tecfee_program\.id\b', 'tecfee_program.program_id', content)
    
    # Replace enrollment.id with enrollment.enrollment_id
    content = re.sub(r'enrollment\.id\b', 'enrollment.enrollment_id', content)
    
    with open(file_path, 'w') as file:
        file.write(content)
    
    print(f"Updated {file_path}")

# Update the public view file
update_file('app/views/public.py')