#!/usr/bin/env python3
"""
Debug script to check recurring appointments and their generated appointments
"""
import os
import sys

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_appointments():
    """Check the current state of recurring appointments and generated appointments"""
    try:
        from app import create_app
        from app.extensions import db
        from app.models.recurring_appointment import RecurringAppointment
        from app.models.appointment import Appointment
        
        app = create_app()
        
        with app.app_context():
            print("=== DEBUGGING RECURRING APPOINTMENTS ===\n")
            
            # Check recurring appointments
            recurring_appointments = RecurringAppointment.query.all()
            print(f"Found {len(recurring_appointments)} recurring appointments:")
            
            for ra in recurring_appointments:
                print(f"\nRecurring Appointment ID: {ra.id}")
                print(f"  Client ID: {ra.client_id}")
                print(f"  Client Name: {ra.client.first_name} {ra.client.last_name}")
                print(f"  Dependant ID: {ra.dependant_id}")
                if ra.dependant_id and ra.dependant:
                    print(f"  Dependant Name: {ra.dependant.first_name} {ra.dependant.last_name}")
                print(f"  Subject Name: {ra.appointment_subject_name}")
                
                # Check generated appointments
                generated_appointments = Appointment.query.filter_by(recurring_appointment_id=ra.id).all()
                print(f"  Generated Appointments: {len(generated_appointments)}")
                
                for apt in generated_appointments[:3]:  # Show first 3
                    print(f"    Appointment ID: {apt.id}")
                    print(f"    Client ID: {apt.client_id}")
                    print(f"    Dependant ID: {apt.dependant_id}")
                    print(f"    Subject Name: {apt.appointment_subject_name}")
                    print(f"    Start Time: {apt.start_time}")
                    print(f"    ---")
            
            print("\n=== CHECKING APPOINTMENTS WITH 'Unknown' NAMES ===\n")
            
            # Check appointments that might show as "Unknown"
            all_appointments = Appointment.query.limit(10).all()
            for apt in all_appointments:
                subject_name = apt.appointment_subject_name
                if subject_name == "Unknown" or not subject_name or subject_name.strip() == "":
                    print(f"Problem Appointment ID: {apt.id}")
                    print(f"  Client ID: {apt.client_id}")
                    print(f"  Dependant ID: {apt.dependant_id}")
                    print(f"  Client exists: {apt.client is not None}")
                    if apt.client:
                        print(f"  Client Name: {apt.client.first_name} {apt.client.last_name}")
                    print(f"  Dependant exists: {apt.dependant is not None}")
                    if apt.dependant:
                        print(f"  Dependant Name: {apt.dependant.first_name} {apt.dependant.last_name}")
                    print(f"  Subject Name: {subject_name}")
                    print(f"  Recurring ID: {apt.recurring_appointment_id}")
                    print(f"  ---")
            
            return True
            
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    debug_appointments()
