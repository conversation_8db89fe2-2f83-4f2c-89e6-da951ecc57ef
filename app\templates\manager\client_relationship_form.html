{% extends "base.html" %}

{% block title %}{{ title }} - TutorAide Inc.{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">{{ title }}</h2>
        <p class="text-muted">
            {% if relationship %}
                Edit the relationship for <strong>{{ client.first_name }} {{ client.last_name }}</strong>
            {% else %}
                Add a new dependant relationship for <strong>{{ client.first_name }} {{ client.last_name }}</strong>
            {% endif %}
        </p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('manager.view_client', id=client.id) }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left"></i> Back to Client
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-users"></i> 
                    {% if relationship %}Edit{% else %}Add{% endif %} Relationship
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.csrf_token }}
                    
                    <div class="mb-3">
                        <label for="related_client_id" class="form-label">
                            Select Dependant <span class="text-danger">*</span>
                        </label>
                        {{ form.related_client_id(class="form-select" + (" is-invalid" if form.related_client_id.errors else "")) }}
                        {% if form.related_client_id.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.related_client_id.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            Choose the client who will be a dependant of {{ client.first_name }} {{ client.last_name }}
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="relationship_type" class="form-label">
                            Relationship Type <span class="text-danger">*</span>
                        </label>
                        {{ form.relationship_type(class="form-select" + (" is-invalid" if form.relationship_type.errors else "")) }}
                        {% if form.relationship_type.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.relationship_type.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">
                            Specify how {{ client.first_name }} {{ client.last_name }} is related to the selected dependant
                        </div>
                    </div>

                    <div class="mb-4">
                        <div class="form-check">
                            {{ form.is_primary(class="form-check-input") }}
                            <label class="form-check-label" for="{{ form.is_primary.id }}">
                                {{ form.is_primary.label.text }}
                            </label>
                            <div class="form-text">
                                Check this if {{ client.first_name }} {{ client.last_name }} is the primary contact for this dependant
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ url_for('manager.view_client', id=client.id) }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> 
                            {% if relationship %}Update{% else %}Add{% endif %} Relationship
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

{% if relationship %}
<div class="row mt-4">
    <div class="col-md-8 mx-auto">
        <div class="card border-danger">
            <div class="card-header bg-danger text-white">
                <h6 class="mb-0">
                    <i class="fas fa-exclamation-triangle"></i> Danger Zone
                </h6>
            </div>
            <div class="card-body">
                <p class="text-muted mb-3">
                    Removing this relationship will permanently delete the connection between these clients.
                    This action cannot be undone.
                </p>
                <form method="POST" action="{{ url_for('manager.remove_client_relationship', id=client.id, relationship_id=relationship.id) }}" 
                      onsubmit="return confirm('Are you sure you want to remove this relationship? This action cannot be undone.')">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Remove Relationship
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add some interactivity if needed
    const relationshipSelect = document.getElementById('{{ form.relationship_type.id }}');
    const primaryCheckbox = document.getElementById('{{ form.is_primary.id }}');
    
    // Auto-suggest primary relationship for certain types
    relationshipSelect.addEventListener('change', function() {
        if (this.value === 'child' || this.value === 'student') {
            primaryCheckbox.checked = true;
        }
    });
});
</script>
{% endblock %}
