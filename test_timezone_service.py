#!/usr/bin/env python3
"""
Unit tests for TimezoneService
Tests timezone conversion accuracy, DST handling, and formatting methods.
"""

from datetime import datetime, timezone
from app import create_app
from app.services.timezone_service import TimezoneService

# Simple assertion helper for tests without pytest
def assert_equal(actual, expected, message=""):
    if actual != expected:
        raise AssertionError(f"Expected {expected}, got {actual}. {message}")

def assert_true(condition, message=""):
    if not condition:
        raise AssertionError(f"Expected True, got False. {message}")

def assert_false(condition, message=""):
    if condition:
        raise AssertionError(f"Expected False, got True. {message}")

def assert_in(item, container, message=""):
    if item not in container:
        raise AssertionError(f"Expected {item} to be in {container}. {message}")

def assert_raises(exception_type, func, *args, **kwargs):
    try:
        func(*args, **kwargs)
        raise AssertionError(f"Expected {exception_type.__name__} to be raised")
    except exception_type:
        pass  # Expected exception was raised


class TestTimezoneService:
    """Test suite for TimezoneService functionality."""
    
    @classmethod
    def setup_class(cls):
        """Set up test application context."""
        cls.app = create_app()
        cls.app_context = cls.app.app_context()
        cls.app_context.push()
    
    @classmethod
    def teardown_class(cls):
        """Clean up test application context."""
        cls.app_context.pop()
    
    def test_utc_to_eastern_conversion_est(self):
        """Test UTC to Eastern conversion during EST period (winter)."""
        # January 15, 2024 12:00 PM UTC (EST period)
        utc_dt = datetime(2024, 1, 15, 12, 0, 0, tzinfo=timezone.utc)
        eastern_dt = TimezoneService.utc_to_eastern(utc_dt)
        
        # Should be 7:00 AM EST (UTC-5)
        assert eastern_dt.hour == 7
        assert eastern_dt.minute == 0
        assert eastern_dt.day == 15
        assert not TimezoneService.is_dst(utc_dt)  # Should not be DST
    
    def test_utc_to_eastern_conversion_edt(self):
        """Test UTC to Eastern conversion during EDT period (summer)."""
        # July 15, 2024 12:00 PM UTC (EDT period)
        utc_dt = datetime(2024, 7, 15, 12, 0, 0, tzinfo=timezone.utc)
        eastern_dt = TimezoneService.utc_to_eastern(utc_dt)
        
        # Should be 8:00 AM EDT (UTC-4)
        assert eastern_dt.hour == 8
        assert eastern_dt.minute == 0
        assert eastern_dt.day == 15
        assert TimezoneService.is_dst(utc_dt)  # Should be DST
    
    def test_utc_to_eastern_naive_datetime(self):
        """Test UTC to Eastern conversion with naive datetime (assumes UTC)."""
        # Naive datetime - should be treated as UTC
        naive_dt = datetime(2024, 1, 15, 12, 0, 0)
        eastern_dt = TimezoneService.utc_to_eastern(naive_dt)
        
        # Should be 7:00 AM EST
        assert eastern_dt.hour == 7
        assert eastern_dt.minute == 0
    
    def test_eastern_to_utc_conversion(self):
        """Test Eastern to UTC conversion."""
        # Create Eastern time datetime
        eastern_tz = TimezoneService.get_eastern_timezone()
        eastern_dt = eastern_tz.localize(datetime(2024, 1, 15, 7, 0, 0))
        
        utc_dt = TimezoneService.eastern_to_utc(eastern_dt)
        
        # Should be 12:00 PM UTC
        assert utc_dt.hour == 12
        assert utc_dt.minute == 0
        assert utc_dt.tzinfo == timezone.utc
    
    def test_eastern_to_utc_naive_datetime(self):
        """Test Eastern to UTC conversion with naive datetime."""
        # Naive datetime - should be treated as Eastern
        naive_dt = datetime(2024, 1, 15, 7, 0, 0)
        utc_dt = TimezoneService.eastern_to_utc(naive_dt)
        
        # Should be 12:00 PM UTC
        assert utc_dt.hour == 12
        assert utc_dt.minute == 0
        assert utc_dt.tzinfo == timezone.utc
    
    def test_format_eastern_datetime_est(self):
        """Test formatting UTC datetime as Eastern time string during EST."""
        utc_dt = datetime(2024, 1, 15, 17, 30, 0, tzinfo=timezone.utc)
        formatted = TimezoneService.format_eastern_datetime(utc_dt)
        
        # Should be 12:30 PM EST
        assert "12:30 PM EST" in formatted
        assert "2024-01-15" in formatted
    
    def test_format_eastern_datetime_edt(self):
        """Test formatting UTC datetime as Eastern time string during EDT."""
        utc_dt = datetime(2024, 7, 15, 16, 30, 0, tzinfo=timezone.utc)
        formatted = TimezoneService.format_eastern_datetime(utc_dt)
        
        # Should be 12:30 PM EDT
        assert "12:30 PM EDT" in formatted
        assert "2024-07-15" in formatted
    
    def test_format_eastern_datetime_custom_format(self):
        """Test formatting with custom format string."""
        utc_dt = datetime(2024, 1, 15, 17, 30, 0, tzinfo=timezone.utc)
        formatted = TimezoneService.format_eastern_datetime(utc_dt, '%B %d, %Y at %I:%M %p EST')
        
        assert "January 15, 2024 at 12:30 PM EST" == formatted
    
    def test_format_eastern_date(self):
        """Test formatting UTC datetime as Eastern date string."""
        utc_dt = datetime(2024, 1, 15, 5, 30, 0, tzinfo=timezone.utc)  # Early UTC time
        formatted = TimezoneService.format_eastern_date(utc_dt)
        
        # Should be same date in Eastern (00:30 EST)
        assert formatted == "2024-01-15"
    
    def test_format_eastern_date_crosses_midnight(self):
        """Test date formatting when UTC to Eastern conversion crosses midnight."""
        utc_dt = datetime(2024, 1, 16, 3, 30, 0, tzinfo=timezone.utc)  # 3:30 AM UTC
        formatted = TimezoneService.format_eastern_date(utc_dt)
        
        # Should be previous date in Eastern (10:30 PM EST on Jan 15)
        assert formatted == "2024-01-15"
    
    def test_format_eastern_time(self):
        """Test formatting UTC datetime as Eastern time string."""
        utc_dt = datetime(2024, 1, 15, 17, 30, 0, tzinfo=timezone.utc)
        formatted = TimezoneService.format_eastern_time(utc_dt)
        
        assert formatted == "12:30 PM"
    
    def test_get_current_eastern_time(self):
        """Test getting current Eastern time."""
        eastern_now = TimezoneService.get_current_eastern_time()
        
        assert eastern_now.tzinfo is not None
        assert isinstance(eastern_now, datetime)
    
    def test_dst_detection_winter(self):
        """Test DST detection during winter (EST)."""
        utc_dt = datetime(2024, 1, 15, 12, 0, 0, tzinfo=timezone.utc)
        assert not TimezoneService.is_dst(utc_dt)
    
    def test_dst_detection_summer(self):
        """Test DST detection during summer (EDT)."""
        utc_dt = datetime(2024, 7, 15, 12, 0, 0, tzinfo=timezone.utc)
        assert TimezoneService.is_dst(utc_dt)
    
    def test_dst_detection_current_time(self):
        """Test DST detection for current time."""
        is_dst = TimezoneService.is_dst()
        assert isinstance(is_dst, bool)
    
    def test_timezone_offset_est(self):
        """Test timezone offset calculation during EST."""
        utc_dt = datetime(2024, 1, 15, 12, 0, 0, tzinfo=timezone.utc)
        offset = TimezoneService.get_timezone_offset(utc_dt)
        
        assert offset == -5  # EST is UTC-5
    
    def test_timezone_offset_edt(self):
        """Test timezone offset calculation during EDT."""
        utc_dt = datetime(2024, 7, 15, 12, 0, 0, tzinfo=timezone.utc)
        offset = TimezoneService.get_timezone_offset(utc_dt)
        
        assert offset == -4  # EDT is UTC-4
    
    def test_format_for_display_short(self):
        """Test short format for display."""
        utc_dt = datetime(2024, 1, 15, 17, 30, 0, tzinfo=timezone.utc)
        formatted = TimezoneService.format_for_display(utc_dt)
        
        assert "Jan 15, 2024 12:30 PM EST" == formatted
    
    def test_format_for_display_long(self):
        """Test long format for display."""
        utc_dt = datetime(2024, 1, 15, 17, 30, 0, tzinfo=timezone.utc)
        formatted = TimezoneService.format_for_display(utc_dt, long_format=True)
        
        assert "Monday, January 15, 2024 at 12:30 PM EST" == formatted
    
    def test_format_for_display_no_timezone(self):
        """Test display format without timezone."""
        utc_dt = datetime(2024, 1, 15, 17, 30, 0, tzinfo=timezone.utc)
        formatted = TimezoneService.format_for_display(utc_dt, include_timezone=False)
        
        assert "Jan 15, 2024 12:30 PM" == formatted
    
    def test_format_empty_datetime(self):
        """Test formatting methods with None/empty datetime."""
        assert TimezoneService.format_eastern_datetime(None) == ""
        assert TimezoneService.format_eastern_date(None) == ""
        assert TimezoneService.format_eastern_time(None) == ""
        assert TimezoneService.format_for_display(None) == ""
    
    def test_invalid_input_types(self):
        """Test error handling for invalid input types."""
        assert_raises(ValueError, TimezoneService.utc_to_eastern, "not a datetime")
        assert_raises(ValueError, TimezoneService.eastern_to_utc, "not a datetime")
    
    def test_dst_transition_spring_forward(self):
        """Test behavior during spring DST transition (2 AM -> 3 AM)."""
        # March 10, 2024 - DST begins at 2:00 AM EST (becomes 3:00 AM EDT)
        # Test time just before transition
        utc_dt = datetime(2024, 3, 10, 6, 30, 0, tzinfo=timezone.utc)  # 1:30 AM EST
        eastern_dt = TimezoneService.utc_to_eastern(utc_dt)
        assert eastern_dt.hour == 1
        assert not TimezoneService.is_dst(utc_dt)
        
        # Test time just after transition
        utc_dt = datetime(2024, 3, 10, 7, 30, 0, tzinfo=timezone.utc)  # 3:30 AM EDT
        eastern_dt = TimezoneService.utc_to_eastern(utc_dt)
        assert eastern_dt.hour == 3
        assert TimezoneService.is_dst(utc_dt)
    
    def test_dst_transition_fall_back(self):
        """Test behavior during fall DST transition (2 AM -> 1 AM)."""
        # November 3, 2024 - DST ends at 2:00 AM EDT (becomes 1:00 AM EST)
        # Test time just before transition
        utc_dt = datetime(2024, 11, 3, 5, 30, 0, tzinfo=timezone.utc)  # 1:30 AM EDT
        eastern_dt = TimezoneService.utc_to_eastern(utc_dt)
        assert eastern_dt.hour == 1
        assert TimezoneService.is_dst(utc_dt)
        
        # Test time just after transition
        utc_dt = datetime(2024, 11, 3, 7, 30, 0, tzinfo=timezone.utc)  # 2:30 AM EST
        eastern_dt = TimezoneService.utc_to_eastern(utc_dt)
        assert eastern_dt.hour == 2
        assert not TimezoneService.is_dst(utc_dt)


def run_tests():
    """Run the timezone service tests."""
    print("Running TimezoneService tests...")
    
    # Create test instance
    test_suite = TestTimezoneService()
    test_suite.setup_class()
    
    try:
        # Run all test methods
        test_methods = [method for method in dir(test_suite) if method.startswith('test_')]
        passed = 0
        failed = 0
        
        for test_method in test_methods:
            try:
                print(f"Running {test_method}...")
                getattr(test_suite, test_method)()
                print(f"✓ {test_method} passed")
                passed += 1
            except Exception as e:
                print(f"✗ {test_method} failed: {str(e)}")
                failed += 1
        
        print(f"\nTest Results: {passed} passed, {failed} failed")
        
        if failed == 0:
            print("🎉 All timezone service tests passed!")
        else:
            print(f"❌ {failed} tests failed")
            
    finally:
        test_suite.teardown_class()


if __name__ == '__main__':
    run_tests()