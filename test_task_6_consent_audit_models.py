#!/usr/bin/env python3
"""
Test script for Task 6: Update Consent and Audit Models
Tests the updated ClientConsent and AppointmentAudit models with new primary key names.
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

def test_consent_audit_models():
    """Test that ClientConsent and AppointmentAudit models work with new primary key names."""
    
    try:
        print("Testing ClientConsent and AppointmentAudit models...")
        
        # Test model imports
        from app.models.client_consent import ClientConsent
        from app.models.appointment_audit import AppointmentAudit
        from app.models.client import Client
        from app.models.user import User
        print("✓ All models imported successfully")
        
        # Test ClientConsent model structure
        print("\n1. Testing ClientConsent model structure...")
        
        # Check primary key
        consent_pk = ClientConsent.__table__.primary_key.columns.keys()
        assert 'consent_id' in consent_pk, f"Expected 'consent_id' as primary key, got {consent_pk}"
        print("✓ ClientConsent uses 'consent_id' as primary key")
        
        # Check foreign key reference
        consent_fks = [fk.target_fullname for fk in ClientConsent.__table__.foreign_keys]
        assert 'clients.client_id' in consent_fks, f"Expected foreign key to 'clients.client_id', got {consent_fks}"
        print("✓ ClientConsent foreign key references 'clients.client_id'")
        
        # Test AppointmentAudit model structure
        print("\n2. Testing AppointmentAudit model structure...")
        
        # Check primary key
        audit_pk = AppointmentAudit.__table__.primary_key.columns.keys()
        assert 'audit_id' in audit_pk, f"Expected 'audit_id' as primary key, got {audit_pk}"
        print("✓ AppointmentAudit uses 'audit_id' as primary key")
        
        # Check foreign key reference
        audit_fks = [fk.target_fullname for fk in AppointmentAudit.__table__.foreign_keys]
        assert 'users.user_id' in audit_fks, f"Expected foreign key to 'users.user_id', got {audit_fks}"
        print("✓ AppointmentAudit foreign key references 'users.user_id'")
        
        # Test model instantiation
        print("\n3. Testing model instantiation...")
        
        # Test ClientConsent instantiation
        consent = ClientConsent(
            client_id=1,
            tos_version="1.0",
            optional_consent=True
        )
        assert hasattr(consent, 'consent_id'), "ClientConsent should have consent_id attribute"
        print("✓ ClientConsent can be instantiated with new primary key")
        
        # Test AppointmentAudit instantiation
        audit = AppointmentAudit(
            appointment_id=1,
            action='create',
            user_id=1
        )
        assert hasattr(audit, 'audit_id'), "AppointmentAudit should have audit_id attribute"
        print("✓ AppointmentAudit can be instantiated with new primary key")
        
        # Test __repr__ methods
        print("\n4. Testing __repr__ methods...")
        
        consent_repr = repr(consent)
        assert 'ClientConsent' in consent_repr, "ClientConsent __repr__ should contain class name"
        print(f"✓ ClientConsent __repr__: {consent_repr}")
        
        audit_repr = repr(audit)
        assert 'AppointmentAudit' in audit_repr, "AppointmentAudit __repr__ should contain class name"
        assert 'audit_id' not in audit_repr or str(audit.audit_id) in audit_repr, "AppointmentAudit __repr__ should handle audit_id correctly"
        print(f"✓ AppointmentAudit __repr__: {audit_repr}")
        
        # Test ClientConsent class methods
        print("\n5. Testing ClientConsent class methods...")
        
        # Test that class methods exist
        assert hasattr(ClientConsent, 'create_or_update'), "ClientConsent should have create_or_update method"
        assert hasattr(ClientConsent, 'has_mandatory_consent'), "ClientConsent should have has_mandatory_consent method"
        assert hasattr(ClientConsent, 'has_optional_consent'), "ClientConsent should have has_optional_consent method"
        print("✓ All ClientConsent class methods exist")
        
        # Test AppointmentAudit class methods
        print("\n6. Testing AppointmentAudit class methods...")
        
        # Test that class methods exist
        assert hasattr(AppointmentAudit, 'log_action'), "AppointmentAudit should have log_action method"
        print("✓ AppointmentAudit.log_action method exists")
        
        # Test AppointmentAudit properties
        assert hasattr(audit, 'action_description'), "AppointmentAudit should have action_description property"
        assert hasattr(audit, 'changes_summary'), "AppointmentAudit should have changes_summary property"
        print("✓ AppointmentAudit properties exist")
        
        # Test property values
        assert audit.action_description == 'Created', f"Expected 'Created', got '{audit.action_description}'"
        assert audit.changes_summary == 'Appointment created', f"Expected 'Appointment created', got '{audit.changes_summary}'"
        print("✓ AppointmentAudit properties return correct values")
        
        print("\n✅ All tests passed! Both models are correctly updated with new primary key names.")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_consent_audit_models()
    sys.exit(0 if success else 1)