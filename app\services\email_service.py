# app/services/email_service.py
from flask import current_app, url_for, render_template
from flask_mail import Message
from app.extensions import mail
import logging

class EmailService:
    """Service for sending emails including verification emails."""
    
    @staticmethod
    def send_verification_email(user, verification_token):
        """Send email verification email to user."""
        try:
            # Generate verification URL
            verification_url = url_for('auth.verify_email', 
                                     token=verification_token, 
                                     _external=True)
            
            # Create email message
            msg = Message(
                subject='Vérifiez votre adresse courriel - TutorAide Inc.',
                sender=current_app.config.get('MAIL_DEFAULT_SENDER', '<EMAIL>'),
                recipients=[user.email]
            )
            
            # Email body (HTML)
            msg.html = render_template('emails/email_verification.html',
                                     user=user,
                                     verification_url=verification_url)
            
            # Email body (Plain text fallback)
            msg.body = f"""
Bon<PERSON>r,

Merci de vous être inscrit au programme TECFÉE de TutorAide Inc.

Pour compléter votre inscription et procéder à la sélection des sessions, 
veuillez vérifier votre adresse courriel en cliquant sur le lien suivant :

{verification_url}

Ce lien est valide pendant 24 heures.

Si vous n'avez pas créé ce compte, vous pouvez ignorer ce courriel.

Cordialement,
L'équipe TutorAide Inc.

---
TutorAide Inc.
2958 Guy-Hoffmann, Saint-Laurent, QC, H4R 2R1
Téléphone: (*************
Courriel: <EMAIL>
Web: www.tutoraide.ca
            """
            
            # Send email
            mail.send(msg)
            current_app.logger.info(f"Verification email sent to {user.email}")
            return True
            
        except Exception as e:
            current_app.logger.error(f"Failed to send verification email to {user.email}: {str(e)}")
            return False
    
    @staticmethod
    def send_welcome_email(user):
        """Send welcome email after successful verification."""
        try:
            # Create email message
            msg = Message(
                subject='Bienvenue au programme TECFÉE - TutorAide Inc.',
                sender=current_app.config.get('MAIL_DEFAULT_SENDER', '<EMAIL>'),
                recipients=[user.email]
            )
            
            # Email body (HTML)
            msg.html = render_template('emails/welcome_tecfee.html', user=user)
            
            # Email body (Plain text fallback)
            msg.body = f"""
Bonjour {user.client.first_name if user.client else ''},

Félicitations! Votre adresse courriel a été vérifiée avec succès.

Vous pouvez maintenant procéder à la sélection de vos sessions TECFÉE 
et finaliser votre inscription.

Connectez-vous à votre compte pour continuer :
{url_for('auth.login', _external=True)}

Merci de faire confiance à TutorAide Inc. pour votre préparation au TECFÉE.

Cordialement,
L'équipe TutorAide Inc.

---
TutorAide Inc.
2958 Guy-Hoffmann, Saint-Laurent, QC, H4R 2R1
Téléphone: (*************
Courriel: <EMAIL>
Web: www.tutoraide.ca
            """
            
            # Send email
            mail.send(msg)
            current_app.logger.info(f"Welcome email sent to {user.email}")
            return True
            
        except Exception as e:
            current_app.logger.error(f"Failed to send welcome email to {user.email}: {str(e)}")
            return False
