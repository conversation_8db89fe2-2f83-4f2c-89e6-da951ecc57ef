# app/models/notification.py
from datetime import datetime
from app.extensions import db

class Notification(db.Model):
    __tablename__ = 'notifications'
    
    notification_id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>ey('users.user_id', ondelete='CASCADE'), nullable=False)
    message = db.Column(db.Text, nullable=False)
    category = db.Column(db.String(50), nullable=False)  # appointment, time_off, system, etc.
    related_id = db.Column(db.Integer, nullable=True)  # ID of related object (appointment, time_off, etc.)
    is_read = db.Column(db.<PERSON>, default=False)
    insert_date = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationship
    user = db.relationship('User', backref='notifications')
    
    def __repr__(self):
        return f'<Notification id={self.notification_id} user_id={self.user_id} category={self.category}>'
    
    @staticmethod
    def create_notification(user_id, message, category, related_id=None):
        """Create a new notification."""
        notification = Notification(
            user_id=user_id,
            message=message,
            category=category,
            related_id=related_id
        )
        db.session.add(notification)
        db.session.commit()
        return notification
    
    @staticmethod
    def mark_as_read(notification_id):
        """Mark a notification as read."""
        notification = Notification.query.get(notification_id)
        if notification:
            notification.is_read = True
            db.session.commit()
            return True
        return False
    
    @staticmethod
    def mark_all_as_read(user_id, category=None):
        """Mark all notifications for a user as read."""
        query = Notification.query.filter_by(user_id=user_id, is_read=False)
        if category:
            query = query.filter_by(category=category)
        
        notifications = query.all()
        for notification in notifications:
            notification.is_read = True
        
        db.session.commit()
        return len(notifications)
