# app/services/google_auth_service.py
import os
import json
import secrets
from flask import current_app, url_for, session, request

# Optional Google imports - gracefully handle missing dependencies
try:
    from google.auth.transport import requests as google_requests
    from google.oauth2 import id_token
    from google_auth_oauthlib.flow import Flow
    GOOGLE_AUTH_AVAILABLE = True
except ImportError:
    GOOGLE_AUTH_AVAILABLE = False
    # Note: This warning will only show when the service is actually used
    # We'll log it in the is_available() method instead

class GoogleAuthService:
    """Service for handling Google OAuth authentication."""

    @staticmethod
    def is_available():
        """Check if Google authentication is available."""
        if not GOOGLE_AUTH_AVAILABLE:
            current_app.logger.warning("Google authentication libraries not available. Install: pip install google-auth google-auth-oauthlib")
            return False

        if not current_app.config.get('GOOGLE_CLIENT_ID') or not current_app.config.get('GOOGLE_CLIENT_SECRET'):
            current_app.logger.info("Google OAuth not configured. Set GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET environment variables.")
            return False

        return True

    @staticmethod
    def get_google_client_config():
        """Get Google OAuth client configuration."""
        if not GoogleAuthService.is_available():
            return None

        return {
            "web": {
                "client_id": current_app.config.get('GOOGLE_CLIENT_ID'),
                "client_secret": current_app.config.get('GOOGLE_CLIENT_SECRET'),
                "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                "token_uri": "https://oauth2.googleapis.com/token",
                "auth_provider_x509_cert_url": "https://www.googleapis.com/oauth2/v1/certs",
                "redirect_uris": [url_for('auth.google_callback', _external=True)]
            }
        }

    @staticmethod
    def create_flow(redirect_uri=None):
        """Create Google OAuth flow."""
        if not GoogleAuthService.is_available():
            return None

        client_config = GoogleAuthService.get_google_client_config()
        if not client_config:
            return None

        if not redirect_uri:
            redirect_uri = url_for('auth.google_callback', _external=True)

        flow = Flow.from_client_config(
            client_config,
            scopes=[
                'openid',
                'email',
                'profile'
            ]
        )
        flow.redirect_uri = redirect_uri
        return flow

    @staticmethod
    def get_authorization_url(state=None):
        """Get Google authorization URL."""
        if not GoogleAuthService.is_available():
            return None

        flow = GoogleAuthService.create_flow()
        if not flow:
            return None

        # Generate state parameter for security
        if not state:
            state = secrets.token_urlsafe(32)
            session['google_auth_state'] = state

        authorization_url, _ = flow.authorization_url(
            access_type='offline',
            include_granted_scopes='true',
            state=state
        )

        return authorization_url

    @staticmethod
    def handle_callback(authorization_code, state):
        """Handle Google OAuth callback."""
        if not GoogleAuthService.is_available():
            return False, None, "Google authentication not available"

        try:
            # Verify state parameter
            if state != session.get('google_auth_state'):
                return False, None, "Invalid state parameter"

            # Clear state from session
            session.pop('google_auth_state', None)

            # Create flow and fetch token
            flow = GoogleAuthService.create_flow()
            flow.fetch_token(authorization_response=request.url)

            # Get user info from Google
            credentials = flow.credentials
            request_session = google_requests.Request()

            # Verify the token and get user info
            id_info = id_token.verify_oauth2_token(
                credentials.id_token,
                request_session,
                current_app.config.get('GOOGLE_CLIENT_ID')
            )

            # Extract user information
            user_info = {
                'google_id': id_info.get('sub'),
                'email': id_info.get('email'),
                'email_verified': id_info.get('email_verified', False),
                'first_name': id_info.get('given_name', ''),
                'last_name': id_info.get('family_name', ''),
                'full_name': id_info.get('name', ''),
                'picture': id_info.get('picture', ''),
                'locale': id_info.get('locale', 'en')
            }

            return True, user_info, "Success"

        except Exception as e:
            current_app.logger.error(f"Google OAuth error: {str(e)}")
            return False, None, f"Authentication failed: {str(e)}"

    @staticmethod
    def verify_token(token):
        """Verify Google ID token."""
        try:
            request_session = google_requests.Request()
            id_info = id_token.verify_oauth2_token(
                token,
                request_session,
                current_app.config.get('GOOGLE_CLIENT_ID')
            )
            return True, id_info
        except Exception as e:
            current_app.logger.error(f"Token verification error: {str(e)}")
            return False, None
