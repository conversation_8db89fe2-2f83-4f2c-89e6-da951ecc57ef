# app/models/program.py
from datetime import datetime, timedelta
from app.extensions import db

class Program(db.Model):
    """Model for tutoring programs offered by the organization."""
    __tablename__ = 'programs'

    program_id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    code = db.Column(db.String(50), nullable=False, unique=True)
    description = db.Column(db.Text, nullable=True)
    is_active = db.Column(db.<PERSON><PERSON><PERSON>, default=True)
    total_sessions = db.Column(db.Integer, nullable=False, default=1)
    session_duration = db.Column(db.Integer, nullable=False, default=60)  # minutes
    min_participants = db.Column(db.Integer, nullable=False, default=1)
    max_participants = db.Column(db.Integer, nullable=False, default=10)
    created_date = db.Column(db.DateTime, default=datetime.utcnow)
    insert_date = db.Column(db.DateTime, default=datetime.utcnow)
    modification_date = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    modules = db.relationship('ProgramModule', backref='program', lazy='dynamic',
                             cascade='all, delete-orphan', order_by='ProgramModule.module_order')
    enrollments = db.relationship('Enrollment', backref='program', lazy='dynamic')

    def __repr__(self):
        return f'<Program {self.program_id} {self.name}>'

    @property
    def active_enrollments_count(self):
        """Get the count of active enrollments in this program."""
        return self.enrollments.filter_by(status='active').count()

    @property
    def total_duration_minutes(self):
        """Calculate the total duration of all modules in the program."""
        return sum(module.duration_minutes for module in self.modules)

    @property
    def total_modules(self):
        """Get the total count of active modules in this program."""
        return self.modules.filter_by(is_active=True).count()

    def get_module_by_order(self, module_order):
        """Get a specific module by its order."""
        return self.modules.filter_by(module_order=module_order).first()


class ProgramModule(db.Model):
    """Model for individual modules/components within a program."""
    __tablename__ = 'program_modules'

    module_id = db.Column(db.Integer, primary_key=True)
    program_id = db.Column(db.Integer, db.ForeignKey('programs.program_id', ondelete='CASCADE'), nullable=False)
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text, nullable=True)
    module_order = db.Column(db.Integer, nullable=False)  # Order within program
    duration_minutes = db.Column(db.Integer, nullable=False, default=60)
    learning_objectives = db.Column(db.Text, nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    insert_date = db.Column(db.DateTime, default=datetime.utcnow)
    modification_date = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    module_progress = db.relationship('ModuleProgress', backref='module', lazy='dynamic')

    __table_args__ = (
        db.UniqueConstraint('program_id', 'module_order', name='program_module_unique'),
    )

    def __repr__(self):
        return f'<ProgramModule {self.module_id} {self.name} (Program {self.program_id}, #{self.module_order})>'

    @property
    def completion_rate(self):
        """Calculate the percentage of enrollments that have completed this module."""
        total = self.module_progress.count()
        if total == 0:
            return 0
        completed = self.module_progress.filter_by(status='completed').count()
        return (completed / total) * 100

    @property
    def module_number(self):
        """Alias for module_order for template compatibility."""
        return self.module_order


class Enrollment(db.Model):
    """Model for tracking client enrollment in programs."""
    __tablename__ = 'enrollments'

    enrollment_id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('clients.client_id', ondelete='CASCADE'), nullable=False)
    program_id = db.Column(db.Integer, db.ForeignKey('programs.program_id', ondelete='CASCADE'), nullable=False)
    pricing_type = db.Column(db.String(50), nullable=False)
    enrollment_date = db.Column(db.DateTime, default=datetime.utcnow)
    start_date = db.Column(db.Date, nullable=True)
    end_date = db.Column(db.Date, nullable=True)
    status = db.Column(db.String(20), nullable=False, default='active')
    total_sessions = db.Column(db.Integer, nullable=False, default=0)
    completed_sessions = db.Column(db.Integer, nullable=False, default=0)
    payment_status = db.Column(db.String(20), nullable=False, default='pending')
    total_amount = db.Column(db.Numeric(10, 2), nullable=True)
    paid_amount = db.Column(db.Numeric(10, 2), nullable=True, default=0.00)
    notes = db.Column(db.Text, nullable=True)
    insert_date = db.Column(db.DateTime, default=datetime.utcnow)
    modification_date = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # New fields for single-step enrollment
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    expires_at = db.Column(db.DateTime, nullable=True)
    stripe_payment_intent = db.Column(db.String(255), nullable=True)
    stripe_checkout_session_id = db.Column(db.String(255), nullable=True)
    recovery_token = db.Column(db.String(255), nullable=True)
    recovery_token_expires = db.Column(db.DateTime, nullable=True)

    # Relationships
    module_progress = db.relationship('ModuleProgress', backref='enrollment', lazy='dynamic',
                                     cascade='all, delete-orphan')

    __table_args__ = (
        db.UniqueConstraint('client_id', 'program_id', 'start_date', name='enrollment_unique'),
    )

    def __repr__(self):
        return f'<Enrollment {self.enrollment_id} Client {self.client_id} in Program {self.program_id}>'

    @property
    def is_active(self):
        """Check if the enrollment is currently active."""
        return self.status == 'active'

    @property
    def is_completed(self):
        """Check if the enrollment is completed."""
        return self.status == 'completed'
    
    @property
    def is_expired(self):
        """Check if a pending enrollment has expired."""
        if self.status != 'pending' or not self.expires_at:
            return False
        return datetime.utcnow() > self.expires_at
    
    def set_expiration(self, hours=24):
        """Set expiration time for pending enrollment."""
        if self.status == 'pending':
            self.expires_at = datetime.utcnow() + timedelta(hours=hours)
    
    def generate_recovery_token(self):
        """Generate a recovery token for incomplete enrollment."""
        import secrets
        self.recovery_token = secrets.token_urlsafe(32)
        self.recovery_token_expires = datetime.utcnow() + timedelta(hours=48)
        return self.recovery_token

    def update_completion_percentage(self):
        """Update the completion percentage based on module progress."""
        total_modules = self.program.modules.filter_by(is_required=True).count()
        if total_modules == 0:
            self.completion_percentage = 0
            return

        completed_modules = self.module_progress.join(ProgramModule).filter(
            ModuleProgress.status == 'completed',
            ProgramModule.is_required == True
        ).count()

        self.completion_percentage = (completed_modules / total_modules) * 100
        db.session.commit()


class ModuleProgress(db.Model):
    """Model for tracking client progress through program modules."""
    __tablename__ = 'module_progress'

    progress_id = db.Column(db.Integer, primary_key=True)
    enrollment_id = db.Column(db.Integer, db.ForeignKey('enrollments.enrollment_id', ondelete='CASCADE'), nullable=False)
    module_id = db.Column(db.Integer, db.ForeignKey('program_modules.module_id', ondelete='CASCADE'), nullable=False)
    status = db.Column(db.String(20), nullable=False, default='not_started')
    start_date = db.Column(db.DateTime, nullable=True)
    completion_date = db.Column(db.DateTime, nullable=True)
    score = db.Column(db.Numeric(5, 2), nullable=True)
    notes = db.Column(db.Text, nullable=True)
    insert_date = db.Column(db.DateTime, default=datetime.utcnow)
    modification_date = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    module_sessions = db.relationship('ModuleSession', backref='module_progress', lazy='dynamic',
                                     cascade='all, delete-orphan')

    __table_args__ = (
        db.UniqueConstraint('enrollment_id', 'module_id', name='module_progress_unique'),
    )

    def __repr__(self):
        return f'<ModuleProgress {self.progress_id} Enrollment {self.enrollment_id} Module {self.module_id}>'

    def mark_as_started(self):
        """Mark this module as started."""
        if self.status == 'not_started':
            self.status = 'in_progress'
            self.start_date = datetime.utcnow()
            db.session.commit()

    def mark_as_completed(self, score=None, notes=None):
        """Mark this module as completed."""
        self.status = 'completed'
        self.completion_date = datetime.utcnow()
        if score is not None:
            self.score = score
        if notes:
            self.notes = notes
        db.session.commit()

        # Update the enrollment completion percentage
        self.enrollment.update_completion_percentage()


class ModuleSession(db.Model):
    """Model for linking program modules to appointments."""
    __tablename__ = 'module_sessions'

    session_id = db.Column(db.Integer, primary_key=True)
    module_progress_id = db.Column(db.Integer, db.ForeignKey('module_progress.progress_id', ondelete='CASCADE'), nullable=False)
    appointment_id = db.Column(db.Integer, db.ForeignKey('appointments.appointment_id', ondelete='CASCADE'), nullable=False)
    session_number = db.Column(db.Integer, nullable=False)  # For modules requiring multiple sessions
    insert_date = db.Column(db.DateTime, default=datetime.utcnow)
    modification_date = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    appointment = db.relationship('Appointment', backref='module_session', uselist=False)

    __table_args__ = (
        db.UniqueConstraint('module_progress_id', 'appointment_id', name='module_session_unique'),
    )

    def __repr__(self):
        return f'<ModuleSession {self.session_id} ModuleProgress {self.module_progress_id} Appointment {self.appointment_id}>'


class ProgramPricing(db.Model):
    """Model for program pricing options (individual sessions vs full package)."""
    __tablename__ = 'program_pricing'

    pricing_id = db.Column(db.Integer, primary_key=True)
    program_id = db.Column(db.Integer, db.ForeignKey('programs.program_id', ondelete='CASCADE'), nullable=False)
    pricing_type = db.Column(db.String(50), nullable=False)  # 'per_session', 'full_package'
    price = db.Column(db.Numeric(10, 2), nullable=False)
    description = db.Column(db.Text, nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    insert_date = db.Column(db.DateTime, default=datetime.utcnow)
    modification_date = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    program = db.relationship('Program', backref='pricing_options')

    __table_args__ = (
        db.UniqueConstraint('program_id', 'pricing_type', name='program_pricing_unique'),
    )

    def __repr__(self):
        return f'<ProgramPricing {self.pricing_id} Program {self.program_id} {self.pricing_type}>'


class GroupSession(db.Model):
    """Model for group sessions within programs."""
    __tablename__ = 'group_sessions'

    group_session_id = db.Column(db.Integer, primary_key=True)
    program_id = db.Column(db.Integer, db.ForeignKey('programs.program_id', ondelete='CASCADE'), nullable=False)
    module_id = db.Column(db.Integer, db.ForeignKey('program_modules.module_id', ondelete='CASCADE'), nullable=True)
    tutor_id = db.Column(db.Integer, db.ForeignKey('tutors.tutor_id', ondelete='CASCADE'), nullable=False)
    session_date = db.Column(db.Date, nullable=False)
    start_time = db.Column(db.Time, nullable=False)
    end_time = db.Column(db.Time, nullable=False)
    max_participants = db.Column(db.Integer, nullable=False, default=10)
    current_participants = db.Column(db.Integer, nullable=False, default=0)
    status = db.Column(db.String(20), nullable=False, default='scheduled')
    session_notes = db.Column(db.Text, nullable=True)
    tutor_payment_rate = db.Column(db.Numeric(8, 2), nullable=False, default=15.00)
    total_tutor_payment = db.Column(db.Numeric(10, 2), nullable=True, default=0.00)
    meeting_link = db.Column(db.String(500), nullable=True)
    insert_date = db.Column(db.DateTime, default=datetime.utcnow)
    modification_date = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    program = db.relationship('Program', backref='group_sessions')
    module = db.relationship('ProgramModule', backref='group_sessions')
    tutor = db.relationship('Tutor', backref='group_sessions')
    participants = db.relationship('GroupSessionParticipant', backref='group_session', lazy='dynamic',
                                  cascade='all, delete-orphan')

    def __repr__(self):
        return f'<GroupSession {self.group_session_id} Program {self.program_id} Module {self.module_id}>'

    def get_current_participants(self):
        """Get the actual count of current participants."""
        # Return the database column value which is maintained by triggers
        # Use getattr to avoid infinite recursion
        return getattr(self, '_current_participants', 0) or 0

    def refresh_participant_count(self):
        """Manually refresh the participant count from the database."""
        count = self.participants.filter(
            GroupSessionParticipant.attendance_status.in_(['registered', 'attended'])
        ).count()
        # Update the actual database column
        object.__setattr__(self, 'current_participants', count)
        db.session.commit()
        return count

    @classmethod
    def refresh_all_participant_counts(cls):
        """Refresh participant counts for all group sessions."""
        sessions = cls.query.all()
        for session in sessions:
            session.refresh_participant_count()
        return len(sessions)

    @property
    def current_participants_count(self):
        """Get the current participant count from the database column."""
        # Access the database column directly to avoid property recursion
        # Use object.__getattribute__ to bypass any property definitions
        try:
            return object.__getattribute__(self, 'current_participants') or 0
        except AttributeError:
            return 0
    
    @property
    def paid_participants_count(self):
        """Get count of paid participants only."""
        return self.participants.filter(
            GroupSessionParticipant.is_paid == True,
            GroupSessionParticipant.attendance_status.in_(['registered', 'attended'])
        ).count()
    
    @property
    def reserved_participants_count(self):
        """Get count of valid reservations (not yet paid)."""
        now = datetime.utcnow()
        return self.participants.filter(
            GroupSessionParticipant.is_paid == False,
            GroupSessionParticipant.reserved_until > now,
            GroupSessionParticipant.attendance_status == 'registered'
        ).count()

    @property
    def is_full(self):
        """Check if the group session is at maximum capacity."""
        return self.current_participants_count >= self.max_participants

    @property
    def has_minimum_participants(self):
        """Check if the group session has minimum participants to proceed (4 for TECFÉE)."""
        return self.current_participants_count >= 4

    @property
    def can_accept_participants(self):
        """Check if the group session can accept more participants."""
        return self.current_participants_count < self.max_participants and self.status == 'scheduled'

    def add_participant(self, enrollment_id=None, client_id=None):
        """Add a participant to the group session."""
        from sqlalchemy.exc import IntegrityError

        try:
            if not self.can_accept_participants:
                return False

            # Only enrollment_id is supported now (no client_id in database)
            if not enrollment_id:
                return False

            # Check if participant is already enrolled
            existing = self.participants.filter_by(enrollment_id=enrollment_id).first()
            if existing:
                return False

            # Create participant
            participant = GroupSessionParticipant(
                group_session_id=self.group_session_id,
                enrollment_id=enrollment_id,
                attendance_status='registered'
            )

            db.session.add(participant)

            # Manually update the current_participants count
            object.__setattr__(self, 'current_participants', self.participants.filter(
                GroupSessionParticipant.attendance_status.in_(['registered', 'attended'])
            ).count() + 1)  # +1 for the participant we just added

            db.session.commit()
            return True
        except IntegrityError as e:
            # Handle unique constraint violation (duplicate participant)
            db.session.rollback()
            return False
        except Exception as e:
            db.session.rollback()
            return False

    def remove_participant(self, enrollment_id=None, client_id=None):
        """Remove a participant from the group session."""
        if not enrollment_id:
            return False

        participant = self.participants.filter_by(enrollment_id=enrollment_id).first()
        if participant:
            db.session.delete(participant)

            # Manually update the current_participants count
            object.__setattr__(self, 'current_participants', self.participants.filter(
                GroupSessionParticipant.attendance_status.in_(['registered', 'attended'])
            ).count() - 1)  # -1 for the participant we just removed

            db.session.commit()
            return True
        return False

    @property
    def calculated_tutor_payment(self):
        """Calculate total payment for the tutor based on participants."""
        return float(self.tutor_payment_rate) * self.current_participants_count

    @property
    def min_participants(self):
        """Minimum participants required for TECFÉE sessions."""
        return 4

    @property
    def notes(self):
        """Alias for session_notes for template compatibility."""
        return self.session_notes

    @property
    def tutor_rate_per_student(self):
        """Alias for tutor_payment_rate for template compatibility."""
        return self.tutor_payment_rate



    @property
    def duration_minutes(self):
        """Calculate duration in minutes from start_time to end_time."""
        if self.start_time and self.end_time:
            start_datetime = datetime.combine(datetime.today(), self.start_time)
            end_datetime = datetime.combine(datetime.today(), self.end_time)
            duration = end_datetime - start_datetime
            return int(duration.total_seconds() / 60)
        return 60  # Default duration


class GroupSessionParticipant(db.Model):
    """Model for tracking participants in group sessions."""
    __tablename__ = 'group_session_participants'

    participant_id = db.Column(db.Integer, primary_key=True)
    group_session_id = db.Column(db.Integer, db.ForeignKey('group_sessions.group_session_id', ondelete='CASCADE'), nullable=False)
    enrollment_id = db.Column(db.Integer, db.ForeignKey('enrollments.enrollment_id', ondelete='CASCADE'), nullable=False)
    registration_date = db.Column(db.DateTime, default=datetime.utcnow)
    attendance_status = db.Column(db.String(20), nullable=False, default='registered')  # registered, attended, absent, cancelled
    attendance_notes = db.Column(db.Text, nullable=True)
    insert_date = db.Column(db.DateTime, default=datetime.utcnow)
    modification_date = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # New fields for reservation system
    reserved_until = db.Column(db.DateTime, nullable=True)
    is_paid = db.Column(db.Boolean, default=False)

    # Relationships
    enrollment = db.relationship('Enrollment', backref='group_session_participations')

    __table_args__ = (
        db.UniqueConstraint('group_session_id', 'enrollment_id', name='group_session_participant_enrollment_unique'),
    )

    def __repr__(self):
        return f'<GroupSessionParticipant {self.participant_id} Session {self.group_session_id} Enrollment {self.enrollment_id}>'
    
    @property
    def is_reservation_valid(self):
        """Check if a reservation is still valid."""
        if self.is_paid:
            return True  # Paid spots are always valid
        if not self.reserved_until:
            return False  # No reservation time set
        return datetime.utcnow() < self.reserved_until
    
    def reserve_spot(self, minutes=30):
        """Reserve a spot for specified minutes."""
        if not self.is_paid:
            self.reserved_until = datetime.utcnow() + timedelta(minutes=minutes)
