{% extends "base.html" %}

{% block title %}{{ dependant.first_name }} {{ dependant.last_name }} - Dependant Details - TutorAide Inc.{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">{{ dependant.first_name }} {{ dependant.last_name }}</h2>
        <p class="text-muted">Dependant Details</p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('manager.dependants_list') }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left"></i> Back to Dependants
        </a>
        <a href="{{ url_for('manager.edit_dependant', id=dependant.id) }}" class="btn btn-primary ms-2">
            <i class="fas fa-edit"></i> Edit
        </a>
    </div>
</div>

<div class="row">
    <!-- Basic Information -->
    <div class="col-md-6 mb-4">
        <div class="card shadow h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Basic Information</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Full Name:</strong></div>
                    <div class="col-sm-8">{{ dependant.first_name }} {{ dependant.last_name }}</div>
                </div>

                {% if dependant.email %}
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Email:</strong></div>
                    <div class="col-sm-8">
                        <a href="mailto:{{ dependant.email }}">{{ dependant.email }}</a>
                    </div>
                </div>
                {% endif %}

                {% if dependant.phone %}
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Phone:</strong></div>
                    <div class="col-sm-8">
                        <a href="tel:{{ dependant.phone }}">{{ dependant.phone }}</a>
                    </div>
                </div>
                {% endif %}

                {% if dependant.date_of_birth %}
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Date of Birth:</strong></div>
                    <div class="col-sm-8">
                        {{ dependant.date_of_birth.strftime('%B %d, %Y') }}
                        {% if dependant.age %}
                            <span class="text-muted">({{ dependant.age }} years old)</span>
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                {% if dependant.school_grade %}
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>School Grade:</strong></div>
                    <div class="col-sm-8">
                        <span class="badge bg-info">{{ dependant.grade_display }}</span>
                    </div>
                </div>
                {% endif %}

                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Status:</strong></div>
                    <div class="col-sm-8">
                        {% if dependant.is_active %}
                            <span class="badge bg-success">Active</span>
                        {% else %}
                            <span class="badge bg-secondary">Inactive</span>
                        {% endif %}
                    </div>
                </div>

                {% if dependant.preferred_language %}
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Language:</strong></div>
                    <div class="col-sm-8">
                        {% if dependant.preferred_language == 'en' %}
                            English
                        {% elif dependant.preferred_language == 'fr' %}
                            French
                        {% else %}
                            {{ dependant.preferred_language }}
                        {% endif %}
                    </div>
                </div>
                {% endif %}

                {% if dependant.address %}
                <div class="row mb-3">
                    <div class="col-sm-4"><strong>Address:</strong></div>
                    <div class="col-sm-8">{{ dependant.address }}</div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Client Relationships -->
    <div class="col-md-6 mb-4">
        <div class="card shadow h-100">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">Client Relationships</h5>
            </div>
            <div class="card-body">
                {% if relationships %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Client</th>
                                    <th>Relationship</th>
                                    <th>Primary</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for relationship in relationships %}
                                    <tr>
                                        <td>
                                            <a href="{{ url_for('manager.view_client', id=relationship.client.id) }}">
                                                {{ relationship.client.first_name }} {{ relationship.client.last_name }}
                                            </a>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ relationship.relationship_type }}</span>
                                        </td>
                                        <td>
                                            {% if relationship.is_primary %}
                                                <i class="fas fa-star text-warning" title="Primary Contact"></i>
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{{ url_for('manager.view_client', id=relationship.client.id) }}"
                                               class="btn btn-sm btn-outline-primary" title="View Client">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-link fa-2x text-muted mb-2"></i>
                        <p class="text-muted">No client relationships found</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Recent Appointments -->
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card shadow">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">Recent Appointments</h5>
            </div>
            <div class="card-body">
                {% if recent_appointments %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Date & Time</th>
                                    <th>Client</th>
                                    <th>Tutor</th>
                                    <th>Service</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for appointment in recent_appointments %}
                                    <tr>
                                        <td>
                                            {{ appointment.start_time.strftime('%Y-%m-%d %H:%M') }}
                                        </td>
                                        <td>
                                            <a href="{{ url_for('manager.view_client', id=appointment.client.id) }}">
                                                {{ appointment.client.first_name }} {{ appointment.client.last_name }}
                                            </a>
                                        </td>
                                        <td>
                                            {% if appointment.tutor %}
                                                <a href="{{ url_for('manager.view_tutor', id=appointment.tutor.id) }}">
                                                    {{ appointment.tutor.first_name }} {{ appointment.tutor.last_name }}
                                                </a>
                                            {% else %}
                                                <span class="text-muted">Not assigned</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if appointment.tutor_service %}
                                                {{ appointment.tutor_service.service.name }}
                                            {% else %}
                                                <span class="text-muted">No service</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if appointment.status == 'scheduled' %}
                                                <span class="badge bg-primary">Scheduled</span>
                                            {% elif appointment.status == 'completed' %}
                                                <span class="badge bg-success">Completed</span>
                                            {% elif appointment.status == 'cancelled' %}
                                                <span class="badge bg-danger">Cancelled</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ appointment.status|title }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <a href="{{ url_for('manager.view_appointment', id=appointment.id) }}"
                                               class="btn btn-sm btn-outline-primary" title="View Appointment">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-calendar fa-2x text-muted mb-2"></i>
                        <p class="text-muted">No recent appointments found</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Notes Section -->
{% if dependant.notes %}
<div class="row">
    <div class="col-md-12 mb-4">
        <div class="card shadow">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">Notes</h5>
            </div>
            <div class="card-body">
                <p class="mb-0">{{ dependant.notes }}</p>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}
