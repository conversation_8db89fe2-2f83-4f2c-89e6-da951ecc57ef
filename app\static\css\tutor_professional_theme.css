/* Clean Minimalist <PERSON><PERSON> Interface Theme */
/* White, <PERSON>, Black with Red Highlights - Matching Client Theme */

/* ===== COLOR VARIABLES ===== */
:root {
    /* Primary Colors */
    --primary-white: #ffffff;
    --primary-black: #000000;
    --primary-red: #e57373;
    --primary-red-dark: #d32f2f;
    --primary-red-light: #ffebee;
    --primary-red-pastel: #f8bbd9;

    /* Gray Scale */
    --light-gray: #f8f9fa;
    --medium-gray: #6c757d;
    --dark-gray: #495057;
    --border-gray: #dee2e6;
    --text-gray: #212529;

    /* Functional Colors - Soft Red Tones */
    --success-red: #e8a5a5;
    --warning-red: #f4a5a5;
    --info-red: #e57373;
    --danger-red: #e57373;

    /* Shadows & Effects */
    --soft-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    --card-shadow: 0 6px 25px rgba(0, 0, 0, 0.06);
    --hover-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);

    /* Typography */
    --font-elegant: 'Playfair Display', serif;
    --font-modern: 'Inter', sans-serif;
    --font-accent: 'Inter', sans-serif;
}

/* ===== GOOGLE FONTS IMPORT ===== */
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600&family=Crimson+Text:wght@400;600&display=swap');

/* ===== GLOBAL TUTOR INTERFACE STYLING ===== */
.tutor-interface {
    font-family: var(--font-modern);
    background: var(--primary-white);
    min-height: 100vh;
}

/* ===== NAVIGATION ENHANCEMENTS ===== */
.navbar-dark.bg-primary {
    background: var(--primary-black) !important;
    box-shadow: var(--soft-shadow);
    border-bottom: 1px solid var(--border-gray);
}

.navbar-brand {
    font-family: var(--font-elegant);
    font-weight: 600;
    color: var(--primary-white) !important;
    text-shadow: none;
}

/* ===== SIDEBAR STYLING ===== */
.sidebar {
    background: var(--primary-white);
    border-right: 1px solid var(--border-gray);
    box-shadow: var(--soft-shadow);
}

.sidebar .nav-link {
    color: var(--text-gray);
    border-radius: 20px;
    margin: 4px 8px;
    padding: 12px 16px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.sidebar .nav-link:hover {
    background: var(--light-gray);
    color: var(--text-gray);
    transform: translateX(2px);
    box-shadow: var(--soft-shadow);
}

.sidebar .nav-link.active {
    background: var(--primary-red);
    color: var(--primary-white);
    box-shadow: var(--card-shadow);
}

/* ===== CARD COMPONENTS ===== */
.card {
    border: 1px solid var(--border-gray);
    border-radius: 20px;
    box-shadow: var(--card-shadow);
    background: var(--primary-white);
    overflow: hidden;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--hover-shadow);
}

.card-header {
    background: var(--light-gray) !important;
    border-bottom: 1px solid var(--border-gray);
    padding: 20px 24px;
    color: var(--text-gray) !important;
    font-family: var(--font-elegant);
    font-weight: 600;
    font-size: 1.1rem;
}

.card-body {
    padding: 24px;
    background: var(--primary-white);
}

/* ===== BUTTONS ===== */
.btn-primary {
    background: var(--primary-red);
    border: none;
    border-radius: 25px;
    padding: 12px 24px;
    font-weight: 500;
    color: var(--primary-white);
    box-shadow: var(--soft-shadow);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: var(--primary-red-dark);
    transform: translateY(-2px);
    box-shadow: var(--hover-shadow);
}

.btn-success {
    background: var(--success-red);
    border: none;
    border-radius: 25px;
    padding: 12px 24px;
    font-weight: 500;
    color: var(--primary-white);
    box-shadow: var(--soft-shadow);
}

.btn-outline-primary {
    border: 2px solid var(--primary-red);
    color: var(--primary-red);
    border-radius: 25px;
    padding: 10px 22px;
    font-weight: 500;
    background: transparent;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background: var(--primary-red);
    border-color: var(--primary-red);
    color: var(--primary-white);
    transform: translateY(-2px);
}

/* ===== BUTTON FOCUS STATES ===== */
/* Override Bootstrap's default blue focus states */
.btn:focus,
.btn:focus-visible,
.btn:active:focus {
    outline: none !important;
    box-shadow: 0 0 0 0.2rem rgba(229, 115, 115, 0.25) !important;
}

.btn-primary:focus,
.btn-primary:focus-visible,
.btn-primary:active:focus {
    background: var(--primary-red) !important;
    border-color: var(--primary-red) !important;
    box-shadow: 0 0 0 0.2rem rgba(229, 115, 115, 0.25) !important;
}

.btn-success:focus,
.btn-success:focus-visible,
.btn-success:active:focus {
    background: var(--success-red) !important;
    border-color: var(--success-red) !important;
    box-shadow: 0 0 0 0.2rem rgba(232, 165, 165, 0.25) !important;
}

.btn-outline-primary:focus,
.btn-outline-primary:focus-visible,
.btn-outline-primary:active:focus {
    color: var(--primary-red) !important;
    border-color: var(--primary-red) !important;
    box-shadow: 0 0 0 0.2rem rgba(229, 115, 115, 0.25) !important;
}

/* ===== FORM ELEMENTS ===== */
.form-control {
    border: 1px solid var(--border-gray);
    border-radius: 15px;
    padding: 12px 16px;
    background: var(--primary-white);
    transition: all 0.3s ease;
    font-family: var(--font-modern);
}

.form-control:focus {
    border-color: var(--primary-red);
    box-shadow: 0 0 0 0.2rem rgba(229, 115, 115, 0.15);
    background: var(--primary-white);
}

.form-label {
    font-weight: 500;
    color: var(--text-gray);
    margin-bottom: 8px;
    font-family: var(--font-modern);
}

/* ===== ALERTS ===== */
.alert {
    border: 1px solid var(--border-gray);
    border-radius: 15px;
    padding: 16px 20px;
    margin-bottom: 20px;
}

.alert-success {
    background: var(--primary-red-light);
    color: var(--primary-red-dark);
    border-color: var(--primary-red-pastel);
    border-left: 4px solid var(--success-red);
}

.alert-info {
    background: var(--primary-red-light);
    color: var(--primary-red-dark);
    border-color: var(--primary-red-pastel);
    border-left: 4px solid var(--info-red);
}

.alert-warning {
    background: var(--primary-red-light);
    color: var(--primary-red-dark);
    border-color: var(--primary-red-pastel);
    border-left: 4px solid var(--warning-red);
}

.alert-danger {
    background: var(--primary-red-light);
    color: var(--primary-red-dark);
    border-color: var(--primary-red-pastel);
    border-left: 4px solid var(--danger-red);
}

/* ===== TABLES ===== */
.table {
    background: var(--primary-white);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--card-shadow);
    border: 1px solid var(--border-gray);
}

.table thead th {
    background: var(--light-gray);
    color: var(--text-gray);
    border: none;
    padding: 16px;
    font-weight: 600;
    font-family: var(--font-elegant);
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background: var(--light-gray);
}

.table tbody td {
    padding: 16px;
    border-color: var(--border-gray);
    vertical-align: middle;
}

/* ===== BADGES ===== */
.badge {
    border-radius: 20px;
    padding: 6px 12px;
    font-weight: 500;
    font-size: 0.75rem;
}

.badge.bg-success {
    background: var(--success-red) !important;
}

.badge.bg-warning {
    background: var(--warning-red) !important;
    color: var(--primary-white) !important;
}

.badge.bg-danger {
    background: var(--danger-red) !important;
}

/* ===== DASHBOARD SPECIFIC ===== */
.dashboard-welcome {
    background: var(--primary-white);
    border: 1px solid var(--border-gray);
    border-radius: 20px;
    padding: 32px;
    margin-bottom: 32px;
    box-shadow: var(--card-shadow);
}

.dashboard-welcome h2 {
    font-family: var(--font-elegant);
    color: var(--text-gray);
    margin-bottom: 8px;
}

.dashboard-welcome p {
    color: var(--medium-gray);
    font-size: 1.1rem;
}

/* ===== STATISTICS CARDS ===== */
.stat-card {
    background: var(--primary-white);
    border: 1px solid var(--border-gray);
    border-radius: 20px;
    padding: 24px;
    text-align: center;
    box-shadow: var(--card-shadow);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--hover-shadow);
}

.stat-card .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    font-family: var(--font-elegant);
    color: var(--primary-red);
}

.stat-card .stat-label {
    color: var(--medium-gray);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.9rem;
}

/* ===== SCHEDULE/CALENDAR SPECIFIC ===== */
.calendar-card {
    background: var(--primary-white);
    border: 1px solid var(--border-gray);
    border-radius: 20px;
    box-shadow: var(--card-shadow);
    padding: 24px;
}

.appointment-item {
    background: var(--light-gray);
    border-radius: 15px;
    padding: 16px;
    margin-bottom: 12px;
    transition: all 0.3s ease;
    border-left: 4px solid var(--primary-red);
}

.appointment-item:hover {
    transform: translateX(4px);
    box-shadow: var(--soft-shadow);
}

/* ===== EARNINGS/PAYMENT SPECIFIC ===== */
.earnings-summary {
    background: var(--primary-white);
    border: 1px solid var(--border-gray);
    border-radius: 20px;
    padding: 24px;
    box-shadow: var(--card-shadow);
}

.payment-status {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.payment-status.paid {
    background: var(--success-red);
    color: var(--primary-white);
}

.payment-status.pending {
    background: var(--warning-red);
    color: var(--primary-white);
}

/* ===== STUDENT LIST SPECIFIC ===== */
.student-card {
    background: var(--primary-white);
    border: 1px solid var(--border-gray);
    border-radius: 20px;
    padding: 20px;
    margin-bottom: 16px;
    box-shadow: var(--card-shadow);
    transition: all 0.3s ease;
}

.student-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--hover-shadow);
}

.student-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 3px solid var(--primary-red);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .card {
        border-radius: 16px;
        margin-bottom: 16px;
    }

    .card-header {
        padding: 16px 20px;
        font-size: 1rem;
    }

    .card-body {
        padding: 20px;
    }

    .btn {
        border-radius: 20px;
        padding: 10px 20px;
    }

    .dashboard-welcome {
        padding: 24px;
        margin-bottom: 24px;
        border-radius: 16px;
    }
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* ===== CUSTOM SCROLLBAR ===== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-gray);
}

::-webkit-scrollbar-thumb {
    background: var(--medium-gray);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-red);
}