#!/usr/bin/env python3
"""
Migration script to add appointment_recurring_schedules table
"""
import psycopg2
import os
from dotenv import load_dotenv

def main():
    load_dotenv()
    
    print("Running appointment_recurring_schedules migration...")
    
    # Read the migration SQL
    with open('app/migrations/add_appointment_recurring_schedules_table.sql', 'r') as f:
        migration_sql = f.read()
    
    # Connect to database using DATABASE_URL
    database_url = os.getenv('DATABASE_URL')
    if not database_url:
        raise ValueError("DATABASE_URL environment variable not found")
    
    conn = psycopg2.connect(database_url)
    
    try:
        with conn.cursor() as cur:
            # Execute the migration
            cur.execute(migration_sql)
            conn.commit()
            print("✓ Migration executed successfully")
            
            # Verify the table was created
            cur.execute("SELECT COUNT(*) FROM information_schema.tables WHERE table_name = 'appointment_recurring_schedules'")
            table_count = cur.fetchone()[0]
            print(f"✓ appointment_recurring_schedules table exists: {table_count > 0}")
            
            # Verify the foreign key column was added
            cur.execute("SELECT COUNT(*) FROM information_schema.columns WHERE table_name = 'appointments' AND column_name = 'recurring_schedule_id'")
            fk_count = cur.fetchone()[0]
            print(f"✓ recurring_schedule_id column added to appointments: {fk_count > 0}")
            
            # Check table structure
            cur.execute("""
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns 
                WHERE table_name = 'appointment_recurring_schedules'
                ORDER BY ordinal_position
            """)
            columns = cur.fetchall()
            print(f"✓ Table has {len(columns)} columns")
            
            # Check indexes
            cur.execute("""
                SELECT indexname 
                FROM pg_indexes 
                WHERE tablename = 'appointment_recurring_schedules'
            """)
            indexes = cur.fetchall()
            print(f"✓ Table has {len(indexes)} indexes")
            
    except Exception as e:
        print(f"✗ Migration failed: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()
    
    print("Migration completed successfully!")
    return True

if __name__ == "__main__":
    main()