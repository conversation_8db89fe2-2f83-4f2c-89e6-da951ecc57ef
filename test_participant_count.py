#!/usr/bin/env python3
"""
Test script to verify participant count functionality
"""
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app import create_app
from app.extensions import db
from app.models.program import GroupSession, GroupSessionParticipant

def test_participant_counts():
    """Test the participant count functionality"""
    app = create_app()
    
    with app.app_context():
        print("Testing participant count functionality...")
        
        # Get all group sessions
        sessions = GroupSession.query.all()
        print(f"Found {len(sessions)} group sessions")
        
        for session in sessions:
            # Get actual participant count from database
            actual_count = GroupSessionParticipant.query.filter_by(
                group_session_id=session.id
            ).filter(
                GroupSessionParticipant.attendance_status.in_(['registered', 'attended'])
            ).count()
            
            # Get count from model property
            model_count = session.current_participants_count
            
            # Get count from database column directly
            db_column_count = object.__getattribute__(session, 'current_participants')
            
            print(f"Session {session.id}:")
            print(f"  - Actual participants in DB: {actual_count}")
            print(f"  - Model property count: {model_count}")
            print(f"  - DB column value: {db_column_count}")
            print(f"  - Match: {actual_count == model_count == db_column_count}")
            
            # If they don't match, update the database column
            if actual_count != db_column_count:
                print(f"  - Updating DB column from {db_column_count} to {actual_count}")
                object.__setattr__(session, 'current_participants', actual_count)
                db.session.commit()
            
            print()

if __name__ == "__main__":
    test_participant_counts()
