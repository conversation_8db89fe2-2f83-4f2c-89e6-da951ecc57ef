<!-- app/templates/client/subscription_detail.html -->
{% extends "base.html" %}

{% block title %}{{ t('subscriptions.subscription_details') }} - TutorAide Inc.{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">{{ t('subscriptions.subscription_details') }}</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('client.subscriptions') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> {{ t('subscriptions.back_to_list') }}
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card shadow mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">{{ t('subscriptions.details') }}</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>{{ t('subscriptions.package') }}:</strong>
                    </div>
                    <div class="col-md-8">
                        {{ subscription.plan.name }}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>{{ t('subscriptions.start_date') }}:</strong>
                    </div>
                    <div class="col-md-8">
                        {{ subscription.start_date.strftime('%Y-%m-%d') }}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>{{ t('subscriptions.end_date') }}:</strong>
                    </div>
                    <div class="col-md-8">
                        {{ subscription.end_date.strftime('%Y-%m-%d') }}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>{{ t('subscriptions.status') }}:</strong>
                    </div>
                    <div class="col-md-8">
                        <span class="badge bg-{{ 'success' if subscription.is_active else 'danger' }}">
                            {{ t('subscriptions.active') if subscription.is_active else t('subscriptions.expired') }}
                        </span>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>{{ t('subscriptions.total_hours') }}:</strong>
                    </div>
                    <div class="col-md-8">
                        {{ subscription.plan.max_hours }}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>{{ t('subscriptions.hours_used') }}:</strong>
                    </div>
                    <div class="col-md-8">
                        {{ subscription.hours_used }}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>{{ t('subscriptions.hours_remaining') }}:</strong>
                    </div>
                    <div class="col-md-8">
                        {{ subscription.hours_remaining }}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>{{ t('subscriptions.price') }}:</strong>
                    </div>
                    <div class="col-md-8">
                        ${{ "%.2f"|format(subscription.price) }}
                    </div>
                </div>
                {% if subscription.notes %}
                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>{{ t('subscriptions.notes') }}:</strong>
                    </div>
                    <div class="col-md-8">
                        {{ subscription.notes|nl2br }}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Usage Progress -->
        <div class="card shadow mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">{{ t('subscriptions.usage') }}</h5>
            </div>
            <div class="card-body">
                <div class="progress" style="height: 25px;">
                    {% set percentage = (subscription.hours_used / subscription.plan.max_hours) * 100 %}
                    <div class="progress-bar {{ 'bg-danger' if percentage > 90 else 'bg-warning' if percentage > 70 else 'bg-success' }}"
                         role="progressbar"
                         style="width: {{ percentage }}%;"
                         aria-valuenow="{{ percentage }}"
                         aria-valuemin="0"
                         aria-valuemax="100">
                        {{ "%.1f"|format(subscription.hours_used) }} / {{ subscription.plan.max_hours }} {{ t('subscriptions.hours') }}
                    </div>
                </div>
                <div class="text-center mt-2">
                    <small class="text-muted">{{ "%.1f"|format(subscription.hours_remaining) }} {{ t('subscriptions.hours_remaining') }}</small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Subscription Appointments -->
        <div class="card shadow mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">{{ t('subscriptions.appointments') }}</h5>
            </div>
            <div class="card-body">
                {% if subscription_appointments %}
                    <div class="list-group">
                        {% for appointment in subscription_appointments %}
                            <a href="{{ url_for('client.view_appointment', id=appointment.id) }}" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">{{ appointment.start_time.strftime('%Y-%m-%d') }}</h6>
                                    <small>{{ appointment.start_time.strftime('%H:%M') }} - {{ appointment.end_time.strftime('%H:%M') }}</small>
                                </div>
                                <p class="mb-1">{{ appointment.tutor_service.service.name }} with {{ appointment.tutor.first_name }}</p>
                                <small class="text-muted">
                                    <span class="badge bg-{{ 'success' if appointment.status == 'completed' else 'warning' if appointment.status == 'scheduled' else 'danger' }}">
                                        {{ appointment.status | capitalize }}
                                    </span>
                                </small>
                            </a>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted text-center my-4">{{ t('subscriptions.no_appointments') }}</p>
                {% endif %}
            </div>
        </div>

        <!-- Related Invoices -->
        <div class="card shadow">
            <div class="card-header bg-light">
                <h5 class="mb-0">{{ t('subscriptions.invoices') }}</h5>
            </div>
            <div class="card-body">
                {% if subscription_invoices %}
                    <div class="list-group">
                        {% for invoice in subscription_invoices %}
                            <a href="{{ url_for('client.view_invoice', id=invoice.id) }}" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">{{ t('invoices.invoice') }} #{{ invoice.id }}</h6>
                                    <small>${{ "%.2f"|format(invoice.total_amount) }}</small>
                                </div>
                                <p class="mb-1">{{ invoice.invoice_date.strftime('%Y-%m-%d') }}</p>
                                <small class="text-muted">
                                    <span class="badge bg-{{ 'success' if invoice.status == 'paid' else 'warning' }}">
                                        {{ t('invoices.' + invoice.status) }}
                                    </span>
                                </small>
                            </a>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted text-center my-4">{{ t('subscriptions.no_invoices') }}</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
