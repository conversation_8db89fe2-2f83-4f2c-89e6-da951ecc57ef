#!/usr/bin/env python3

from app import create_app
from app.extensions import db
from sqlalchemy import text

app = create_app()
with app.app_context():
    print('Running appointments table cleanup migration...')
    
    # Read and execute the migration SQL
    with open('app/migrations/cleanup_appointments_recurring_fields.sql', 'r') as f:
        migration_sql = f.read()
    
    try:
        # Execute the migration
        db.session.execute(text(migration_sql))
        db.session.commit()
        print('✓ Migration completed successfully')
        
        # Verify the changes
        print('\nVerifying changes...')
        
        # Check that legacy fields are removed
        query = "SELECT column_name FROM information_schema.columns WHERE table_name = 'appointments' AND column_name IN ('is_recurring', 'recurrence_pattern', 'recurrence_end_date')"
        result = db.session.execute(text(query))
        legacy_fields = result.fetchall()
        
        if legacy_fields:
            print(f'⚠ Warning: Legacy fields still exist: {[field[0] for field in legacy_fields]}')
        else:
            print('✓ Legacy recurring fields successfully removed')
        
        # Check that recurring_schedule_id foreign key exists
        query2 = "SELECT constraint_name FROM information_schema.table_constraints WHERE table_name = 'appointments' AND constraint_name = 'fk_appointments_recurring_schedule'"
        result2 = db.session.execute(text(query2))
        fk_exists = result2.fetchone()
        
        if fk_exists:
            print('✓ Foreign key constraint for recurring_schedule_id exists')
        else:
            print('⚠ Warning: Foreign key constraint for recurring_schedule_id not found')
            
    except Exception as e:
        print(f'✗ Migration failed: {e}')
        db.session.rollback()