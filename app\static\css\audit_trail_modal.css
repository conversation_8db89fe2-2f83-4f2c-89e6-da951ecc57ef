/* Audit Trail Modal Styles */
/* Consistent with manager professional theme */

/* ===== MODAL OVERRIDES ===== */
#auditTrailModal .modal-dialog {
    max-width: 900px;
}

#auditTrailModal .modal-content {
    border-radius: 20px;
    border: 1px solid var(--border-gray, #dee2e6);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

#auditTrailModal .modal-header {
    background: var(--light-gray, #f8f9fa);
    border-bottom: 1px solid var(--border-gray, #dee2e6);
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    padding: 20px 24px;
}

#auditTrailModal .modal-title {
    font-family: var(--font-elegant, 'Playfair Display', serif);
    font-weight: 600;
    color: var(--text-gray, #212529);
    font-size: 1.25rem;
}

#auditTrailModal .modal-body {
    padding: 24px;
    max-height: 70vh;
    overflow-y: auto;
}

#auditTrailModal .modal-footer {
    border-top: 1px solid var(--border-gray, #dee2e6);
    border-bottom-left-radius: 20px;
    border-bottom-right-radius: 20px;
    padding: 16px 24px;
}

/* ===== APPOINTMENT SUMMARY ===== */
.appointment-summary .card {
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.appointment-summary .card-body {
    padding: 16px 20px;
}

#appointmentSummaryTitle {
    font-family: var(--font-elegant, 'Playfair Display', serif);
    font-weight: 600;
    color: var(--text-gray, #212529);
}

/* ===== AUDIT TIMELINE ===== */
.audit-timeline {
    position: relative;
}

/* Timeline connector line */
.audit-timeline::before {
    content: '';
    position: absolute;
    left: 24px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--border-gray, #dee2e6);
    z-index: 1;
}

/* ===== AUDIT ENTRIES ===== */
.audit-entry {
    position: relative;
    margin-left: 0;
}

.audit-entry-card {
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border-left-width: 4px !important;
    margin-left: 48px;
}

.audit-entry-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

/* Action-specific border colors */
.audit-entry-card[data-action="create"] {
    border-left-color: #28a745 !important;
}

.audit-entry-card[data-action="update"] {
    border-left-color: #007bff !important;
}

.audit-entry-card[data-action="delete"] {
    border-left-color: #dc3545 !important;
}

.audit-entry-card[data-action="read"] {
    border-left-color: #17a2b8 !important;
}

.audit-entry-card[data-action="cancel"] {
    border-left-color: #fd7e14 !important;
}

/* ===== ACTION ICONS ===== */
.audit-action-icon {
    position: absolute;
    left: -36px;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    background: var(--primary-white, #ffffff);
    border: 2px solid var(--border-gray, #dee2e6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.audit-action-icon .audit-icon {
    font-size: 1.1rem;
}

/* ===== ENTRY CONTENT ===== */
.audit-action-title {
    font-family: var(--font-elegant, 'Playfair Display', serif);
    font-weight: 600;
    color: var(--text-gray, #212529);
    margin-bottom: 4px;
}

.audit-meta {
    font-size: 0.9rem;
    color: var(--medium-gray, #6c757d);
}

.audit-summary {
    font-size: 1rem;
    line-height: 1.5;
    color: var(--medium-gray, #6c757d);
}

.audit-user {
    font-weight: 500;
    color: var(--text-gray, #212529);
}

.audit-timestamp {
    font-weight: 400;
}

/* ===== DETAILS TOGGLE BUTTON ===== */
.audit-details-toggle {
    border-radius: 20px;
    padding: 6px 12px;
    font-size: 0.85rem;
    border-color: var(--border-gray, #dee2e6);
    color: var(--medium-gray, #6c757d);
    transition: all 0.3s ease;
}

.audit-details-toggle:hover {
    background: var(--light-gray, #f8f9fa);
    border-color: var(--primary-red, #e57373);
    color: var(--primary-red, #e57373);
}

.audit-details-toggle[aria-expanded="true"] {
    background: var(--primary-red, #e57373);
    border-color: var(--primary-red, #e57373);
    color: var(--primary-white, #ffffff);
}

.audit-details-toggle[aria-expanded="true"] .fas {
    transform: rotate(180deg);
}

.audit-details-toggle .fas {
    transition: transform 0.3s ease;
}

/* ===== COLLAPSIBLE DETAILS ===== */
.audit-details .card {
    border-radius: 12px;
    background: var(--light-gray, #f8f9fa);
    border: 1px solid var(--border-gray, #dee2e6);
}

.audit-details .card-body {
    padding: 16px;
}

/* ===== CHANGE ITEMS ===== */
.change-item {
    padding: 12px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.change-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.change-field {
    font-weight: 600;
    color: var(--text-gray, #212529);
    font-size: 0.95rem;
}

.change-values {
    font-size: 0.9rem;
}

/* Enhanced Before/After Comparison Styling */
.change-comparison {
    position: relative;
    background: #fff;
    border-radius: 8px;
    padding: 12px;
    border: 1px solid var(--border-gray, #dee2e6);
    margin-top: 8px;
}

.old-value, .new-value {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    border-radius: 6px;
    margin: 4px 0;
}

.old-value {
    background: #fff5f5;
    border: 1px solid #fed7d7;
}

.new-value {
    background: #f0fff4;
    border: 1px solid #c6f6d5;
}

.change-arrow {
    text-align: center;
    margin: 8px 0;
    position: relative;
}

.change-arrow .fas {
    font-size: 1rem;
    color: var(--medium-gray, #6c757d);
    background: #fff;
    padding: 4px;
    border-radius: 50%;
    border: 2px solid var(--border-gray, #dee2e6);
}

.old-value .badge {
    background: #fed7d7 !important;
    color: #c53030 !important;
    border: 1px solid #feb2b2;
    font-size: 0.85rem;
    padding: 6px 12px;
    font-weight: 500;
}

.new-value .badge {
    background: #c6f6d5 !important;
    color: #2f855a !important;
    border: 1px solid #9ae6b4;
    font-size: 0.85rem;
    padding: 6px 12px;
    font-weight: 500;
}

/* Field-specific visual indicators */
.change-item[data-field-category="core"] {
    border-left: 3px solid #dc3545;
    padding-left: 16px;
    background: rgba(220, 53, 69, 0.02);
}

.change-item[data-field-category="scheduling"] {
    border-left: 3px solid #007bff;
    padding-left: 16px;
    background: rgba(0, 123, 255, 0.02);
}

.change-item[data-field-category="participants"] {
    border-left: 3px solid #28a745;
    padding-left: 16px;
    background: rgba(40, 167, 69, 0.02);
}

.change-item[data-field-category="financial"] {
    border-left: 3px solid #ffc107;
    padding-left: 16px;
    background: rgba(255, 193, 7, 0.02);
}

.change-item[data-field-category="details"] {
    border-left: 3px solid #6c757d;
    padding-left: 16px;
    background: rgba(108, 117, 125, 0.02);
}

/* Visual indicators for change types */
.change-indicator {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-size: 0.75rem;
    padding: 4px 8px;
    border-radius: 12px;
    margin-bottom: 8px;
    font-weight: 500;
}

.change-indicator.status-change {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.change-indicator.time-change {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.change-indicator.person-change {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.change-indicator.financial-change {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* Enhanced badges for different field types */
.badge.field-status {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
}

.badge.field-time {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
    color: white !important;
}

.badge.field-person {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
    color: white !important;
}

.badge.field-money {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%) !important;
    color: #2d3748 !important;
}

/* Creation values styling */
.creation-details .badge {
    background: #d4edda !important;
    color: #155724 !important;
    border: 1px solid #c3e6cb;
    font-size: 0.85rem;
    padding: 6px 10px;
}

/* Deletion context styling */
.deletion-details .text-muted {
    font-size: 0.85rem;
}

/* ===== USER INFORMATION STYLING ===== */
.user-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.audit-user {
    font-weight: 500;
    color: var(--text-gray, #212529);
}

.badge-role {
    font-size: 0.7rem;
    padding: 3px 8px;
    border-radius: 12px;
    background: var(--light-gray, #f8f9fa) !important;
    color: var(--medium-gray, #6c757d) !important;
    border: 1px solid var(--border-gray, #dee2e6);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ===== TIMESTAMP STYLING ===== */
.timestamp-info {
    display: flex;
    align-items: center;
    gap: 4px;
}

.audit-timestamp {
    font-weight: 400;
    color: var(--medium-gray, #6c757d);
    font-size: 0.9rem;
}

/* EST timezone emphasis - only add if not already present */
.audit-timestamp:not([data-has-timezone])::after {
    content: " EST";
    font-weight: 600;
    color: var(--primary-red, #e57373);
    font-size: 0.8rem;
}

/* ===== ACTION-SPECIFIC DETAILS STYLING ===== */
.creation-details h6,
.update-details h6,
.deletion-details h6 {
    font-family: var(--font-elegant, 'Playfair Display', serif);
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 16px;
}

.creation-details h6 {
    color: #28a745;
}

.update-details h6 {
    color: #007bff;
}

.deletion-details h6 {
    color: #dc3545;
}

/* Special styling for cancel actions */
.update-details h6.text-warning {
    color: #fd7e14 !important;
}

/* ===== ACTION ICON STYLING ===== */
.audit-action-icon[data-action="create"] {
    border-color: #28a745;
}

.audit-action-icon[data-action="update"] {
    border-color: #007bff;
}

.audit-action-icon[data-action="delete"] {
    border-color: #dc3545;
}

.audit-action-icon[data-action="cancel"] {
    border-color: #fd7e14;
}

.audit-action-icon[data-action="read"] {
    border-color: #17a2b8;
}

/* ===== CONTEXT INFORMATION ===== */
.context-info {
    background: rgba(0, 0, 0, 0.02);
    border-radius: 8px;
    padding: 12px;
}

.context-info h6 {
    font-size: 0.9rem;
    color: var(--medium-gray, #6c757d);
    margin-bottom: 8px;
}

.context-item {
    font-size: 0.85rem;
    color: var(--medium-gray, #6c757d);
}

.context-item strong {
    color: var(--text-gray, #212529);
}

/* ===== ENHANCED AUDIT META STYLING ===== */
.audit-meta {
    font-size: 0.9rem;
    color: var(--medium-gray, #6c757d);
}

.audit-meta .d-flex {
    gap: 16px;
}

.audit-meta i {
    opacity: 0.7;
    font-size: 0.85rem;
}

/* ===== DELETED VALUES STYLING ===== */
.deleted-values {
    background: rgba(220, 53, 69, 0.05);
    border-radius: 8px;
    padding: 12px;
    border-left: 3px solid #dc3545;
}

.deleted-values .change-item {
    padding: 6px 0;
    border-bottom: 1px solid rgba(220, 53, 69, 0.1);
}

.deleted-values .change-item:last-child {
    border-bottom: none;
}

.deleted-values .change-field {
    font-size: 0.85rem;
    font-weight: 500;
    color: #721c24;
}

.deleted-values .text-muted {
    color: #856404 !important;
}

/* ===== LOADING STATE ===== */
#auditLoadingState .spinner-border {
    width: 3rem;
    height: 3rem;
    border-width: 0.3em;
}

/* ===== EMPTY STATE ===== */
#auditEmptyState .fa-clipboard-list {
    opacity: 0.3;
}

/* ===== PAGINATION ===== */
#auditPagination .pagination .page-link {
    border: 1px solid var(--border-gray, #dee2e6);
    color: var(--primary-red, #e57373);
    padding: 8px 12px;
    border-radius: 15px;
    margin: 0 2px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

#auditPagination .pagination .page-link:hover {
    background: var(--primary-red, #e57373);
    color: var(--primary-white, #ffffff);
    border-color: var(--primary-red, #e57373);
    transform: translateY(-1px);
}

#auditPagination .pagination .page-item.active .page-link {
    background: var(--primary-red, #e57373);
    border-color: var(--primary-red, #e57373);
    color: var(--primary-white, #ffffff);
}

#auditPagination .pagination .page-item.disabled .page-link {
    color: var(--medium-gray, #6c757d);
    background: transparent;
    border-color: var(--border-gray, #dee2e6);
}

.pagination-info {
    font-size: 0.9rem;
    color: var(--medium-gray, #6c757d);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    #auditTrailModal .modal-dialog {
        margin: 10px;
        max-width: calc(100% - 20px);
    }

    #auditTrailModal .modal-body {
        padding: 16px;
        max-height: 60vh;
    }

    #auditTrailModal .modal-header,
    #auditTrailModal .modal-footer {
        padding: 16px;
    }

    .audit-entry-card {
        margin-left: 32px;
    }

    .audit-action-icon {
        left: -28px;
        width: 32px;
        height: 32px;
    }

    .audit-action-icon .audit-icon {
        font-size: 1rem;
    }

    .audit-timeline::before {
        left: 16px;
    }

    .change-item .row {
        flex-direction: column;
    }

    .change-item .col-md-3,
    .change-item .col-md-9 {
        max-width: 100%;
        flex: 0 0 100%;
    }

    .change-item .col-md-9 {
        margin-top: 8px;
    }

    #auditPagination {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }
}

/* ===== ACCESSIBILITY ENHANCEMENTS ===== */
.audit-entry-card:focus-within {
    outline: 2px solid var(--primary-red, #e57373);
    outline-offset: 2px;
}

.audit-details-toggle:focus {
    outline: 2px solid var(--primary-red, #e57373);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .audit-entry-card {
        border-width: 2px;
    }
    
    .audit-action-icon {
        border-width: 3px;
    }
    
    .audit-timeline::before {
        width: 3px;
        background: var(--text-gray, #212529);
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .audit-entry-card,
    .audit-details-toggle,
    .audit-details-toggle .fas,
    #auditPagination .pagination .page-link {
        transition: none;
    }
    
    .audit-entry-card:hover {
        transform: none;
    }
}

/* ===== PRINT STYLES ===== */
@media print {
    #auditTrailModal .modal-header,
    #auditTrailModal .modal-footer,
    .audit-details-toggle {
        display: none !important;
    }
    
    .audit-details {
        display: block !important;
    }
    
    .audit-entry-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #000;
    }
    
    .audit-timeline::before {
        background: #000;
    }
}
/* =====
 VIRTUAL SCROLLING STYLES ===== */
.virtual-scroll-container {
    position: relative;
    overflow-y: auto;
    overflow-x: hidden;
    height: 600px;
    border-radius: 12px;
    border: 1px solid var(--border-gray, #dee2e6);
    background: var(--primary-white, #ffffff);
}

.virtual-scroll-content {
    position: relative;
    z-index: 1;
}

.virtual-scroll-spacer-top,
.virtual-scroll-spacer-bottom {
    background: transparent;
    pointer-events: none;
}

/* Virtual scroll loading indicator */
.virtual-scroll-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* ===== PERFORMANCE INDICATORS ===== */
.performance-indicator {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-family: monospace;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.performance-indicator.show {
    opacity: 1;
}

.performance-indicator.fast {
    background: rgba(40, 167, 69, 0.9);
}

.performance-indicator.medium {
    background: rgba(255, 193, 7, 0.9);
}

.performance-indicator.slow {
    background: rgba(220, 53, 69, 0.9);
}

/* Cache status indicator */
.cache-indicator {
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 8px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ===== ENHANCED PAGINATION STYLES ===== */
#auditPagination {
    background: var(--light-gray, #f8f9fa);
    border-radius: 15px;
    padding: 16px 20px;
    border: 1px solid var(--border-gray, #dee2e6);
    margin-top: 20px;
}

#auditPagination .pagination-info {
    font-size: 0.9rem;
    color: var(--medium-gray, #6c757d);
    font-weight: 500;
}

#auditPagination .pagination .page-item {
    margin: 0 3px;
}

#auditPagination .pagination .page-link {
    border: 2px solid var(--border-gray, #dee2e6);
    color: var(--primary-red, #e57373);
    padding: 10px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    background: var(--primary-white, #ffffff);
    min-width: 44px;
    text-align: center;
}

#auditPagination .pagination .page-link:hover {
    background: var(--primary-red, #e57373);
    color: var(--primary-white, #ffffff);
    border-color: var(--primary-red, #e57373);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(229, 115, 115, 0.3);
}

#auditPagination .pagination .page-item.active .page-link {
    background: var(--primary-red, #e57373);
    border-color: var(--primary-red, #e57373);
    color: var(--primary-white, #ffffff);
    box-shadow: 0 4px 12px rgba(229, 115, 115, 0.3);
}

#auditPagination .pagination .page-item.disabled .page-link {
    color: var(--medium-gray, #6c757d);
    background: var(--light-gray, #f8f9fa);
    border-color: var(--border-gray, #dee2e6);
    cursor: not-allowed;
    opacity: 0.6;
}

#auditPagination .pagination .page-item.disabled .page-link:hover {
    transform: none;
    box-shadow: none;
}

/* Page loading spinner */
.pagination .page-link .spinner-border-sm {
    width: 1rem;
    height: 1rem;
    border-width: 0.15em;
}

/* ===== LAZY LOADING INDICATORS ===== */
.lazy-loading-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: var(--medium-gray, #6c757d);
    font-size: 0.9rem;
    background: var(--light-gray, #f8f9fa);
    border-radius: 12px;
    margin: 10px 0;
    border: 1px dashed var(--border-gray, #dee2e6);
}

.lazy-loading-indicator .spinner-border {
    width: 1.5rem;
    height: 1.5rem;
    margin-right: 10px;
    border-width: 0.2em;
}

/* ===== PERFORMANCE METRICS DISPLAY ===== */
.performance-metrics {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 12px 16px;
    border-radius: 12px;
    font-family: monospace;
    font-size: 0.8rem;
    z-index: 2000;
    max-width: 300px;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
    pointer-events: none;
}

.performance-metrics.show {
    opacity: 1;
    transform: translateY(0);
}

.performance-metrics .metric-row {
    display: flex;
    justify-content: space-between;
    margin: 2px 0;
}

.performance-metrics .metric-label {
    color: #ccc;
}

.performance-metrics .metric-value {
    color: #fff;
    font-weight: bold;
}

.performance-metrics .metric-value.fast {
    color: #28a745;
}

.performance-metrics .metric-value.medium {
    color: #ffc107;
}

.performance-metrics .metric-value.slow {
    color: #dc3545;
}

/* ===== ENHANCED LOADING STATES ===== */
.audit-entry-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading-shimmer 1.5s infinite;
    border-radius: 15px;
    height: 120px;
    margin-bottom: 16px;
}

@keyframes loading-shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Progressive loading states */
.audit-entry.loading {
    opacity: 0.7;
    pointer-events: none;
}

.audit-entry.loaded {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== OPTIMIZED SCROLLBAR STYLES ===== */
.virtual-scroll-container::-webkit-scrollbar {
    width: 8px;
}

.virtual-scroll-container::-webkit-scrollbar-track {
    background: var(--light-gray, #f8f9fa);
    border-radius: 4px;
}

.virtual-scroll-container::-webkit-scrollbar-thumb {
    background: var(--primary-red, #e57373);
    border-radius: 4px;
    transition: background 0.3s ease;
}

.virtual-scroll-container::-webkit-scrollbar-thumb:hover {
    background: #d32f2f;
}

/* Firefox scrollbar */
.virtual-scroll-container {
    scrollbar-width: thin;
    scrollbar-color: var(--primary-red, #e57373) var(--light-gray, #f8f9fa);
}

/* ===== ENHANCED RESPONSIVE DESIGN FOR PERFORMANCE ===== */
@media (max-width: 768px) {
    .virtual-scroll-container {
        height: 400px;
    }
    
    .performance-metrics {
        bottom: 10px;
        right: 10px;
        font-size: 0.7rem;
        padding: 8px 12px;
        max-width: 250px;
    }
    
    #auditPagination {
        padding: 12px 16px;
    }
    
    #auditPagination .pagination .page-link {
        padding: 8px 12px;
        font-size: 0.8rem;
        min-width: 36px;
    }
    
    .lazy-loading-indicator {
        padding: 15px;
        font-size: 0.8rem;
    }
}

/* ===== ACCESSIBILITY ENHANCEMENTS FOR PERFORMANCE FEATURES ===== */
.virtual-scroll-container:focus {
    outline: 2px solid var(--primary-red, #e57373);
    outline-offset: 2px;
}

/* Screen reader announcements for loading states */
.sr-only-loading {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* High contrast mode support for performance indicators */
@media (prefers-contrast: high) {
    .performance-indicator {
        border: 2px solid white;
    }
    
    .cache-indicator {
        border: 1px solid currentColor;
    }
    
    .virtual-scroll-container {
        border-width: 2px;
    }
}

/* Reduced motion support for performance animations */
@media (prefers-reduced-motion: reduce) {
    .audit-entry-skeleton {
        animation: none;
        background: #f0f0f0;
    }
    
    .audit-entry.loaded {
        animation: none;
    }
    
    .performance-metrics {
        transition: none;
    }
    
    .virtual-scroll-container::-webkit-scrollbar-thumb {
        transition: none;
    }
}

/* ===== DEBUG MODE STYLES ===== */
.debug-mode .audit-entry {
    border: 1px dashed #007bff;
    position: relative;
}

.debug-mode .audit-entry::before {
    content: attr(data-entry-id);
    position: absolute;
    top: -10px;
    right: -10px;
    background: #007bff;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.7rem;
    font-family: monospace;
    z-index: 10;
}

.debug-mode .virtual-scroll-spacer-top,
.debug-mode .virtual-scroll-spacer-bottom {
    background: rgba(255, 0, 0, 0.1);
    border: 1px dashed red;
}

.debug-mode .virtual-scroll-spacer-top::before,
.debug-mode .virtual-scroll-spacer-bottom::before {
    content: attr(style);
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: red;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 0.7rem;
    font-family: monospace;
}