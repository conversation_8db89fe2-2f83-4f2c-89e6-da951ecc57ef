# app/models/appointment_recurring_schedule.py
from datetime import datetime, timedelta, date
from app.extensions import db

class AppointmentRecurringSchedule(db.Model):
    __tablename__ = 'appointment_recurring_schedules'

    schedule_id = db.Column(db.Integer, primary_key=True)
    
    # Core appointment details
    tutor_id = db.Column(db.<PERSON>ger, db.<PERSON>('tutors.tutor_id'), nullable=False)
    client_id = db.Column(db.Integer, db.<PERSON>ey('clients.client_id'), nullable=False)
    dependant_id = db.Column(db.Integer, db.<PERSON>ey('dependants.dependant_id'), nullable=True)
    tutor_service_id = db.Column(db.Integer, db.<PERSON>ey('tutor_services.tutor_service_id'), nullable=False)
    
    # Schedule timing
    start_time = db.Column(db.Time, nullable=False)  # Time of day for appointments
    duration_minutes = db.Column(db.Integer, nullable=False, default=60)
    
    # Recurring pattern
    frequency = db.Column(db.String(20), nullable=False)  # 'weekly', 'biweekly', 'monthly'
    day_of_week = db.Column(db.Integer, nullable=True)    # 0=Monday, 6=Sunday (for weekly/biweekly)
    week_of_month = db.Column(db.Integer, nullable=True)  # 1-5 (for monthly patterns)
    
    # Schedule boundaries
    pattern_start_date = db.Column(db.Date, nullable=False)
    pattern_end_date = db.Column(db.Date, nullable=True)  # NULL means no end date
    pattern_occurrences = db.Column(db.Integer, nullable=True)  # Alternative to end_date
    
    # Generation tracking
    last_generated_date = db.Column(db.Date, nullable=True)  # Track the last date appointments were generated
    
    # Default appointment settings
    default_status = db.Column(db.String(50), nullable=False, default='scheduled')
    default_notes = db.Column(db.Text, nullable=True)
    transport_fee = db.Column(db.Numeric(10, 2), nullable=True)
    transport_fee_for_tutor = db.Column(db.Boolean, default=True)
    is_subscription_based = db.Column(db.Boolean, default=False)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscriptions.subscription_id'), nullable=True)
    
    # Status and metadata
    is_active = db.Column(db.Boolean, nullable=False, default=True)
    created_by = db.Column(db.Integer, db.ForeignKey('users.user_id'), nullable=True)
    modified_by = db.Column(db.Integer, db.ForeignKey('users.user_id'), nullable=True)
    insert_date = db.Column(db.DateTime, default=datetime.utcnow)
    modification_date = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    client = db.relationship('Client', foreign_keys=[client_id], lazy='joined')
    tutor = db.relationship('Tutor', foreign_keys=[tutor_id], lazy='joined')
    tutor_service = db.relationship('TutorService', foreign_keys=[tutor_service_id], lazy='joined')
    dependant = db.relationship('Dependant', foreign_keys=[dependant_id], lazy='joined')
    subscription = db.relationship('Subscription', foreign_keys=[subscription_id], lazy='select')
    
    # Reverse relationship to appointments
    generated_appointments = db.relationship('Appointment', 
                                           foreign_keys='Appointment.recurring_schedule_id',
                                           backref='recurring_schedule',
                                           lazy='dynamic')

    def __repr__(self):
        return f'<AppointmentRecurringSchedule {self.schedule_id} tutor={self.tutor_id} client={self.client_id} frequency={self.frequency}>'

    @property
    def appointment_subject(self):
        """Get the subject of the appointment (dependant if present, otherwise client)."""
        if self.dependant_id and self.dependant:
            return self.dependant
        elif self.client:
            return self.client
        return None

    @property
    def appointment_subject_name(self):
        """Get the name of the appointment subject (dependant name if present, otherwise client name)."""
        subject = self.appointment_subject
        if subject:
            return f"{subject.first_name} {subject.last_name}"
        else:
            # Fallback: try to get names directly if relationships aren't loaded
            if self.dependant_id:
                from app.models.dependant import Dependant
                dependant = Dependant.query.get(self.dependant_id)
                if dependant:
                    return f"{dependant.first_name} {dependant.last_name}"
            
            if self.client_id:
                from app.models.client import Client
                client = Client.query.get(self.client_id)
                if client:
                    return f"{client.first_name} {client.last_name}"
            
            return "Unknown"

    @property
    def is_for_dependant(self):
        """Check if this recurring schedule is for a dependant."""
        return self.dependant_id is not None

    def get_next_occurrence(self, from_date=None):
        """Calculate the next occurrence date after the given date."""
        if self.is_active is False:  # Only return None if explicitly False, not if None
            return None

        if from_date is None:
            # If no from_date provided, start from pattern_start_date or today, whichever is later
            today = datetime.now().date()
            from_date = max(today, self.pattern_start_date) if self.pattern_start_date else today

        # Check if we've passed the end date
        if self.pattern_end_date and from_date > self.pattern_end_date:
            return None

        # If we haven't reached the start date yet, return the start date
        if from_date < self.pattern_start_date:
            return self.pattern_start_date

        if self.frequency == 'weekly':
            # Find next date with the same day of week
            days_ahead = self.day_of_week - from_date.weekday()
            if days_ahead <= 0:  # Target day already happened this week
                days_ahead += 7
            return from_date + timedelta(days=days_ahead)

        elif self.frequency == 'biweekly':
            # Calculate weeks since start date
            weeks_since_start = (from_date - self.pattern_start_date).days // 7
            # Find next biweekly occurrence
            next_week = ((weeks_since_start // 2) + 1) * 2
            next_date = self.pattern_start_date + timedelta(weeks=next_week)

            # Adjust to correct day of week
            days_ahead = self.day_of_week - next_date.weekday()
            if days_ahead != 0:
                next_date += timedelta(days=days_ahead)

            return next_date

        elif self.frequency == 'monthly':
            # Monthly recurring logic - find next occurrence of the same week and day
            if self.week_of_month and self.day_of_week is not None:
                # Start from the next month
                next_month = from_date.replace(day=1)
                if next_month <= from_date:
                    if next_month.month == 12:
                        next_month = next_month.replace(year=next_month.year + 1, month=1)
                    else:
                        next_month = next_month.replace(month=next_month.month + 1)
                
                # Find the specific week and day in that month
                first_day_of_month = next_month
                first_weekday = first_day_of_month.weekday()
                
                # Calculate the date of the first occurrence of the target day
                days_to_target = (self.day_of_week - first_weekday) % 7
                first_occurrence = first_day_of_month + timedelta(days=days_to_target)
                
                # Add weeks to get to the target week of month
                target_date = first_occurrence + timedelta(weeks=self.week_of_month - 1)
                
                # Make sure we didn't go into the next month
                if target_date.month == next_month.month:
                    return target_date

        return None

    def generate_next_appointment(self):
        """Generate the next appointment instance from this recurring schedule."""
        if self.is_active is False:  # Only return None if explicitly False, not if None
            return None

        next_date = self.get_next_occurrence(self.last_generated_date)
        if not next_date:
            return None

        # Create start and end datetime objects
        start_datetime = datetime.combine(next_date, self.start_time)
        end_datetime = start_datetime + timedelta(minutes=self.duration_minutes)

        # Import here to avoid circular imports
        from app.models.appointment import Appointment

        # Create new appointment instance
        appointment = Appointment(
            recurring_schedule_id=self.schedule_id,
            tutor_id=self.tutor_id,
            client_id=self.client_id,
            dependant_id=self.dependant_id,
            tutor_service_id=self.tutor_service_id,
            start_time=start_datetime,
            end_time=end_datetime,
            status=self.default_status,
            notes=self.default_notes,
            transport_fee=self.transport_fee,
            transport_fee_for_tutor=self.transport_fee_for_tutor,
            is_subscription_based=self.is_subscription_based,
            subscription_id=self.subscription_id,
            is_recurring=False  # Generated appointments are not templates
        )

        # Update the last generated date
        self.last_generated_date = next_date

        return appointment

    def generate_appointments_until(self, end_date):
        """Generate all appointments from now until the specified end date."""
        appointments = []
        current_date = self.last_generated_date or datetime.now().date()
        
        while True:
            next_date = self.get_next_occurrence(current_date)
            if not next_date or next_date > end_date:
                break
                
            appointment = self.generate_next_appointment()
            if appointment:
                appointments.append(appointment)
                current_date = next_date
            else:
                break
                
        return appointments

    def deactivate(self):
        """Deactivate this recurring schedule."""
        self.is_active = False
        self.modification_date = datetime.utcnow()