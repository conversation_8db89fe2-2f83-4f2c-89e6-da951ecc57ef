#!/usr/bin/env python3
"""
Fix existing recurring appointments and their generated appointments
This script will:
1. Identify recurring appointments that should be for dependants
2. Update them with the correct dependant_id
3. Update all generated appointments from those recurring appointments
"""
import os
import sys

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def fix_appointments():
    """Fix existing recurring appointments and generated appointments"""
    try:
        from app import create_app
        from app.extensions import db
        from app.models.recurring_appointment import RecurringAppointment
        from app.models.appointment import Appointment
        from app.models.dependant import Dependant, DependantRelationship
        
        app = create_app()
        
        with app.app_context():
            print("=== FIXING RECURRING APPOINTMENTS ===\n")
            
            # Get all recurring appointments without dependant_id
            recurring_appointments = RecurringAppointment.query.filter_by(dependant_id=None).all()
            print(f"Found {len(recurring_appointments)} recurring appointments without dependant_id")
            
            fixed_count = 0
            
            for ra in recurring_appointments:
                print(f"\nChecking Recurring Appointment ID: {ra.id}")
                print(f"  Client: {ra.client.first_name} {ra.client.last_name}")
                
                # Check if this client is actually a dependant
                # Look for a dependant with the same name as the client
                potential_dependants = Dependant.query.filter_by(
                    first_name=ra.client.first_name,
                    last_name=ra.client.last_name
                ).all()
                
                if potential_dependants:
                    print(f"  Found potential dependant matches: {len(potential_dependants)}")
                    
                    # Use the first match (you might want to add more logic here)
                    dependant = potential_dependants[0]
                    
                    # Find the responsible client for this dependant
                    relationship = DependantRelationship.query.filter_by(
                        dependant_id=dependant.id,
                        is_primary=True
                    ).first()
                    
                    if relationship:
                        print(f"  Found responsible client: {relationship.client.first_name} {relationship.client.last_name}")
                        
                        # Update the recurring appointment
                        ra.client_id = relationship.client_id
                        ra.dependant_id = dependant.id
                        
                        print(f"  ✓ Updated recurring appointment")
                        
                        # Update all generated appointments from this recurring appointment
                        generated_appointments = Appointment.query.filter_by(recurring_appointment_id=ra.id).all()
                        print(f"  Updating {len(generated_appointments)} generated appointments...")
                        
                        for apt in generated_appointments:
                            apt.client_id = relationship.client_id
                            apt.dependant_id = dependant.id
                        
                        print(f"  ✓ Updated {len(generated_appointments)} appointments")
                        fixed_count += 1
                    else:
                        print(f"  ⚠ No primary relationship found for dependant")
                else:
                    print(f"  - No matching dependant found")
            
            if fixed_count > 0:
                print(f"\n=== COMMITTING CHANGES ===")
                db.session.commit()
                print(f"✅ Fixed {fixed_count} recurring appointments and their generated appointments")
            else:
                print(f"\n✓ No recurring appointments needed fixing")
            
            return True
            
    except Exception as e:
        print(f"❌ Fix failed: {e}")
        import traceback
        traceback.print_exc()
        try:
            db.session.rollback()
        except:
            pass
        return False

if __name__ == '__main__':
    print("Fixing recurring appointments...")
    success = fix_appointments()
    
    if success:
        print("\n🎉 Fix completed!")
    else:
        print("\n💥 Fix failed. Please check the error messages above.")
    
    sys.exit(0 if success else 1)
