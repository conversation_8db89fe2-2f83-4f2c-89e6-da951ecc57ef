# app/services/audit_service.py
from typing import List, Dict, Any, Optional, Tu<PERSON>
from flask import current_app
from sqlalchemy import desc, text
from app.models.appointment_audit import AppointmentAudit
from app.models.user import User
from app.models.tutor import Tutor
from app.models.client import Client
from app.services.timezone_service import TimezoneService
from app.extensions import db
import time
from functools import lru_cache
from datetime import datetime, timedelta


class AuditService:
    """Service for handling appointment audit operations and display formatting with performance optimizations."""
    
    # Cache for frequently accessed audit data (appointment_id -> cached_data)
    _audit_cache = {}
    _cache_timestamps = {}
    _cache_ttl = 300  # 5 minutes cache TTL
    
    @staticmethod
    def get_appointment_audit_history(appointment_id: int, page: int = 1, per_page: int = 20, use_cache: bool = True) -> Dict[str, Any]:
        """
        Get paginated audit history for a specific appointment with comprehensive error handling.
        
        Args:
            appointment_id (int): The appointment ID to get audit history for
            page (int): Page number for pagination (default: 1)
            per_page (int): Number of entries per page (default: 20)
            use_cache (bool): Whether to use caching (default: True)
            
        Returns:
            Dict containing:
                - entries: List of formatted audit entries
                - pagination: Pagination metadata
                - total: Total number of audit entries
                - error_info: Error information if partial failure occurred
        """
        error_info = None
        formatted_entries = []
        
        try:
            # Validate input parameters
            if not isinstance(appointment_id, int) or appointment_id <= 0:
                raise ValueError(f"Invalid appointment_id: {appointment_id}")
            
            if not isinstance(page, int) or page < 1:
                page = 1
                
            if not isinstance(per_page, int) or per_page < 1 or per_page > 100:
                per_page = 20
            
            # Check cache first if enabled
            cache_key = f"audit_history_{appointment_id}_{page}_{per_page}"
            if use_cache and cache_key in AuditService._audit_cache:
                cache_timestamp = AuditService._cache_timestamps.get(cache_key, 0)
                if time.time() - cache_timestamp < AuditService._cache_ttl:
                    try:
                        current_app.logger.debug(f"Returning cached audit history for appointment {appointment_id}")
                    except RuntimeError:
                        pass
                    cached_data = AuditService._audit_cache[cache_key].copy()
                    cached_data['from_cache'] = True
                    return cached_data
            
            # Query audit entries for the appointment, ordered by timestamp (newest first)
            try:
                query = AppointmentAudit.query.filter_by(appointment_id=appointment_id).order_by(desc(AppointmentAudit.timestamp))
                
                # Apply pagination with error handling
                paginated_results = query.paginate(
                    page=page,
                    per_page=per_page,
                    error_out=False
                )
                
                if not paginated_results:
                    raise ValueError("Pagination query returned no results")
                    
            except Exception as db_error:
                try:
                    current_app.logger.error(f"Database error querying audit history for appointment {appointment_id}: {str(db_error)}")
                except RuntimeError:
                    pass
                raise Exception(f"Database query failed: {str(db_error)}")
            
            # Format entries for display with individual error handling
            formatting_errors = []
            
            for i, entry in enumerate(paginated_results.items):
                try:
                    formatted_entry = AuditService.format_audit_entry_for_display(entry)
                    if formatted_entry:
                        formatted_entries.append(formatted_entry)
                    else:
                        formatting_errors.append(f"Entry {entry.id}: Formatting returned None")
                        
                except Exception as format_error:
                    formatting_errors.append(f"Entry {entry.id}: {str(format_error)}")
                    try:
                        current_app.logger.warning(f"Error formatting audit entry {entry.id}: {str(format_error)}")
                    except RuntimeError:
                        pass
                    
                    # Create minimal fallback entry
                    try:
                        fallback_entry = AuditService._create_fallback_entry(entry, format_error)
                        formatted_entries.append(fallback_entry)
                    except Exception as fallback_error:
                        try:
                            current_app.logger.error(f"Failed to create fallback entry for {entry.id}: {str(fallback_error)}")
                        except RuntimeError:
                            pass
            
            # Set error info if there were formatting issues
            if formatting_errors:
                error_info = {
                    'type': 'partial_formatting_failure',
                    'message': f'{len(formatting_errors)} entries had formatting issues',
                    'details': formatting_errors[:5],  # Limit to first 5 errors
                    'total_errors': len(formatting_errors)
                }
            
            result = {
                'entries': formatted_entries,
                'pagination': {
                    'page': paginated_results.page,
                    'per_page': paginated_results.per_page,
                    'total_pages': paginated_results.pages,
                    'has_prev': paginated_results.has_prev,
                    'has_next': paginated_results.has_next,
                    'prev_num': paginated_results.prev_num,
                    'next_num': paginated_results.next_num
                },
                'total': paginated_results.total,
                'from_cache': False
            }
            
            # Add error info if present
            if error_info:
                result['error_info'] = error_info
            
            # Cache the result if caching is enabled and no errors occurred
            if use_cache and not error_info:
                try:
                    AuditService._audit_cache[cache_key] = result.copy()
                    AuditService._cache_timestamps[cache_key] = time.time()
                    
                    # Clean up old cache entries (keep only last 50)
                    if len(AuditService._audit_cache) > 50:
                        oldest_keys = sorted(AuditService._cache_timestamps.items(), key=lambda x: x[1])[:10]
                        for old_key, _ in oldest_keys:
                            AuditService._audit_cache.pop(old_key, None)
                            AuditService._cache_timestamps.pop(old_key, None)
                            
                except Exception as cache_error:
                    try:
                        current_app.logger.warning(f"Failed to cache audit history: {str(cache_error)}")
                    except RuntimeError:
                        pass
            
            return result
            
        except Exception as e:
            try:
                current_app.logger.error(f"Critical error getting audit history for appointment {appointment_id}: {str(e)}")
            except RuntimeError:
                pass
            
            # Return minimal error response
            return {
                'entries': formatted_entries,  # Return any entries that were successfully formatted
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total_pages': 0,
                    'has_prev': False,
                    'has_next': False,
                    'prev_num': None,
                    'next_num': None
                },
                'total': len(formatted_entries),
                'error_info': {
                    'type': 'critical_failure',
                    'message': 'Failed to retrieve audit history',
                    'details': str(e),
                    'recoverable': 'database' not in str(e).lower()
                },
                'from_cache': False
            }
    
    @staticmethod
    def _create_fallback_entry(entry: AppointmentAudit, error: Exception) -> Dict[str, Any]:
        """
        Create a minimal fallback entry when formatting fails.
        
        Args:
            entry (AppointmentAudit): The original audit entry
            error (Exception): The error that occurred during formatting
            
        Returns:
            Dict containing minimal entry data
        """
        try:
            return {
                'id': entry.id,
                'appointment_id': entry.appointment_id,
                'action': entry.action or 'unknown',
                'action_description': entry.action_description or 'Unknown action',
                'action_icon': '⚠️',
                'action_color': 'warning',
                'user_name': 'Unknown User',
                'user_role': entry.user_role or 'Unknown',
                'user_email': entry.user_email or '',
                'timestamp_utc': entry.timestamp.isoformat() if entry.timestamp else None,
                'timestamp_est': 'Unknown Time',
                'timestamp_est_long': 'Unknown Time',
                'changes_summary': f'Entry formatting failed: {str(error)[:100]}',
                'changes_detail': [],
                'field_changes': [],
                'has_changes': False,
                'notes': entry.notes,
                'formatting_error': True,
                'error_message': str(error)
            }
        except Exception as fallback_error:
            # Ultimate fallback - return absolute minimum
            return {
                'id': getattr(entry, 'id', 'unknown'),
                'appointment_id': getattr(entry, 'appointment_id', 'unknown'),
                'action': 'error',
                'action_description': 'Entry could not be displayed',
                'action_icon': '❌',
                'action_color': 'danger',
                'user_name': 'System Error',
                'user_role': 'System',
                'user_email': '',
                'timestamp_utc': None,
                'timestamp_est': 'Error',
                'timestamp_est_long': 'Error',
                'changes_summary': f'Critical formatting error: {str(fallback_error)}',
                'changes_detail': [],
                'field_changes': [],
                'has_changes': False,
                'notes': None,
                'formatting_error': True,
                'critical_error': True,
                'error_message': str(fallback_error)
            }
    
    @staticmethod
    def format_audit_entry_for_display(audit_entry: AppointmentAudit) -> Dict[str, Any]:
        """
        Format an audit entry for frontend display.
        
        Args:
            audit_entry (AppointmentAudit): The audit entry to format
            
        Returns:
            Dict containing formatted audit entry data
        """
        try:
            # Get user display information
            user_info = AuditService._get_user_display_info(audit_entry)
            
            # Format timestamp in Eastern Time
            timestamp_est = TimezoneService.format_for_display(
                audit_entry.timestamp, 
                include_timezone=True, 
                long_format=False
            )
            
            # Get detailed changes information
            changes_detail = AuditService._format_changes_detail(audit_entry)
            
            # Determine action icon and color
            action_display = AuditService._get_action_display_info(audit_entry.action)
            
            # Format field changes for frontend compatibility
            field_changes = AuditService._format_field_changes_for_frontend(changes_detail)
            
            return {
                'id': audit_entry.id,
                'appointment_id': audit_entry.appointment_id,
                'action': audit_entry.action,
                'action_description': audit_entry.action_description,
                'action_icon': action_display['icon'],
                'action_color': action_display['color'],
                'user_name': user_info['name'],
                'user_role': user_info['role'],
                'user_email': user_info['email'],
                'timestamp_utc': audit_entry.timestamp.isoformat() if audit_entry.timestamp else None,
                'timestamp_est': timestamp_est,
                'timestamp_est_long': TimezoneService.format_for_display(
                    audit_entry.timestamp, 
                    include_timezone=True, 
                    long_format=True
                ),
                'changes_summary': audit_entry.changes_summary,
                'changes_detail': changes_detail,
                'field_changes': field_changes,  # Frontend-compatible format
                'has_changes': len(changes_detail) > 0,
                'notes': audit_entry.notes,
                # Add structured data for different action types
                'initial_values': AuditService._get_initial_values(audit_entry) if audit_entry.action == 'create' else None,
                'deleted_values': AuditService._get_deleted_values(audit_entry) if audit_entry.action == 'delete' else None,
                'context_info': AuditService._get_context_info(audit_entry)
            }
            
        except Exception as e:
            try:
                current_app.logger.error(f"Error formatting audit entry {audit_entry.id}: {str(e)}")
            except RuntimeError:
                # No application context available, skip logging
                pass
            # Return minimal safe data
            return {
                'id': audit_entry.id,
                'appointment_id': audit_entry.appointment_id,
                'action': audit_entry.action,
                'action_description': audit_entry.action_description,
                'action_icon': '📝',
                'action_color': 'blue',
                'user_name': 'Unknown User',
                'user_role': 'Unknown',
                'user_email': '',
                'timestamp_utc': audit_entry.timestamp.isoformat() if audit_entry.timestamp else None,
                'timestamp_est': 'Unknown Time',
                'timestamp_est_long': 'Unknown Time',
                'changes_summary': 'Error loading changes',
                'changes_detail': [],
                'has_changes': False,
                'notes': audit_entry.notes
            }
    
    @staticmethod
    def get_audit_summary(appointment_id: int) -> Dict[str, Any]:
        """
        Get a quick audit summary for an appointment.
        
        Args:
            appointment_id (int): The appointment ID to get summary for
            
        Returns:
            Dict containing audit summary information
        """
        try:
            # Get the most recent audit entry
            latest_entry = AppointmentAudit.query.filter_by(
                appointment_id=appointment_id
            ).order_by(desc(AppointmentAudit.timestamp)).first()
            
            # Get total count of audit entries
            total_entries = AppointmentAudit.query.filter_by(appointment_id=appointment_id).count()
            
            # Get creation entry
            creation_entry = AppointmentAudit.query.filter_by(
                appointment_id=appointment_id,
                action='create'
            ).first()
            
            if not latest_entry:
                return {
                    'has_audit_trail': False,
                    'total_entries': 0,
                    'last_modified': None,
                    'last_modified_by': None,
                    'created_at': None,
                    'created_by': None
                }
            
            # Format latest entry info
            latest_user_info = AuditService._get_user_display_info(latest_entry)
            latest_timestamp = TimezoneService.format_for_display(
                latest_entry.timestamp,
                include_timezone=True,
                long_format=False
            )
            
            # Format creation info if available
            created_info = {}
            if creation_entry:
                creation_user_info = AuditService._get_user_display_info(creation_entry)
                created_info = {
                    'created_at': TimezoneService.format_for_display(
                        creation_entry.timestamp,
                        include_timezone=True,
                        long_format=False
                    ),
                    'created_by': creation_user_info['name']
                }
            
            return {
                'has_audit_trail': True,
                'total_entries': total_entries,
                'last_modified': latest_timestamp,
                'last_modified_by': latest_user_info['name'],
                'last_action': latest_entry.action_description,
                'last_changes_summary': latest_entry.changes_summary,
                **created_info
            }
            
        except Exception as e:
            try:
                current_app.logger.error(f"Error getting audit summary for appointment {appointment_id}: {str(e)}")
            except RuntimeError:
                # No application context available, skip logging
                pass
            return {
                'has_audit_trail': False,
                'total_entries': 0,
                'last_modified': None,
                'last_modified_by': None,
                'created_at': None,
                'created_by': None,
                'error': 'Unable to load audit summary'
            }
    
    @staticmethod
    def _get_user_display_info(audit_entry: AppointmentAudit) -> Dict[str, str]:
        """
        Get user display information from audit entry.
        
        Args:
            audit_entry (AppointmentAudit): The audit entry
            
        Returns:
            Dict containing user display information
        """
        try:
            # Try to get user from relationship first
            if audit_entry.user:
                # For users, use email as display name since User model doesn't have get_display_name
                display_name = audit_entry.user.email.split('@')[0].replace('.', ' ').title()
                return {
                    'name': display_name,
                    'role': audit_entry.user_role or audit_entry.user.role,
                    'email': audit_entry.user.email
                }
            
            # Fall back to stored user information
            if audit_entry.user_email:
                # Try to find user by email to get current name
                user = User.query.filter_by(email=audit_entry.user_email).first()
                if user:
                    display_name = user.email.split('@')[0].replace('.', ' ').title()
                    return {
                        'name': display_name,
                        'role': audit_entry.user_role or user.role,
                        'email': audit_entry.user_email
                    }
                
                # Use email as name if user not found
                return {
                    'name': audit_entry.user_email.split('@')[0].replace('.', ' ').title(),
                    'role': audit_entry.user_role or 'Unknown',
                    'email': audit_entry.user_email
                }
            
            # System action or unknown user
            return {
                'name': 'System',
                'role': 'System',
                'email': ''
            }
            
        except Exception as e:
            try:
                current_app.logger.error(f"Error getting user display info: {str(e)}")
            except RuntimeError:
                # No application context available, skip logging
                pass
            return {
                'name': 'Unknown User',
                'role': 'Unknown',
                'email': ''
            }
    
    @staticmethod
    def _format_changes_detail(audit_entry: AppointmentAudit) -> List[Dict[str, Any]]:
        """
        Format detailed changes information for display with enhanced comparison.
        
        Args:
            audit_entry (AppointmentAudit): The audit entry
            
        Returns:
            List of change detail dictionaries with enhanced formatting
        """
        changes = []
        
        try:
            if audit_entry.action == 'create':
                # For create actions, show the initial values
                if audit_entry.new_values:
                    for field, value in audit_entry.new_values.items():
                        if value is not None and value != '':
                            formatted_value = AuditService._format_field_value(field, value)
                            change_info = AuditService._get_change_metadata(field, None, value)
                            changes.append({
                                'field': field,
                                'field_display': AuditService._get_field_display_name(field),
                                'change_type': 'created',
                                'old_value': None,
                                'new_value': value,
                                'old_value_display': None,
                                'new_value_display': formatted_value,
                                'field_category': change_info['category'],
                                'importance': change_info['importance'],
                                'visual_indicator': change_info['visual_indicator'],
                                'description': f"Initial {AuditService._get_field_display_name(field).lower()} set"
                            })
                return changes
            
            if audit_entry.action in ['update', 'cancel'] and audit_entry.old_values and audit_entry.new_values:
                old_values = audit_entry.old_values or {}
                new_values = audit_entry.new_values or {}
                
                # Get all fields that changed
                all_fields = set(old_values.keys()) | set(new_values.keys())
                
                for field in all_fields:
                    old_val = old_values.get(field)
                    new_val = new_values.get(field)
                    
                    # Skip if values are the same (with proper comparison for different types)
                    if AuditService._values_are_equal(old_val, new_val):
                        continue
                    
                    # Format values for display
                    old_display = AuditService._format_field_value(field, old_val)
                    new_display = AuditService._format_field_value(field, new_val)
                    
                    # Get change metadata for enhanced display
                    change_info = AuditService._get_change_metadata(field, old_val, new_val)
                    
                    changes.append({
                        'field': field,
                        'field_display': AuditService._get_field_display_name(field),
                        'change_type': 'updated' if audit_entry.action == 'update' else 'cancelled',
                        'old_value': old_val,
                        'new_value': new_val,
                        'old_value_display': old_display,
                        'new_value_display': new_display,
                        'field_category': change_info['category'],
                        'importance': change_info['importance'],
                        'visual_indicator': change_info['visual_indicator'],
                        'description': AuditService._get_change_description(field, old_val, new_val, audit_entry.action)
                    })
            
            elif audit_entry.action == 'delete':
                # For delete actions, show what was deleted
                if audit_entry.old_values:
                    for field, value in audit_entry.old_values.items():
                        if value is not None and value != '':
                            formatted_value = AuditService._format_field_value(field, value)
                            change_info = AuditService._get_change_metadata(field, value, None)
                            changes.append({
                                'field': field,
                                'field_display': AuditService._get_field_display_name(field),
                                'change_type': 'deleted',
                                'old_value': value,
                                'new_value': None,
                                'old_value_display': formatted_value,
                                'new_value_display': None,
                                'field_category': change_info['category'],
                                'importance': change_info['importance'],
                                'visual_indicator': change_info['visual_indicator'],
                                'description': f"{AuditService._get_field_display_name(field)} was removed"
                            })
            
            # Sort changes by importance and category
            changes.sort(key=lambda x: (x['importance'], x['field_category'], x['field']))
            
            return changes
            
        except Exception as e:
            try:
                current_app.logger.error(f"Error formatting changes detail: {str(e)}")
            except RuntimeError:
                # No application context available, skip logging
                pass
            return []
    
    @staticmethod
    def _format_field_value(field: str, value: Any) -> str:
        """
        Format a field value for display.
        
        Args:
            field (str): The field name
            value (Any): The field value
            
        Returns:
            str: Formatted value for display
        """
        if value is None:
            return "Not set"
        
        if value == "":
            return "Empty"
        
        # Handle datetime fields
        if field in ['start_time', 'end_time'] and isinstance(value, str):
            try:
                from datetime import datetime
                dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
                return TimezoneService.format_for_display(dt, include_timezone=True, long_format=False)
            except:
                return str(value)
        
        # Handle user ID fields
        if field in ['tutor_id', 'client_id']:
            try:
                if field == 'tutor_id':
                    tutor = Tutor.query.get(value)
                    return tutor.full_name if tutor else f"Tutor ID {value}"
                elif field == 'client_id':
                    client = Client.query.get(value)
                    return client.full_name if client else f"Client ID {value}"
            except:
                return f"ID {value}"
        
        # Handle monetary values
        if field == 'transport_fee' and value is not None:
            try:
                return f"${float(value):.2f}"
            except:
                return str(value)
        
        # Handle duration
        if field == 'duration_minutes' and value is not None:
            try:
                minutes = int(value)
                if minutes >= 60:
                    hours = minutes // 60
                    remaining_minutes = minutes % 60
                    if remaining_minutes > 0:
                        return f"{hours}h {remaining_minutes}m"
                    else:
                        return f"{hours}h"
                else:
                    return f"{minutes}m"
            except:
                return str(value)
        
        # Handle status with capitalization
        if field == 'status':
            return str(value).replace('_', ' ').title()
        
        return str(value)
    
    @staticmethod
    def _get_field_display_name(field: str) -> str:
        """
        Get a human-readable display name for a field.
        
        Args:
            field (str): The field name
            
        Returns:
            str: Human-readable field name
        """
        field_names = {
            'status': 'Status',
            'tutor_id': 'Tutor',
            'client_id': 'Client',
            'dependant_id': 'Dependant',
            'start_time': 'Start Time',
            'end_time': 'End Time',
            'notes': 'Notes',
            'transport_fee': 'Transport Fee',
            'duration_minutes': 'Duration',
            'tutor_service_id': 'Service',
            'created_at': 'Created At',
            'updated_at': 'Updated At'
        }
        
        return field_names.get(field, field.replace('_', ' ').title())
    
    @staticmethod
    def _values_are_equal(val1: Any, val2: Any) -> bool:
        """
        Compare two values for equality, handling different types properly.
        
        Args:
            val1: First value
            val2: Second value
            
        Returns:
            bool: Whether values are equal
        """
        try:
            # Handle None values
            if val1 is None and val2 is None:
                return True
            if val1 is None or val2 is None:
                return False
            
            # Handle string comparison
            if isinstance(val1, str) and isinstance(val2, str):
                return val1.strip() == val2.strip()
            
            # Handle numeric comparison
            if isinstance(val1, (int, float)) and isinstance(val2, (int, float)):
                return abs(val1 - val2) < 0.001  # Handle floating point precision
            
            # Handle datetime comparison
            if hasattr(val1, 'isoformat') and hasattr(val2, 'isoformat'):
                return val1.isoformat() == val2.isoformat()
            
            # Default comparison
            return val1 == val2
            
        except Exception:
            # If comparison fails, assume they're different
            return False
    
    @staticmethod
    def _get_change_metadata(field: str, old_value: Any, new_value: Any) -> Dict[str, Any]:
        """
        Get metadata about a field change for enhanced display.
        
        Args:
            field (str): The field name
            old_value: The old value
            new_value: The new value
            
        Returns:
            Dict containing change metadata
        """
        # Categorize fields
        critical_fields = ['status', 'start_time', 'end_time', 'tutor_id', 'client_id']
        important_fields = ['notes', 'transport_fee', 'duration_minutes']
        
        if field in critical_fields:
            category = 'critical'
            importance = 1
        elif field in important_fields:
            category = 'important'
            importance = 2
        else:
            category = 'minor'
            importance = 3
        
        # Determine visual indicator
        if old_value is None and new_value is not None:
            visual_indicator = '➕'  # Added
        elif old_value is not None and new_value is None:
            visual_indicator = '➖'  # Removed
        else:
            visual_indicator = '✏️'  # Modified
        
        return {
            'category': category,
            'importance': importance,
            'visual_indicator': visual_indicator
        }
    
    @staticmethod
    def _get_change_description(field: str, old_value: Any, new_value: Any, action: str) -> str:
        """
        Get a human-readable description of a field change.
        
        Args:
            field (str): The field name
            old_value: The old value
            new_value: The new value
            action (str): The action type
            
        Returns:
            str: Human-readable change description
        """
        field_display = AuditService._get_field_display_name(field)
        
        if action == 'cancel':
            return f"{field_display} changed during cancellation"
        
        if old_value is None and new_value is not None:
            return f"{field_display} was set"
        elif old_value is not None and new_value is None:
            return f"{field_display} was removed"
        else:
            return f"{field_display} was changed"
    
    @staticmethod
    def _format_field_changes_for_frontend(changes_detail: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Format field changes for frontend compatibility.
        
        Args:
            changes_detail: List of change detail dictionaries
            
        Returns:
            List of frontend-compatible change objects
        """
        try:
            frontend_changes = []
            
            for change in changes_detail:
                frontend_change = {
                    'field': change.get('field', ''),
                    'fieldDisplay': change.get('field_display', ''),
                    'changeType': change.get('change_type', 'updated'),
                    'oldValue': change.get('old_value'),
                    'newValue': change.get('new_value'),
                    'oldValueDisplay': change.get('old_value_display', ''),
                    'newValueDisplay': change.get('new_value_display', ''),
                    'description': change.get('description', ''),
                    'category': change.get('field_category', 'minor'),
                    'importance': change.get('importance', 3),
                    'visualIndicator': change.get('visual_indicator', '✏️')
                }
                frontend_changes.append(frontend_change)
            
            return frontend_changes
            
        except Exception as e:
            try:
                current_app.logger.error(f"Error formatting field changes for frontend: {str(e)}")
            except RuntimeError:
                pass
            return []
    
    @staticmethod
    def _get_initial_values(audit_entry: AppointmentAudit) -> Dict[str, Any]:
        """
        Get initial values for create actions.
        
        Args:
            audit_entry: The audit entry
            
        Returns:
            Dict containing initial values
        """
        try:
            if audit_entry.new_values:
                return {
                    field: AuditService._format_field_value(field, value)
                    for field, value in audit_entry.new_values.items()
                    if value is not None and value != ''
                }
            return {}
        except Exception:
            return {}
    
    @staticmethod
    def _get_deleted_values(audit_entry: AppointmentAudit) -> Dict[str, Any]:
        """
        Get deleted values for delete actions.
        
        Args:
            audit_entry: The audit entry
            
        Returns:
            Dict containing deleted values
        """
        try:
            if audit_entry.old_values:
                return {
                    field: AuditService._format_field_value(field, value)
                    for field, value in audit_entry.old_values.items()
                    if value is not None and value != ''
                }
            return {}
        except Exception:
            return {}
    
    @staticmethod
    def _get_context_info(audit_entry: AppointmentAudit) -> Dict[str, Any]:
        """
        Get contextual information about the audit entry.
        
        Args:
            audit_entry: The audit entry
            
        Returns:
            Dict containing context information
        """
        try:
            context = {
                'entry_id': audit_entry.id,
                'appointment_id': audit_entry.appointment_id,
                'timestamp_utc': audit_entry.timestamp.isoformat() if audit_entry.timestamp else None,
                'has_notes': bool(audit_entry.notes),
                'notes_preview': audit_entry.notes[:100] + '...' if audit_entry.notes and len(audit_entry.notes) > 100 else audit_entry.notes
            }
            
            # Add change count for update actions
            if audit_entry.action == 'update' and audit_entry.old_values and audit_entry.new_values:
                old_values = audit_entry.old_values or {}
                new_values = audit_entry.new_values or {}
                all_fields = set(old_values.keys()) | set(new_values.keys())
                
                change_count = sum(1 for field in all_fields 
                                 if not AuditService._values_are_equal(old_values.get(field), new_values.get(field)))
                context['change_count'] = change_count
            
            return context
            
        except Exception as e:
            try:
                current_app.logger.error(f"Error getting context info: {str(e)}")
            except RuntimeError:
                pass
            return {'entry_id': audit_entry.id if audit_entry else 'unknown'}
    
    @staticmethod
    def _get_action_display_info(action: str) -> Dict[str, str]:
        """
        Get display information for an action type.
        
        Args:
            action (str): The action type
            
        Returns:
            Dict containing icon and color information
        """
        action_info = {
            'create': {'icon': '➕', 'color': 'success'},
            'update': {'icon': '✏️', 'color': 'primary'},
            'delete': {'icon': '🗑️', 'color': 'danger'},
            'cancel': {'icon': '❌', 'color': 'warning'},
            'read': {'icon': '👁️', 'color': 'info'}
        }
        
        return action_info.get(action, {'icon': '📝', 'color': 'secondary'})
    
    @staticmethod
    def clear_cache():
        """Clear the audit cache."""
        try:
            AuditService._audit_cache.clear()
            AuditService._cache_timestamps.clear()
            try:
                current_app.logger.info("Audit service cache cleared")
            except RuntimeError:
                pass
        except Exception as e:
            try:
                current_app.logger.error(f"Error clearing audit cache: {str(e)}")
            except RuntimeError:
                pass
    
    @staticmethod
    def get_cache_stats() -> Dict[str, Any]:
        """Get cache statistics for monitoring."""
        try:
            return {
                'cache_size': len(AuditService._audit_cache),
                'cache_keys': list(AuditService._audit_cache.keys()),
                'oldest_entry': min(AuditService._cache_timestamps.values()) if AuditService._cache_timestamps else None,
                'newest_entry': max(AuditService._cache_timestamps.values()) if AuditService._cache_timestamps else None,
                'cache_ttl': AuditService._cache_ttl
            }
        except Exception as e:
            try:
                current_app.logger.error(f"Error getting cache stats: {str(e)}")
            except RuntimeError:
                pass
            return {'error': str(e)}
        
        return field_names.get(field, field.replace('_', ' ').title())
    
    @staticmethod
    def _values_are_equal(val1: Any, val2: Any) -> bool:
        """
        Compare two values for equality, handling different types properly.
        
        Args:
            val1: First value
            val2: Second value
            
        Returns:
            bool: Whether values are equal
        """
        try:
            # Handle None values
            if val1 is None and val2 is None:
                return True
            if val1 is None or val2 is None:
                return False
            
            # Handle string comparison
            if isinstance(val1, str) and isinstance(val2, str):
                return val1.strip() == val2.strip()
            
            # Handle numeric comparison
            if isinstance(val1, (int, float)) and isinstance(val2, (int, float)):
                return abs(val1 - val2) < 0.001  # Handle floating point precision
            
            # Handle datetime comparison
            if hasattr(val1, 'isoformat') and hasattr(val2, 'isoformat'):
                return val1.isoformat() == val2.isoformat()
            
            # Default comparison
            return val1 == val2
            
        except Exception:
            # If comparison fails, assume they're different
            return False
    
    @staticmethod
    def _get_change_metadata(field: str, old_value: Any, new_value: Any) -> Dict[str, Any]:
        """
        Get metadata about a field change for enhanced display.
        
        Args:
            field (str): The field name
            old_value: The old value
            new_value: The new value
            
        Returns:
            Dict containing change metadata
        """
        # Categorize fields
        critical_fields = ['status', 'start_time', 'end_time', 'tutor_id', 'client_id']
        important_fields = ['notes', 'transport_fee', 'duration_minutes']
        
        if field in critical_fields:
            category = 'critical'
            importance = 1
        elif field in important_fields:
            category = 'important'
            importance = 2
        else:
            category = 'minor'
            importance = 3
        
        # Determine visual indicator
        if old_value is None and new_value is not None:
            visual_indicator = '➕'  # Added
        elif old_value is not None and new_value is None:
            visual_indicator = '➖'  # Removed
        else:
            visual_indicator = '✏️'  # Modified
        
        return {
            'category': category,
            'importance': importance,
            'visual_indicator': visual_indicator
        }
    
    @staticmethod
    def _get_change_description(field: str, old_value: Any, new_value: Any, action: str) -> str:
        """
        Get a human-readable description of a field change.
        
        Args:
            field (str): The field name
            old_value: The old value
            new_value: The new value
            action (str): The action type
            
        Returns:
            str: Human-readable change description
        """
        field_display = AuditService._get_field_display_name(field)
        
        if action == 'cancel':
            return f"{field_display} changed during cancellation"
        
        if old_value is None and new_value is not None:
            return f"{field_display} was set"
        elif old_value is not None and new_value is None:
            return f"{field_display} was removed"
        else:
            return f"{field_display} was changed"
    
    @staticmethod
    def _format_field_changes_for_frontend(changes_detail: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Format field changes for frontend compatibility.
        
        Args:
            changes_detail: List of change detail dictionaries
            
        Returns:
            List of frontend-compatible change objects
        """
        try:
            frontend_changes = []
            
            for change in changes_detail:
                frontend_change = {
                    'field': change.get('field', ''),
                    'fieldDisplay': change.get('field_display', ''),
                    'changeType': change.get('change_type', 'updated'),
                    'oldValue': change.get('old_value'),
                    'newValue': change.get('new_value'),
                    'oldValueDisplay': change.get('old_value_display', ''),
                    'newValueDisplay': change.get('new_value_display', ''),
                    'description': change.get('description', ''),
                    'category': change.get('field_category', 'minor'),
                    'importance': change.get('importance', 3),
                    'visualIndicator': change.get('visual_indicator', '✏️')
                }
                frontend_changes.append(frontend_change)
            
            return frontend_changes
            
        except Exception as e:
            try:
                current_app.logger.error(f"Error formatting field changes for frontend: {str(e)}")
            except RuntimeError:
                pass
            return []
    
    @staticmethod
    def _get_initial_values(audit_entry: AppointmentAudit) -> Dict[str, Any]:
        """
        Get initial values for create actions.
        
        Args:
            audit_entry: The audit entry
            
        Returns:
            Dict containing initial values
        """
        try:
            if audit_entry.new_values:
                return {
                    field: AuditService._format_field_value(field, value)
                    for field, value in audit_entry.new_values.items()
                    if value is not None and value != ''
                }
            return {}
        except Exception:
            return {}
    
    @staticmethod
    def _get_deleted_values(audit_entry: AppointmentAudit) -> Dict[str, Any]:
        """
        Get deleted values for delete actions.
        
        Args:
            audit_entry: The audit entry
            
        Returns:
            Dict containing deleted values
        """
        try:
            if audit_entry.old_values:
                return {
                    field: AuditService._format_field_value(field, value)
                    for field, value in audit_entry.old_values.items()
                    if value is not None and value != ''
                }
            return {}
        except Exception:
            return {}
    
    @staticmethod
    def _get_context_info(audit_entry: AppointmentAudit) -> Dict[str, Any]:
        """
        Get contextual information about the audit entry.
        
        Args:
            audit_entry: The audit entry
            
        Returns:
            Dict containing context information
        """
        try:
            context = {
                'entry_id': audit_entry.id,
                'appointment_id': audit_entry.appointment_id,
                'timestamp_utc': audit_entry.timestamp.isoformat() if audit_entry.timestamp else None,
                'has_notes': bool(audit_entry.notes),
                'notes_preview': audit_entry.notes[:100] + '...' if audit_entry.notes and len(audit_entry.notes) > 100 else audit_entry.notes
            }
            
            # Add change count for update actions
            if audit_entry.action == 'update' and audit_entry.old_values and audit_entry.new_values:
                old_values = audit_entry.old_values or {}
                new_values = audit_entry.new_values or {}
                all_fields = set(old_values.keys()) | set(new_values.keys())
                
                change_count = sum(1 for field in all_fields 
                                 if not AuditService._values_are_equal(old_values.get(field), new_values.get(field)))
                context['change_count'] = change_count
            
            return context
            
        except Exception as e:
            try:
                current_app.logger.error(f"Error getting context info: {str(e)}")
            except RuntimeError:
                pass
            return {'entry_id': audit_entry.id if audit_entry else 'unknown'}
    
    @staticmethod
    def _get_action_display_info(action: str) -> Dict[str, str]:
        """
        Get display information for an action type.
        
        Args:
            action (str): The action type
            
        Returns:
            Dict containing icon and color information
        """
        action_info = {
            'create': {'icon': '➕', 'color': 'success'},
            'update': {'icon': '✏️', 'color': 'primary'},
            'delete': {'icon': '🗑️', 'color': 'danger'},
            'cancel': {'icon': '❌', 'color': 'warning'},
            'read': {'icon': '👁️', 'color': 'info'}
        }
        
        return action_info.get(action, {'icon': '📝', 'color': 'secondary'})
    
    @staticmethod
    def clear_cache():
        """Clear the audit cache."""
        try:
            AuditService._audit_cache.clear()
            AuditService._cache_timestamps.clear()
            try:
                current_app.logger.info("Audit service cache cleared")
            except RuntimeError:
                pass
        except Exception as e:
            try:
                current_app.logger.error(f"Error clearing audit cache: {str(e)}")
            except RuntimeError:
                pass
    
    @staticmethod
    def get_cache_stats() -> Dict[str, Any]:
        """Get cache statistics for monitoring."""
        try:
            return {
                'cache_size': len(AuditService._audit_cache),
                'cache_keys': list(AuditService._audit_cache.keys()),
                'oldest_entry': min(AuditService._cache_timestamps.values()) if AuditService._cache_timestamps else None,
                'newest_entry': max(AuditService._cache_timestamps.values()) if AuditService._cache_timestamps else None,
                'cache_ttl': AuditService._cache_ttl
            }
        except Exception as e:
            try:
                current_app.logger.error(f"Error getting cache stats: {str(e)}")
            except RuntimeError:
                pass
            return {'error': str(e)}
    
    @staticmethod
    def _get_action_display_info(action: str) -> Dict[str, str]:
        """
        Get display information for an action type.
        
        Args:
            action (str): The action type
            
        Returns:
            Dict containing icon and color information
        """
        action_info = {
            'create': {'icon': '➕', 'color': 'green'},
            'update': {'icon': '📝', 'color': 'blue'},
            'delete': {'icon': '🗑️', 'color': 'red'},
            'cancel': {'icon': '❌', 'color': 'orange'}
        }
        
        return action_info.get(action, {'icon': '📝', 'color': 'gray'})
    
    @staticmethod
    def _values_are_equal(old_val: Any, new_val: Any) -> bool:
        """
        Compare two values for equality, handling different data types properly.
        
        Args:
            old_val: The old value
            new_val: The new value
            
        Returns:
            bool: True if values are considered equal
        """
        # Handle None values
        if old_val is None and new_val is None:
            return True
        if old_val is None or new_val is None:
            return False
        
        # Handle empty strings
        if str(old_val).strip() == '' and str(new_val).strip() == '':
            return True
        
        # Handle numeric values (for transport_fee, etc.)
        try:
            if isinstance(old_val, (int, float)) or isinstance(new_val, (int, float)):
                return float(old_val) == float(new_val)
        except (ValueError, TypeError):
            pass
        
        # Handle datetime strings
        if isinstance(old_val, str) and isinstance(new_val, str):
            # Try to parse as datetime and compare
            try:
                from datetime import datetime
                old_dt = datetime.fromisoformat(old_val.replace('Z', '+00:00'))
                new_dt = datetime.fromisoformat(new_val.replace('Z', '+00:00'))
                return old_dt == new_dt
            except (ValueError, AttributeError):
                pass
        
        # Default string comparison
        return str(old_val) == str(new_val)
    
    @staticmethod
    def _get_change_metadata(field: str, old_value: Any, new_value: Any) -> Dict[str, Any]:
        """
        Get metadata about a field change for enhanced display.
        
        Args:
            field (str): The field name
            old_value: The old value
            new_value: The new value
            
        Returns:
            Dict containing change metadata
        """
        # Define field categories and importance
        field_categories = {
            'status': {'category': 'core', 'importance': 1},
            'start_time': {'category': 'scheduling', 'importance': 1},
            'end_time': {'category': 'scheduling', 'importance': 1},
            'tutor_id': {'category': 'participants', 'importance': 1},
            'client_id': {'category': 'participants', 'importance': 1},
            'dependant_id': {'category': 'participants', 'importance': 1},
            'transport_fee': {'category': 'financial', 'importance': 2},
            'duration_minutes': {'category': 'scheduling', 'importance': 2},
            'notes': {'category': 'details', 'importance': 3}
        }
        
        field_info = field_categories.get(field, {'category': 'other', 'importance': 3})
        
        # Determine visual indicator based on field and change type
        visual_indicator = AuditService._get_visual_indicator(field, old_value, new_value)
        
        return {
            'category': field_info['category'],
            'importance': field_info['importance'],
            'visual_indicator': visual_indicator
        }
    
    @staticmethod
    def _get_visual_indicator(field: str, old_value: Any, new_value: Any) -> Dict[str, str]:
        """
        Get visual indicator (icon and color) for a field change.
        
        Args:
            field (str): The field name
            old_value: The old value
            new_value: The new value
            
        Returns:
            Dict containing icon and color for the change
        """
        # Status changes get special treatment
        if field == 'status':
            if new_value == 'cancelled':
                return {'icon': '❌', 'color': 'danger'}
            elif new_value == 'completed':
                return {'icon': '✅', 'color': 'success'}
            elif new_value == 'scheduled':
                return {'icon': '📅', 'color': 'primary'}
            else:
                return {'icon': '🔄', 'color': 'warning'}
        
        # Time changes
        elif field in ['start_time', 'end_time']:
            return {'icon': '🕐', 'color': 'info'}
        
        # People changes
        elif field in ['tutor_id', 'client_id', 'dependant_id']:
            return {'icon': '👤', 'color': 'primary'}
        
        # Financial changes
        elif field == 'transport_fee':
            return {'icon': '💰', 'color': 'warning'}
        
        # Duration changes
        elif field == 'duration_minutes':
            return {'icon': '⏱️', 'color': 'info'}
        
        # Notes changes
        elif field == 'notes':
            return {'icon': '📝', 'color': 'secondary'}
        
        # Default
        else:
            return {'icon': '📝', 'color': 'secondary'}
    
    @staticmethod
    def _get_change_description(field: str, old_value: Any, new_value: Any, action: str) -> str:
        """
        Get a descriptive text for a field change.
        
        Args:
            field (str): The field name
            old_value: The old value
            new_value: The new value
            action (str): The action type
            
        Returns:
            str: Descriptive text for the change
        """
        field_display = AuditService._get_field_display_name(field)
        
        if action == 'cancel':
            return f"{field_display} changed during cancellation"
        
        # Special descriptions for certain fields
        if field == 'status':
            old_display = AuditService._format_field_value(field, old_value)
            new_display = AuditService._format_field_value(field, new_value)
            return f"Status changed from {old_display} to {new_display}"
        
        elif field in ['start_time', 'end_time']:
            return f"Appointment {field.replace('_', ' ')} was rescheduled"
        
        elif field in ['tutor_id', 'client_id', 'dependant_id']:
            return f"{field_display} was reassigned"
        
        elif field == 'transport_fee':
            old_display = AuditService._format_field_value(field, old_value)
            new_display = AuditService._format_field_value(field, new_value)
            return f"Transport fee changed from {old_display} to {new_display}"
        
        elif field == 'notes':
            if old_value and new_value:
                return "Notes were updated"
            elif new_value:
                return "Notes were added"
            else:
                return "Notes were removed"
        
        else:
            return f"{field_display} was modified"
    
    @staticmethod
    def _format_field_changes_for_frontend(changes_detail: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Format changes detail for frontend compatibility.
        
        Args:
            changes_detail: List of detailed change dictionaries
            
        Returns:
            List of frontend-compatible change dictionaries
        """
        frontend_changes = []
        
        for change in changes_detail:
            frontend_change = {
                'field': change.get('field', ''),
                'field_display_name': change.get('field_display', ''),
                'old_value': change.get('old_value_display', change.get('old_value', '')),
                'new_value': change.get('new_value_display', change.get('new_value', '')),
                'change_type': change.get('change_type', 'updated'),
                'description': change.get('description', ''),
                'field_category': change.get('field_category', 'other'),
                'importance': change.get('importance', 3),
                'visual_indicator': change.get('visual_indicator', {'icon': '📝', 'color': 'secondary'})
            }
            frontend_changes.append(frontend_change)
        
        return frontend_changes
    
    @staticmethod
    def _get_initial_values(audit_entry: AppointmentAudit) -> Dict[str, Any]:
        """
        Get initial values for creation entries.
        
        Args:
            audit_entry: The audit entry
            
        Returns:
            Dict of initial values
        """
        if audit_entry.action == 'create' and audit_entry.new_values:
            return audit_entry.new_values
        return {}
    
    @staticmethod
    def _get_deleted_values(audit_entry: AppointmentAudit) -> Dict[str, Any]:
        """
        Get deleted values for deletion entries.
        
        Args:
            audit_entry: The audit entry
            
        Returns:
            Dict of deleted values
        """
        if audit_entry.action == 'delete' and audit_entry.old_values:
            return audit_entry.old_values
        return {}
    
    @staticmethod
    def _get_context_info(audit_entry: AppointmentAudit) -> Dict[str, Any]:
        """
        Get additional context information for an audit entry.
        
        Args:
            audit_entry: The audit entry
            
        Returns:
            Dict of context information
        """
        context = {}
        
        # Add IP address if available
        if hasattr(audit_entry, 'ip_address') and audit_entry.ip_address:
            context['ip_address'] = audit_entry.ip_address
        
        # Add user agent if available
        if hasattr(audit_entry, 'user_agent') and audit_entry.user_agent:
            context['user_agent'] = audit_entry.user_agent
        
        # Add session info if available
        if hasattr(audit_entry, 'session_id') and audit_entry.session_id:
            context['session_id'] = audit_entry.session_id
        
        return context
        
        for change in changes_detail:
            field_change = {
                'field': change['field'],
                'field_display_name': change['field_display'],
                'old_value': change['old_value_display'] or change.get('old_value', 'Not set'),
                'new_value': change['new_value_display'] or change.get('new_value', 'Not set'),
                'change_type': change['change_type'],
                'description': change.get('description', ''),
                'visual_indicator': change.get('visual_indicator', {'icon': '📝', 'color': 'secondary'}),
                'field_category': change.get('field_category', 'other'),
                'importance': change.get('importance', 3)
            }
            field_changes.append(field_change)
        
        return field_changes
    
    @staticmethod
    def _get_initial_values(audit_entry: AppointmentAudit) -> Dict[str, Any]:
        """
        Get initial values for create actions.
        
        Args:
            audit_entry: The audit entry
            
        Returns:
            Dict of initial values formatted for display
        """
        if audit_entry.action != 'create' or not audit_entry.new_values:
            return {}
        
        initial_values = {}
        for field, value in audit_entry.new_values.items():
            if value is not None and value != '':
                initial_values[field] = AuditService._format_field_value(field, value)
        
        return initial_values
    
    @staticmethod
    def _get_deleted_values(audit_entry: AppointmentAudit) -> Dict[str, Any]:
        """
        Get deleted values for delete actions.
        
        Args:
            audit_entry: The audit entry
            
        Returns:
            Dict of deleted values formatted for display
        """
        if audit_entry.action != 'delete' or not audit_entry.old_values:
            return {}
        
        deleted_values = {}
        for field, value in audit_entry.old_values.items():
            if value is not None and value != '':
                deleted_values[field] = AuditService._format_field_value(field, value)
        
        return deleted_values
    
    @staticmethod
    def _get_context_info(audit_entry: AppointmentAudit) -> Dict[str, Any]:
        """
        Get additional context information for the audit entry.
        
        Args:
            audit_entry: The audit entry
            
        Returns:
            Dict of context information
        """
        context = {}
        
        # Add user agent or IP if available (would need to be stored in audit entry)
        if hasattr(audit_entry, 'user_agent') and audit_entry.user_agent:
            context['user_agent'] = audit_entry.user_agent
        
        # Add any additional metadata
        if audit_entry.notes:
            context['notes'] = audit_entry.notes
        
        # Add action-specific context
        if audit_entry.action == 'cancel' and audit_entry.old_values and audit_entry.new_values:
            # For cancellations, show what the status changed from/to
            old_status = audit_entry.old_values.get('status')
            new_status = audit_entry.new_values.get('status')
            if old_status and new_status:
                context['status_change'] = f"Changed from {old_status} to {new_status}"
        
        return context   
 
    # ===== PERFORMANCE OPTIMIZATION METHODS =====
    
    @staticmethod
    def get_appointment_audit_history_optimized(appointment_id: int, page: int = 1, per_page: int = 20, use_cache: bool = True) -> Dict[str, Any]:
        """
        Enhanced version of get_appointment_audit_history with caching and performance optimizations.
        
        Args:
            appointment_id (int): The appointment ID to get audit history for
            page (int): Page number for pagination (default: 1)
            per_page (int): Number of entries per page (default: 20)
            use_cache (bool): Whether to use caching (default: True)
            
        Returns:
            Dict containing optimized audit data with performance metrics
        """
        start_time = time.time()
        cache_key = f"{appointment_id}_{page}_{per_page}"
        
        # Check cache first if enabled
        if use_cache and AuditService._is_cache_valid(cache_key):
            cached_data = AuditService._audit_cache.get(cache_key)
            if cached_data:
                cached_data['performance'] = {
                    'cache_hit': True,
                    'query_time_ms': 0,
                    'total_time_ms': round((time.time() - start_time) * 1000, 2),
                    'cache_size': len(AuditService._audit_cache),
                    'cache_ttl_remaining': AuditService._cache_ttl - (time.time() - AuditService._cache_timestamps.get(cache_key, 0))
                }
                return cached_data
        
        try:
            # Optimized query with eager loading and proper indexing
            query = db.session.query(AppointmentAudit).options(
                db.joinedload(AppointmentAudit.user)
            ).filter(
                AppointmentAudit.appointment_id == appointment_id
            ).order_by(desc(AppointmentAudit.timestamp))
            
            # Use optimized count query for large datasets
            count_start = time.time()
            if use_cache:
                count_cache_key = f"count_{appointment_id}"
                if AuditService._is_cache_valid(count_cache_key):
                    total_count = AuditService._audit_cache.get(count_cache_key, 0)
                else:
                    total_count = query.count()
                    AuditService._cache_result(count_cache_key, total_count)
            else:
                total_count = query.count()
            count_time = time.time() - count_start
            
            # Only fetch the items we need for this page
            fetch_start = time.time()
            offset = (page - 1) * per_page
            items = query.offset(offset).limit(per_page).all()
            fetch_time = time.time() - fetch_start
            
            # Format entries for display with batch processing
            format_start = time.time()
            formatted_entries = AuditService._batch_format_entries(items)
            format_time = time.time() - format_start
            
            query_time = time.time() - start_time
            
            # Calculate pagination metadata
            total_pages = (total_count + per_page - 1) // per_page
            has_prev = page > 1
            has_next = page < total_pages
            prev_num = page - 1 if has_prev else None
            next_num = page + 1 if has_next else None
            
            # Determine if virtual scrolling should be recommended
            should_use_virtual_scrolling = total_count > 100
            
            result = {
                'entries': formatted_entries,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total_pages': total_pages,
                    'has_prev': has_prev,
                    'has_next': has_next,
                    'prev_num': prev_num,
                    'next_num': next_num
                },
                'total': total_count,
                'virtual_scrolling': {
                    'recommended': should_use_virtual_scrolling,
                    'threshold': 100,
                    'estimated_item_height': 120,
                    'viewport_size': 10
                },
                'performance': {
                    'cache_hit': False,
                    'count_query_ms': round(count_time * 1000, 2),
                    'fetch_query_ms': round(fetch_time * 1000, 2),
                    'format_time_ms': round(format_time * 1000, 2),
                    'total_time_ms': round(query_time * 1000, 2),
                    'entries_processed': len(items),
                    'cache_size': len(AuditService._audit_cache),
                    'optimization_level': 'high' if use_cache else 'standard'
                }
            }
            
            # Cache the result if caching is enabled
            if use_cache:
                AuditService._cache_result(cache_key, result)
            
            return result
            
        except Exception as e:
            try:
                current_app.logger.error(f"Error in optimized audit history for appointment {appointment_id}: {str(e)}")
            except RuntimeError:
                pass
            
            # Return empty result with error info
            return {
                'entries': [],
                'pagination': {
                    'page': 1,
                    'per_page': per_page,
                    'total_pages': 0,
                    'has_prev': False,
                    'has_next': False,
                    'prev_num': None,
                    'next_num': None
                },
                'total': 0,
                'virtual_scrolling': {
                    'recommended': False,
                    'threshold': 100,
                    'estimated_item_height': 120,
                    'viewport_size': 10
                },
                'error': str(e),
                'performance': {
                    'cache_hit': False,
                    'query_time_ms': 0,
                    'total_time_ms': round((time.time() - start_time) * 1000, 2),
                    'entries_processed': 0,
                    'cache_size': len(AuditService._audit_cache),
                    'optimization_level': 'error'
                }
            }
    
    @staticmethod
    def _batch_format_entries(entries: List[AppointmentAudit]) -> List[Dict[str, Any]]:
        """
        Batch format multiple audit entries for improved performance.
        
        Args:
            entries: List of AppointmentAudit objects
            
        Returns:
            List of formatted audit entries
        """
        if not entries:
            return []
        
        # Pre-fetch related data to avoid N+1 queries
        user_ids = [entry.user_id for entry in entries if entry.user_id]
        users_dict = {}
        if user_ids:
            users = User.query.filter(User.id.in_(user_ids)).all()
            users_dict = {user.id: user for user in users}
        
        # Batch format entries
        formatted_entries = []
        for entry in entries:
            try:
                # Use cached user data if available
                if entry.user_id and entry.user_id in users_dict:
                    entry._cached_user = users_dict[entry.user_id]
                
                formatted_entry = AuditService.format_audit_entry_for_display(entry)
                formatted_entries.append(formatted_entry)
            except Exception as e:
                try:
                    current_app.logger.error(f"Error formatting audit entry {entry.id}: {str(e)}")
                except RuntimeError:
                    pass
                # Skip problematic entries rather than failing completely
                continue
        
        return formatted_entries
    
    @staticmethod
    def _is_cache_valid(cache_key: str) -> bool:
        """
        Check if cached data is still valid based on TTL.
        
        Args:
            cache_key: The cache key to check
            
        Returns:
            bool: True if cache is valid, False otherwise
        """
        if cache_key not in AuditService._cache_timestamps:
            return False
        
        cache_time = AuditService._cache_timestamps[cache_key]
        return (time.time() - cache_time) < AuditService._cache_ttl
    
    @staticmethod
    def _cache_result(cache_key: str, result: Dict[str, Any]) -> None:
        """
        Cache audit result data.
        
        Args:
            cache_key: The cache key
            result: The result data to cache
        """
        # Implement simple LRU-like behavior by limiting cache size
        if len(AuditService._audit_cache) > 100:  # Max 100 cached entries
            # Remove oldest entries
            oldest_keys = sorted(AuditService._cache_timestamps.keys(), 
                               key=lambda k: AuditService._cache_timestamps[k])[:20]
            for key in oldest_keys:
                AuditService._audit_cache.pop(key, None)
                AuditService._cache_timestamps.pop(key, None)
        
        # Store the result (without performance data to avoid caching that)
        cached_result = result.copy()
        cached_result.pop('performance', None)
        
        AuditService._audit_cache[cache_key] = cached_result
        AuditService._cache_timestamps[cache_key] = time.time()
    
    @staticmethod
    def clear_audit_cache(appointment_id: Optional[int] = None) -> None:
        """
        Clear audit cache for a specific appointment or all cached data.
        
        Args:
            appointment_id: If provided, clear cache only for this appointment
        """
        if appointment_id is None:
            # Clear all cache
            AuditService._audit_cache.clear()
            AuditService._cache_timestamps.clear()
        else:
            # Clear cache for specific appointment
            keys_to_remove = [key for key in AuditService._audit_cache.keys() 
                            if key.startswith(f"{appointment_id}_")]
            for key in keys_to_remove:
                AuditService._audit_cache.pop(key, None)
                AuditService._cache_timestamps.pop(key, None)
    
    @staticmethod
    def verify_database_indexes() -> Dict[str, Any]:
        """
        Verify that proper database indexes exist for optimal audit query performance.
        
        Returns:
            Dict containing index verification results
        """
        try:
            # Check for expected indexes on appointment_audit table
            index_queries = [
                {
                    'name': 'appointment_id_index',
                    'query': """
                        SELECT indexname, indexdef 
                        FROM pg_indexes 
                        WHERE tablename = 'appointment_audit' 
                        AND indexdef LIKE '%appointment_id%'
                    """
                },
                {
                    'name': 'timestamp_index',
                    'query': """
                        SELECT indexname, indexdef 
                        FROM pg_indexes 
                        WHERE tablename = 'appointment_audit' 
                        AND indexdef LIKE '%timestamp%'
                    """
                },
                {
                    'name': 'action_index',
                    'query': """
                        SELECT indexname, indexdef 
                        FROM pg_indexes 
                        WHERE tablename = 'appointment_audit' 
                        AND indexdef LIKE '%action%'
                    """
                },
                {
                    'name': 'composite_index',
                    'query': """
                        SELECT indexname, indexdef 
                        FROM pg_indexes 
                        WHERE tablename = 'appointment_audit' 
                        AND (indexdef LIKE '%appointment_id%' AND indexdef LIKE '%timestamp%')
                    """
                }
            ]
            
            results = {}
            missing_indexes = []
            
            for index_check in index_queries:
                try:
                    result = db.session.execute(text(index_check['query'])).fetchall()
                    results[index_check['name']] = {
                        'exists': len(result) > 0,
                        'indexes': [{'name': row[0], 'definition': row[1]} for row in result]
                    }
                    
                    if not result:
                        missing_indexes.append(index_check['name'])
                        
                except Exception as e:
                    results[index_check['name']] = {
                        'exists': False,
                        'error': str(e)
                    }
                    missing_indexes.append(index_check['name'])
            
            # Check table statistics for performance insights
            try:
                stats_query = """
                    SELECT 
                        schemaname,
                        tablename,
                        n_tup_ins as inserts,
                        n_tup_upd as updates,
                        n_tup_del as deletes,
                        n_live_tup as live_tuples,
                        n_dead_tup as dead_tuples,
                        last_vacuum,
                        last_autovacuum,
                        last_analyze,
                        last_autoanalyze
                    FROM pg_stat_user_tables 
                    WHERE tablename = 'appointment_audit'
                """
                
                stats_result = db.session.execute(text(stats_query)).fetchone()
                table_stats = dict(stats_result) if stats_result else {}
                
            except Exception as e:
                table_stats = {'error': str(e)}
            
            return {
                'indexes': results,
                'missing_indexes': missing_indexes,
                'table_statistics': table_stats,
                'recommendations': AuditService._get_index_recommendations(results, missing_indexes),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            try:
                current_app.logger.error(f"Error verifying database indexes: {str(e)}")
            except RuntimeError:
                pass
            
            return {
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    @staticmethod
    def _get_index_recommendations(index_results: Dict[str, Any], missing_indexes: List[str]) -> List[str]:
        """
        Generate index recommendations based on verification results.
        
        Args:
            index_results: Results from index verification
            missing_indexes: List of missing index names
            
        Returns:
            List of recommendation strings
        """
        recommendations = []
        
        if 'appointment_id_index' in missing_indexes:
            recommendations.append(
                "CREATE INDEX idx_appointment_audit_appointment_id ON appointment_audit(appointment_id);"
            )
        
        if 'timestamp_index' in missing_indexes:
            recommendations.append(
                "CREATE INDEX idx_appointment_audit_timestamp ON appointment_audit(timestamp DESC);"
            )
        
        if 'action_index' in missing_indexes:
            recommendations.append(
                "CREATE INDEX idx_appointment_audit_action ON appointment_audit(action);"
            )
        
        if 'composite_index' in missing_indexes:
            recommendations.append(
                "CREATE INDEX idx_appointment_audit_composite ON appointment_audit(appointment_id, timestamp DESC);"
            )
        
        # Additional performance recommendations
        recommendations.extend([
            "Consider running VACUUM ANALYZE appointment_audit; to update table statistics",
            "Monitor query performance with EXPLAIN ANALYZE for complex audit queries",
            "Consider partitioning the audit table by date if it grows very large"
        ])
        
        return recommendations
    
    @staticmethod
    def get_audit_performance_metrics(appointment_id: int) -> Dict[str, Any]:
        """
        Get performance metrics for audit queries on a specific appointment.
        
        Args:
            appointment_id: The appointment ID to analyze
            
        Returns:
            Dict containing performance metrics
        """
        try:
            start_time = time.time()
            
            # Test query performance
            count_query_start = time.time()
            total_entries = AppointmentAudit.query.filter_by(appointment_id=appointment_id).count()
            count_query_time = time.time() - count_query_start
            
            # Test paginated query performance
            paginated_query_start = time.time()
            first_page = AppointmentAudit.query.filter_by(
                appointment_id=appointment_id
            ).order_by(desc(AppointmentAudit.timestamp)).limit(20).all()
            paginated_query_time = time.time() - paginated_query_start
            
            # Test with eager loading
            eager_query_start = time.time()
            eager_results = db.session.query(AppointmentAudit).options(
                db.joinedload(AppointmentAudit.user)
            ).filter(
                AppointmentAudit.appointment_id == appointment_id
            ).order_by(desc(AppointmentAudit.timestamp)).limit(20).all()
            eager_query_time = time.time() - eager_query_start
            
            total_time = time.time() - start_time
            
            return {
                'appointment_id': appointment_id,
                'total_entries': total_entries,
                'performance': {
                    'count_query_ms': round(count_query_time * 1000, 2),
                    'paginated_query_ms': round(paginated_query_time * 1000, 2),
                    'eager_loading_query_ms': round(eager_query_time * 1000, 2),
                    'total_analysis_time_ms': round(total_time * 1000, 2)
                },
                'recommendations': AuditService._get_performance_recommendations(
                    total_entries, count_query_time, paginated_query_time, eager_query_time
                ),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            try:
                current_app.logger.error(f"Error getting performance metrics for appointment {appointment_id}: {str(e)}")
            except RuntimeError:
                pass
            
            return {
                'appointment_id': appointment_id,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    @staticmethod
    def _get_performance_recommendations(total_entries: int, count_time: float, 
                                       paginated_time: float, eager_time: float) -> List[str]:
        """
        Generate performance recommendations based on query metrics.
        
        Args:
            total_entries: Total number of audit entries
            count_time: Time taken for count query
            paginated_time: Time taken for paginated query
            eager_time: Time taken for eager loading query
            
        Returns:
            List of performance recommendations
        """
        recommendations = []
        
        if count_time > 0.1:  # 100ms
            recommendations.append("Count query is slow - consider adding index on appointment_id")
        
        if paginated_time > 0.2:  # 200ms
            recommendations.append("Paginated query is slow - consider composite index on (appointment_id, timestamp)")
        
        if eager_time > paginated_time * 1.5:
            recommendations.append("Eager loading is significantly slower - check if user relationship is needed")
        
        if total_entries > 1000:
            recommendations.append("Large audit history - consider implementing virtual scrolling")
            recommendations.append("Consider archiving old audit entries to improve performance")
        
        if total_entries > 100:
            recommendations.append("Enable caching for frequently accessed audit data")
        
        return recommendations