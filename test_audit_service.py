# test_audit_service.py
import unittest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timezone
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.services.audit_service import AuditService
from app.models.appointment_audit import AppointmentAudit
from app.models.user import User
from app.models.tutor import Tutor
from app.models.client import Client


class TestAuditService(unittest.TestCase):
    """Test cases for AuditService methods."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.sample_timestamp = datetime(2024, 1, 15, 14, 30, 0, tzinfo=timezone.utc)
        
        # Mock user
        self.mock_user = Mock(spec=User)
        self.mock_user.id = 1
        self.mock_user.email = "<EMAIL>"
        self.mock_user.role = "manager"
        
        # Mock tutor
        self.mock_tutor = Mock(spec=<PERSON><PERSON>)
        self.mock_tutor.id = 2
        self.mock_tutor.full_name = "<PERSON>"
        
        # Mock client
        self.mock_client = Mock(spec=Client)
        self.mock_client.id = 3
        self.mock_client.full_name = "<PERSON>"
    
    def create_mock_audit_entry(self, action='update', appointment_id=123):
        """Create a mock audit entry for testing."""
        audit_entry = Mock(spec=AppointmentAudit)
        audit_entry.id = 1
        audit_entry.appointment_id = appointment_id
        audit_entry.action = action
        audit_entry.action_description = action.title()
        audit_entry.user_id = 1
        audit_entry.user_role = "manager"
        audit_entry.user_email = "<EMAIL>"
        audit_entry.timestamp = self.sample_timestamp
        audit_entry.user = self.mock_user
        audit_entry.notes = "Test notes"
        audit_entry.changes_summary = "Status changed"
        
        if action == 'update':
            audit_entry.old_values = {
                'status': 'scheduled',
                'tutor_id': 2,
                'start_time': '2024-01-15T10:00:00'
            }
            audit_entry.new_values = {
                'status': 'confirmed',
                'tutor_id': 2,
                'start_time': '2024-01-15T11:00:00'
            }
        elif action == 'create':
            audit_entry.old_values = None
            audit_entry.new_values = {
                'status': 'scheduled',
                'tutor_id': 2,
                'client_id': 3,
                'start_time': '2024-01-15T10:00:00'
            }
        else:
            audit_entry.old_values = None
            audit_entry.new_values = None
        
        return audit_entry
    
    @patch('app.services.audit_service.AppointmentAudit')
    @patch('app.services.audit_service.TimezoneService')
    def test_get_appointment_audit_history_success(self, mock_timezone_service, mock_audit_model):
        """Test successful retrieval of appointment audit history."""
        # Setup mocks
        mock_audit_entries = [
            self.create_mock_audit_entry('update', 123),
            self.create_mock_audit_entry('create', 123)
        ]
        
        mock_paginated_results = Mock()
        mock_paginated_results.items = mock_audit_entries
        mock_paginated_results.page = 1
        mock_paginated_results.per_page = 20
        mock_paginated_results.pages = 1
        mock_paginated_results.total = 2
        mock_paginated_results.has_prev = False
        mock_paginated_results.has_next = False
        mock_paginated_results.prev_num = None
        mock_paginated_results.next_num = None
        
        mock_query = Mock()
        mock_query.filter_by.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.paginate.return_value = mock_paginated_results
        mock_audit_model.query = mock_query
        
        mock_timezone_service.format_for_display.return_value = "Jan 15, 2024 2:30 PM EST"
        
        # Test the method
        result = AuditService.get_appointment_audit_history(123, page=1, per_page=20)
        
        # Assertions
        self.assertIsInstance(result, dict)
        self.assertIn('entries', result)
        self.assertIn('pagination', result)
        self.assertIn('total', result)
        self.assertEqual(len(result['entries']), 2)
        self.assertEqual(result['total'], 2)
        self.assertEqual(result['pagination']['page'], 1)
        self.assertEqual(result['pagination']['total_pages'], 1)
        
        # Verify query was called correctly
        mock_query.filter_by.assert_called_once_with(appointment_id=123)
        mock_query.paginate.assert_called_once_with(page=1, per_page=20, error_out=False)
    
    @patch('app.services.audit_service.AppointmentAudit')
    @patch('app.services.audit_service.current_app')
    def test_get_appointment_audit_history_error(self, mock_current_app, mock_audit_model):
        """Test error handling in get_appointment_audit_history."""
        # Setup mock to raise exception
        mock_audit_model.query.filter_by.side_effect = Exception("Database error")
        
        # Test the method
        result = AuditService.get_appointment_audit_history(123)
        
        # Assertions
        self.assertEqual(result['entries'], [])
        self.assertEqual(result['total'], 0)
        self.assertEqual(result['pagination']['total_pages'], 0)
        
        # Verify error was logged
        mock_current_app.logger.error.assert_called_once()
    
    @patch('app.services.audit_service.TimezoneService')
    @patch('app.services.audit_service.Tutor')
    def test_format_audit_entry_for_display_update_action(self, mock_tutor_model, mock_timezone_service):
        """Test formatting audit entry for display with update action."""
        # Setup mocks
        mock_timezone_service.format_for_display.side_effect = [
            "Jan 15, 2024 2:30 PM EST",  # First call (short format)
            "Monday, January 15, 2024 at 2:30 PM EST"  # Second call (long format)
        ]
        
        mock_tutor_model.query.get.return_value = self.mock_tutor
        
        audit_entry = self.create_mock_audit_entry('update')
        
        # Test the method
        result = AuditService.format_audit_entry_for_display(audit_entry)
        
        # Assertions
        self.assertIsInstance(result, dict)
        self.assertEqual(result['id'], 1)
        self.assertEqual(result['appointment_id'], 123)
        self.assertEqual(result['action'], 'update')
        self.assertEqual(result['action_description'], 'Update')
        self.assertEqual(result['action_icon'], '📝')
        self.assertEqual(result['action_color'], 'blue')
        self.assertEqual(result['user_name'], 'Test')
        self.assertEqual(result['user_role'], 'manager')
        self.assertEqual(result['user_email'], '<EMAIL>')
        self.assertEqual(result['timestamp_est'], "Jan 15, 2024 2:30 PM EST")
        self.assertEqual(result['timestamp_est_long'], "Monday, January 15, 2024 at 2:30 PM EST")
        self.assertEqual(result['changes_summary'], 'Status changed')
        self.assertTrue(result['has_changes'])
        self.assertIsInstance(result['changes_detail'], list)
        self.assertEqual(result['notes'], 'Test notes')
    
    @patch('app.services.audit_service.TimezoneService')
    def test_format_audit_entry_for_display_create_action(self, mock_timezone_service):
        """Test formatting audit entry for display with create action."""
        # Setup mocks
        mock_timezone_service.format_for_display.side_effect = [
            "Jan 15, 2024 2:30 PM EST",
            "Monday, January 15, 2024 at 2:30 PM EST"
        ]
        
        audit_entry = self.create_mock_audit_entry('create')
        
        # Test the method
        result = AuditService.format_audit_entry_for_display(audit_entry)
        
        # Assertions
        self.assertEqual(result['action'], 'create')
        self.assertEqual(result['action_icon'], '➕')
        self.assertEqual(result['action_color'], 'green')
        self.assertTrue(result['has_changes'])
    
    @patch('app.services.audit_service.current_app')
    def test_format_audit_entry_for_display_error_handling(self, mock_current_app):
        """Test error handling in format_audit_entry_for_display."""
        # Create audit entry that will cause an error
        audit_entry = Mock()
        audit_entry.id = 1
        audit_entry.appointment_id = 123
        audit_entry.action = 'update'
        audit_entry.action_description = 'Update'
        audit_entry.timestamp = None  # This will cause an error
        audit_entry.notes = 'Test notes'
        
        # Test the method
        result = AuditService.format_audit_entry_for_display(audit_entry)
        
        # Assertions - should return safe default values
        self.assertEqual(result['user_name'], 'Unknown User')
        self.assertEqual(result['timestamp_est'], 'Unknown Time')
        self.assertEqual(result['changes_summary'], 'Error loading changes')
        self.assertFalse(result['has_changes'])
        
        # Verify error was logged
        mock_current_app.logger.error.assert_called_once()
    
    @patch('app.services.audit_service.AppointmentAudit')
    @patch('app.services.audit_service.TimezoneService')
    def test_get_audit_summary_success(self, mock_timezone_service, mock_audit_model):
        """Test successful retrieval of audit summary."""
        # Setup mocks
        latest_entry = self.create_mock_audit_entry('update', 123)
        creation_entry = self.create_mock_audit_entry('create', 123)
        
        mock_query = Mock()
        mock_query.filter_by.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.first.side_effect = [latest_entry, creation_entry]  # Two calls to first()
        mock_query.count.return_value = 5
        mock_audit_model.query = mock_query
        
        mock_timezone_service.format_for_display.return_value = "Jan 15, 2024 2:30 PM EST"
        
        # Test the method
        result = AuditService.get_audit_summary(123)
        
        # Assertions
        self.assertTrue(result['has_audit_trail'])
        self.assertEqual(result['total_entries'], 5)
        self.assertEqual(result['last_modified'], "Jan 15, 2024 2:30 PM EST")
        self.assertEqual(result['last_modified_by'], 'Test')
        self.assertEqual(result['last_action'], 'Update')
        self.assertEqual(result['last_changes_summary'], 'Status changed')
        self.assertEqual(result['created_at'], "Jan 15, 2024 2:30 PM EST")
        self.assertEqual(result['created_by'], 'Test')
    
    @patch('app.services.audit_service.AppointmentAudit')
    def test_get_audit_summary_no_entries(self, mock_audit_model):
        """Test get_audit_summary when no audit entries exist."""
        # Setup mocks
        mock_query = Mock()
        mock_query.filter_by.return_value = mock_query
        mock_query.order_by.return_value = mock_query
        mock_query.first.return_value = None
        mock_query.count.return_value = 0
        mock_audit_model.query = mock_query
        
        # Test the method
        result = AuditService.get_audit_summary(123)
        
        # Assertions
        self.assertFalse(result['has_audit_trail'])
        self.assertEqual(result['total_entries'], 0)
        self.assertIsNone(result['last_modified'])
        self.assertIsNone(result['last_modified_by'])
        self.assertIsNone(result['created_at'])
        self.assertIsNone(result['created_by'])
    
    def test_get_user_display_info_with_user_relationship(self):
        """Test _get_user_display_info with user relationship."""
        audit_entry = Mock()
        audit_entry.user = self.mock_user
        audit_entry.user_role = "manager"
        
        result = AuditService._get_user_display_info(audit_entry)
        
        self.assertEqual(result['name'], 'Test')
        self.assertEqual(result['role'], 'manager')
        self.assertEqual(result['email'], '<EMAIL>')
    
    @patch('app.services.audit_service.User')
    def test_get_user_display_info_with_email_lookup(self, mock_user_model):
        """Test _get_user_display_info with email lookup."""
        audit_entry = Mock()
        audit_entry.user = None
        audit_entry.user_email = "<EMAIL>"
        audit_entry.user_role = "tutor"
        
        mock_user_model.query.filter_by.return_value.first.return_value = self.mock_user
        
        result = AuditService._get_user_display_info(audit_entry)
        
        self.assertEqual(result['name'], 'Test')
        self.assertEqual(result['role'], 'tutor')
        self.assertEqual(result['email'], '<EMAIL>')
    
    def test_get_user_display_info_system_user(self):
        """Test _get_user_display_info for system user."""
        audit_entry = Mock()
        audit_entry.user = None
        audit_entry.user_email = None
        audit_entry.user_role = None
        
        result = AuditService._get_user_display_info(audit_entry)
        
        self.assertEqual(result['name'], 'System')
        self.assertEqual(result['role'], 'System')
        self.assertEqual(result['email'], '')
    
    @patch('app.services.audit_service.Tutor')
    @patch('app.services.audit_service.Client')
    def test_format_changes_detail_update_action(self, mock_client_model, mock_tutor_model):
        """Test _format_changes_detail for update action."""
        mock_tutor_model.query.get.return_value = self.mock_tutor
        mock_client_model.query.get.return_value = self.mock_client
        
        audit_entry = self.create_mock_audit_entry('update')
        
        result = AuditService._format_changes_detail(audit_entry)
        
        self.assertIsInstance(result, list)
        self.assertTrue(len(result) > 0)
        
        # Check that status change is captured
        status_change = next((change for change in result if change['field'] == 'status'), None)
        self.assertIsNotNone(status_change)
        self.assertEqual(status_change['old_value_display'], 'Scheduled')
        self.assertEqual(status_change['new_value_display'], 'Confirmed')
        self.assertEqual(status_change['change_type'], 'updated')
    
    def test_format_changes_detail_create_action(self):
        """Test _format_changes_detail for create action."""
        audit_entry = self.create_mock_audit_entry('create')
        
        result = AuditService._format_changes_detail(audit_entry)
        
        self.assertIsInstance(result, list)
        self.assertTrue(len(result) > 0)
        
        # All changes should be 'created' type
        for change in result:
            self.assertEqual(change['change_type'], 'created')
            self.assertIsNone(change['old_value'])
    
    def test_format_field_value_datetime(self):
        """Test _format_field_value for datetime fields."""
        # Test datetime string formatting
        datetime_str = "2024-01-15T10:00:00"
        with patch('app.services.audit_service.TimezoneService') as mock_timezone:
            mock_timezone.format_for_display.return_value = "Jan 15, 2024 10:00 AM EST"
            result = AuditService._format_field_value('start_time', datetime_str)
            self.assertEqual(result, "Jan 15, 2024 10:00 AM EST")
    
    @patch('app.services.audit_service.Tutor')
    def test_format_field_value_tutor_id(self, mock_tutor_model):
        """Test _format_field_value for tutor_id field."""
        mock_tutor_model.query.get.return_value = self.mock_tutor
        
        result = AuditService._format_field_value('tutor_id', 2)
        
        self.assertEqual(result, 'Jane Smith')
        mock_tutor_model.query.get.assert_called_once_with(2)
    
    @patch('app.services.audit_service.Client')
    def test_format_field_value_client_id(self, mock_client_model):
        """Test _format_field_value for client_id field."""
        mock_client_model.query.get.return_value = self.mock_client
        
        result = AuditService._format_field_value('client_id', 3)
        
        self.assertEqual(result, 'John Doe')
        mock_client_model.query.get.assert_called_once_with(3)
    
    def test_format_field_value_transport_fee(self):
        """Test _format_field_value for transport_fee field."""
        result = AuditService._format_field_value('transport_fee', 25.50)
        self.assertEqual(result, '$25.50')
        
        result = AuditService._format_field_value('transport_fee', 0)
        self.assertEqual(result, '$0.00')
    
    def test_format_field_value_duration_minutes(self):
        """Test _format_field_value for duration_minutes field."""
        # Test less than 60 minutes
        result = AuditService._format_field_value('duration_minutes', 45)
        self.assertEqual(result, '45m')
        
        # Test exactly 60 minutes
        result = AuditService._format_field_value('duration_minutes', 60)
        self.assertEqual(result, '1h')
        
        # Test more than 60 minutes
        result = AuditService._format_field_value('duration_minutes', 90)
        self.assertEqual(result, '1h 30m')
    
    def test_format_field_value_status(self):
        """Test _format_field_value for status field."""
        result = AuditService._format_field_value('status', 'scheduled')
        self.assertEqual(result, 'Scheduled')
        
        result = AuditService._format_field_value('status', 'in_progress')
        self.assertEqual(result, 'In Progress')
    
    def test_format_field_value_none_and_empty(self):
        """Test _format_field_value for None and empty values."""
        result = AuditService._format_field_value('any_field', None)
        self.assertEqual(result, 'Not set')
        
        result = AuditService._format_field_value('any_field', '')
        self.assertEqual(result, 'Empty')
    
    def test_get_field_display_name(self):
        """Test _get_field_display_name method."""
        test_cases = {
            'status': 'Status',
            'tutor_id': 'Tutor',
            'client_id': 'Client',
            'start_time': 'Start Time',
            'transport_fee': 'Transport Fee',
            'custom_field': 'Custom Field'
        }
        
        for field, expected in test_cases.items():
            result = AuditService._get_field_display_name(field)
            self.assertEqual(result, expected)
    
    def test_get_action_display_info(self):
        """Test _get_action_display_info method."""
        test_cases = {
            'create': {'icon': '➕', 'color': 'green'},
            'update': {'icon': '📝', 'color': 'blue'},
            'delete': {'icon': '🗑️', 'color': 'red'},
            'cancel': {'icon': '❌', 'color': 'orange'},
            'unknown': {'icon': '📝', 'color': 'gray'}
        }
        
        for action, expected in test_cases.items():
            result = AuditService._get_action_display_info(action)
            self.assertEqual(result, expected)


if __name__ == '__main__':
    unittest.main()