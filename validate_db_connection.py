#!/usr/bin/env python3
"""
Simple script to validate database connection and basic operations
"""

import os
import sys
from sqlalchemy import create_engine, text

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def get_database_url():
    """Get database URL from environment or use default"""
    database_url = os.environ.get('DATABASE_URL')
    if not database_url:
        # Try to get the URL from the Flask app config
        try:
            from app import create_app
            app = create_app()
            database_url = app.config.get('SQLALCHEMY_DATABASE_URI')
            print(f"Using database URL from Flask app config: {database_url}")
        except Exception as e:
            # Default development database URL
            database_url = 'postgresql://postgres:postgres@localhost/tutoring_dev'
            print(f"Error getting database URL from app config: {e}")
            print(f"Using default development database URL: {database_url}")
    else:
        print(f"Using DATABASE_URL from environment: {database_url}")
        
    # Handle SQLAlchemy 1.4+ compatibility issue with postgres:// URLs
    if database_url and database_url.startswith('postgres://'):
        database_url = database_url.replace('postgres://', 'postgresql://', 1)
        
    return database_url

def main():
    """Test database connection and basic operations"""
    print("=== Database Connection Test ===")
    
    try:
        # Get database URL
        database_url = get_database_url()
        
        # Create engine
        engine = create_engine(database_url)
        print("Database engine created successfully")
        
        # Test connection
        with engine.connect() as conn:
            # Test basic connectivity
            result = conn.execute(text("SELECT 1 as test"))
            test_value = result.scalar()
            
            if test_value == 1:
                print("✓ Database connectivity test passed")
            else:
                print("✗ Database connectivity test failed")
                return False
            
            # Test table count
            result = conn.execute(text("""
                SELECT COUNT(*) FROM information_schema.tables 
                WHERE table_schema = 'public'
            """))
            table_count = result.scalar()
            
            print(f"✓ Found {table_count} tables in the database")
            
            # Check some key tables for correct primary keys
            key_tables = ['users', 'clients', 'tutors', 'services', 'appointments']
            for table in key_tables:
                result = conn.execute(text("""
                    SELECT column_name
                    FROM information_schema.table_constraints tc
                    JOIN information_schema.key_column_usage kcu 
                        ON tc.constraint_name = kcu.constraint_name
                    WHERE tc.table_name = :table_name
                    AND tc.constraint_type = 'PRIMARY KEY'
                    AND tc.table_schema = 'public'
                """), {"table_name": table})
                
                pk_columns = [row[0] for row in result]
                print(f"  - {table}: primary key = {pk_columns}")
            
            print("\n✓ Database connection and basic operations successful!")
            return True
            
    except Exception as e:
        print(f"✗ Error during database test: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Clean up
        if 'engine' in locals():
            engine.dispose()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)