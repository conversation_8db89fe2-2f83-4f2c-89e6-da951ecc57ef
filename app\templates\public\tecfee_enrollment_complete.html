<!DOCTYPE html>
<html lang="{{ session.get('language', 'en') }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ t('public.tecfee.enrollment_complete.page_title') }}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-red: #e57373;
            --primary-red-dark: #d32f2f;
            --primary-red-light: #ffcdd2;
            --text-dark: #212529;
            --text-gray: #6c757d;
            --bg-light: #f8f9fa;
            --bg-white: #ffffff;
            --border-color: #dee2e6;
            --success-green: #28a745;
            --shadow-sm: 0 2px 4px rgba(0,0,0,0.05);
            --shadow-md: 0 4px 6px rgba(0,0,0,0.07);
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--bg-light);
            color: var(--text-dark);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Playfair Display', serif;
            color: var(--text-dark);
        }
        
        /* Success Animation */
        @keyframes checkmark {
            0% {
                stroke-dashoffset: 50;
            }
            100% {
                stroke-dashoffset: 0;
            }
        }
        
        @keyframes circle {
            0% {
                stroke-dashoffset: 166;
            }
            100% {
                stroke-dashoffset: 0;
            }
        }
        
        .success-checkmark {
            width: 100px;
            height: 100px;
            margin: 0 auto 2rem;
        }
        
        .success-checkmark circle {
            fill: none;
            stroke: var(--success-green);
            stroke-width: 3;
            stroke-dasharray: 166;
            stroke-dashoffset: 166;
            animation: circle 0.8s ease-out forwards;
        }
        
        .success-checkmark path {
            fill: none;
            stroke: var(--success-green);
            stroke-width: 3;
            stroke-dasharray: 50;
            stroke-dashoffset: 50;
            animation: checkmark 0.8s 0.5s ease-out forwards;
        }
        
        /* Main Container */
        .success-container {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 0;
        }
        
        .success-card {
            background: var(--bg-white);
            border-radius: 16px;
            box-shadow: var(--shadow-md);
            padding: 3rem;
            max-width: 600px;
            width: 100%;
            text-align: center;
        }
        
        .success-title {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: var(--success-green);
        }
        
        .success-subtitle {
            font-size: 1.25rem;
            color: var(--text-gray);
            margin-bottom: 2rem;
        }
        
        /* Info Box */
        .info-box {
            background: var(--bg-light);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            text-align: left;
        }
        
        .info-box h5 {
            font-size: 1.25rem;
            margin-bottom: 1rem;
            color: var(--text-dark);
        }
        
        .info-item {
            display: flex;
            align-items: start;
            margin-bottom: 0.75rem;
        }
        
        .info-item i {
            color: var(--primary-red);
            margin-right: 0.75rem;
            margin-top: 0.2rem;
            min-width: 20px;
        }
        
        .info-label {
            font-weight: 600;
            color: var(--text-dark);
            margin-right: 0.5rem;
        }
        
        .info-value {
            color: var(--text-gray);
        }
        
        /* Next Steps */
        .next-steps {
            background: #e8f5e9;
            border-left: 4px solid var(--success-green);
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            text-align: left;
        }
        
        .next-steps h5 {
            color: var(--success-green);
            margin-bottom: 1rem;
        }
        
        .next-steps ol {
            margin: 0;
            padding-left: 1.5rem;
        }
        
        .next-steps li {
            margin-bottom: 0.5rem;
            color: var(--text-dark);
        }
        
        /* Buttons */
        .btn-success {
            background: var(--success-green);
            border-color: var(--success-green);
            color: white;
            padding: 0.75rem 2rem;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .btn-success:hover {
            background: #1e7e34;
            border-color: #1e7e34;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
        }
        
        .btn-outline-primary {
            color: var(--primary-red);
            border-color: var(--primary-red);
            padding: 0.75rem 2rem;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .btn-outline-primary:hover {
            background: var(--primary-red);
            border-color: var(--primary-red);
            color: white;
        }
        
        /* Print Button */
        .print-button {
            position: absolute;
            top: 1rem;
            right: 1rem;
            color: var(--text-gray);
            cursor: pointer;
            transition: color 0.3s ease;
        }
        
        .print-button:hover {
            color: var(--primary-red);
        }
        
        @media print {
            .print-button,
            .btn-group {
                display: none !important;
            }
            
            .success-card {
                box-shadow: none;
                border: 1px solid var(--border-color);
            }
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .success-card {
                padding: 2rem 1.5rem;
            }
            
            .success-title {
                font-size: 2rem;
            }
            
            .btn-group {
                flex-direction: column;
            }
            
            .btn-group .btn {
                width: 100%;
                margin-bottom: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="success-container">
        <div class="container">
            <div class="success-card position-relative">
                <!-- Print Button -->
                <div class="print-button" onclick="window.print()">
                    <i class="fas fa-print fa-lg"></i>
                </div>

                <!-- Success Animation -->
                <svg class="success-checkmark" viewBox="0 0 52 52">
                    <circle cx="26" cy="26" r="25"/>
                    <path d="M14 27l7 7 16-16"/>
                </svg>

                <h1 class="success-title">{{ t('public.tecfee.enrollment_complete.congratulations') }}</h1>
                <p class="success-subtitle">{{ t('public.tecfee.enrollment_complete.subtitle') }}</p>

                <!-- Enrollment Information -->
                <div class="info-box">
                    <h5><i class="fas fa-user-graduate"></i> {{ t('public.tecfee.enrollment_complete.enrollment_information') }}</h5>
                    <div class="info-item">
                        <i class="fas fa-user"></i>
                        <div>
                            <span class="info-label">{{ t('public.tecfee.enrollment_complete.name') }}:</span>
                            <span class="info-value">{{ client.first_name }} {{ client.last_name }}</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-envelope"></i>
                        <div>
                            <span class="info-label">{{ t('public.tecfee.enrollment_complete.email') }}:</span>
                            <span class="info-value">{{ client.user.email }}</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-calendar-check"></i>
                        <div>
                            <span class="info-label">{{ t('public.tecfee.enrollment_complete.enrollment_date') }}:</span>
                            <span class="info-value">{{ enrollment.enrollment_date.strftime('%d %B %Y') }}</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-book"></i>
                        <div>
                            <span class="info-label">{{ t('public.tecfee.enrollment_complete.program') }}:</span>
                            <span class="info-value">TECFÉE - {{ enrollment.program.name }}</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-graduation-cap"></i>
                        <div>
                            <span class="info-label">{{ t('public.tecfee.enrollment_complete.sessions_enrolled') }}:</span>
                            <span class="info-value">{{ enrollment.total_sessions }} {{ t('public.tecfee.enrollment_complete.sessions') }}</span>
                        </div>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-dollar-sign"></i>
                        <div>
                            <span class="info-label">{{ t('public.tecfee.enrollment_complete.package_type') }}:</span>
                            <span class="info-value">
                                {% if enrollment.pricing_type == 'full_package' %}
                                    {{ t('public.tecfee.enrollment_complete.full_package') }}
                                {% else %}
                                    {{ t('public.tecfee.enrollment_complete.per_session_payment') }}
                                {% endif %}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Next Steps -->
                <div class="next-steps">
                    <h5><i class="fas fa-clipboard-list"></i> {{ t('public.tecfee.enrollment_complete.next_steps') }}</h5>
                    <ol>
                        {% if new_account %}
                            <li>{{ t('public.tecfee.enrollment_complete.check_inbox') }}</li>
                            <li>{{ t('public.tecfee.enrollment_complete.login_temp_password') }}</li>
                            <li>{{ t('public.tecfee.enrollment_complete.change_password') }}</li>
                        {% else %}
                            <li>{{ t('public.tecfee.enrollment_complete.login_to_account') }}</li>
                        {% endif %}
                        <li>{{ t('public.tecfee.enrollment_complete.check_session_calendar') }}</li>
                        <li>{{ t('public.tecfee.enrollment_complete.prepare_first_session') }}</li>
                    </ol>
                </div>

                <!-- Important Notice -->
                {% if new_account %}
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>{{ t('public.tecfee.enrollment_complete.important_notice') }}:</strong> {{ t('public.tecfee.enrollment_complete.login_email_sent', email=client.user.email) }}
                </div>
                {% endif %}

                <!-- Action Buttons -->
                <div class="btn-group d-flex gap-3 justify-content-center">
                    {% if current_user.is_authenticated %}
                        <a href="{{ url_for('client.dashboard') }}" class="btn btn-success">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            {{ t('public.tecfee.enrollment_complete.access_dashboard') }}
                        </a>
                    {% else %}
                        <a href="{{ url_for('auth.login') }}" class="btn btn-success">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            {{ t('public.tecfee.enrollment_complete.login') }}
                        </a>
                    {% endif %}
                    <button onclick="window.print()" class="btn btn-outline-primary">
                        <i class="fas fa-print me-2"></i>
                        {{ t('public.tecfee.enrollment_complete.print_page') }}
                    </button>
                </div>
            </div>

            <!-- Contact Support -->
            <div class="text-center mt-4">
                <p class="text-muted">
                    {{ t('public.tecfee.enrollment_complete.need_help') }} 
                    <a href="mailto:{{ t('public.tecfee.enrollment_complete.support_email') }}" class="text-decoration-none">{{ t('public.tecfee.enrollment_complete.support_email') }}</a>
                </p>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>