import re
import os

def update_file(file_path):
    try:
        # Try UTF-8 first
        with open(file_path, 'r', encoding='utf-8') as file:
            content = file.read()
    except UnicodeDecodeError:
        try:
            # Try Latin-1 as fallback
            with open(file_path, 'r', encoding='latin-1') as file:
                content = file.read()
        except Exception as e:
            print(f"Error reading {file_path}: {e}")
            return
    
    # Replace client.id with client.client_id
    content = re.sub(r'client\.id\b', 'client.client_id', content)
    
    # Replace Client.id with Client.client_id
    content = re.sub(r'Client\.id\b', 'Client.client_id', content)
    
    # Replace invoice.id with invoice.invoice_id
    content = re.sub(r'invoice\.id\b', 'invoice.invoice_id', content)
    
    # Replace Invoice.id with Invoice.invoice_id
    content = re.sub(r'Invoice\.id\b', 'Invoice.invoice_id', content)
    
    # Replace appointment.id with appointment.appointment_id
    content = re.sub(r'appointment\.id\b', 'appointment.appointment_id', content)
    
    # Replace Appointment.id with Appointment.appointment_id
    content = re.sub(r'Appointment\.id\b', 'Appointment.appointment_id', content)
    
    # Replace subscription.id with subscription.subscription_id
    content = re.sub(r'subscription\.id\b', 'subscription.subscription_id', content)
    
    # Replace Subscription.id with Subscription.subscription_id
    content = re.sub(r'Subscription\.id\b', 'Subscription.subscription_id', content)
    
    # Replace tutor.id with tutor.tutor_id
    content = re.sub(r'tutor\.id\b', 'tutor.tutor_id', content)
    
    # Replace Tutor.id with Tutor.tutor_id
    content = re.sub(r'Tutor\.id\b', 'Tutor.tutor_id', content)
    
    # Replace user.id with user.user_id
    content = re.sub(r'user\.id\b', 'user.user_id', content)
    
    # Replace User.id with User.user_id
    content = re.sub(r'User\.id\b', 'User.user_id', content)
    
    # Replace service.id with service.service_id
    content = re.sub(r'service\.id\b', 'service.service_id', content)
    
    # Replace Service.id with Service.service_id
    content = re.sub(r'Service\.id\b', 'Service.service_id', content)
    
    # Replace filter_by(id= with filter_by(model_id=
    content = re.sub(r'filter_by\(id=([^)]+)\)', r'filter_by(appointment_id=\1)', content)
    
    try:
        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(content)
        print(f"Updated {file_path}")
    except Exception as e:
        print(f"Error writing {file_path}: {e}")

# Update specific files that need changes
files_to_update = [
    'app/services/invoice_service.py',
    'app/services/subscription_service.py',
    'app/services/program_service.py',
    'app/services/appointment_service.py',
    'app/services/group_session_service.py',
    'app/services/user_service.py'
]

for file_path in files_to_update:
    if os.path.exists(file_path):
        update_file(file_path)
    else:
        print(f"File not found: {file_path}")