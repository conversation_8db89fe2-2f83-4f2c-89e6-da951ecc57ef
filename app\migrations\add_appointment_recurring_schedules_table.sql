-- Migration: Add appointment_recurring_schedules table
-- This table provides a clean schema design for managing recurring appointment patterns
-- separate from individual appointment instances

-- Create the appointment_recurring_schedules table
CREATE TABLE IF NOT EXISTS appointment_recurring_schedules (
    id SERIAL PRIMARY KEY,
    
    -- Core appointment details
    tutor_id INTEGER NOT NULL REFERENCES tutors(id) ON DELETE CASCADE,
    client_id INTEGER NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    dependant_id INTEGER REFERENCES dependants(id) ON DELETE SET NULL,
    tutor_service_id INTEGER NOT NULL REFERENCES tutor_services(id) ON DELETE CASCADE,
    
    -- Schedule timing
    start_time TIME NOT NULL,  -- Time of day for appointments
    duration_minutes INTEGER NOT NULL DEFAULT 60,
    
    -- Recurring pattern
    frequency VARCHAR(20) NOT NULL CHECK (frequency IN ('weekly', 'biweekly', 'monthly')),
    day_of_week INTEGER CHECK (day_of_week >= 0 AND day_of_week <= 6),  -- 0=Monday, 6=Sunday
    week_of_month INTEGER CHECK (week_of_month >= 1 AND week_of_month <= 5),  -- 1-5 for monthly patterns
    
    -- Schedule boundaries
    pattern_start_date DATE NOT NULL,
    pattern_end_date DATE,  -- NULL means no end date
    pattern_occurrences INTEGER CHECK (pattern_occurrences > 0),  -- Alternative to end_date
    
    -- Generation tracking
    last_generated_date DATE,  -- Track the last date appointments were generated
    
    -- Default appointment settings
    default_status VARCHAR(50) NOT NULL DEFAULT 'scheduled' CHECK (default_status IN ('scheduled', 'confirmed', 'completed', 'cancelled', 'no-show', 'awaiting_confirmation')),
    default_notes TEXT,
    transport_fee DECIMAL(10,2),
    transport_fee_for_tutor BOOLEAN DEFAULT TRUE,
    is_subscription_based BOOLEAN DEFAULT FALSE,
    subscription_id INTEGER REFERENCES subscriptions(id) ON DELETE SET NULL,
    
    -- Status and metadata
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
    modified_by INTEGER REFERENCES users(id) ON DELETE SET NULL,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add foreign key relationship from appointments to recurring schedules
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointments' 
                   AND column_name = 'recurring_schedule_id') THEN
        ALTER TABLE appointments ADD COLUMN recurring_schedule_id INTEGER REFERENCES appointment_recurring_schedules(id) ON DELETE SET NULL;
    END IF;
END $$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_appointment_recurring_schedules_tutor_id ON appointment_recurring_schedules(tutor_id);
CREATE INDEX IF NOT EXISTS idx_appointment_recurring_schedules_client_id ON appointment_recurring_schedules(client_id);
CREATE INDEX IF NOT EXISTS idx_appointment_recurring_schedules_dependant_id ON appointment_recurring_schedules(dependant_id);
CREATE INDEX IF NOT EXISTS idx_appointment_recurring_schedules_tutor_service_id ON appointment_recurring_schedules(tutor_service_id);
CREATE INDEX IF NOT EXISTS idx_appointment_recurring_schedules_pattern_start_date ON appointment_recurring_schedules(pattern_start_date);
CREATE INDEX IF NOT EXISTS idx_appointment_recurring_schedules_pattern_end_date ON appointment_recurring_schedules(pattern_end_date);
CREATE INDEX IF NOT EXISTS idx_appointment_recurring_schedules_frequency ON appointment_recurring_schedules(frequency);
CREATE INDEX IF NOT EXISTS idx_appointment_recurring_schedules_is_active ON appointment_recurring_schedules(is_active);
CREATE INDEX IF NOT EXISTS idx_appointment_recurring_schedules_last_generated_date ON appointment_recurring_schedules(last_generated_date);

-- Create index on appointments table for the new foreign key
CREATE INDEX IF NOT EXISTS idx_appointments_recurring_schedule_id ON appointments(recurring_schedule_id);

-- Add trigger for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_appointment_recurring_schedules_modification_date()
RETURNS TRIGGER AS $$
BEGIN
    NEW.modification_date = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply trigger to the new table
DROP TRIGGER IF EXISTS update_appointment_recurring_schedules_modification_date ON appointment_recurring_schedules;
CREATE TRIGGER update_appointment_recurring_schedules_modification_date 
    BEFORE UPDATE ON appointment_recurring_schedules 
    FOR EACH ROW 
    EXECUTE FUNCTION update_appointment_recurring_schedules_modification_date();

-- Add constraints to ensure data integrity
ALTER TABLE appointment_recurring_schedules 
ADD CONSTRAINT check_pattern_end_constraint 
CHECK (pattern_end_date IS NULL OR pattern_end_date >= pattern_start_date);

ALTER TABLE appointment_recurring_schedules 
ADD CONSTRAINT check_pattern_occurrences_or_end_date 
CHECK (pattern_end_date IS NOT NULL OR pattern_occurrences IS NOT NULL);

-- Add constraint for weekly/biweekly patterns to require day_of_week
ALTER TABLE appointment_recurring_schedules 
ADD CONSTRAINT check_weekly_day_of_week 
CHECK (
    (frequency IN ('weekly', 'biweekly') AND day_of_week IS NOT NULL) OR 
    (frequency = 'monthly')
);

-- Add constraint for monthly patterns to require both day_of_week and week_of_month
ALTER TABLE appointment_recurring_schedules 
ADD CONSTRAINT check_monthly_pattern 
CHECK (
    (frequency = 'monthly' AND day_of_week IS NOT NULL AND week_of_month IS NOT NULL) OR 
    (frequency IN ('weekly', 'biweekly'))
);

-- Grant appropriate permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON appointment_recurring_schedules TO PUBLIC;
GRANT USAGE, SELECT ON SEQUENCE appointment_recurring_schedules_id_seq TO PUBLIC;

-- Add comment to document the table purpose
COMMENT ON TABLE appointment_recurring_schedules IS 'Stores recurring appointment schedule patterns separate from individual appointment instances. Replaces embedded recurring fields in appointments table for cleaner architecture.';