import re

def update_file(file_path):
    with open(file_path, 'r') as file:
        content = file.read()
    
    # Replace tutor.id with tutor.tutor_id
    content = re.sub(r'tutor\.id\b', 'tutor.tutor_id', content)
    
    # Replace Client.id with Client.client_id
    content = re.sub(r'Client\.id\b', 'Client.client_id', content)
    
    # Replace filter_by(id=id with filter_by(appointment_id=id
    content = re.sub(r'filter_by\(id=id', 'filter_by(appointment_id=id', content)
    
    # Replace url_for('tutor.view_appointment', id=appointment.id) with url_for('tutor.view_appointment', id=appointment.appointment_id)
    content = re.sub(r'url_for\([\'"]tutor\.view_appointment[\'"], id=appointment\.id\)', 
                    r'url_for("tutor.view_appointment", id=appointment.appointment_id)', content)
    
    # Replace SubscriptionService.record_usage(appointment.id) with SubscriptionService.record_usage(appointment.appointment_id)
    content = re.sub(r'SubscriptionService\.record_usage\(appointment\.id\)', 
                    r'SubscriptionService.record_usage(appointment.appointment_id)', content)
    
    # Replace TutorAvailability.query.filter_by(tutor_id=tutor.id) with TutorAvailability.query.filter_by(tutor_id=tutor.tutor_id)
    content = re.sub(r'TutorAvailability\.query\.filter_by\(tutor_id=tutor\.id\)', 
                    r'TutorAvailability.query.filter_by(tutor_id=tutor.tutor_id)', content)
    
    # Replace TutorServiceRate.query.filter_by(tutor_id=tutor.id) with TutorServiceRate.query.filter_by(tutor_id=tutor.tutor_id)
    content = re.sub(r'TutorServiceRate\.query\.filter_by\(tutor_id=tutor\.id\)', 
                    r'TutorServiceRate.query.filter_by(tutor_id=tutor.tutor_id)', content)
    
    # Replace TutorPayment.query.filter_by(tutor_id=tutor.id) with TutorPayment.query.filter_by(tutor_id=tutor.tutor_id)
    content = re.sub(r'TutorPayment\.query\.filter_by\(tutor_id=tutor\.id\)', 
                    r'TutorPayment.query.filter_by(tutor_id=tutor.tutor_id)', content)
    
    # Replace TimeOff.query.filter_by(tutor_id=tutor.id) with TimeOff.query.filter_by(tutor_id=tutor.tutor_id)
    content = re.sub(r'TimeOff\.query\.filter_by\(tutor_id=tutor\.id\)', 
                    r'TimeOff.query.filter_by(tutor_id=tutor.tutor_id)', content)
    
    with open(file_path, 'w') as file:
        file.write(content)
    
    print(f"Updated {file_path}")

# Update the tutor view file
update_file('app/views/tutor.py')