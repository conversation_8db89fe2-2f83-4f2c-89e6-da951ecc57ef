#!/bin/bash

# PostgreSQL Migration Script for TECFÉE Prospect System
# Make sure to update the database connection details below

# Database connection details - UPDATE THESE WITH YOUR ACTUAL VALUES
DB_HOST="localhost"
DB_PORT="5432"
DB_NAME="your_database_name"
DB_USER="your_username"
DB_PASSWORD="your_password"

echo "Running TECFÉE Prospect System Migrations..."
echo "=============================================="

# Check if psql is available
if ! command -v psql &> /dev/null; then
    echo "Error: psql command not found. Please install PostgreSQL client tools."
    exit 1
fi

# Set PGPASSWORD environment variable to avoid password prompt
export PGPASSWORD="$DB_PASSWORD"

echo "1. Running main migration (tables, indexes, triggers)..."
psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f "app/migrations/complete_prospect_and_tecfee_migration.sql"

if [ $? -eq 0 ]; then
    echo "✓ Main migration completed successfully"
else
    echo "✗ Main migration failed"
    exit 1
fi

echo ""
echo "2. Inserting TECFÉE program data..."
psql -h "$DB_HOST" -p "$DB_PORT" -U "$DB_USER" -d "$DB_NAME" -f "app/migrations/insert_tecfee_program_data.sql"

if [ $? -eq 0 ]; then
    echo "✓ TECFÉE program data inserted successfully"
else
    echo "✗ TECFÉE program data insertion failed"
    exit 1
fi

echo ""
echo "=============================================="
echo "Migration completed successfully!"
echo ""
echo "You can now:"
echo "1. Start your Flask application"
echo "2. Visit /public/tecfee/enrollment to test prospect registration"
echo "3. Access manager views to manage prospects and TECFÉE program"
echo ""
echo "Note: Make sure your .env file has the correct database credentials."
