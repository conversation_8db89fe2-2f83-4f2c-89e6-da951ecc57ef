-- TutorAide Application Database Schema
-- Updated with consistent descriptive primary key naming conventions
-- PostgreSQL Database Schema for Tutoring Management System

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table (authentication and basic user info)
CREATE TABLE users (
    user_id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL CHECK (role IN ('manager', 'tutor', 'client', 'parent')),
    is_active BOOLEAN DEFAULT TRUE,
    email_verified BOOLEAN DEFAULT FALSE,
    verification_token VARCHAR(255),
    verification_token_expires TIMESTAMP,
    google_id VARCHAR(255) UNIQUE,
    auth_provider VARCHAR(50) DEFAULT 'local',
    reset_token VARCHAR(255),
    reset_token_expires TIMESTAMP,
    last_login TIMESTAMP,
    language_preference VARCHAR(10) DEFAULT 'fr',
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Clients table (client information)
CREATE TABLE clients (
    client_id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(user_id) ON DELETE CASCADE,
    client_type VARCHAR(50) DEFAULT 'individual' CHECK (client_type IN ('individual', 'institutional')),
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    civic_number VARCHAR(20),
    street VARCHAR(200),
    city VARCHAR(100),
    postal_code VARCHAR(10),
    province VARCHAR(50) DEFAULT 'Quebec',
    country VARCHAR(50) DEFAULT 'Canada',
    date_of_birth DATE,
    emergency_contact_name VARCHAR(200),
    emergency_contact_phone VARCHAR(20),
    notes TEXT,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Dependants table (children/students of clients)
CREATE TABLE dependants (
    dependant_id SERIAL PRIMARY KEY,
    client_id INTEGER REFERENCES clients(client_id) ON DELETE CASCADE,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    date_of_birth DATE NOT NULL,
    grade_level VARCHAR(50),
    school_name VARCHAR(200),
    special_needs TEXT,
    notes TEXT,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Dependant relationships table
CREATE TABLE dependant_relationships (
    relationship_id SERIAL PRIMARY KEY,
    client_id INTEGER REFERENCES clients(client_id) ON DELETE CASCADE,
    dependant_id INTEGER REFERENCES dependants(dependant_id) ON DELETE CASCADE,
    relationship_type VARCHAR(50) NOT NULL,
    is_primary_contact BOOLEAN DEFAULT FALSE,
    can_schedule BOOLEAN DEFAULT TRUE,
    can_receive_updates BOOLEAN DEFAULT TRUE,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(client_id, dependant_id)
);

-- Tutors table
CREATE TABLE tutors (
    tutor_id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(user_id) ON DELETE CASCADE,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    civic_number VARCHAR(20),
    street VARCHAR(200),
    city VARCHAR(100),
    postal_code VARCHAR(10),
    province VARCHAR(50) DEFAULT 'Quebec',
    country VARCHAR(50) DEFAULT 'Canada',
    date_of_birth DATE,
    sin VARCHAR(20),
    bank_account_number_encrypted TEXT,
    bank_institution_number VARCHAR(10),
    bank_transit_number VARCHAR(10),
    hourly_rate DECIMAL(8,2),
    bio TEXT,
    qualifications TEXT,
    availability_notes TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Services table
CREATE TABLE services (
    service_id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    base_rate DECIMAL(8,2),
    duration_minutes INTEGER DEFAULT 60,
    is_active BOOLEAN DEFAULT TRUE,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tutor services (many-to-many with custom rates)
CREATE TABLE tutor_services (
    tutor_service_id SERIAL PRIMARY KEY,
    tutor_id INTEGER REFERENCES tutors(tutor_id) ON DELETE CASCADE,
    service_id INTEGER REFERENCES services(service_id) ON DELETE CASCADE,
    custom_rate DECIMAL(8,2),
    transport_fee DECIMAL(8,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tutor_id, service_id)
);

-- Programs table (like TECFÉE)
CREATE TABLE programs (
    program_id SERIAL PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    total_sessions INTEGER,
    session_duration INTEGER DEFAULT 60,
    min_participants INTEGER DEFAULT 1,
    max_participants INTEGER DEFAULT 10,
    is_active BOOLEAN DEFAULT TRUE,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Program modules
CREATE TABLE program_modules (
    module_id SERIAL PRIMARY KEY,
    program_id INTEGER REFERENCES programs(program_id) ON DELETE CASCADE,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    module_order INTEGER NOT NULL,
    duration_minutes INTEGER DEFAULT 60,
    learning_objectives TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Program pricing options
CREATE TABLE program_pricing (
    pricing_id SERIAL PRIMARY KEY,
    program_id INTEGER REFERENCES programs(program_id) ON DELETE CASCADE,
    pricing_type VARCHAR(50) NOT NULL CHECK (pricing_type IN ('per_session', 'full_package')),
    price DECIMAL(10,2) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Client enrollments in programs
CREATE TABLE enrollments (
    enrollment_id SERIAL PRIMARY KEY,
    client_id INTEGER REFERENCES clients(client_id) ON DELETE CASCADE,
    program_id INTEGER REFERENCES programs(program_id) ON DELETE CASCADE,
    pricing_type VARCHAR(50) NOT NULL,
    enrollment_date DATE NOT NULL,
    start_date DATE,
    end_date DATE,
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled', 'suspended')),
    created_by INTEGER REFERENCES users(user_id),
    notes TEXT,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Module progress tracking
CREATE TABLE module_progress (
    progress_id SERIAL PRIMARY KEY,
    enrollment_id INTEGER REFERENCES enrollments(enrollment_id) ON DELETE CASCADE,
    module_id INTEGER REFERENCES program_modules(module_id) ON DELETE CASCADE,
    status VARCHAR(50) DEFAULT 'not_started' CHECK (status IN ('not_started', 'in_progress', 'completed')),
    completion_date DATE,
    score DECIMAL(5,2),
    notes TEXT,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(enrollment_id, module_id)
);

-- Group sessions
CREATE TABLE group_sessions (
    session_id SERIAL PRIMARY KEY,
    program_id INTEGER REFERENCES programs(program_id) ON DELETE CASCADE,
    module_id INTEGER REFERENCES program_modules(module_id) ON DELETE SET NULL,
    tutor_id INTEGER REFERENCES tutors(tutor_id) ON DELETE CASCADE,
    session_date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    max_participants INTEGER DEFAULT 10,
    current_participants INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'confirmed', 'completed', 'cancelled')),
    session_notes TEXT,
    tutor_payment_rate DECIMAL(8,2) DEFAULT 15.00,
    total_tutor_payment DECIMAL(10,2) DEFAULT 0.00,
    meeting_link VARCHAR(500),
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Group session participants
CREATE TABLE group_session_participants (
    participant_id SERIAL PRIMARY KEY,
    group_session_id INTEGER REFERENCES group_sessions(session_id) ON DELETE CASCADE,
    enrollment_id INTEGER REFERENCES enrollments(enrollment_id) ON DELETE CASCADE,
    attendance_status VARCHAR(50) CHECK (attendance_status IN ('registered', 'attended', 'absent', 'cancelled')),
    join_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    UNIQUE(group_session_id, enrollment_id)
);

-- Subscription plans
CREATE TABLE subscription_plans (
    plan_id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    max_hours INTEGER NOT NULL,
    validity_days INTEGER NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Client subscriptions
CREATE TABLE subscriptions (
    subscription_id SERIAL PRIMARY KEY,
    client_id INTEGER REFERENCES clients(client_id) ON DELETE CASCADE,
    plan_id INTEGER REFERENCES subscription_plans(plan_id) ON DELETE CASCADE,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    hours_remaining DECIMAL(5,2) NOT NULL,
    status VARCHAR(50) DEFAULT 'active' CHECK (status IN ('active', 'expired', 'cancelled')),
    auto_renew BOOLEAN DEFAULT FALSE,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Appointments table
CREATE TABLE appointments (
    appointment_id SERIAL PRIMARY KEY,
    client_id INTEGER REFERENCES clients(client_id) ON DELETE CASCADE,
    dependant_id INTEGER REFERENCES dependants(dependant_id) ON DELETE SET NULL,
    tutor_id INTEGER REFERENCES tutors(tutor_id) ON DELETE CASCADE,
    tutor_service_id INTEGER REFERENCES tutor_services(tutor_service_id) ON DELETE CASCADE,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP NOT NULL,
    status VARCHAR(50) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'confirmed', 'completed', 'cancelled', 'no-show', 'awaiting_confirmation')),
    location VARCHAR(200),
    notes TEXT,
    transport_fee DECIMAL(10, 2),
    transport_fee_for_tutor BOOLEAN DEFAULT TRUE,
    subscription_id INTEGER REFERENCES subscriptions(subscription_id),
    is_subscription_based BOOLEAN DEFAULT FALSE,
    
    -- Legacy recurring fields (deprecated - use appointment_recurring_schedules instead)
    is_recurring BOOLEAN DEFAULT FALSE,
    recurrence_pattern JSONB,
    recurrence_end_date DATE,
    
    -- Relationship to recurring schedules (new approach) - added later via ALTER TABLE
    recurring_schedule_id INTEGER,
    
    -- Audit tracking
    created_by INTEGER REFERENCES users(user_id) ON DELETE SET NULL,
    modified_by INTEGER REFERENCES users(user_id) ON DELETE SET NULL,
    deleted_by INTEGER REFERENCES users(user_id) ON DELETE SET NULL,
    deleted_at TIMESTAMP,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Subscription usage tracking
CREATE TABLE subscription_usage (
    usage_id SERIAL PRIMARY KEY,
    subscription_id INTEGER REFERENCES subscriptions(subscription_id) ON DELETE CASCADE,
    appointment_id INTEGER REFERENCES appointments(appointment_id) ON DELETE CASCADE,
    hours_used DECIMAL(5,2) NOT NULL,
    usage_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(subscription_id, appointment_id)
);

-- Appointment recurring schedules table (clean separation of concerns)
CREATE TABLE appointment_recurring_schedules (
    schedule_id SERIAL PRIMARY KEY,
    
    -- Core appointment details
    tutor_id INTEGER NOT NULL REFERENCES tutors(tutor_id) ON DELETE CASCADE,
    client_id INTEGER NOT NULL REFERENCES clients(client_id) ON DELETE CASCADE,
    dependant_id INTEGER REFERENCES dependants(dependant_id) ON DELETE SET NULL,
    tutor_service_id INTEGER NOT NULL REFERENCES tutor_services(tutor_service_id) ON DELETE CASCADE,
    
    -- Schedule timing
    start_time TIME NOT NULL,  -- Time of day for appointments
    duration_minutes INTEGER NOT NULL DEFAULT 60,
    
    -- Recurring pattern
    frequency VARCHAR(20) NOT NULL CHECK (frequency IN ('weekly', 'biweekly', 'monthly')),
    day_of_week INTEGER CHECK (day_of_week >= 0 AND day_of_week <= 6),  -- 0=Monday, 6=Sunday
    week_of_month INTEGER CHECK (week_of_month >= 1 AND week_of_month <= 5),  -- 1-5 for monthly patterns
    
    -- Schedule boundaries
    pattern_start_date DATE NOT NULL,
    pattern_end_date DATE,  -- NULL means no end date
    pattern_occurrences INTEGER CHECK (pattern_occurrences > 0),  -- Alternative to end_date
    
    -- Generation tracking
    last_generated_date DATE,  -- Track the last date appointments were generated
    
    -- Default appointment settings
    default_status VARCHAR(50) NOT NULL DEFAULT 'scheduled' CHECK (default_status IN ('scheduled', 'confirmed', 'completed', 'cancelled', 'no-show', 'awaiting_confirmation')),
    default_notes TEXT,
    transport_fee DECIMAL(10,2),
    transport_fee_for_tutor BOOLEAN DEFAULT TRUE,
    is_subscription_based BOOLEAN DEFAULT FALSE,
    subscription_id INTEGER REFERENCES subscriptions(subscription_id) ON DELETE SET NULL,
    
    -- Status and metadata
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_by INTEGER REFERENCES users(user_id) ON DELETE SET NULL,
    modified_by INTEGER REFERENCES users(user_id) ON DELETE SET NULL,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Constraints for data integrity
    CONSTRAINT check_pattern_end_constraint CHECK (pattern_end_date IS NULL OR pattern_end_date >= pattern_start_date),
    CONSTRAINT check_pattern_occurrences_or_end_date CHECK (pattern_end_date IS NOT NULL OR pattern_occurrences IS NOT NULL),
    CONSTRAINT check_weekly_day_of_week CHECK (
        (frequency IN ('weekly', 'biweekly') AND day_of_week IS NOT NULL) OR 
        (frequency = 'monthly')
    ),
    CONSTRAINT check_monthly_pattern CHECK (
        (frequency = 'monthly' AND day_of_week IS NOT NULL AND week_of_month IS NOT NULL) OR 
        (frequency IN ('weekly', 'biweekly'))
    )
);

-- Invoices table
CREATE TABLE invoices (
    invoice_id SERIAL PRIMARY KEY,
    client_id INTEGER REFERENCES clients(client_id) ON DELETE CASCADE,
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    issue_date DATE NOT NULL,
    due_date DATE NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) DEFAULT 0.00,
    status VARCHAR(50) DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'overdue', 'cancelled')),
    payment_date TIMESTAMP,
    payment_method VARCHAR(50),
    stripe_payment_intent_id VARCHAR(255),
    notes TEXT,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Invoice items
CREATE TABLE invoice_items (
    item_id SERIAL PRIMARY KEY,
    invoice_id INTEGER REFERENCES invoices(invoice_id) ON DELETE CASCADE,
    appointment_id INTEGER REFERENCES appointments(appointment_id) ON DELETE SET NULL,
    description TEXT NOT NULL,
    quantity INTEGER DEFAULT 1,
    unit_price DECIMAL(10,2) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tutor payments
CREATE TABLE tutor_payments (
    payment_id SERIAL PRIMARY KEY,
    tutor_id INTEGER REFERENCES tutors(tutor_id) ON DELETE CASCADE,
    appointment_id INTEGER REFERENCES appointments(appointment_id) ON DELETE CASCADE,
    service_amount DECIMAL(10,2) NOT NULL,
    transport_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    payment_date TIMESTAMP,
    stripe_payout_id VARCHAR(255),
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tutor availabilities
CREATE TABLE tutor_availabilities (
    availability_id SERIAL PRIMARY KEY,
    tutor_id INTEGER REFERENCES tutors(tutor_id) ON DELETE CASCADE,
    day_of_week INTEGER NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Time off requests
CREATE TABLE time_off_requests (
    time_off_id SERIAL PRIMARY KEY,
    tutor_id INTEGER REFERENCES tutors(tutor_id) ON DELETE CASCADE,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    reason TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    manager_notes TEXT,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Client consent tracking
CREATE TABLE client_consents (
    consent_id SERIAL PRIMARY KEY,
    client_id INTEGER REFERENCES clients(client_id) ON DELETE CASCADE,
    consent_type VARCHAR(100) NOT NULL,
    consent_given BOOLEAN NOT NULL,
    consent_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address INET,
    user_agent TEXT,
    notes TEXT
);

-- Tutor service rates (separate from tutor_services for rate management)
CREATE TABLE tutor_service_rates (
    rate_id SERIAL PRIMARY KEY,
    tutor_id INTEGER REFERENCES tutors(tutor_id) ON DELETE CASCADE,
    service_id INTEGER REFERENCES services(service_id) ON DELETE CASCADE,
    rate DECIMAL(8,2) NOT NULL,
    effective_date DATE NOT NULL,
    end_date DATE,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER REFERENCES users(user_id),
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tutor_id, service_id, effective_date)
);

-- Notifications table
CREATE TABLE notifications (
    notification_id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(user_id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    notification_type VARCHAR(50) NOT NULL CHECK (notification_type IN ('appointment', 'payment', 'system', 'reminder')),
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP,
    priority VARCHAR(20) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
    related_appointment_id INTEGER REFERENCES appointments(appointment_id) ON DELETE SET NULL,
    related_invoice_id INTEGER REFERENCES invoices(invoice_id) ON DELETE SET NULL,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Invoice generation settings
CREATE TABLE invoice_generation_settings (
    setting_id SERIAL PRIMARY KEY,
    setting_name VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER REFERENCES users(user_id),
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Appointment audit trail
CREATE TABLE appointment_audit (
    audit_id SERIAL PRIMARY KEY,
    appointment_id INTEGER REFERENCES appointments(appointment_id) ON DELETE CASCADE,
    action VARCHAR(50) NOT NULL CHECK (action IN ('created', 'updated', 'cancelled', 'completed', 'rescheduled')),
    old_values JSONB,
    new_values JSONB,
    changed_by INTEGER REFERENCES users(user_id) ON DELETE SET NULL,
    change_reason TEXT,
    ip_address INET,
    user_agent TEXT,
    insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_clients_user_id ON clients(user_id);
CREATE INDEX idx_tutors_user_id ON tutors(user_id);
CREATE INDEX idx_appointments_client_id ON appointments(client_id);
CREATE INDEX idx_appointments_tutor_id ON appointments(tutor_id);
CREATE INDEX idx_appointments_start_time ON appointments(start_time);
CREATE INDEX idx_appointments_status ON appointments(status);
CREATE INDEX idx_invoices_client_id ON invoices(client_id);
CREATE INDEX idx_invoices_status ON invoices(status);
CREATE INDEX idx_group_sessions_program_id ON group_sessions(program_id);
CREATE INDEX idx_group_sessions_date ON group_sessions(session_date);
CREATE INDEX idx_enrollments_client_id ON enrollments(client_id);
CREATE INDEX idx_enrollments_program_id ON enrollments(program_id);
CREATE INDEX idx_tutor_payments_tutor_id ON tutor_payments(tutor_id);
CREATE INDEX idx_tutor_payments_appointment_id ON tutor_payments(appointment_id);
CREATE INDEX idx_tutor_availabilities_tutor_id ON tutor_availabilities(tutor_id);
CREATE INDEX idx_time_off_requests_tutor_id ON time_off_requests(tutor_id);

-- Indexes for new tables
CREATE INDEX idx_tutor_service_rates_tutor_id ON tutor_service_rates(tutor_id);
CREATE INDEX idx_tutor_service_rates_service_id ON tutor_service_rates(service_id);
CREATE INDEX idx_tutor_service_rates_effective_date ON tutor_service_rates(effective_date);
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_type ON notifications(notification_type);
CREATE INDEX idx_notifications_is_read ON notifications(is_read);
CREATE INDEX idx_notifications_priority ON notifications(priority);
CREATE INDEX idx_appointment_audit_appointment_id ON appointment_audit(appointment_id);
CREATE INDEX idx_appointment_audit_changed_by ON appointment_audit(changed_by);
CREATE INDEX idx_appointment_audit_action ON appointment_audit(action);

-- Indexes for appointment_recurring_schedules table
CREATE INDEX idx_appointment_recurring_schedules_tutor_id ON appointment_recurring_schedules(tutor_id);
CREATE INDEX idx_appointment_recurring_schedules_client_id ON appointment_recurring_schedules(client_id);
CREATE INDEX idx_appointment_recurring_schedules_dependant_id ON appointment_recurring_schedules(dependant_id);
CREATE INDEX idx_appointment_recurring_schedules_tutor_service_id ON appointment_recurring_schedules(tutor_service_id);
CREATE INDEX idx_appointment_recurring_schedules_pattern_start_date ON appointment_recurring_schedules(pattern_start_date);
CREATE INDEX idx_appointment_recurring_schedules_pattern_end_date ON appointment_recurring_schedules(pattern_end_date);
CREATE INDEX idx_appointment_recurring_schedules_frequency ON appointment_recurring_schedules(frequency);
CREATE INDEX idx_appointment_recurring_schedules_is_active ON appointment_recurring_schedules(is_active);
CREATE INDEX idx_appointment_recurring_schedules_last_generated_date ON appointment_recurring_schedules(last_generated_date);

-- Add foreign key constraint for recurring_schedule_id (after appointment_recurring_schedules table is created)
ALTER TABLE appointments ADD CONSTRAINT fk_appointments_recurring_schedule 
    FOREIGN KEY (recurring_schedule_id) REFERENCES appointment_recurring_schedules(schedule_id) ON DELETE SET NULL;

-- Index for the new foreign key in appointments
CREATE INDEX idx_appointments_recurring_schedule_id ON appointments(recurring_schedule_id);

-- Add triggers for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_modification_date()
RETURNS TRIGGER AS $$
BEGIN
    NEW.modification_date = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply triggers to relevant tables
CREATE TRIGGER update_users_modification_date BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_modification_date();
CREATE TRIGGER update_clients_modification_date BEFORE UPDATE ON clients FOR EACH ROW EXECUTE FUNCTION update_modification_date();
CREATE TRIGGER update_tutors_modification_date BEFORE UPDATE ON tutors FOR EACH ROW EXECUTE FUNCTION update_modification_date();
CREATE TRIGGER update_appointments_modification_date BEFORE UPDATE ON appointments FOR EACH ROW EXECUTE FUNCTION update_modification_date();
CREATE TRIGGER update_invoices_modification_date BEFORE UPDATE ON invoices FOR EACH ROW EXECUTE FUNCTION update_modification_date();
CREATE TRIGGER update_programs_modification_date BEFORE UPDATE ON programs FOR EACH ROW EXECUTE FUNCTION update_modification_date();
CREATE TRIGGER update_group_sessions_modification_date BEFORE UPDATE ON group_sessions FOR EACH ROW EXECUTE FUNCTION update_modification_date();
CREATE TRIGGER update_enrollments_modification_date BEFORE UPDATE ON enrollments FOR EACH ROW EXECUTE FUNCTION update_modification_date();
CREATE TRIGGER update_appointment_recurring_schedules_modification_date BEFORE UPDATE ON appointment_recurring_schedules FOR EACH ROW EXECUTE FUNCTION update_modification_date();
CREATE TRIGGER update_tutor_availabilities_modification_date BEFORE UPDATE ON tutor_availabilities FOR EACH ROW EXECUTE FUNCTION update_modification_date();
CREATE TRIGGER update_time_off_requests_modification_date BEFORE UPDATE ON time_off_requests FOR EACH ROW EXECUTE FUNCTION update_modification_date();
CREATE TRIGGER update_tutor_service_rates_modification_date BEFORE UPDATE ON tutor_service_rates FOR EACH ROW EXECUTE FUNCTION update_modification_date();
CREATE TRIGGER update_notifications_modification_date BEFORE UPDATE ON notifications FOR EACH ROW EXECUTE FUNCTION update_modification_date();
CREATE TRIGGER update_invoice_generation_settings_modification_date BEFORE UPDATE ON invoice_generation_settings FOR EACH ROW EXECUTE FUNCTION update_modification_date();

-- Insert default data
INSERT INTO subscription_plans (name, description, price, max_hours, validity_days) VALUES
('Basic Plan', '10 hours of tutoring valid for 30 days', 299.99, 10, 30),
('Standard Plan', '20 hours of tutoring valid for 60 days', 549.99, 20, 60),
('Premium Plan', '40 hours of tutoring valid for 90 days', 999.99, 40, 90);

-- Create TECFÉE program
INSERT INTO programs (code, name, description, total_sessions, session_duration, min_participants, max_participants) VALUES
('TECFEE', 'PARCOURS COMPLET LINGUISTIQUE TECFÉE', 'Préparation complète au test TECFÉE en 12 modules structurés', 12, 60, 4, 10);

-- Get the TECFÉE program ID for module insertion
DO $$
DECLARE
    tecfee_program_id INTEGER;
BEGIN
    SELECT program_id INTO tecfee_program_id FROM programs WHERE code = 'TECFEE';

    -- Insert TECFÉE modules
    INSERT INTO program_modules (program_id, name, description, module_order, duration_minutes, learning_objectives) VALUES
    (tecfee_program_id, 'Manipulation Syntaxiques et Classe de Mots', 'Manipulation des structures syntaxiques et identification des classes de mots', 1, 60, 'Maîtriser les manipulations syntaxiques et identifier correctement les classes de mots'),
    (tecfee_program_id, 'Connecteurs', 'Utilisation appropriée des connecteurs logiques et temporels', 2, 60, 'Utiliser correctement les connecteurs pour structurer le discours'),
    (tecfee_program_id, 'Pléonasmes', 'Identification et correction des pléonasmes', 3, 60, 'Reconnaître et éviter les pléonasmes dans l''expression écrite'),
    (tecfee_program_id, 'Affixes et Expressions (Partie 1)', 'Formation des mots par affixation - Partie 1', 4, 60, 'Comprendre la formation des mots par préfixes et suffixes'),
    (tecfee_program_id, 'Affixes et Expressions (Partie 2)', 'Formation des mots par affixation - Partie 2', 5, 60, 'Maîtriser l''utilisation des expressions figées'),
    (tecfee_program_id, 'Syntaxe (Partie 1)', 'Analyse syntaxique et construction de phrases - Partie 1', 6, 60, 'Analyser la structure syntaxique des phrases simples'),
    (tecfee_program_id, 'Syntaxe (Partie 2)', 'Analyse syntaxique et construction de phrases - Partie 2', 7, 60, 'Construire des phrases complexes syntaxiquement correctes'),
    (tecfee_program_id, 'Orthographe (Partie 1)', 'Règles d''orthographe et application - Partie 1', 8, 60, 'Appliquer les règles d''orthographe de base'),
    (tecfee_program_id, 'Orthographe (Partie 2)', 'Règles d''orthographe et application - Partie 2', 9, 60, 'Maîtriser l''orthographe des mots complexes'),
    (tecfee_program_id, 'Ponctuation', 'Utilisation correcte de la ponctuation', 10, 60, 'Utiliser la ponctuation pour clarifier le sens'),
    (tecfee_program_id, 'Révision Générale', 'Révision de tous les concepts abordés', 11, 60, 'Consolider les apprentissages et identifier les points à améliorer'),
    (tecfee_program_id, 'Simulation Examen', 'Examen blanc complet dans les conditions réelles du TECFÉE', 12, 120, 'Se familiariser avec le format d''examen et évaluer sa préparation');

    -- Insert TECFÉE pricing options
    INSERT INTO program_pricing (program_id, pricing_type, price, description) VALUES
    (tecfee_program_id, 'per_session', 44.99, 'Paiement par module individuel'),
    (tecfee_program_id, 'full_package', 399.00, 'Package complet - 12 modules (économisez 140,88$!)');

END $$;

-- This schema represents the current state of the TutorAide application
-- Updated with consistent descriptive primary key naming conventions
