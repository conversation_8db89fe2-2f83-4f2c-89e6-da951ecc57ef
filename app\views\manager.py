# app/views/manager.py
from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app, make_response
from flask_login import login_required, current_user
from werkzeug.security import check_password_hash

from app.extensions import db
from app.models.user import User
from app.models.manager import Manager
from app.models.client import Client, IndividualClient, InstitutionalClient, ClientRelationship


from app.models.tutor import Tutor
from app.models.service import Service, TutorService
from app.models.appointment import Appointment
from app.models.invoice import Invoice, InvoiceItem
from app.models.subscription import SubscriptionPlan, Subscription, SubscriptionUsage
from app.models.tutor_availability import TutorAvailability
from app.models.tutor_service_rate import TutorServiceRate
from app.models.time_off import TimeOff
from app.models.notification import Notification
from app.models.dependant import Dependant, DependantRelationship
from app.models.program import GroupSessionParticipant
from app.services.subscription_service import SubscriptionService
from app.forms.manager_forms import (
    ClientForm, TutorForm, ServiceForm,
    TutorServiceForm, AppointmentForm, FilterForm,
    SubscriptionForm, SubscriptionPlanForm, TimeOffResponseForm, ManagerProfileForm)


from app.models.tutor_payment import TutorPayment
from app.services.tutor_payment_service import TutorPaymentService
from app.services.appointment_service import AppointmentService
from sqlalchemy import or_, func
from app.services.invoice_service import InvoiceService
from app.utils.email import send_invoice_email
from datetime import datetime, timedelta



manager = Blueprint('manager', __name__)

# Require manager role for all routes in this blueprint
@manager.before_request
def check_manager():
    if not current_user.is_authenticated or current_user.role != 'manager':
        flash('You must be a manager to access this area.', 'danger')
        return redirect(url_for('auth.login'))


@manager.route('/')
@login_required
def dashboard():
    # Get counts for dashboard stats
    clients_count = Client.query.count()
    individual_clients_count = IndividualClient.query.count()
    tutors_count = Tutor.query.count()

    # Get upcoming appointments (next 7 days)
    today = datetime.now()
    end_date = today + timedelta(days=7)

    upcoming_appointments = Appointment.query.filter(
        Appointment.start_time.between(today, end_date),
        Appointment.status == 'scheduled'
    ).order_by(Appointment.start_time).limit(10).all()

    # Get unpaid invoices grouped by parent and sorted by amount
    grouped_unpaid_invoices = InvoiceService.get_unpaid_invoices_by_client()

    # Flatten for dashboard display (limited to 10)
    unpaid_invoices = []
    for parent_invoices in grouped_unpaid_invoices.values():
        unpaid_invoices.extend(parent_invoices)
        if len(unpaid_invoices) >= 10:
            unpaid_invoices = unpaid_invoices[:10]
            break

    # Get past appointments that need status updates
    yesterday = today - timedelta(days=1)
    past_appointments_needing_update = Appointment.query.filter(
        Appointment.end_time < yesterday,
        Appointment.status == 'scheduled'
    ).order_by(Appointment.start_time.desc()).limit(10).all()

    # Get pending time-off requests
    pending_time_off_requests = TimeOff.query.filter_by(status='pending').order_by(TimeOff.start_date).all()

    # Get unread notifications
    unread_notifications = Notification.query.filter_by(
        user_id=current_user.user_id,
        is_read=False
    ).order_by(Notification.insert_date.desc()).all()

    return render_template('manager/dashboard.html',
                           clients_count=clients_count,
                           individual_clients_count=individual_clients_count,
                           tutors_count=tutors_count,
                           upcoming_appointments=upcoming_appointments,
                           unpaid_invoices=unpaid_invoices,
                           past_appointments_needing_update=past_appointments_needing_update,
                           pending_time_off_requests=pending_time_off_requests,
                           unread_notifications=unread_notifications)

@manager.route('/notifications')
@login_required
def notifications():
    """View all notifications."""
    notifications = Notification.query.filter_by(
        user_id=current_user.id
    ).order_by(Notification.insert_date.desc()).all()

    return render_template('manager/notifications.html',
                          notifications=notifications)

@manager.route('/notifications/mark-read', methods=['POST'])
@login_required
def mark_notifications_read():
    """Mark all notifications as read."""
    notification_id = request.form.get('notification_id')

    if notification_id:
        # Mark a specific notification as read
        Notification.mark_as_read(notification_id)
        flash('Notification marked as read.', 'success')
    else:
        # Mark all notifications as read
        count = Notification.mark_all_as_read(current_user.id)
        flash(f'{count} notifications marked as read.', 'success')

    return redirect(url_for('manager.notifications'))

@manager.route('/time-off-requests')
@login_required
def time_off_requests():
    """View all time-off requests."""
    # Get filter parameters
    status = request.args.get('status', 'all')

    # Base query
    query = TimeOff.query

    # Apply filters
    if status != 'all':
        query = query.filter_by(status=status)

    # Get time-off requests
    requests = query.order_by(TimeOff.start_date.desc()).all()

    return render_template('manager/time_off_requests.html',
                          requests=requests,
                          current_status=status)

@manager.route('/time-off-requests/<int:id>', methods=['GET', 'POST'])
@login_required
def view_time_off_request(id):
    """View and respond to a time-off request."""
    time_off = TimeOff.query.get_or_404(id)
    tutor = Tutor.query.get_or_404(time_off.tutor_id)

    form = TimeOffResponseForm()

    if form.validate_on_submit():
        time_off.status = form.status.data
        time_off.manager_notes = form.manager_notes.data
        db.session.commit()

        # Create notification for tutor
        Notification.create_notification(
            user_id=tutor.user_id,
            message=f"Your time-off request for {time_off.start_date.strftime('%Y-%m-%d')} to {time_off.end_date.strftime('%Y-%m-%d')} has been {time_off.status}.",
            category='time_off',
            related_id=time_off.id
        )

        flash('Time-off request updated successfully.', 'success')
        return redirect(url_for('manager.time_off_requests'))

    return render_template('manager/time_off_request_detail.html',
                          time_off=time_off,
                          tutor=tutor,
                          form=form)

# Schedule views
@manager.route('/schedule')
@login_required
def schedule():
    # Get all active tutors for filtering
    tutors = Tutor.query.filter_by(is_active=True).all()

    # Default view is by week, starting with current date
    view_type = request.args.get('view', 'week')
    start_date_str = request.args.get('start_date', datetime.now().strftime('%Y-%m-%d'))

    try:
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
    except ValueError:
        start_date = datetime.now()

    # Get end date based on view type
    if view_type == 'day':
        end_date = start_date + timedelta(days=1)
    elif view_type == 'week':
        # Start from Monday of the current week
        monday = start_date - timedelta(days=start_date.weekday())
        start_date = monday
        end_date = start_date + timedelta(days=7)
    elif view_type == 'month':
        # Start from the 1st of the month
        start_date = start_date.replace(day=1)
        # Go to the 1st of next month
        if start_date.month == 12:
            end_date = start_date.replace(year=start_date.year+1, month=1)
        else:
            end_date = start_date.replace(month=start_date.month+1)

    # Get appointments for the selected period with eager loading of relationships
    from sqlalchemy.orm import joinedload, selectinload
    appointments = Appointment.query.options(
        selectinload(Appointment.client),
        selectinload(Appointment.dependant),
        selectinload(Appointment.tutor),
        selectinload(Appointment.tutor_service).selectinload(TutorService.service)
    ).filter(
        Appointment.start_time >= start_date,
        Appointment.start_time < end_date
    ).all()



    # Get group sessions for the same date range
    from app.models.program import GroupSession
    group_sessions = GroupSession.query.filter(
        GroupSession.session_date >= start_date.date(),
        GroupSession.session_date <= end_date.date()
    ).all()

    # Parse appointments into a format suitable for the calendar
    calendar_data = AppointmentService.prepare_calendar_data(appointments, tutors, start_date, end_date, view_type)

    # Add group sessions to calendar data
    for group_session in group_sessions:
        # Create a datetime object for start and end times
        session_start = datetime.combine(group_session.session_date, group_session.start_time)
        session_end = datetime.combine(group_session.session_date, group_session.end_time)

        calendar_data['appointments'].append({
            'id': f"group_{group_session.id}",  # Prefix to distinguish from regular appointments
            'type': 'group_session',
            'tutor_id': group_session.tutor_id,
            'client_name': f"TECFÉE - Module {group_session.module.module_order if group_session.module else 'TBD'}",
            'student_name': f"TECFÉE Group ({group_session.current_participants}/{group_session.max_participants})",
            'service_name': f"TECFÉE - {group_session.module.name if group_session.module else 'Group Session'}",
            'start_time': session_start.strftime('%Y-%m-%d %H:%M'),
            'end_time': session_end.strftime('%Y-%m-%d %H:%M'),
            'status': group_session.status,
            'notes': group_session.session_notes or '',
            'participants': group_session.current_participants,
            'max_participants': group_session.max_participants
        })



    # Determine navigation dates
    prev_date, next_date = AppointmentService.get_navigation_dates(start_date, view_type)

    response = render_template('manager/schedule.html',
                          tutors=tutors,
                          view_type=view_type,
                          start_date=start_date,
                          calendar_data=calendar_data,
                          prev_date=prev_date,
                          next_date=next_date,
                          timedelta=timedelta,
                          now=datetime.now())

    # Add cache-busting headers to prevent browser caching
    from flask import make_response
    response = make_response(response)
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'

    return response

@manager.route('/appointments/new', methods=['GET', 'POST'])
@login_required
def new_appointment():
    form = AppointmentForm()

    # Check if this should default to recurring mode
    recurring_param = request.args.get('recurring', 'false').lower() == 'true'
    if recurring_param:
        form.is_recurring.data = True

    # Populate form choices
    AppointmentService.populate_appointment_form_choices(form)

    if form.validate_on_submit():
        if form.is_recurring.data:
            # Create recurring appointment template and initial appointments
            recurring_template, generated_appointments = AppointmentService.create_appointment_from_form(form)

            if recurring_template:
                flash(f'Recurring appointment created successfully! {len(generated_appointments)} appointments have been scheduled.', 'success')
                return redirect(url_for('manager.schedule'))
            else:
                flash('Error creating recurring appointment.', 'danger')
        else:
            # Create single appointment
            appointment = AppointmentService.create_appointment_from_form(form)

            if appointment:
                flash('Appointment scheduled successfully!', 'success')
                return redirect(url_for('manager.schedule'))
            else:
                flash('Error scheduling appointment. Please check for conflicts.', 'danger')

    # Add this line to pass hasattr to the template
    return render_template('manager/appointment_form.html', form=form, title="Schedule New Appointment", hasattr=hasattr)

@manager.route('/appointments/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_appointment(id):
    appointment = Appointment.query.get_or_404(id)
    form = AppointmentForm()

    # Only populate form from appointment object on GET request
    if request.method == 'GET':
        # Handle different field population based on appointment type
        if appointment.is_recurring:
            # For recurring templates, populate recurring fields
            form.is_recurring.data = True
            # Use start_time for recurring appointments (not template_start_time)
            if appointment.start_time:
                form.start_time.data = appointment.start_time.strftime('%H:%M')
            form.frequency.data = appointment.frequency
            form.day_of_week.data = appointment.day_of_week
            form.week_of_month.data = appointment.week_of_month
            form.pattern_start_date.data = appointment.pattern_start_date
            form.pattern_end_date.data = appointment.pattern_end_date
            form.pattern_occurrences.data = appointment.pattern_occurrences

            # Set end_type based on what's populated
            if appointment.pattern_end_date:
                form.end_type.data = 'date'
            elif appointment.pattern_occurrences:
                form.end_type.data = 'occurrences'
            else:
                form.end_type.data = 'never'
        else:
            # For regular appointments, populate regular fields
            form.is_recurring.data = False
            if appointment.start_time:
                form.start_date.data = appointment.start_time.date()
                # Convert time to string format for SelectField
                form.start_time.data = appointment.start_time.strftime('%H:%M')
            if appointment.end_time:
                form.end_time.data = appointment.end_time.time()

            # Populate client/dependant selection using prefixed format
            if appointment.dependant_id:
                # This appointment is for a dependant
                form.client_id.data = f"d_{appointment.dependant_id}"
                form.client_type.data = 'dependant'
                form.dependant_id.data = appointment.dependant_id
            else:
                # This appointment is for a client
                form.client_id.data = f"c_{appointment.client_id}"
                form.client_type.data = 'client'

            # Populate other fields
            form.tutor_id.data = appointment.tutor_id
            form.tutor_service_id.data = appointment.tutor_service_id
            form.status.data = appointment.status
            form.notes.data = appointment.notes

    # Populate form choices
    AppointmentService.populate_appointment_form_choices(form)

    if form.validate_on_submit():
        if appointment.is_recurring and form.is_recurring.data:
            # Update recurring template (this would need special handling)
            flash('Updating recurring appointment templates is not yet implemented.', 'warning')
            return redirect(url_for('manager.schedule'))
        elif not appointment.is_recurring and not form.is_recurring.data:
            # Update regular appointment
            success = AppointmentService.update_appointment_from_form(appointment, form)
            if success:
                # Force a database session refresh to ensure changes are visible
                db.session.refresh(appointment)

                flash('Appointment updated successfully!', 'success')
                # Always redirect to calendar view after successful edit
                import time
                return redirect(url_for('manager.schedule', _t=int(time.time())))
            else:
                flash('Error updating appointment. Please check for conflicts.', 'danger')
        else:
            flash('Cannot change appointment type from regular to recurring or vice versa.', 'danger')

    title = "Edit Recurring Appointment" if appointment.is_recurring else "Edit Appointment"
    return render_template('manager/appointment_form.html', form=form, appointment=appointment, title=title, hasattr=hasattr)

@manager.route('/appointments/<int:id>')
@login_required
def view_appointment(id):
    from sqlalchemy.orm import joinedload
    appointment = Appointment.query.options(
        joinedload(Appointment.client),
        joinedload(Appointment.dependant),
        joinedload(Appointment.tutor),
        joinedload(Appointment.tutor_service).joinedload(TutorService.service)
    ).get_or_404(id)

    # Force fresh data from database to avoid cache issues
    db.session.refresh(appointment)

    # Create response with cache-busting headers
    response = make_response(render_template('manager/appointment_detail.html', appointment=appointment))
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate, max-age=0'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '-1'
    response.headers['Last-Modified'] = datetime.now().strftime('%a, %d %b %Y %H:%M:%S GMT')

    return response

@manager.route('/appointments/<int:id>/cancel', methods=['POST'])
@login_required
def cancel_appointment(id):
    appointment = Appointment.query.get_or_404(id)

    if appointment.status != 'cancelled':
        # Use the service method which includes audit logging
        AppointmentService.update_appointment_status(id, 'cancelled', 'Cancelled by manager')
        flash('Appointment cancelled successfully.', 'success')
    else:
        flash('Appointment was already cancelled.', 'info')

    return redirect(url_for('manager.schedule'))

@manager.route('/appointments/<int:id>/take-attendance', methods=['POST'])
@login_required
def take_attendance(id):
    from app.services.appointment_service import AppointmentService

    appointment = Appointment.query.get_or_404(id)

    if appointment.status == 'scheduled':
        # Use enhanced service with better transaction management
        updated_appointment, error = AppointmentService.update_appointment_status(
            id, 'awaiting_confirmation'
        )

        if updated_appointment:
            flash('Attendance marked for confirmation.', 'success')
        else:
            flash(f'Error marking attendance: {error}', 'error')
    else:
        flash('Cannot take attendance for this appointment.', 'warning')

    # Create response with strong cache-busting headers
    response = make_response(redirect(url_for('manager.view_appointment', id=appointment.id)))
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate, max-age=0'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '-1'
    response.headers['Last-Modified'] = datetime.now().strftime('%a, %d %b %Y %H:%M:%S GMT')

    return response

@manager.route('/appointments/<int:id>/confirm-attendance', methods=['POST'])
@login_required
def confirm_attendance(id):
    from app.services.appointment_service import AppointmentService

    appointment = Appointment.query.get_or_404(id)
    confirmation = request.form.get('confirmation')

    # Handle both full and shortened status values
    if appointment.status not in ['awaiting_confirmation', 'awaiting_confirm']:
        flash('This appointment is not awaiting confirmation.', 'warning')
        return redirect(url_for('manager.view_appointment', id=appointment.id))

    try:
        if confirmation == 'attended':
            # Use enhanced service for status update
            updated_appointment, error = AppointmentService.update_appointment_status(
                id, 'completed'
            )

            if updated_appointment:
                flash('Appointment marked as completed.', 'success')
                current_app.logger.info(f"Successfully completed appointment {id}")
                # Redirect to schedule view when appointment is completed
                response = make_response(redirect(url_for('manager.schedule')))
            else:
                flash(f'Error completing appointment: {error}', 'error')
                current_app.logger.error(f"Error completing appointment {id}: {error}")
                response = make_response(redirect(url_for('manager.view_appointment', id=appointment.id)))

        elif confirmation == 'cancelled_with_notice':
            # Update notes and status
            notes = (appointment.notes or '') + '\nCancelled with notice.'
            updated_appointment, error = AppointmentService.update_appointment_status(
                id, 'cancelled', notes
            )

            if updated_appointment:
                flash('Appointment marked as cancelled with notice.', 'success')
            else:
                flash(f'Error cancelling appointment: {error}', 'error')
            response = make_response(redirect(url_for('manager.view_appointment', id=appointment.id)))

        elif confirmation == 'cancelled_without_notice':
            # Update notes and status
            notes = (appointment.notes or '') + '\nCancelled without notice (no-show).'
            updated_appointment, error = AppointmentService.update_appointment_status(
                id, 'no-show', notes
            )

            if updated_appointment:
                flash('Appointment marked as no-show.', 'success')
            else:
                flash(f'Error marking no-show: {error}', 'error')
            response = make_response(redirect(url_for('manager.view_appointment', id=appointment.id)))

        else:
            flash('Invalid confirmation status.', 'danger')
            response = make_response(redirect(url_for('manager.view_appointment', id=appointment.id)))

    except Exception as e:
        current_app.logger.error(f"Error confirming attendance for appointment {id}: {str(e)}")
        flash('Error updating appointment. Please try again.', 'danger')
        response = make_response(redirect(url_for('manager.view_appointment', id=appointment.id)))

    # Add cache-busting headers
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate, max-age=0'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '-1'
    response.headers['Last-Modified'] = datetime.now().strftime('%a, %d %b %Y %H:%M:%S GMT')

    return response

@manager.route('/appointments/<int:id>/quick-status-change', methods=['POST'])
@login_required
def quick_status_change(id):
    """Quick status change for appointments without confirmation dialogs."""
    from app.services.appointment_service import AppointmentService

    appointment = Appointment.query.get_or_404(id)
    new_status = request.form.get('status')

    if not new_status:
        flash('Invalid status provided.', 'danger')
        return redirect(url_for('manager.view_appointment', id=appointment.id))

    # Validate allowed status transitions
    valid_statuses = ['scheduled', 'completed', 'cancelled', 'no-show', 'awaiting_confirmation']
    if new_status not in valid_statuses:
        flash('Invalid status provided.', 'danger')
        return redirect(url_for('manager.view_appointment', id=appointment.id))

    try:
        # Use the appointment service for status updates
        updated_appointment, error = AppointmentService.update_appointment_status(
            id, new_status
        )

        if updated_appointment:
            if new_status == 'completed':
                flash('Appointment marked as completed.', 'success')
                # Redirect to schedule view when appointment is completed
                response = make_response(redirect(url_for('manager.schedule')))
            elif new_status == 'cancelled':
                flash('Appointment cancelled.', 'success')
                response = make_response(redirect(url_for('manager.view_appointment', id=appointment.id)))
            elif new_status == 'awaiting_confirmation':
                flash('Appointment marked as awaiting confirmation.', 'success')
                response = make_response(redirect(url_for('manager.view_appointment', id=appointment.id)))
            else:
                flash(f'Appointment status updated to {new_status}.', 'success')
                response = make_response(redirect(url_for('manager.view_appointment', id=appointment.id)))
        else:
            flash(f'Error updating appointment status: {error}', 'error')
            response = make_response(redirect(url_for('manager.view_appointment', id=appointment.id)))

    except Exception as e:
        current_app.logger.error(f"Error updating appointment {id} status to {new_status}: {str(e)}")
        flash('Error updating appointment. Please try again.', 'danger')
        response = make_response(redirect(url_for('manager.view_appointment', id=appointment.id)))

    # Add cache-busting headers
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate, max-age=0'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '-1'
    response.headers['Last-Modified'] = datetime.now().strftime('%a, %d %b %Y %H:%M:%S GMT')

    return response

# Client management
@manager.route('/clients')
@login_required
def clients_list():
    form = FilterForm(formdata=request.args)

    # Use LEFT JOIN to include clients without user accounts
    query = Client.query.outerjoin(User)

    # Apply filters if provided
    if form.validate():
        if form.search.data:
            search_term = f"%{form.search.data}%"
            query = query.filter(or_(
                Client.first_name.ilike(search_term),
                Client.last_name.ilike(search_term),
                User.email.ilike(search_term),
                Client.phone.ilike(search_term),
                Client.email.ilike(search_term)  # Also search client.email for clients without user accounts
            ))

    clients = query.order_by(Client.last_name).all()

    return render_template('manager/clients_list.html', clients=clients, form=form)

# Legacy route for backward compatibility
@manager.route('/parents')
@login_required
def parents_list():
    return redirect(url_for('manager.clients_list'))

@manager.route('/clients/new', methods=['GET', 'POST'])
@login_required
def new_client():
    form = ClientForm()

    if form.validate_on_submit():
        # Check if email already exists
        if User.query.filter_by(email=form.email.data).first():
            flash('Email already registered', 'danger')
            return render_template('manager/client_form.html', form=form, title="New Client")

        # Create user first - only if password is provided
        if form.password.data and form.password.data.strip():
            user = User(
                email=form.email.data,
                password=form.password.data,
                role='client'
            )
            db.session.add(user)
            db.session.flush()  # Get the user ID without committing
            user_id = user.id
        else:
            # No password provided, create client without user account
            user_id = None

        # Create the appropriate client type directly
        if form.client_type.data == 'individual':
            client = IndividualClient()
            client.date_of_birth = form.date_of_birth.data
            client.school_grade = form.school_grade.data
            client.notes = form.notes.data
        elif form.client_type.data == 'institutional':
            client = InstitutionalClient()
            client.institution_name = form.organization_name.data
            client.contact_person = form.contact_person.data
            client.notes = form.notes.data
        else:
            flash('Invalid client type selected', 'danger')
            return render_template('manager/client_form.html', form=form, title="New Client")

        # Set common client fields
        client.user_id = user_id
        client.first_name = form.first_name.data
        client.last_name = form.last_name.data
        client.phone = form.phone.data if form.phone.data else None
        client.email = form.email.data if form.email.data else None

        # Set structured address fields
        client.civic_number = form.civic_number.data if form.civic_number.data else None
        client.street = form.street.data if form.street.data else None
        client.city = form.city.data if form.city.data else None
        client.postal_code = form.postal_code.data if form.postal_code.data else None
        client.province = form.province.data if form.province.data else 'Quebec'
        client.country = form.country.data if form.country.data else 'Canada'

        client.is_suspended = False
        client.preferred_language = 'en'
        db.session.add(client)
        db.session.flush()  # Get the client ID without committing

        # Handle dependant relationships
        if form.dependant_ids.data and form.dependant_relationships.data:
            try:
                import json
                dependant_ids = json.loads(form.dependant_ids.data)
                dependant_relationships = json.loads(form.dependant_relationships.data)

                # Create relationships using DependantRelationship
                for relationship_data in dependant_relationships:
                    dependant_id = relationship_data['id']
                    relationship_type = relationship_data['relationship_type']
                    is_primary = relationship_data['is_primary']

                    # Verify the dependant exists in the dependants table
                    if dependant_id in dependant_ids:
                        dependant = Dependant.query.get(dependant_id)
                        if dependant:
                            relationship = DependantRelationship(
                                client_id=client.id,
                                dependant_id=dependant_id,
                                relationship_type=relationship_type,
                                is_primary=is_primary
                            )
                            db.session.add(relationship)

            except (json.JSONDecodeError, KeyError) as e:
                # Log error but don't fail the client creation
                print(f"Error processing dependant relationships: {e}")

        db.session.commit()

        flash('Client account created successfully!', 'success')
        return redirect(url_for('manager.clients_list'))

    return render_template('manager/client_form.html', form=form, existing_dependants=[], title="New Client")

# Legacy route for backward compatibility
@manager.route('/parents/new', methods=['GET', 'POST'])
@login_required
def new_parent():
    return redirect(url_for('manager.new_client'))

@manager.route('/clients/<int:id>')
@login_required
def view_client(id):
    """View client details."""
    client = Client.query.get_or_404(id)

    # Get client's recent appointments (last 10)
    recent_appointments = Appointment.query.filter_by(client_id=client.id).order_by(Appointment.start_time.desc()).limit(10).all()

    # Get client's subscriptions
    subscriptions = Subscription.query.filter_by(client_id=client.id).order_by(Subscription.start_date.desc()).all()

    # Get client's invoices
    invoices = Invoice.query.filter_by(client_id=client.id).order_by(Invoice.invoice_date.desc()).limit(5).all()

    # Get dependant relationships
    dependant_relationships = DependantRelationship.query.filter_by(client_id=client.id).all()

    # Get specific client type information
    individual_client = None
    institutional_client = None

    if client.client_type == 'individual':
        individual_client = IndividualClient.query.filter_by(client_id=client.id).first()
    elif client.client_type == 'institutional':
        institutional_client = InstitutionalClient.query.filter_by(client_id=client.id).first()

    return render_template('manager/client_detail.html',
                          client=client,
                          recent_appointments=recent_appointments,
                          subscriptions=subscriptions,
                          invoices=invoices,
                          dependant_relationships=dependant_relationships,
                          individual_client=individual_client,
                          institutional_client=institutional_client)

@manager.route('/clients/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_client(id):
    """Edit client information."""
    client = Client.query.get_or_404(id)
    user = User.query.get(client.user_id) if client.user_id else None

    # Get specific client type information
    individual_client = None
    institutional_client = None

    if client.client_type == 'individual':
        individual_client = IndividualClient.query.filter_by(client_id=client.id).first()
    elif client.client_type == 'institutional':
        institutional_client = InstitutionalClient.query.filter_by(client_id=client.id).first()

    # Get existing dependant relationships
    from app.models.dependant import DependantRelationship, Dependant
    existing_relationships = DependantRelationship.query.filter_by(client_id=client.id).all()

    # Pre-populate form with existing data
    form = ClientForm(obj=client)
    # Handle clients without user accounts
    form.email.data = user.email if user else client.email or ''

    # Populate specific client type fields
    if individual_client:
        form.date_of_birth.data = individual_client.date_of_birth
        # Handle school_grade safely in case the column doesn't exist yet
        form.school_grade.data = getattr(individual_client, 'school_grade', None)
        form.notes.data = individual_client.notes
    elif institutional_client:
        form.organization_name.data = institutional_client.institution_name
        form.contact_person.data = institutional_client.contact_person
        form.notes.data = institutional_client.notes

    if form.validate_on_submit():
        # Handle email validation for clients with and without user accounts
        current_email = user.email if user else client.email
        if current_email != form.email.data and User.query.filter_by(email=form.email.data).first():
            flash('Email already registered', 'danger')
            return render_template('manager/client_form.html', form=form, client=client, title="Edit Client")

        # Update or create user if email and password are provided
        if form.email.data and form.password.data:
            if not user:
                # Create new user account
                user = User(
                    email=form.email.data,
                    password=form.password.data,
                    role='client'
                )
                db.session.add(user)
                db.session.flush()
                client.user_id = user.id
            else:
                # Update existing user
                user.email = form.email.data
                if form.password.data:  # Only update password if provided
                    user.set_password(form.password.data)
        elif user and not form.email.data:
            # Remove user account if email is cleared
            db.session.delete(user)
            client.user_id = None
            user = None

        # Update client
        client.first_name = form.first_name.data
        client.last_name = form.last_name.data
        client.phone = form.phone.data
        client.client_type = form.client_type.data

        # Update structured address fields
        client.civic_number = form.civic_number.data if form.civic_number.data else None
        client.street = form.street.data if form.street.data else None
        client.city = form.city.data if form.city.data else None
        client.postal_code = form.postal_code.data if form.postal_code.data else None
        client.province = form.province.data if form.province.data else 'Quebec'
        client.country = form.country.data if form.country.data else 'Canada'

        # Update client email if no user account exists
        if not user:
            client.email = form.email.data

        # Handle client type changes
        if client.client_type == 'individual':
            # Remove institutional client record if it exists
            if institutional_client:
                db.session.delete(institutional_client)

            # Create or update individual client record
            if not individual_client:
                individual_client = IndividualClient(client_id=client.id)
                db.session.add(individual_client)

            individual_client.date_of_birth = form.date_of_birth.data
            # Handle school_grade safely in case the column doesn't exist yet
            if hasattr(individual_client, 'school_grade'):
                individual_client.school_grade = form.school_grade.data
            individual_client.notes = form.notes.data

        elif client.client_type == 'institutional':
            # Remove individual client record if it exists
            if individual_client:
                db.session.delete(individual_client)

            # Create or update institutional client record
            if not institutional_client:
                institutional_client = InstitutionalClient(client_id=client.id)
                db.session.add(institutional_client)

            institutional_client.institution_name = form.organization_name.data
            institutional_client.contact_person = form.contact_person.data
            institutional_client.notes = form.notes.data

        # Handle dependant relationships updates
        if form.dependant_ids.data and form.dependant_relationships.data:
            try:
                import json
                dependant_ids = json.loads(form.dependant_ids.data)
                dependant_relationships = json.loads(form.dependant_relationships.data)

                # Remove existing relationships
                DependantRelationship.query.filter_by(client_id=client.id).delete()

                # Create new relationships using DependantRelationship
                for relationship_data in dependant_relationships:
                    dependant_id = relationship_data['id']
                    relationship_type = relationship_data['relationship_type']
                    is_primary = relationship_data['is_primary']

                    # Verify the dependant exists in the dependants table
                    if dependant_id in dependant_ids:
                        dependant = Dependant.query.get(dependant_id)
                        if dependant:
                            relationship = DependantRelationship(
                                client_id=client.id,
                                dependant_id=dependant_id,
                                relationship_type=relationship_type,
                                is_primary=is_primary
                            )
                            db.session.add(relationship)

            except (json.JSONDecodeError, KeyError) as e:
                # Log error but don't fail the client update
                print(f"Error processing dependant relationships: {e}")
        else:
            # If no dependant data provided, remove all existing relationships
            DependantRelationship.query.filter_by(client_id=client.id).delete()

        db.session.commit()

        flash('Client information updated successfully!', 'success')
        return redirect(url_for('manager.clients_list'))

    # Prepare existing dependant relationships for the template
    existing_dependants = []
    for relationship in existing_relationships:
        dependant = relationship.dependant
        existing_dependants.append({
            'id': dependant.id,
            'name': f"{dependant.first_name} {dependant.last_name}",
            'email': dependant.email or '',
            'phone': dependant.phone or '',
            'client_type': 'dependant',
            'relationship_type': relationship.relationship_type,
            'is_primary': relationship.is_primary
        })

    return render_template('manager/client_form.html',
                          form=form,
                          client=client,
                          existing_dependants=existing_dependants,
                          title="Edit Client")

# Legacy route for backward compatibility
@manager.route('/parents/<int:id>')
@login_required
def view_parent(id):
    return redirect(url_for('manager.view_client', id=id))

@manager.route('/parents/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_parent(id):
    return redirect(url_for('manager.edit_client', id=id))

# Client relationship management routes
@manager.route('/clients/<int:id>/relationships/add', methods=['GET', 'POST'])
@login_required
def add_client_relationship(id):
    """Add a relationship between clients."""
    from app.forms.client_forms import ManagerClientRelationshipForm
    from app.models.client import Client, ClientRelationship

    client = Client.query.get_or_404(id)
    form = ManagerClientRelationshipForm()

    # Populate the related_client_id choices with all clients except the current one
    # and exclude clients that already have a relationship with this client
    existing_related_ids = [rel.related_client_id for rel in client.outgoing_relationships]
    available_clients = Client.query.filter(
        Client.id != client.id,
        ~Client.id.in_(existing_related_ids)
    ).all()

    form.related_client_id.choices = [(c.id, f"{c.first_name} {c.last_name} ({c.client_type})") for c in available_clients]

    if form.validate_on_submit():
        try:
            # Create the relationship
            relationship = ClientRelationship(
                client_id=client.id,
                related_client_id=form.related_client_id.data,
                relationship_type=form.relationship_type.data,
                is_primary=form.is_primary.data
            )
            db.session.add(relationship)
            db.session.commit()

            flash('Relationship added successfully!', 'success')
            return redirect(url_for('manager.view_client', id=id))

        except Exception as e:
            db.session.rollback()
            flash('Error adding relationship. Please try again.', 'error')

    return render_template('manager/client_relationship_form.html',
                         form=form, client=client, title="Add Relationship")

@manager.route('/clients/<int:id>/relationships/<int:relationship_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_client_relationship(id, relationship_id):
    """Edit a client relationship."""
    from app.forms.client_forms import ManagerClientRelationshipForm
    from app.models.client import Client, ClientRelationship

    client = Client.query.get_or_404(id)
    relationship = ClientRelationship.query.get_or_404(relationship_id)

    # Verify the relationship belongs to this client
    if relationship.client_id != client.id:
        flash('Invalid relationship.', 'error')
        return redirect(url_for('manager.view_client', id=id))

    form = ManagerClientRelationshipForm(obj=relationship)

    # Populate choices - include current related client plus other available ones
    existing_related_ids = [rel.related_client_id for rel in client.outgoing_relationships if rel.id != relationship_id]
    available_clients = Client.query.filter(
        Client.id != client.id,
        ~Client.id.in_(existing_related_ids)
    ).all()

    form.related_client_id.choices = [(c.id, f"{c.first_name} {c.last_name} ({c.client_type})") for c in available_clients]

    if form.validate_on_submit():
        try:
            relationship.related_client_id = form.related_client_id.data
            relationship.relationship_type = form.relationship_type.data
            relationship.is_primary = form.is_primary.data
            relationship.modification_date = datetime.utcnow()

            db.session.commit()
            flash('Relationship updated successfully!', 'success')
            return redirect(url_for('manager.view_client', id=id))

        except Exception as e:
            db.session.rollback()
            flash('Error updating relationship. Please try again.', 'error')

    return render_template('manager/client_relationship_form.html',
                         form=form, client=client, relationship=relationship, title="Edit Relationship")

@manager.route('/clients/<int:id>/relationships/<int:relationship_id>/remove', methods=['GET', 'POST'])
@login_required
def remove_client_relationship(id, relationship_id):
    """Remove a client relationship."""
    from app.models.client import Client, ClientRelationship

    client = Client.query.get_or_404(id)
    relationship = ClientRelationship.query.get_or_404(relationship_id)

    # Verify the relationship belongs to this client
    if relationship.client_id != client.id:
        flash('Invalid relationship.', 'error')
        return redirect(url_for('manager.view_client', id=id))

    if request.method == 'POST':
        try:
            db.session.delete(relationship)
            db.session.commit()
            flash('Relationship removed successfully!', 'success')
        except Exception as e:
            db.session.rollback()
            flash('Error removing relationship. Please try again.', 'error')

        return redirect(url_for('manager.view_client', id=id))

    # GET request - show confirmation page
    return render_template('manager/confirm_remove_relationship.html',
                         client=client, relationship=relationship)

@manager.route('/clients/<int:id>/enroll', methods=['GET', 'POST'])
@login_required
def enroll_client(id):
    """Enroll client in a program (placeholder)."""
    flash('Program enrollment functionality is coming soon!', 'info')
    return redirect(url_for('manager.view_client', id=id))

@manager.route('/enrollments/<int:id>')
@login_required
def view_enrollment(id):
    """View enrollment details (placeholder)."""
    flash('Enrollment details functionality is coming soon!', 'info')
    return redirect(url_for('manager.clients_list'))

@manager.route('/clients/<int:id>/appointments')
@login_required
def client_appointments(id):
    """View all appointments for a client (placeholder)."""
    flash('Client appointments view is coming soon!', 'info')
    return redirect(url_for('manager.view_client', id=id))

@manager.route('/clients/<int:id>/suspend', methods=['POST'])
@login_required
def suspend_client(id):
    """Suspend a client (placeholder)."""
    flash('Client suspension functionality is coming soon!', 'info')
    return redirect(url_for('manager.view_client', id=id))

# Additional placeholder routes for appointment management

@manager.route('/appointments/schedule')
@login_required
def schedule_appointment():
    """Schedule a new appointment (redirect to unified form)."""
    client_id = request.args.get('client_id', type=int)
    if client_id:
        return redirect(url_for('manager.new_appointment', client_id=client_id))
    return redirect(url_for('manager.new_appointment'))

# Tutor management routes
@manager.route('/tutors')
@login_required
def tutors_list():
    form = FilterForm(formdata=request.args)

    query = Tutor.query.join(User)

    # Apply filters if provided
    if form.validate():
        if form.search.data:
            search_term = f"%{form.search.data}%"
            query = query.filter(or_(
                Tutor.first_name.ilike(search_term),
                Tutor.last_name.ilike(search_term),
                User.email.ilike(search_term),
                Tutor.phone.ilike(search_term)
            ))

    # Get active services for filtering
    services = Service.query.filter_by(is_active=True).all()

    # Filter by service if provided
    service_id = request.args.get('service_id', type=int)
    if service_id:
        query = query.join(TutorService).filter(TutorService.service_id == service_id)

    tutors = query.order_by(Tutor.last_name).all()

    return render_template('manager/tutors_list.html',
                          tutors=tutors,
                          form=form,
                          services=services)

@manager.route('/tutors/new', methods=['GET', 'POST'])
@login_required
def new_tutor():
    form = TutorForm()

    if form.validate_on_submit():
        # Check if email already exists
        if User.query.filter_by(email=form.email.data).first():
            flash('Email already registered', 'danger')
            return render_template('manager/tutor_form.html', form=form, title="New Tutor")

        # Create user first
        user = User(
            email=form.email.data,
            password=form.password.data,
            role='tutor'
        )
        db.session.add(user)
        db.session.flush()  # Get the user ID without committing

        # Create tutor profile
        tutor = Tutor(
            user_id=user.user_id,
            first_name=form.first_name.data,
            last_name=form.last_name.data,
            phone=form.phone.data,

            # Address information
            street_address=form.street_address.data,
            city=form.city.data,
            province=form.province.data,
            zip_code=form.zip_code.data,
            country=form.country.data,

            # Personal information
            birthdate=form.birthdate.data,

            # Professional information
            bio=form.bio.data,
            qualifications=form.qualifications.data,
            is_active=form.is_active.data
        )

        # Set encrypted banking information if provided
        if form.bank_transit_number.data:
            tutor.set_bank_transit_number(form.bank_transit_number.data)
        if form.bank_institution_number.data:
            tutor.set_bank_institution_number(form.bank_institution_number.data)
        if form.bank_account_number.data:
            tutor.set_bank_account_number(form.bank_account_number.data)
        db.session.add(tutor)
        db.session.commit()

        flash('Tutor account created successfully!', 'success')
        return redirect(url_for('manager.tutors_list'))

    return render_template('manager/tutor_form.html', form=form, title="New Tutor")

@manager.route('/tutors/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_tutor(id):
    tutor = Tutor.query.get_or_404(id)
    user = User.query.get(tutor.user_id)

    # Pre-populate form with existing data
    form = TutorForm(obj=tutor)
    form.email.data = user.email

    # Populate encrypted banking fields if they exist and can be decrypted
    # Only populate if decryption succeeds (returns non-None value)
    transit = tutor.get_bank_transit_number()
    if transit:
        form.bank_transit_number.data = transit

    institution = tutor.get_bank_institution_number()
    if institution:
        form.bank_institution_number.data = institution

    account = tutor.get_bank_account_number()
    if account:
        form.bank_account_number.data = account

    if form.validate_on_submit():
        # Check if email changed and if it's already taken
        if user.email != form.email.data and User.query.filter_by(email=form.email.data).first():
            flash('Email already registered', 'danger')
            return render_template('manager/tutor_form.html', form=form, tutor=tutor, title="Edit Tutor")

        # Update user
        user.email = form.email.data
        if form.password.data:  # Only update password if provided
            user.set_password(form.password.data)

        # Update tutor
        tutor.first_name = form.first_name.data
        tutor.last_name = form.last_name.data
        tutor.phone = form.phone.data

        # Address information
        tutor.street_address = form.street_address.data
        tutor.city = form.city.data
        tutor.province = form.province.data
        tutor.zip_code = form.zip_code.data
        tutor.country = form.country.data

        # Personal information
        tutor.birthdate = form.birthdate.data

        # Professional information
        tutor.bio = form.bio.data
        tutor.qualifications = form.qualifications.data
        tutor.is_active = form.is_active.data

        # Banking information (encrypted) - always update, even if empty
        tutor.set_bank_transit_number(form.bank_transit_number.data)
        tutor.set_bank_institution_number(form.bank_institution_number.data)
        tutor.set_bank_account_number(form.bank_account_number.data)

        db.session.commit()

        flash('Tutor information updated successfully!', 'success')
        return redirect(url_for('manager.tutors_list'))

    return render_template('manager/tutor_form.html', form=form, tutor=tutor, title="Edit Tutor")

@manager.route('/tutors/<int:id>')
@login_required
def view_tutor(id):
    tutor = Tutor.query.get_or_404(id)

    # Get tutor's services
    tutor_services = TutorService.query.filter_by(tutor_id=tutor.tutor_id).all()

    # Get tutor's availability
    availabilities = TutorAvailability.query.filter_by(tutor_id=tutor.tutor_id).order_by(
        TutorAvailability.day_of_week, TutorAvailability.start_time
    ).all()

    # Get tutor's service rates
    service_rates = TutorServiceRate.query.filter_by(tutor_id=tutor.tutor_id).all()

    # Get upcoming appointments for this tutor
    upcoming_appointments = Appointment.query.filter(
        Appointment.tutor_id == tutor.tutor_id,
        Appointment.start_time >= datetime.now(),
        Appointment.status == 'scheduled'
    ).order_by(Appointment.start_time).all()

    return render_template('manager/tutor_detail.html',
                          tutor=tutor,
                          tutor_services=tutor_services,
                          availabilities=availabilities,
                          service_rates=service_rates,
                          upcoming_appointments=upcoming_appointments)

# Dependant management routes
@manager.route('/dependants')
@login_required
def dependants_list():
    """View all dependants in the system."""
    form = FilterForm(formdata=request.args)

    query = Dependant.query

    # Apply filters if provided
    if form.validate():
        if form.search.data:
            search_term = f"%{form.search.data}%"
            query = query.filter(or_(
                Dependant.first_name.ilike(search_term),
                Dependant.last_name.ilike(search_term),
                Dependant.email.ilike(search_term),
                Dependant.phone.ilike(search_term),
                Dependant.school_grade.ilike(search_term)
            ))

    dependants = query.order_by(Dependant.last_name, Dependant.first_name).all()

    return render_template('manager/dependants_list.html', dependants=dependants, form=form)

@manager.route('/dependants/new', methods=['GET', 'POST'])
@login_required
def new_dependant():
    """Create a new dependant."""
    from app.forms.dependant_forms import DependantForm

    form = DependantForm()

    if form.validate_on_submit():
        try:
            # Create dependant (no user account by default)
            dependant = Dependant(
                first_name=form.first_name.data,
                last_name=form.last_name.data,
                email=form.email.data if form.email.data else None,
                phone=form.phone.data if form.phone.data else None,

                # Structured address fields
                civic_number=form.civic_number.data if form.civic_number.data else None,
                street=form.street.data if form.street.data else None,
                city=form.city.data if form.city.data else None,
                postal_code=form.postal_code.data if form.postal_code.data else None,
                province=form.province.data if form.province.data else 'Quebec',
                country=form.country.data if form.country.data else 'Canada',

                date_of_birth=form.date_of_birth.data if form.date_of_birth.data else None,
                school_grade=form.school_grade.data if form.school_grade.data else None,
                notes=form.notes.data if form.notes.data else None,
                is_active=True,
                preferred_language=form.preferred_language.data if form.preferred_language.data else 'en'
            )

            db.session.add(dependant)
            db.session.commit()

            return redirect(url_for('manager.dependants_list'))

        except Exception as e:
            db.session.rollback()
            flash(f'Error creating dependant: {str(e)}', 'error')

    return render_template('manager/dependant_form.html', form=form, title="New Dependant")

@manager.route('/dependants/<int:id>')
@login_required
def view_dependant(id):
    """View dependant details."""
    dependant = Dependant.query.get_or_404(id)

    # Get dependant's relationships with clients
    relationships = DependantRelationship.query.filter_by(dependant_id=dependant.id).all()

    # Get dependant's recent appointments through their relationships
    client_ids = [rel.client_id for rel in relationships]
    recent_appointments = []
    if client_ids:
        recent_appointments = Appointment.query.filter(
            Appointment.client_id.in_(client_ids)
        ).order_by(Appointment.start_time.desc()).limit(10).all()

    return render_template('manager/dependant_detail.html',
                          dependant=dependant,
                          relationships=relationships,
                          recent_appointments=recent_appointments)

@manager.route('/dependants/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_dependant(id):
    """Edit dependant information."""
    from app.forms.dependant_forms import DependantForm

    dependant = Dependant.query.get_or_404(id)
    form = DependantForm(obj=dependant)

    if form.validate_on_submit():
        try:
            # Update dependant information
            dependant.first_name = form.first_name.data
            dependant.last_name = form.last_name.data
            dependant.email = form.email.data if form.email.data else None
            dependant.phone = form.phone.data if form.phone.data else None

            # Update structured address fields
            dependant.civic_number = form.civic_number.data if form.civic_number.data else None
            dependant.street = form.street.data if form.street.data else None
            dependant.city = form.city.data if form.city.data else None
            dependant.postal_code = form.postal_code.data if form.postal_code.data else None
            dependant.province = form.province.data if form.province.data else 'Quebec'
            dependant.country = form.country.data if form.country.data else 'Canada'

            dependant.date_of_birth = form.date_of_birth.data if form.date_of_birth.data else None
            dependant.school_grade = form.school_grade.data if form.school_grade.data else None
            dependant.notes = form.notes.data if form.notes.data else None
            dependant.preferred_language = form.preferred_language.data if form.preferred_language.data else 'en'
            dependant.modification_date = datetime.utcnow()

            db.session.commit()

            flash('Dependant updated successfully!', 'success')
            return redirect(url_for('manager.view_dependant', id=id))

        except Exception as e:
            db.session.rollback()
            flash(f'Error updating dependant: {str(e)}', 'error')

    return render_template('manager/dependant_form.html', form=form, dependant=dependant, title="Edit Dependant")

@manager.route('/dependants/<int:id>/delete', methods=['POST'])
@login_required
def delete_dependant(id):
    """Delete a dependant."""
    dependant = Dependant.query.get_or_404(id)

    try:
        # Delete all relationships first
        DependantRelationship.query.filter_by(dependant_id=dependant.id).delete()

        # Delete the dependant
        db.session.delete(dependant)
        db.session.commit()

        flash('Dependant deleted successfully!', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'Error deleting dependant: {str(e)}', 'error')

    return redirect(url_for('manager.dependants_list'))

# Service management routes
@manager.route('/services')
@login_required
def services_list():
    form = FilterForm(formdata=request.args)

    query = Service.query

    # Apply filters if provided
    if form.validate():
        if form.search.data:
            search_term = f"%{form.search.data}%"
            query = query.filter(or_(
                Service.name.ilike(search_term),
                Service.description.ilike(search_term)
            ))

    services = query.order_by(Service.name).all()

    return render_template('manager/services_list.html', services=services, form=form)

@manager.route('/services/new', methods=['GET', 'POST'])
@login_required
def new_service():
    form = ServiceForm()

    if form.validate_on_submit():
        service = Service(
            name=form.name.data,
            description=form.description.data,
            default_price=form.default_price.data,
            duration_minutes=form.duration_minutes.data,
            is_active=form.is_active.data
        )
        db.session.add(service)
        db.session.commit()

        flash('Service added successfully!', 'success')
        return redirect(url_for('manager.services_list'))

    return render_template('manager/service_form.html', form=form, title="New Service")

@manager.route('/services/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_service(id):
    service = Service.query.get_or_404(id)

    form = ServiceForm(obj=service)

    if form.validate_on_submit():
        service.name = form.name.data
        service.description = form.description.data
        service.default_price = form.default_price.data
        service.duration_minutes = form.duration_minutes.data
        service.is_active = form.is_active.data

        db.session.commit()

        flash('Service updated successfully!', 'success')
        return redirect(url_for('manager.services_list'))

    return render_template('manager/service_form.html', form=form, service=service, title="Edit Service")

@manager.route('/tutors/by-service/<int:id>')
@login_required
def tutors_by_service(id):
    service = Service.query.get_or_404(id)
    tutors = Tutor.query.join(TutorService).filter(TutorService.service_id == id).all()
    return render_template('manager/tutors_by_service.html', tutors=tutors, service=service)

# Tutor-Service assignments
@manager.route('/tutor-services/new', methods=['GET', 'POST'])
@login_required
def new_tutor_service():
    form = TutorServiceForm()

    # Populate form choices
    form.tutor_id.choices = [(t.tutor_id, f"{t.first_name} {t.last_name}") for t in Tutor.query.filter_by(is_active=True).order_by(Tutor.last_name).all()]
    form.service_id.choices = [(s.service_id, s.name) for s in Service.query.filter_by(is_active=True).order_by(Service.name).all()]

    if form.validate_on_submit():
        # Check if this tutor-service combination already exists
        existing = TutorService.query.filter_by(
            tutor_id=form.tutor_id.data,
            service_id=form.service_id.data
        ).first()

        if existing:
            flash('This tutor is already assigned to this service.', 'danger')
        else:
            tutor_service = TutorService(
                tutor_id=form.tutor_id.data,
                service_id=form.service_id.data,
                custom_rate=form.client_rate.data,  # Use client_rate as the custom_rate
                transport_fee=form.transport_fee.data,
                is_active=form.is_active.data
            )
            db.session.add(tutor_service)
            db.session.commit()

            flash('Tutor service assignment created successfully!', 'success')
            return redirect(url_for('manager.services_list'))

    return render_template('manager/tutor_service_form.html', form=form, title="Assign Service to Tutor")

@manager.route('/tutor-services/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_tutor_service(id):
    tutor_service = TutorService.query.get_or_404(id)

    form = TutorServiceForm(obj=tutor_service)

    # Populate form choices
    form.tutor_id.choices = [(t.tutor_id, f"{t.first_name} {t.last_name}") for t in Tutor.query.filter_by(is_active=True).order_by(Tutor.last_name).all()]
    form.service_id.choices = [(s.service_id, s.name) for s in Service.query.filter_by(is_active=True).order_by(Service.name).all()]

    if form.validate_on_submit():
        tutor_service.custom_rate = form.client_rate.data  # Use client_rate as the custom_rate
        tutor_service.transport_fee = form.transport_fee.data
        tutor_service.is_active = form.is_active.data

        db.session.commit()

        flash('Tutor service assignment updated successfully!', 'success')
        return redirect(url_for('manager.services_list'))

    return render_template('manager/tutor_service_form.html', form=form, tutor_service=tutor_service, title="Edit Tutor Service Assignment")

# Invoice management
@manager.route('/invoices')
@login_required
def invoices_list():
    # Get all clients for filtering
    parents = Client.query.order_by(Client.last_name).all()

    # Get filter parameters
    status = request.args.get('status', 'all')
    parent_id = request.args.get('parent_id', type=int)
    start_date = request.args.get('start_date')

    # Get grouped unpaid invoices if showing pending/overdue
    if status in ['pending', 'overdue', 'all']:
        grouped_invoices = InvoiceService.get_unpaid_invoices_by_client()

        # Flatten the grouped invoices for display
        invoices = []
        for parent_invoices in grouped_invoices.values():
            # Filter by parent if specified
            if parent_id and parent_invoices and parent_invoices[0].parent_id != parent_id:
                continue

            # Filter by overdue status if needed
            if status == 'overdue':
                parent_invoices = [inv for inv in parent_invoices if inv.is_overdue]

            invoices.extend(parent_invoices)

        # Sort by amount descending
        invoices = sorted(invoices, key=lambda x: float(x.total_amount), reverse=True)
    else:
        # For paid or cancelled invoices, use the regular query
        query = Invoice.query

        if status == 'paid':
            query = query.filter_by(status='paid')
        elif status == 'cancelled':
            query = query.filter_by(status='cancelled')

        if parent_id:
            query = query.filter_by(client_id=parent_id)

        invoices = query.order_by(Invoice.invoice_date.desc()).all()

    # Filter by start date if provided
    if start_date:
        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            # Filter the list of invoices
            invoices = [inv for inv in invoices if inv.invoice_date >= start_date_obj]
        except ValueError:
            pass

    return render_template('manager/invoices_list.html',
                          invoices=invoices,
                          parents=parents,
                          request=request)

@manager.route('/invoices/generate', methods=['GET', 'POST'])
@login_required
def generate_invoices():
    if request.method == 'POST':
        client_id = request.form.get('client_id', type=int)

        if not client_id:
            flash('Please select a client.', 'danger')
            return redirect(url_for('manager.generate_invoices'))

        # Ensure the client exists
        client = Client.query.get(client_id)
        if not client:
            flash('Selected client not found.', 'warning')
            return redirect(url_for('manager.generate_invoices'))

        # Get start and end dates if provided
        start_date = None
        end_date = None

        start_date_str = request.form.get('start_date')
        end_date_str = request.form.get('end_date')

        if start_date_str:
            try:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
            except ValueError:
                pass

        if end_date_str:
            try:
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
                # Set end time to end of day
                end_date = end_date.replace(hour=23, minute=59, second=59)
            except ValueError:
                pass

        # Generate invoice for the client
        invoice = InvoiceService.generate_invoice(client_id, start_date=start_date, end_date=end_date)

        if invoice:
            flash(f'Invoice #{invoice.id} generated successfully!', 'success')
            return redirect(url_for('manager.view_invoice', id=invoice.id))
        else:
            flash('No billable appointments found for this client.', 'warning')

    # Get all clients for dropdown, sorted by last name
    clients = Client.query.order_by(Client.last_name).all()

    return render_template('manager/generate_invoice.html', clients=clients)

@manager.route('/invoices/<int:id>')
@login_required
def view_invoice(id):
    invoice = Invoice.query.get_or_404(id)

    # Get invoice items
    invoice_items = InvoiceItem.query.filter_by(invoice_id=invoice.id).all()

    # Get all clients who have access to this invoice
    accessible_clients = invoice.get_accessible_clients()

    # Get the client who paid if applicable
    paying_client = None
    if hasattr(invoice, 'paid_by_client_id') and invoice.paid_by_client_id:
        paying_client = Client.query.get(invoice.paid_by_client_id)

    return render_template('manager/invoice_detail.html',
                          invoice=invoice,
                          invoice_items=invoice_items,
                          accessible_clients=accessible_clients,
                          paying_client=paying_client)

@manager.route('/invoices/<int:id>/update-status', methods=['POST'])
@login_required
def update_invoice_status(id):
    invoice = Invoice.query.get_or_404(id)

    status = request.form.get('status')
    if status in ['pending', 'paid', 'cancelled']:
        invoice.status = status
        db.session.commit()
        flash(f'Invoice status updated to {status}.', 'success')
    else:
        flash('Invalid status.', 'danger')

    return redirect(url_for('manager.view_invoice', id=invoice.id))

@manager.route('/invoices/<int:id>/email', methods=['POST'])
@login_required
def email_invoice(id):
    invoice = Invoice.query.get_or_404(id)

    email = request.form.get('email')
    message = request.form.get('message')

    if not email:
        email = invoice.parent.user.email

    # Send email
    try:
        send_invoice_email(invoice, email, message)
        flash(f'Invoice sent to {email}', 'success')
    except Exception as e:
        flash(f'Error sending email: {str(e)}', 'danger')

    return redirect(url_for('manager.view_invoice', id=invoice.id))

@manager.route('/invoices/<int:id>/delete', methods=['POST'])
@login_required
def delete_invoice(id):
    invoice = Invoice.query.get_or_404(id)

    try:
        # Delete all invoice items first
        InvoiceItem.query.filter_by(invoice_id=invoice.id).delete()

        # Delete invoice
        db.session.delete(invoice)
        db.session.commit()

        flash('Invoice deleted successfully.', 'success')
        return redirect(url_for('manager.invoices_list'))
    except Exception as e:
        db.session.rollback()
        flash(f'Error deleting invoice: {str(e)}', 'danger')
        return redirect(url_for('manager.view_invoice', id=invoice.id))

@manager.route('/subscription-plans')
@login_required
def subscription_plans_list():
    plans = SubscriptionPlan.query.all()
    return render_template('manager/subscription_plans_list.html', plans=plans)

@manager.route('/subscription-plans/new', methods=['GET', 'POST'])
@login_required
def new_subscription_plan():
    form = SubscriptionPlanForm()

    if form.validate_on_submit():
        plan = SubscriptionPlan(
            name=form.name.data,
            description=form.description.data,
            price=form.price.data,
            duration_months=form.duration_months.data,
            max_hours=form.max_hours.data,
            is_active=form.is_active.data
        )
        db.session.add(plan)
        db.session.commit()

        flash('Subscription plan created successfully!', 'success')
        return redirect(url_for('manager.subscription_plans_list'))

    return render_template('manager/subscription_plan_form.html', form=form, title="New Subscription Plan")

@manager.route('/subscriptions')
@login_required
def subscriptions_list():
    subscriptions = Subscription.query.all()
    return render_template('manager/subscriptions_list.html', subscriptions=subscriptions)

@manager.route('/subscriptions/new', methods=['GET', 'POST'])
@login_required
def new_subscription():
    form = SubscriptionForm()

    # Populate form choices
    form.client_id.choices = [(p.id, f"{p.first_name} {p.last_name}") for p in Client.query.order_by(Client.last_name).all()]
    form.plan_id.choices = [(p.id, f"{p.name} (${p.price}, {p.duration_months} months, {p.max_hours} hours)") for p in SubscriptionPlan.query.filter_by(is_active=True).all()]

    if form.validate_on_submit():
        subscription = SubscriptionService.create_subscription(
            client_id=form.client_id.data,
            plan_id=form.plan_id.data,
            start_date=form.start_date.data
        )

        flash('Subscription created successfully!', 'success')
        return redirect(url_for('manager.subscriptions_list'))

    return render_template('manager/subscription_form.html', form=form, title="New Subscription")

# Add to app/views/manager.py



# Add to app/views/manager.py

@manager.route('/tutor-payments')
@login_required
def tutor_payments_list():
    """View all tutor payments."""
    # Get all tutors for filtering
    tutors = Tutor.query.filter_by(is_active=True).all()

    # Get filter parameters
    tutor_id = request.args.get('tutor_id', type=int)
    status = request.args.get('status', 'all')

    # Base query
    query = TutorPayment.query

    # Apply filters
    if tutor_id:
        query = query.filter_by(tutor_id=tutor_id)

    if status == 'pending':
        # Include both 'pending' and 'ready' statuses for pending payments
        query = query.filter(TutorPayment.status.in_(['pending', 'ready']))
    elif status == 'paid':
        query = query.filter_by(status='paid')

    # Order by date
    payments = query.order_by(TutorPayment.insert_date.desc()).all()

    # Check if there are any pending payments that can be processed
    has_pending_payments = any(payment.status in ['pending', 'ready'] for payment in payments)

    return render_template('manager/tutor_payments_list.html',
                          payments=payments,
                          tutors=tutors,
                          current_tutor_id=tutor_id,
                          current_status=status,
                          has_pending_payments=has_pending_payments)

@manager.route('/tutor-payments/process', methods=['POST'])
@login_required
def process_tutor_payments():
    """Process selected tutor payments with Stripe payouts."""
    payment_ids = request.form.getlist('payment_ids', type=int)
    use_stripe = request.form.get('use_stripe_payout', 'true').lower() == 'true'

    if not payment_ids:
        flash('No payments selected', 'warning')
        return redirect(url_for('manager.tutor_payments_list'))

    # Process payments in batch
    results = TutorPaymentService.process_multiple_payments(payment_ids, use_stripe)

    # Display results
    if results['successful']:
        success_msg = f"{results['total_processed']} payments processed successfully (${results['total_amount']:.2f} total)"
        if use_stripe:
            success_msg += " with Stripe payouts"
        flash(success_msg, 'success')

        # Log successful payments for audit
        for payment in results['successful']:
            current_app.logger.info(f"Payment processed: ID {payment['payment_id']}, "
                                  f"Tutor: {payment['tutor_name']}, "
                                  f"Amount: ${payment['amount']:.2f}, "
                                  f"Stripe Payout: {payment.get('stripe_payout_id', 'N/A')}")

    if results['failed']:
        for failed_payment in results['failed']:
            flash(f"Failed to process payment {failed_payment['payment_id']} "
                 f"for {failed_payment['tutor_name']}: {failed_payment['error']}", 'danger')

    return redirect(url_for('manager.tutor_payments_list'))

@manager.route('/clear-encrypted-bank-data', methods=['GET', 'POST'])
@login_required
def clear_encrypted_bank_data():
    """Clear corrupted encrypted bank data."""
    if request.method == 'POST':
        confirm = request.form.get('confirm')
        if confirm == 'YES':
            try:
                # Find tutors with encrypted data
                tutors_with_data = Tutor.query.filter(
                    (Tutor.bank_transit_number_encrypted.isnot(None)) |
                    (Tutor.bank_institution_number_encrypted.isnot(None)) |
                    (Tutor.bank_account_number_encrypted.isnot(None))
                ).all()

                # Clear the encrypted data
                for tutor in tutors_with_data:
                    tutor.bank_transit_number_encrypted = None
                    tutor.bank_institution_number_encrypted = None
                    tutor.bank_account_number_encrypted = None

                db.session.commit()

                flash(f'Successfully cleared encrypted bank data for {len(tutors_with_data)} tutors. '
                      f'They can now re-enter their banking information.', 'success')

            except Exception as e:
                db.session.rollback()
                flash(f'Error clearing encrypted data: {str(e)}', 'danger')
        else:
            flash('Operation cancelled - confirmation not provided.', 'warning')

        return redirect(url_for('manager.tutor_payments_list'))

    # GET request - show confirmation page
    tutors_with_data = Tutor.query.filter(
        (Tutor.bank_transit_number_encrypted.isnot(None)) |
        (Tutor.bank_institution_number_encrypted.isnot(None)) |
        (Tutor.bank_account_number_encrypted.isnot(None))
    ).all()

    return render_template('manager/clear_encrypted_data.html', tutors=tutors_with_data)

# Debug route removed - no longer needed in production

# Add to app/views/manager.py

@manager.route('/reports/tutor-earnings')
@login_required
def tutor_earnings_report():
    """Generate a report of tutor earnings."""
    # Get all tutors for filtering
    tutors = Tutor.query.filter_by(is_active=True).all()

    # Get filter parameters
    tutor_id = request.args.get('tutor_id', type=int)
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    # Parse dates
    start_date_obj = None
    end_date_obj = None

    if start_date:
        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d')
        except ValueError:
            pass

    if end_date:
        try:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d')
            # Set to end of day
            end_date_obj = end_date_obj.replace(hour=23, minute=59, second=59)
        except ValueError:
            pass

    # Get earnings report
    earnings = None
    if tutor_id:
        earnings = TutorPaymentService.calculate_tutor_earnings(
            tutor_id=tutor_id,
            start_date=start_date_obj,
            end_date=end_date_obj
        )

    return render_template('manager/tutor_earnings_report.html',
                          tutors=tutors,
                          current_tutor_id=tutor_id,
                          start_date=start_date,
                          end_date=end_date,
                          earnings=earnings)


@manager.route('/reports/weekly-payment-report')
@login_required
def weekly_payment_report():
    """Generate weekly payment report for validation before processing tutor payments.
    
    Note: Weeks run from Thursday to Wednesday to align with typical payroll cycles.
    """
    # Get current week or selected week
    week_offset = request.args.get('week_offset', 0, type=int)
    
    # Calculate the Thursday of the target week (Thursday-to-Wednesday weeks)
    today = datetime.now().date()
    days_since_thursday = (today.weekday() - 3) % 7  # Thursday = 3, adjust for Thursday start
    current_thursday = today - timedelta(days=days_since_thursday)
    target_thursday = current_thursday - timedelta(weeks=week_offset)
    target_wednesday = target_thursday + timedelta(days=6)
    
    # Convert to datetime for database queries
    week_start = datetime.combine(target_thursday, datetime.min.time())
    week_end = datetime.combine(target_wednesday, datetime.max.time())
    
    # Get all completed appointments for the week with payment info
    query = db.session.query(
        Tutor.tutor_id.label('tutor_id'),
        Tutor.first_name,
        Tutor.last_name,
        func.count(Appointment.appointment_id).label('sessions_count'),
        func.sum(TutorPayment.service_amount).label('total_service_amount'),
        func.sum(TutorPayment.transport_amount).label('total_transport_amount'),
        func.sum(TutorPayment.total_amount).label('total_payment_amount')
    ).select_from(Appointment)\
    .join(Tutor, Appointment.tutor_id == Tutor.tutor_id)\
    .join(TutorPayment, Appointment.appointment_id == TutorPayment.appointment_id)\
    .filter(
        Appointment.status == 'completed',
        Appointment.start_time >= week_start,
        Appointment.start_time <= week_end
    ).group_by(Tutor.tutor_id, Tutor.first_name, Tutor.last_name)\
    .order_by(Tutor.last_name, Tutor.first_name)
    
    tutors_data = query.all()
    
    # Calculate totals
    total_sessions = sum(t.sessions_count for t in tutors_data)
    total_service_amount = sum(float(t.total_service_amount or 0) for t in tutors_data)
    total_transport_amount = sum(float(t.total_transport_amount or 0) for t in tutors_data)
    total_payment_amount = sum(float(t.total_payment_amount or 0) for t in tutors_data)
    
    # Get previous week data for comparison (if requested)
    previous_week_data = None
    show_comparison = request.args.get('compare', 'false').lower() == 'true'
    
    if show_comparison:
        prev_thursday = target_thursday - timedelta(weeks=1)
        prev_wednesday = prev_thursday + timedelta(days=6)
        prev_week_start = datetime.combine(prev_thursday, datetime.min.time())
        prev_week_end = datetime.combine(prev_wednesday, datetime.max.time())
        
        prev_query = db.session.query(
            func.count(Appointment.appointment_id).label('sessions_count'),
            func.sum(TutorPayment.total_amount).label('total_payment_amount')
        ).select_from(Appointment)\
        .join(TutorPayment, Appointment.appointment_id == TutorPayment.appointment_id)\
        .filter(
            Appointment.status == 'completed',
            Appointment.start_time >= prev_week_start,
            Appointment.start_time <= prev_week_end
        )
        
        previous_week_data = prev_query.first()
    
    return render_template('manager/weekly_payment_report.html',
                          tutors_data=tutors_data,
                          week_start=target_thursday,
                          week_end=target_wednesday,
                          week_offset=week_offset,
                          total_sessions=total_sessions,
                          total_service_amount=total_service_amount,
                          total_transport_amount=total_transport_amount,
                          total_payment_amount=total_payment_amount,
                          previous_week_data=previous_week_data,
                          show_comparison=show_comparison)


@manager.route('/subscriptions/<int:id>/cancel', methods=['POST'])
@login_required
def cancel_subscription(id):
    """Cancel a subscription."""
    subscription = Subscription.query.get_or_404(id)

    # Check if subscription is active
    if subscription.status != 'active':
        flash('This subscription is already inactive.', 'warning')
        return redirect(url_for('manager.view_subscription', id=id))

    # Cancel the subscription
    subscription = SubscriptionService.cancel_subscription(id)

    if subscription:
        flash('Subscription cancelled successfully.', 'success')
    else:
        flash('Error cancelling subscription.', 'danger')

    return redirect(url_for('manager.subscriptions_list'))

@manager.route('/subscriptions/<int:id>')
@login_required
def view_subscription(id):
    """View subscription details."""
    subscription = Subscription.query.get_or_404(id)

    # Get usage history
    usage_history = SubscriptionUsage.query.filter_by(subscription_id=id).order_by(SubscriptionUsage.insert_date.desc()).all()

    # Get upcoming appointments with this subscription
    upcoming_appointments = Appointment.query.filter(
        Appointment.subscription_id == id,
        Appointment.start_time >= datetime.now(),
        Appointment.status == 'scheduled'
    ).order_by(Appointment.start_time).all()

    return render_template('manager/subscription_detail.html',
                          subscription=subscription,
                          usage_history=usage_history,
                          upcoming_appointments=upcoming_appointments,
                          now=datetime.now())

@manager.route('/subscriptions/<int:id>/update', methods=['POST'])
@login_required
def update_subscription(id):
    """Update a subscription's status."""
    subscription = Subscription.query.get_or_404(id)

    status = request.form.get('status')
    if status in ['active', 'cancelled']:
        subscription.status = status
        db.session.commit()
        flash(f'Subscription status updated to {status}.', 'success')
    else:
        flash('Invalid status.', 'danger')

    return redirect(url_for('manager.view_subscription', id=id))

@manager.route('/subscriptions/<int:id>/update-usage', methods=['POST'])
@login_required
def update_subscription_usage(id):
    """Update usage for a specific subscription."""
    subscription = Subscription.query.get_or_404(id)

    # Find all completed appointments for this subscription
    completed_appointments = Appointment.query.filter(
        Appointment.status == 'completed',
        Appointment.is_subscription_based == True,
        Appointment.subscription_id == id
    ).all()

    # Find appointments that don't have usage records
    appointments_without_usage = []
    for appointment in completed_appointments:
        # Check if there's already a usage record for this appointment
        usage = SubscriptionUsage.query.filter_by(appointment_id=appointment.id).first()
        if not usage:
            appointments_without_usage.append(appointment)

    # Record usage for each appointment
    count = 0
    for appointment in appointments_without_usage:
        if SubscriptionService.record_usage(appointment.id):
            count += 1

    if count > 0:
        flash(f'Updated subscription usage for {count} completed appointments.', 'success')
    else:
        flash('No missing usage records found for this subscription.', 'info')

    return redirect(url_for('manager.view_subscription', id=id))

@manager.route('/subscription-plans/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_subscription_plan(id):
    """Edit an existing subscription plan."""
    plan = SubscriptionPlan.query.get_or_404(id)

    form = SubscriptionPlanForm(obj=plan)

    if form.validate_on_submit():
        plan.name = form.name.data
        plan.description = form.description.data
        plan.price = form.price.data
        plan.duration_months = form.duration_months.data
        plan.max_hours = form.max_hours.data
        plan.is_active = form.is_active.data

        db.session.commit()

        flash('Subscription plan updated successfully!', 'success')
        return redirect(url_for('manager.subscription_plans_list'))

    return render_template('manager/subscription_plan_form.html', form=form, plan=plan, title="Edit Subscription Plan")

@manager.route('/recurring-appointments')
@login_required
def recurring_appointments_list():
    """View all recurring appointment patterns - now using unified system."""
    # Get all recurring templates from the unified appointments table
    recurring_templates = Appointment.query.filter_by(is_recurring=True).all()

    return render_template('manager/recurring_appointments_list.html',
                          recurring_appointments=recurring_templates)

@manager.route('/recurring-appointments/new', methods=['GET', 'POST'])
@login_required
def new_recurring_appointment():
    """Redirect to unified appointment form with recurring flag."""
    return redirect(url_for('manager.new_appointment', recurring=True))

@manager.route('/recurring-appointments/<int:id>')
@login_required
def view_recurring_appointment(id):
    """View a recurring appointment series - now using unified system."""
    # Get the recurring template from the unified appointments table
    recurring_template = Appointment.query.filter_by(id=id, is_recurring=True).first_or_404()

    # Get all appointments generated from this template
    generated_appointments = Appointment.query.filter_by(recurring_template_id=id).all()

    # Since we removed the recurring_appointment_detail.html template,
    # redirect to the unified appointment list or schedule view
    flash('Recurring appointment details view has been integrated into the unified system.', 'info')
    return redirect(url_for('manager.schedule'))

@manager.route('/recurring-appointments/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_recurring_appointment(id):
    """Redirect to unified appointment edit form."""
    # Since we've unified the system, redirect to the regular appointment edit
    # This assumes the recurring appointment has been converted to the unified system
    flash('Recurring appointment editing has been integrated into the unified appointment system.', 'info')
    return redirect(url_for('manager.edit_appointment', id=id))

@manager.route('/recurring-appointments/<int:id>/cancel', methods=['POST'])
@login_required
def cancel_recurring_appointment(id):
    """Cancel a recurring appointment series - now using unified system."""
    # Get cancel option
    cancel_future_only = request.form.get('cancel_future_only', 'true') == 'true'

    # Get the recurring template
    recurring_template = Appointment.query.filter_by(id=id, is_recurring=True).first_or_404()

    # Cancel all future appointments generated from this template
    today = datetime.now()
    query = Appointment.query.filter_by(recurring_template_id=id)

    if cancel_future_only:
        # Only cancel future appointments
        query = query.filter(Appointment.start_time >= today)

    # Update all matching appointments
    query.update({"status": "cancelled"})

    # Mark the template as inactive
    recurring_template.status = 'cancelled'

    db.session.commit()
    flash('Recurring appointment series cancelled successfully!', 'success')
    return redirect(url_for('manager.recurring_appointments_list'))

@manager.route('/search')
@login_required
def global_search():
    """Global search across multiple entities."""
    query = request.args.get('q', '')

    if not query or len(query) < 2:
        return render_template('manager/search_results.html',
                              query=query,
                              results={},
                              total_count=0)

    # Search term with wildcards for SQL LIKE
    search_term = f"%{query}%"

    # Search clients - specify join condition explicitly
    clients = Client.query.join(User, Client.user_id == User.id).filter(or_(
        Client.first_name.ilike(search_term),
        Client.last_name.ilike(search_term),
        User.email.ilike(search_term),
        Client.phone.ilike(search_term)
    )).limit(10).all()

    # Students are now handled as clients

    # Search tutors - specify join condition explicitly
    tutors = Tutor.query.join(User, Tutor.user_id == User.id).filter(or_(
        Tutor.first_name.ilike(search_term),
        Tutor.last_name.ilike(search_term),
        User.email.ilike(search_term),
        Tutor.phone.ilike(search_term)
    )).limit(10).all()

    # Search services
    services = Service.query.filter(or_(
        Service.name.ilike(search_term),
        Service.description.ilike(search_term)
    )).limit(10).all()

    # Search appointments - specify join conditions explicitly
    appointments = Appointment.query\
        .join(Client, Appointment.client_id == Client.id)\
        .join(Tutor, Appointment.tutor_id == Tutor.id)\
        .join(TutorService, Appointment.tutor_service_id == TutorService.id)\
        .join(Service, TutorService.service_id == Service.id)\
        .filter(or_(
            Client.first_name.ilike(search_term),
            Client.last_name.ilike(search_term),
            Tutor.first_name.ilike(search_term),
            Tutor.last_name.ilike(search_term),
            Service.name.ilike(search_term),
            Appointment.notes.ilike(search_term)
        )).order_by(Appointment.start_time.desc()).limit(10).all()

    # Search invoices - specify the join condition explicitly to avoid ambiguity
    invoices = Invoice.query.join(Client, Invoice.client_id == Client.id).filter(or_(
        func.cast(Invoice.id, db.String).ilike(search_term),  # Search by ID instead of invoice_number
        Client.first_name.ilike(search_term),
        Client.last_name.ilike(search_term),
        func.cast(Invoice.total_amount, db.String).ilike(search_term)
    )).order_by(Invoice.invoice_date.desc()).limit(10).all()

    # Compile results
    results = {
        'clients': clients,
        'students': [],  # Legacy compatibility - students are now clients
        'tutors': tutors,
        'services': services,
        'appointments': appointments,
        'invoices': invoices
    }

    # Calculate total count
    total_count = sum(len(items) for items in results.values())

    return render_template('manager/search_results.html',
                          query=query,
                          results=results,
                          total_count=total_count)


# TECFÉE Program Management Routes
@manager.route('/tecfee')
@login_required
def tecfee_dashboard():
    """TECFÉE program management dashboard."""
    from app.services.group_session_service import GroupSessionService
    from app.models.program import Program, Enrollment, GroupSession

    # Get TECFÉE program
    tecfee_program = GroupSessionService.get_tecfee_program()
    if not tecfee_program:
        flash('TECFÉE program not found. Please run the database migration first.', 'warning')
        return redirect(url_for('manager.dashboard'))

    # Get program statistics
    total_enrollments = Enrollment.query.filter_by(program_id=tecfee_program.id).count()
    active_enrollments = Enrollment.query.filter_by(program_id=tecfee_program.id, status='active').count()

    # Get upcoming group sessions
    upcoming_sessions = GroupSession.query.filter(
        GroupSession.program_id == tecfee_program.id,
        GroupSession.session_date >= datetime.now(),
        GroupSession.status.in_(['scheduled', 'confirmed'])
    ).order_by(GroupSession.session_date).limit(10).all()

    # Get recent enrollments
    recent_enrollments = Enrollment.query.filter_by(
        program_id=tecfee_program.id
    ).order_by(Enrollment.enrollment_date.desc()).limit(10).all()

    return render_template('manager/tecfee_dashboard.html',
                          program=tecfee_program,
                          total_enrollments=total_enrollments,
                          active_enrollments=active_enrollments,
                          upcoming_sessions=upcoming_sessions,
                          recent_enrollments=recent_enrollments)


@manager.route('/tecfee/enrollments')
@login_required
def tecfee_enrollments():
    """View all TECFÉE enrollments."""
    from app.services.group_session_service import GroupSessionService
    from app.models.program import Enrollment

    # Get TECFÉE program
    tecfee_program = GroupSessionService.get_tecfee_program()
    if not tecfee_program:
        flash('TECFÉE program not found.', 'error')
        return redirect(url_for('manager.dashboard'))

    # Get filter parameters
    status = request.args.get('status', 'all')

    # Base query with proper client relationship loading
    from sqlalchemy.orm import joinedload
    query = Enrollment.query.options(
        joinedload(Enrollment.client)
    ).filter_by(program_id=tecfee_program.id)

    # Apply filters
    if status != 'all':
        query = query.filter_by(status=status)

    enrollments = query.order_by(Enrollment.enrollment_date.desc()).all()

    return render_template('manager/tecfee_enrollments.html',
                          program=tecfee_program,
                          enrollments=enrollments,
                          current_status=status)


@manager.route('/tecfee/enroll', methods=['GET', 'POST'])
@login_required
def tecfee_enroll_client():
    """Enroll a client in TECFÉE program."""
    from app.forms.manager_forms import TecfeeEnrollmentForm
    from app.services.group_session_service import GroupSessionService

    form = TecfeeEnrollmentForm()

    # Populate client choices
    form.client_id.choices = [(c.id, f"{c.first_name} {c.last_name}") for c in Client.query.order_by(Client.last_name).all()]

    if form.validate_on_submit():
        enrollment, message = GroupSessionService.enroll_client_in_tecfee(
            client_id=form.client_id.data,
            pricing_type=form.pricing_type.data,
            start_date=form.start_date.data
        )

        if enrollment:
            flash(f'Client enrolled successfully! {message}', 'success')
            return redirect(url_for('manager.tecfee_enrollments'))
        else:
            flash(f'Enrollment failed: {message}', 'error')

    return render_template('manager/tecfee_enrollment_form.html', form=form)


@manager.route('/tecfee/group-sessions')
@login_required
def tecfee_group_sessions():
    """View all TECFÉE group sessions."""
    from app.services.group_session_service import GroupSessionService
    from app.models.program import GroupSession

    # Get TECFÉE program
    tecfee_program = GroupSessionService.get_tecfee_program()
    if not tecfee_program:
        flash('TECFÉE program not found.', 'error')
        return redirect(url_for('manager.dashboard'))

    # Get filter parameters
    status = request.args.get('status', 'all')
    tutor_id = request.args.get('tutor_id', '')

    # Base query
    query = GroupSession.query.filter_by(program_id=tecfee_program.id)

    # Apply filters
    if status != 'all':
        query = query.filter_by(status=status)
    if tutor_id:
        query = query.filter_by(tutor_id=int(tutor_id))

    sessions = query.order_by(GroupSession.session_date.desc()).all()

    # Get tutors for filter dropdown
    tutors = Tutor.query.filter_by(is_active=True).order_by(Tutor.last_name).all()

    return render_template('manager/tecfee_group_sessions.html',
                          program=tecfee_program,
                          sessions=sessions,
                          tutors=tutors,
                          current_status=status,
                          current_tutor_id=tutor_id)


@manager.route('/tecfee/group-sessions/new', methods=['GET', 'POST'])
@login_required
def create_tecfee_group_session():
    """Create a new TECFÉE group session."""
    from app.forms.manager_forms import GroupSessionForm
    from app.services.group_session_service import GroupSessionService
    from app.models.program import Program, ProgramModule
    from datetime import datetime, timedelta

    form = GroupSessionForm()

    # Get TECFÉE program
    tecfee_program = GroupSessionService.get_tecfee_program()
    if not tecfee_program:
        flash('TECFÉE program not found.', 'error')
        return redirect(url_for('manager.dashboard'))

    # Populate form choices
    form.program_id.choices = [(tecfee_program.id, tecfee_program.name)]
    form.program_id.data = tecfee_program.id

    form.module_id.choices = [(m.id, f"Module {m.module_order}: {m.name}") for m in tecfee_program.modules.order_by(ProgramModule.module_order)]
    form.tutor_id.choices = [(t.id, f"{t.first_name} {t.last_name}") for t in Tutor.query.filter_by(is_active=True).order_by(Tutor.last_name).all()]

    if form.validate_on_submit():
        # Calculate end time based on start time and duration
        # Convert string time to time object
        start_time_str = form.session_time.data
        start_time = datetime.strptime(start_time_str, '%H:%M').time()
        duration_minutes = form.duration_minutes.data
        end_time = (datetime.combine(datetime.today(), start_time) + timedelta(minutes=duration_minutes)).time()

        group_session = GroupSessionService.create_group_session(
            program_id=form.program_id.data,
            module_id=form.module_id.data,
            tutor_id=form.tutor_id.data,
            session_date=form.session_date.data,
            start_time=start_time,
            end_time=end_time,
            max_participants=form.max_participants.data,
            tutor_payment_rate=form.tutor_rate_per_student.data,
            session_notes=form.notes.data
        )

        flash('Group session created successfully!', 'success')
        return redirect(url_for('manager.tecfee_group_sessions'))

    return render_template('manager/tecfee_group_session_form.html', form=form, program=tecfee_program)


@manager.route('/tecfee/group-sessions/<int:id>')
@login_required
def view_tecfee_group_session(id):
    """View TECFÉE group session details."""
    from app.models.program import GroupSession, GroupSessionParticipant, Enrollment

    group_session = GroupSession.query.get_or_404(id)

    # Get participants
    participants = db.session.query(GroupSessionParticipant, Enrollment, Client).\
        join(Enrollment, GroupSessionParticipant.enrollment_id == Enrollment.id).\
        join(Client, Enrollment.client_id == Client.id).\
        filter(GroupSessionParticipant.group_session_id == id).all()

    return render_template('manager/tecfee_group_session_detail.html',
                          group_session=group_session,
                          participants=participants)


@manager.route('/tecfee/group-sessions/<int:id>/add-participant', methods=['POST'])
@login_required
def add_participant_to_session(id):
    """Add a participant to a group session."""
    from app.services.group_session_service import GroupSessionService
    from app.models.program import Enrollment

    enrollment_id = request.form.get('enrollment_id')
    if not enrollment_id:
        flash('Please select an enrollment.', 'error')
        return redirect(url_for('manager.view_tecfee_group_session', id=id))

    success, message = GroupSessionService.add_participant_to_session(id, int(enrollment_id))

    if success:
        flash(message, 'success')
    else:
        flash(message, 'error')

    return redirect(url_for('manager.view_tecfee_group_session', id=id))


@manager.route('/tecfee/group-sessions/<int:id>/complete', methods=['POST'])
@login_required
def complete_tecfee_group_session(id):
    """Mark a group session as completed and create tutor payments."""
    from app.services.group_session_service import GroupSessionService

    success, message = GroupSessionService.complete_session_and_create_payments(id)

    if success:
        flash(message, 'success')
    else:
        flash(message, 'error')

    return redirect(url_for('manager.view_tecfee_group_session', id=id))


@manager.route('/tecfee/group-sessions/<int:id>/cancel', methods=['POST'])
@login_required
def cancel_tecfee_group_session(id):
    """Cancel a group session."""
    try:
        from app.services.group_session_service import GroupSessionService

        # Get cancellation reason from request
        reason = request.form.get('reason')
        if not reason and request.is_json:
            try:
                reason = request.json.get('reason')
            except:
                reason = None

        success, message = GroupSessionService.cancel_session(id, reason)

        # Always return JSON for AJAX requests
        return jsonify({
            'success': success,
            'message': message
        })
    except Exception as e:
        # Return error as JSON
        return jsonify({
            'success': False,
            'message': f'Server error: {str(e)}'
        }), 500


@manager.route('/tecfee/group-sessions/<int:id>/confirm', methods=['POST'])
@login_required
def confirm_tecfee_group_session(id):
    """Confirm a group session."""
    from app.services.group_session_service import GroupSessionService

    success, message = GroupSessionService.confirm_session_if_minimum_met(id)

    # Always return JSON for AJAX requests
    return jsonify({
        'success': success,
        'message': message
    })

@manager.route('/tecfee/incomplete-enrollments')
@login_required
def tecfee_incomplete_enrollments():
    """View and manage incomplete TECFÉE enrollments."""
    from app.models.program import Program, Enrollment
    
    # Get TECFÉE program
    tecfee_program = Program.query.filter_by(code='TECFEE').first()
    if not tecfee_program:
        flash('Programme TECFÉE non trouvé.', 'error')
        return redirect(url_for('manager.dashboard'))
    
    # Get incomplete enrollments with client and user info
    incomplete = db.session.query(Enrollment, Client, User).join(
        Client, Enrollment.client_id == Client.id
    ).join(
        User, Client.user_id == User.id
    ).filter(
        Enrollment.status == 'pending',
        Enrollment.program_id == tecfee_program.id
    ).order_by(Enrollment.created_at.desc()).all()
    
    # Calculate time since creation and check expiration
    now = datetime.utcnow()
    enrollments_data = []
    
    for enrollment, client, user in incomplete:
        time_elapsed = now - enrollment.created_at
        hours_elapsed = time_elapsed.total_seconds() / 3600
        
        # Check if expired (more than 24 hours)
        is_expired = hours_elapsed > 24
        
        # Check if recovery email was sent
        has_recovery_token = enrollment.recovery_token is not None
        
        enrollments_data.append({
            'enrollment': enrollment,
            'client': client,
            'user': user,
            'hours_elapsed': round(hours_elapsed, 1),
            'is_expired': is_expired,
            'has_recovery_token': has_recovery_token,
            'time_remaining': max(0, 24 - hours_elapsed)
        })
    
    return render_template('manager/tecfee_incomplete_enrollments.html',
                         enrollments_data=enrollments_data,
                         now=now)

@manager.route('/tecfee/incomplete-enrollments/<int:id>/send-reminder', methods=['POST'])
@login_required
def send_enrollment_reminder(id):
    """Send reminder email for incomplete enrollment."""
    from app.utils.email import send_email
    
    enrollment = Enrollment.query.get_or_404(id)
    
    if enrollment.status != 'pending':
        flash('Cette inscription n\'est plus en attente.', 'warning')
        return redirect(url_for('manager.tecfee_incomplete_enrollments'))
    
    # Get client and user
    client = Client.query.get(enrollment.client_id)
    user = User.query.get(client.user_id)
    
    # Generate recovery token if not exists
    if not enrollment.recovery_token:
        enrollment.generate_recovery_token()
        db.session.commit()
    
    # Send reminder email
    recovery_url = url_for('public.continue_tecfee_enrollment', 
                         token=enrollment.recovery_token,
                         _external=True)
    
    subject = "Rappel: Complétez votre inscription TECFÉE"
    html_body = f"""
    <h2>Rappel d'inscription TECFÉE</h2>
    <p>Bonjour {client.first_name},</p>
    <p>Nous vous rappelons que votre inscription au programme TECFÉE n'est pas complétée.</p>
    <p><a href="{recovery_url}" style="background: #e57373; color: white; padding: 12px 30px; 
                            text-decoration: none; border-radius: 8px; display: inline-block;">
        Continuer mon inscription
    </a></p>
    <p>Cordialement,<br>L'équipe TutorAide</p>
    """
    
    send_email(subject=subject, recipients=[user.email], text_body=html_body, html_body=html_body)
    
    flash(f'Email de rappel envoyé à {user.email}', 'success')
    return redirect(url_for('manager.tecfee_incomplete_enrollments'))

@manager.route('/tecfee/incomplete-enrollments/<int:id>/cancel', methods=['POST'])
@login_required
def cancel_incomplete_enrollment(id):
    """Cancel an incomplete enrollment."""
    enrollment = Enrollment.query.get_or_404(id)
    
    if enrollment.status != 'pending':
        flash('Cette inscription n\'est plus en attente.', 'warning')
        return redirect(url_for('manager.tecfee_incomplete_enrollments'))
    
    # Remove any group session participants
    GroupSessionParticipant.query.filter_by(
        enrollment_id=enrollment.id,
        is_paid=False
    ).delete()
    
    # Delete the enrollment
    db.session.delete(enrollment)
    db.session.commit()
    
    flash('Inscription incomplète annulée avec succès.', 'success')
    return redirect(url_for('manager.tecfee_incomplete_enrollments'))


@manager.route('/settings', methods=['GET', 'POST'])
@login_required
def settings():
    """Manager profile and settings page."""
    form = ManagerProfileForm()
    
    if form.validate_on_submit():
        # Check current password if user is trying to change password
        if form.password.data:
            if not form.current_password.data:
                flash('Current password is required to change password.', 'danger')
                return render_template('manager/settings.html', form=form)
            
            if not check_password_hash(current_user.password_hash, form.current_password.data):
                flash('Current password is incorrect.', 'danger')
                return render_template('manager/settings.html', form=form)
            
            # Update password
            current_user.set_password(form.password.data)
        
        # Update profile information
        # Get or create the manager profile associated with this user
        manager = Manager.query.filter_by(user_id=current_user.user_id).first()
        if manager and form.phone.data:
            manager.phone = form.phone.data
        # Note: Manager model doesn't have address fields in the current schema
        # Address fields are ignored for now
        
        try:
            db.session.commit()
            flash('Profile updated successfully.', 'success')
            return redirect(url_for('manager.settings'))
        except Exception as e:
            db.session.rollback()
            flash('An error occurred while updating your profile.', 'danger')
    
    # Pre-populate form with current user data
    if request.method == 'GET':
        # Get the manager profile associated with this user
        manager = Manager.query.filter_by(user_id=current_user.user_id).first()
        if manager:
            form.phone.data = manager.phone
            # Note: Manager model doesn't have address fields in the schema
            # These fields will remain empty for now
            form.street_address.data = None
            form.city.data = None
            form.province.data = None
            form.zip_code.data = None
            form.country.data = None
    
    return render_template('manager/settings.html', form=form)

# Audit routes removed - audit information now integrated into appointment detail modal





