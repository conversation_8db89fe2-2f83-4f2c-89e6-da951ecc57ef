#!/usr/bin/env python3

from app import create_app
from app.extensions import db
from sqlalchemy import text

app = create_app()
with app.app_context():
    print('Checking for existing recurring appointments...')
    
    # Check for appointments with legacy recurring data
    query = "SELECT appointment_id, is_recurring, recurrence_pattern, recurrence_end_date FROM appointments WHERE is_recurring = true OR recurrence_pattern IS NOT NULL"
    result = db.session.execute(text(query))
    recurring_appointments = result.fetchall()
    
    print(f'Found {len(recurring_appointments)} appointments with recurring data')
    
    for apt in recurring_appointments:
        print(f'Appointment {apt[0]}: is_recurring={apt[1]}, pattern={apt[2]}, end_date={apt[3]}')
    
    # Check if appointment_recurring_schedules table exists
    print('\nChecking if appointment_recurring_schedules table exists...')
    query2 = "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'appointment_recurring_schedules')"
    result2 = db.session.execute(text(query2))
    table_exists = result2.scalar()
    print(f'appointment_recurring_schedules table exists: {table_exists}')