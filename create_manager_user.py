import sys
import os
import secrets
import string
from datetime import datetime

# Add the current directory to the path so we can import the app
sys.path.append(os.path.abspath(os.path.dirname(__file__)))

# Import the app and models
from app import create_app
from app.extensions import db
from app.models.user import User
from app.models.manager import Manager

def create_manager_user(email, first_name, last_name, phone=None):
    """Create a manager user with the specified email"""
    
    # Generate a secure random password
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    password = ''.join(secrets.choice(alphabet) for i in range(16))
    
    # Create the app context
    app = create_app()
    
    with app.app_context():
        # Check if user already exists
        existing_user = User.query.filter_by(email=email).first()
        if existing_user:
            print(f"User with email {email} already exists.")
            return None
        
        # Create the user
        user = User(email=email, password=password, role='manager')
        user.email_verified = True  # Auto-verify email for manager
        db.session.add(user)
        db.session.flush()  # Flush to get the user_id
        
        # Create the manager profile
        manager = Manager(
            user_id=user.user_id,
            first_name=first_name,
            last_name=last_name,
            phone=phone,
            insert_date=datetime.utcnow(),
            modification_date=datetime.utcnow()
        )
        db.session.add(manager)
        
        # Commit the changes
        db.session.commit()
        
        print(f"Manager user created successfully!")
        print(f"Email: {email}")
        print(f"Password: {password}")
        print(f"Role: manager")
        
        return password

if __name__ == "__main__":
    email = "<EMAIL>"
    first_name = "Danny"
    last_name = "Quan"
    phone = "************"  # Optional
    
    create_manager_user(email, first_name, last_name, phone)