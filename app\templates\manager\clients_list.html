<!-- app/templates/manager/clients_list.html -->
{% extends "base.html" %}

{% block title %}{{ t('manager.clients.title_full') }}{% endblock %}

{% block content %}
<div class="page-header fade-in-up">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>{{ t('manager.clients.title') }}</h1>
            <p>{{ t('manager.clients.subtitle', 'Manage client accounts and information') }}</p>
        </div>
        <div>
            <a href="{{ url_for('manager.new_client') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> {{ t('manager.clients.add_new_client') }}
            </a>
        </div>
    </div>
</div>

<!-- Filter Controls -->
<div class="search-box fade-in-up">
        <form method="GET" action="{{ url_for('manager.clients_list') }}" class="row g-3">
            <div class="col-md-3">
                <label for="client_type" class="form-label">{{ t('manager.clients.filters.client_type') }}</label>
                <select name="client_type" id="client_type" class="form-select">
                    <option value="">{{ t('manager.clients.filters.all_types') }}</option>
                    <option value="individual" {% if request.args.get('client_type') == 'individual' %}selected{% endif %}>{{ t('manager.clients.filters.individual') }}</option>
                    <option value="institutional" {% if request.args.get('client_type') == 'institutional' %}selected{% endif %}>{{ t('manager.clients.filters.institutional') }}</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">{{ t('manager.clients.filters.status') }}</label>
                <select name="status" id="status" class="form-select">
                    <option value="">{{ t('manager.clients.filters.all_statuses') }}</option>
                    <option value="active" {% if request.args.get('status') == 'active' %}selected{% endif %}>{{ t('manager.clients.filters.active') }}</option>
                    <option value="suspended" {% if request.args.get('status') == 'suspended' %}selected{% endif %}>{{ t('manager.clients.filters.suspended') }}</option>
                </select>
            </div>
            <div class="col-md-4">
                <label for="search" class="form-label">{{ t('manager.clients.filters.search') }}</label>
                <input type="text" name="search" id="search" class="form-control" placeholder="{{ t('manager.clients.filters.search_placeholder') }}" value="{{ request.args.get('search', '') }}">
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-search"></i> {{ t('manager.clients.filters.filter') }}
                </button>
            </div>
        </form>
</div>

<!-- Clients Table -->
<div class="card fade-in-up">
    <div class="card-body">
        {% if clients %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>{{ t('manager.clients.table.name') }}</th>
                            <th>{{ t('manager.clients.table.type') }}</th>
                            <th>{{ t('manager.clients.table.email') }}</th>
                            <th>{{ t('manager.clients.table.phone') }}</th>
                            <th>{{ t('manager.clients.table.dependants') }}</th>
                            <th>{{ t('manager.clients.table.status') }}</th>
                            <th>{{ t('manager.clients.table.actions') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for client in clients %}
                            <tr>
                                <td>{{ client.first_name }} {{ client.last_name }}</td>
                                <td>
                                    <span class="badge bg-primary">
                                        {{ t('manager.clients.filters.' + client.client_type) }}
                                    </span>
                                </td>
                                <td>
                                    {% if client.user %}
                                        {{ client.user.email }}
                                    {% else %}
                                        {{ client.email }}
                                    {% endif %}
                                </td>
                                <td>{{ client.phone }}</td>
                                <td>
                                    {% set dependants_count = client.get_dependants_count() %}
                                    {% if dependants_count > 0 %}
                                        <span class="badge bg-primary rounded-pill">
                                            {{ dependants_count }}
                                        </span>
                                    {% else %}
                                        <span class="badge bg-light text-dark">0</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if client.is_suspended %}
                                        <span class="badge bg-danger">{{ t('manager.clients.filters.suspended') }}</span>
                                    {% else %}
                                        <span class="badge bg-success">{{ t('manager.clients.filters.active') }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="{{ url_for('manager.edit_client', id=client.id) }}" class="btn btn-sm btn-primary" title="Edit client">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{{ url_for('manager.view_client', id=client.id) }}" class="btn btn-sm btn-outline-primary" title="View client details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <div class="dropdown d-inline">
                                            <button class="btn btn-sm btn-secondary dropdown-toggle" type="button" id="dropdownMenuButton{{ client.id }}" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="fas fa-ellipsis-h"></i>
                                            </button>
                                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton{{ client.id }}">
                                            <li>
                                                <span class="dropdown-item-text text-muted">
                                                    <i class="fas fa-calendar"></i> {{ t('manager.clients.actions.appointments') }}
                                                </span>
                                            </li>
                                            <li>
                                                <span class="dropdown-item-text text-muted">
                                                    <i class="fas fa-file-invoice-dollar"></i> {{ t('manager.clients.actions.invoices') }}
                                                </span>
                                            </li>
                                            <li>
                                                <span class="dropdown-item-text text-muted">
                                                    <i class="fas fa-ticket-alt"></i> {{ t('manager.clients.actions.subscriptions') }}
                                                </span>
                                            </li>
                                            <li>
                                                <span class="dropdown-item-text text-muted">
                                                    <i class="fas fa-graduation-cap"></i> {{ t('manager.clients.actions.programs') }}
                                                </span>
                                            </li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <span class="dropdown-item-text text-muted">
                                                    <i class="fas fa-cog"></i> {{ t('manager.clients.actions.management_features') }}
                                                </span>
                                            </li>
                                        </ul>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination will be added when needed -->
        {% else %}
            <div class="text-center p-5">
                <i class="fas fa-users fa-4x text-muted mb-3"></i>
                <h4 class="mb-3">{{ t('manager.clients.messages.no_clients_found') }}</h4>
                <p class="mb-4">{{ t('manager.clients.messages.try_adjusting_search') }}</p>
                <a href="{{ url_for('manager.new_client') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> {{ t('manager.clients.add_new_client') }}
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Client management modals will be added here when functionality is implemented -->
{% endblock %}

{% block scripts %}
<script>
    // Client management functionality will be added here
    console.log('Client management page loaded');
</script>
{% endblock %}
