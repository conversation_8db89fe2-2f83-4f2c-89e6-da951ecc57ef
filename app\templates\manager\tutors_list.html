<!-- app/templates/manager/tutors_list.html -->
{% extends "base.html" %}

{% block title %}{{ t('manager.tutors.title_full') }}{% endblock %}

{% block content %}
<div class="page-header fade-in-up">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>{{ t('manager.tutors.title') }}</h1>
            <p>{{ t('manager.tutors.subtitle', 'Manage tutor accounts and assignments') }}</p>
        </div>
        <div>
            <a href="{{ url_for('manager.new_tutor') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> {{ t('manager.tutors.add_tutor') }}
            </a>
        </div>
    </div>
</div>

<!-- Filter Form -->
<div class="search-box fade-in-up">
        <form method="GET" action="{{ url_for('manager.tutors_list') }}" class="row g-3 filter-form">
            <div class="col-md-5">
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" class="form-control" name="search" value="{{ form.search.data or '' }}" placeholder="{{ t('manager.tutors.filters.search_placeholder') }}">
                </div>
            </div>
            <div class="col-md-5">
                <select name="service_id" class="form-select">
                    <option value="">{{ t('manager.tutors.filters.all_services') }}</option>
                    {% for service in services %}
                        <option value="{{ service.id }}" {% if request.args.get('service_id') == service.id|string %}selected{% endif %}>
                            {{ service.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary w-100">{{ t('manager.tutors.filters.filter') }}</button>
            </div>
        </form>
</div>

<!-- Tutors Table -->
<div class="card fade-in-up">
    <div class="card-body">
        {% if tutors %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>{{ t('manager.tutors.table.name') }}</th>
                            <th>{{ t('manager.tutors.table.email') }}</th>
                            <th>{{ t('manager.tutors.table.phone') }}</th>
                            <th>{{ t('manager.tutors.table.services') }}</th>
                            <th>{{ t('manager.tutors.table.status') }}</th>
                            <th>{{ t('manager.tutors.table.actions') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for tutor in tutors %}
                            <tr>
                                <td>{{ tutor.first_name }} {{ tutor.last_name }}</td>
                                <td>{{ tutor.user.email }}</td>
                                <td>{{ tutor.phone }}</td>
                                <td>
                                    {% for tutor_service in tutor.tutor_services %}
                                        <span class="badge bg-primary">{{ tutor_service.service.name }}</span>
                                    {% endfor %}
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if tutor.is_active else 'danger' }}">
                                        {{ t('manager.tutors.status.active') if tutor.is_active else t('manager.tutors.status.inactive') }}
                                    </span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="{{ url_for('manager.edit_tutor', id=tutor.tutor_id) }}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{{ url_for('manager.view_tutor', id=tutor.tutor_id) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center p-5">
                <i class="fas fa-chalkboard-teacher fa-3x text-muted mb-3"></i>
                <h5>{{ t('manager.tutors.messages.no_tutors_found') }}</h5>
                <p class="text-muted">{{ t('manager.tutors.messages.no_tutors_message') }}</p>
                <a href="{{ url_for('manager.new_tutor') }}" class="btn btn-primary mt-3">
                    <i class="fas fa-plus"></i> {{ t('manager.tutors.add_tutor') }}
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}