# Connec-T/TutorAide Management System - Claude Documentation

## Project Overview

Connec-T (also known as TutorAide) is a comprehensive tutoring management system built with Flask. It handles client management, tutor scheduling, appointment booking, invoicing, payment processing, and subscription management for tutoring services.

### Key Features
- Multi-role authentication (Clients, Tutors, Managers)
- Appointment scheduling and management
- Automated invoicing and payment processing via Stripe
- Subscription-based service plans
- Group session management (TEC FEE program)
- Multi-language support (English/French)
- Email notifications and reminders
- Tutor availability and time-off management

### Tech Stack
- **Backend**: Flask (Python)
- **Database**: PostgreSQL
- **Payment Processing**: Stripe
- **Authentication**: Flask-Login with optional Google OAuth
- **Frontend**: Jinja2 templates, Bootstrap, jQuery
- **Email**: Flask-Mail (SMTP)
- **Task Scheduling**: APScheduler
- **Forms**: Flask-WTF

## Design System and Color Scheme

### Color Palette

The application uses a clean, minimalist design with a distinctive soft red accent color theme across all user interfaces.

#### Primary Colors
- **Primary Red**: `#e57373` - Main accent color used for active states, primary buttons, and key UI elements
- **Primary Red Dark**: `#d32f2f` - Used for hover states and emphasis
- **Primary Red Light**: 
  - Client/Tutor: `#ffebee` 
  - Manager: `#ffcdd2`
- **Primary Red Pastel**: `#f8bbd9` - Subtle backgrounds and borders
- **Primary White**: `#ffffff` - Main background color
- **Primary Black**: `#000000` - Navigation bar background

#### Gray Scale
- **Light Gray**: `#f8f9fa` - Section backgrounds, card headers
- **Medium Gray**: `#6c757d` - Secondary text, muted elements
- **Dark Gray**: `#495057` - Emphasis text
- **Border Gray**: `#dee2e6` - Dividers, borders, subtle separators
- **Text Gray**: `#212529` - Primary text color

#### Functional Colors (Red-Based)
All functional colors use variations of red instead of traditional colors:
- **Success**: `#e8a5a5` (soft red)
- **Warning**: `#f4a5a5` (medium red)
- **Info**: `#e57373` (primary red)
- **Danger**: `#e57373` (primary red)

### Typography

#### Font Families
- **Headers**: 'Playfair Display', serif - Elegant serif font for headings and titles
- **Body**: 'Inter', sans-serif - Modern, clean sans-serif for body text and UI elements
- **Google Sign-In**: 'Roboto', sans-serif - Used specifically for Google authentication buttons

#### Font Sizes
- **Base**: `1.1rem` (increased from default)
- **General Elements**: `1.05rem`
- **Headings**:
  - H1: `2.2rem`
  - H2: `1.9rem`
  - H3: `1.6rem`
  - H4: `1.4rem`
  - H5: `1.2rem`
  - H6: `1.1rem`

### Design Patterns

#### Visual Style
- **Clean Minimalist Interface**: White backgrounds with subtle gray accents and red highlights
- **Consistent Rounded Corners**:
  - Cards: `20px` border-radius
  - Buttons: `25px` border-radius
  - Form inputs: `15px` border-radius
  - Smaller elements: `15px` border-radius

#### Shadow Effects
- **Soft Shadow**: `0 4px 20px rgba(0, 0, 0, 0.08)` - General UI elements
- **Card Shadow**: `0 6px 25px rgba(0, 0, 0, 0.06)` - Cards and containers
- **Hover Shadow**: `0 8px 30px rgba(0, 0, 0, 0.1)` - Enhanced depth on hover

#### Transitions and Animations
- **Standard Transition**: `all 0.3s ease`
- **Hover Transform**: `translateY(-2px)` - Subtle lift effect
- **Sidebar Hover**: `translateX(3px)` - Horizontal shift for navigation items
- **Fade In Animation**: `fadeInUp 0.6s ease-out` - Content appearance

#### Role-Specific Themes
All three role interfaces (Client, Tutor, Manager) share the same color scheme but with subtle differences:
- **Client Interface**: Pure white background (`#ffffff`)
- **Tutor Interface**: Pure white background (`#ffffff`) matching client theme
- **Manager Interface**: Light gray background (`#f8f9fa`) for main content area

### Bootstrap Customization

The project uses Bootstrap 5.3.0 as a base framework but heavily customizes it:
- All Bootstrap primary colors are overridden with the red color scheme
- Custom professional theme CSS files override Bootstrap defaults
- Button styles, form controls, and alerts are completely restyled
- Bootstrap's blue accent color is replaced with soft red throughout

### UI Components

#### Cards
- White background with gray border
- 20px rounded corners
- Subtle shadow effect
- Header with light gray background
- Hover effect with translateY animation

#### Buttons
- Primary buttons: Red background with white text
- Rounded pill shape (25px border-radius)
- Soft shadow effect
- Hover state with darker red and enhanced shadow
- Consistent padding: `12px 24px`

#### Forms
- Clean white inputs with gray borders
- 15px rounded corners
- Red focus state with subtle glow
- Increased font size for better readability

#### Navigation
- Black navbar with white text
- Sidebar with white background and gray borders
- Active states in primary red
- Rounded navigation items with hover effects

This design system creates a cohesive, professional appearance that's consistent across all user roles while maintaining excellent readability and user experience.

## Directory Structure

```
Connec-T/
├── app/                          # Main application directory
│   ├── __init__.py              # App factory and initialization
│   ├── config.py                # Configuration classes
│   ├── extensions.py            # Flask extensions initialization
│   ├── models/                  # Database models
│   │   ├── user.py             # Base user model
│   │   ├── client.py           # Client model
│   │   ├── tutor.py            # Tutor model
│   │   ├── manager.py          # Manager model
│   │   ├── appointment.py      # Appointment model
│   │   ├── invoice.py          # Invoice model
│   │   ├── subscription.py     # Subscription model
│   │   └── ...                 # Other models
│   ├── views/                   # Route handlers
│   │   ├── auth.py             # Authentication routes
│   │   ├── client.py           # Client dashboard routes
│   │   ├── tutor.py            # Tutor dashboard routes
│   │   ├── manager.py          # Manager dashboard routes
│   │   └── ...                 # Other route modules
│   ├── services/                # Business logic layer
│   │   ├── appointment_service.py
│   │   ├── invoice_service.py
│   │   ├── stripe_service.py
│   │   └── ...                 # Other services
│   ├── forms/                   # WTForms definitions
│   ├── templates/               # Jinja2 templates
│   ├── static/                  # CSS, JS, images
│   ├── commands/                # CLI commands
│   ├── tasks/                   # Background tasks
│   ├── migrations/              # SQL migration files
│   └── locales/                 # i18n translation files
├── run.py                       # Application entry point
├── requirements.txt             # Python dependencies
├── .env                         # Environment variables (create from .env.example)
└── README.md                    # Project documentation
```

## Development Setup

### Prerequisites
- Python 3.8+
- PostgreSQL 12+
- Node.js (for some frontend dependencies)
- Stripe account (for payment processing)

### Installation Steps

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Connec-T
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

5. **Set up PostgreSQL database**
   ```bash
   createdb connect_db
   ```

6. **Run database migrations**
   ```bash
   python run_migrations.py
   # Or use the shell script: ./run_migrations.sh
   ```

7. **Create initial admin user**
   ```bash
   flask create-admin
   ```

8. **Run the application**
   ```bash
   python run.py
   ```

## Key Commands

### Flask CLI Commands
```bash
# Create admin user
flask create-admin

# Test database connection
flask test-db-connection

# Initialize database
flask init-db

# Run appointment commands
flask appointments <command>

# Update participant counts
flask update-participant-count
```

### Migration Scripts
```bash
# Run all migrations
python run_migrations.py

# Apply specific migration
python app/init_db.py
```

### Testing Commands
```bash
# Run tests (when implemented)
python -m pytest

# Test specific functionality
python test_participant_count.py
```

## Important Files & Directories

### Configuration
- `app/config.py` - Application configuration classes
- `app/extensions.py` - Flask extension initialization
- `.env` - Environment variables

### Core Application
- `run.py` - Main entry point
- `app/__init__.py` - App factory and blueprint registration

### Models (app/models/)
- `user.py` - Base User model with authentication
- `client.py` - Client-specific fields and methods
- `tutor.py` - Tutor profiles and rates
- `appointment.py` - Appointment scheduling
- `invoice.py` - Invoice generation and tracking
- `subscription.py` - Subscription plans and management

### Services (app/services/)
- `appointment_service.py` - Appointment logic
- `invoice_service.py` - Invoice generation
- `stripe_service.py` - Payment processing
- `email_service.py` - Email notifications
- `group_session_service.py` - Group session management

### Views (app/views/)
- `auth.py` - Login, registration, password reset
- `client.py` - Client dashboard and features
- `tutor.py` - Tutor dashboard and features
- `manager.py` - Admin/manager interface
- `api.py` - API endpoints

## Common Tasks

### Creating Users
1. Managers can create users through the admin interface
2. Clients can self-register with email verification
3. Tutors are created by managers

### Managing Appointments
- Managers create appointments and assign tutors
- Tutors can view their schedule and update attendance
- Clients can view their appointments and invoices
- Automatic reminders sent 24 hours before appointments

### Processing Payments
- Invoices generated automatically based on appointments
- Stripe integration for payment processing
- Support for one-time and subscription payments
- Automatic payment tracking and reconciliation

### Handling Subscriptions
- Multiple subscription plans available
- Automatic session allocation
- Usage tracking and reporting
- Renewal notifications

## Database Schema Overview

### Key Tables
- `users` - Base user authentication
- `clients` - Client profiles and relationships
- `tutors` - Tutor profiles and availability
- `appointments` - Scheduled sessions
- `invoices` - Billing records
- `subscriptions` - Active subscriptions
- `services` - Available tutoring services
- `programs` - Special programs (e.g., TEC FEE)

### Relationships
- Users → Clients/Tutors/Managers (one-to-one)
- Clients → Appointments (one-to-many)
- Tutors → Appointments (one-to-many)
- Appointments → Invoices (many-to-one)
- Clients → Subscriptions (one-to-many)

## Development Workflow

### Adding New Features
1. Create/update models in `app/models/`
2. Add service logic in `app/services/`
3. Create forms in `app/forms/`
4. Add routes in `app/views/`
5. Create templates in `app/templates/`
6. Add translations in `app/locales/`
7. Run migrations if database changes needed

### Important Development Guidelines

⚠️ **NO MOCKS POLICY**: All new development should avoid using mocks. Instead:
- Use real database connections (test database)
- Use Stripe test mode for payment testing
- Use actual SMTP servers (can use services like Mailtrap for testing)
- Implement real integrations, not mock services
- For external APIs, use their sandbox/test environments

This ensures code works with real services and reduces integration issues in production.

⚠️ **ERROR HANDLING APPROACH**: When encountering errors, don't take shortcuts. Instead:
- **Investigate thoroughly** - Understand the root cause, not just the symptom
- **Check full context** - Review related files, dependencies, configurations, and database state
- **Provide complete solutions** - Fix underlying issues, not just surface problems
- **Consider side effects** - Ensure fixes don't break existing functionality
- **Document the resolution** - Explain what was wrong and why the solution works
- **Test comprehensively** - Verify the fix in different scenarios
- **Update related code** - If the error reveals a pattern, fix it everywhere

Example: If a database query fails, don't just add a try-catch. Investigate why it fails, check the schema, verify migrations, ensure proper indexes, and handle edge cases properly.

⚠️ **DATABASE DESIGN PRINCIPLE**: When implementing new features:
- **First check existing tables** - Can you add columns to existing tables rather than creating new ones?
- **Reuse when possible** - Extend existing models/tables before creating new ones
- **Maintain relationships** - Ensure proper foreign keys and relationships
- **Keep it simple** - Avoid unnecessary complexity in the schema
- **Document changes** - Add comments explaining why new tables were necessary

Example: For audit logs, first check if you can add audit columns to existing tables before creating a separate audit table.

⚠️ **FULL-STACK IMPLEMENTATION**: Always implement complete features:
- **Backend + Frontend** - Never implement backend without corresponding UI
- **Complete workflow** - Ensure users can actually use the feature
- **Navigation** - Add menu items/links to access new features
- **Forms and validation** - Create proper forms with client and server validation
- **Error handling** - Show user-friendly error messages
- **Success feedback** - Confirm actions with appropriate messages
- **Testing** - Verify the complete flow works end-to-end

Example: When adding audit logs, also create the manager view, add menu item, implement filters, and ensure proper display of audit data.

⚠️ **INTERNATIONALIZATION (i18n)**: All user-facing text must be translated:
- **Use translation function** - Always use `{{ t('key.path') }}` in templates, never hardcode text
- **Add to both languages** - Every text key must exist in BOTH:
  - `/app/locales/en/common.json` (English)
  - `/app/locales/fr/common.json` (French)
- **Maintain consistency** - Keys should have the same structure in both files
- **Check before committing** - Verify all new text has translations in both languages
- **Use descriptive keys** - Follow the existing naming pattern (e.g., `manager.appointments.form.title`)

Example: When adding a new button:
```html
<!-- Wrong -->
<button>Create Appointment</button>

<!-- Correct -->
<button>{{ t('manager.appointments.create_button') }}</button>
```
Then add to both JSON files:
- English: `"create_button": "Create Appointment"`
- French: `"create_button": "Créer un rendez-vous"`

### Testing Procedures
1. Unit test services and models
2. Integration test views and forms
3. Test payment flows in Stripe test mode
4. Verify email notifications
5. Check multi-language support

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   ```bash
   flask test-db-connection
   # Check DATABASE_URL in .env
   ```

2. **Email Not Sending**
   - Verify SMTP settings in .env
   - Check email service credentials
   - Enable "less secure apps" for Gmail

3. **Stripe Webhook Failures**
   - Ensure STRIPE_WEBHOOK_SECRET is correct
   - Check webhook endpoint URL
   - Verify SSL certificate for production

4. **Import Errors**
   - Ensure virtual environment is activated
   - Run `pip install -r requirements.txt`
   - Check PYTHONPATH includes project root

### Debug Mode
Set in .env:
```
FLASK_DEBUG=True
FLASK_ENV=development
```

### Logging
- Check `app.log` for application logs
- Database queries logged in debug mode
- Stripe events logged for troubleshooting

## Security Considerations

1. **Environment Variables**
   - Never commit .env file
   - Use strong SECRET_KEY
   - Rotate API keys regularly

2. **Database**
   - Use parameterized queries (SQLAlchemy handles this)
   - Regular backups
   - Encrypt sensitive data

3. **Authentication**
   - Password hashing with bcrypt
   - Session management
   - CSRF protection enabled

4. **Payment Security**
   - PCI compliance via Stripe
   - Webhook signature verification
   - No card details stored locally

## Deployment Considerations

1. **Production Settings**
   - Set FLASK_ENV=production
   - Disable DEBUG mode
   - Use production database
   - Configure proper logging

2. **Required Services**
   - PostgreSQL database
   - SMTP email service
   - Stripe account
   - SSL certificate

3. **Performance**
   - Enable caching
   - Optimize database queries
   - Use CDN for static assets
   - Configure proper worker processes

## Useful Resources

- [Flask Documentation](https://flask.palletsprojects.com/)
- [SQLAlchemy Documentation](https://www.sqlalchemy.org/)
- [Stripe API Reference](https://stripe.com/docs/api)
- [Flask-Login Documentation](https://flask-login.readthedocs.io/)
- [Flask-Mail Documentation](https://pythonhosted.org/Flask-Mail/)