// app/static/js/main.js

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        document.querySelectorAll('.alert').forEach(function(alert) {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);
    
    // Activate current nav item based on URL
    activateNavItem();
    
    // Initialize dynamic dependent dropdowns
    initDependentDropdowns();
    
    // Initialize date pickers
    initDatePickers();
    
    // Add auto-submit functionality to filter forms
    initFilterForms();
});

/**
 * Activate the current navigation item based on the current URL
 */
function activateNavItem() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');
    
    navLinks.forEach(function(link) {
        const href = link.getAttribute('href');
        if (href && currentPath.startsWith(href) && href !== '/') {
            link.classList.add('active');
            
            // If in a dropdown, activate parent too
            const dropdown = link.closest('.dropdown-menu');
            if (dropdown) {
                const dropdownToggle = dropdown.previousElementSibling;
                if (dropdownToggle && dropdownToggle.classList.contains('dropdown-toggle')) {
                    dropdownToggle.classList.add('active');
                }
            }
        }
    });
}

/**
 * Initialize dependent dropdowns (like tutor -> services)
 */
function initDependentDropdowns() {
    // Tutor Service Selection
    const tutorSelect = document.getElementById('tutor_id');
    const serviceSelect = document.getElementById('tutor_service_id');
    
    if (tutorSelect && serviceSelect) {
        tutorSelect.addEventListener('change', function() {
            const tutorId = this.value;
            
            if (tutorId) {
                // Clear current options
                serviceSelect.innerHTML = '<option value="">Loading services...</option>';
                serviceSelect.disabled = true;
                
                // Fetch services for this tutor
                fetch(`/api/tutor-services/${tutorId}`)
                    .then(response => response.json())
                    .then(data => {
                        serviceSelect.innerHTML = '<option value="">Select a service</option>';
                        
                        data.forEach(function(service) {
                            const option = document.createElement('option');
                            option.value = service.id;
                            option.textContent = `${service.name} ($${service.client_rate.toFixed(2)}/hr)`;
                            serviceSelect.appendChild(option);
                        });
                        
                        serviceSelect.disabled = false;
                    })
                    .catch(error => {
                        console.error('Error fetching services:', error);
                        serviceSelect.innerHTML = '<option value="">Error loading services</option>';
                        serviceSelect.disabled = true;
                    });
            } else {
                serviceSelect.innerHTML = '<option value="">Select a tutor first</option>';
                serviceSelect.disabled = true;
            }
        });
        
        // Trigger change event if tutor is already selected (like on edit forms)
        if (tutorSelect.value) {
            tutorSelect.dispatchEvent(new Event('change'));
        }
    }
    
    // Parent Student Selection
    const parentSelect = document.getElementById('parent_id');
    const studentSelect = document.getElementById('student_id');
    
    if (parentSelect && studentSelect) {
        parentSelect.addEventListener('change', function() {
            const parentId = this.value;
            
            if (parentId) {
                // Clear current options
                studentSelect.innerHTML = '<option value="">Loading students...</option>';
                studentSelect.disabled = true;
                
                // Fetch students for this parent
                fetch(`/api/parent-students/${parentId}`)
                    .then(response => response.json())
                    .then(data => {
                        studentSelect.innerHTML = '<option value="">Select a student</option>';
                        
                        data.forEach(function(student) {
                            const option = document.createElement('option');
                            option.value = student.id;
                            option.textContent = student.name;
                            studentSelect.appendChild(option);
                        });
                        
                        studentSelect.disabled = false;
                    })
                    .catch(error => {
                        console.error('Error fetching students:', error);
                        studentSelect.innerHTML = '<option value="">Error loading students</option>';
                        studentSelect.disabled = true;
                    });
            } else {
                studentSelect.innerHTML = '<option value="">Select a parent first</option>';
                studentSelect.disabled = true;
            }
        });