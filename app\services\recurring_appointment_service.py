# app/services/recurring_appointment_service.py
from datetime import datetime, timedelta, date
from app.extensions import db
from app.models.appointment import Appointment
from app.models.appointment_recurring_schedule import AppointmentRecurringSchedule
from sqlalchemy import and_, or_
from flask import current_app
import logging

class RecurringAppointmentService:
    """Service for managing recurring appointment schedules and generating appointments."""

    @staticmethod
    def validate_recurring_schedule_parameters(schedule_data):
        """
        Validate recurring schedule parameters before creation or update.
        
        Args:
            schedule_data (dict): Dictionary containing schedule parameters
            
        Returns:
            tuple: (is_valid, error_messages)
        """
        errors = []
        
        # Required fields validation
        required_fields = ['tutor_id', 'client_id', 'tutor_service_id', 'start_time', 
                          'duration_minutes', 'frequency', 'pattern_start_date']
        
        for field in required_fields:
            if field not in schedule_data or schedule_data[field] is None:
                errors.append(f"Missing required field: {field}")
        
        if errors:
            return False, errors
        
        # Frequency-specific validation
        frequency = schedule_data.get('frequency')
        if frequency not in ['weekly', 'biweekly', 'monthly']:
            errors.append("Frequency must be 'weekly', 'biweekly', or 'monthly'")
        
        # Day of week validation for weekly/biweekly
        if frequency in ['weekly', 'biweekly']:
            day_of_week = schedule_data.get('day_of_week')
            if day_of_week is None or not (0 <= day_of_week <= 6):
                errors.append("day_of_week must be between 0 (Monday) and 6 (Sunday) for weekly/biweekly schedules")
        
        # Week of month validation for monthly
        if frequency == 'monthly':
            week_of_month = schedule_data.get('week_of_month')
            day_of_week = schedule_data.get('day_of_week')
            if week_of_month is None or not (1 <= week_of_month <= 5):
                errors.append("week_of_month must be between 1 and 5 for monthly schedules")
            if day_of_week is None or not (0 <= day_of_week <= 6):
                errors.append("day_of_week must be between 0 (Monday) and 6 (Sunday) for monthly schedules")
        
        # Duration validation
        duration = schedule_data.get('duration_minutes', 0)
        if duration <= 0 or duration > 480:  # Max 8 hours
            errors.append("duration_minutes must be between 1 and 480 minutes")
        
        # Date validation
        start_date = schedule_data.get('pattern_start_date')
        end_date = schedule_data.get('pattern_end_date')
        
        if isinstance(start_date, str):
            try:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
            except ValueError:
                errors.append("Invalid pattern_start_date format. Use YYYY-MM-DD")
        
        if end_date:
            if isinstance(end_date, str):
                try:
                    end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                except ValueError:
                    errors.append("Invalid pattern_end_date format. Use YYYY-MM-DD")
            
            if start_date and end_date and end_date <= start_date:
                errors.append("pattern_end_date must be after pattern_start_date")
        
        # Occurrences validation
        occurrences = schedule_data.get('pattern_occurrences')
        if occurrences is not None and occurrences <= 0:
            errors.append("pattern_occurrences must be greater than 0")
        
        return len(errors) == 0, errors

    @staticmethod
    def create_recurring_schedule(schedule_data):
        """
        Create a new recurring appointment schedule.
        
        Args:
            schedule_data (dict): Dictionary containing schedule parameters
            
        Returns:
            tuple: (schedule, error_message)
        """
        try:
            # Validate parameters
            is_valid, errors = RecurringAppointmentService.validate_recurring_schedule_parameters(schedule_data)
            if not is_valid:
                return None, "; ".join(errors)
            
            # Create the schedule
            schedule = AppointmentRecurringSchedule(
                tutor_id=schedule_data['tutor_id'],
                client_id=schedule_data['client_id'],
                dependant_id=schedule_data.get('dependant_id'),
                tutor_service_id=schedule_data['tutor_service_id'],
                start_time=schedule_data['start_time'],
                duration_minutes=schedule_data['duration_minutes'],
                frequency=schedule_data['frequency'],
                day_of_week=schedule_data.get('day_of_week'),
                week_of_month=schedule_data.get('week_of_month'),
                pattern_start_date=schedule_data['pattern_start_date'],
                pattern_end_date=schedule_data.get('pattern_end_date'),
                pattern_occurrences=schedule_data.get('pattern_occurrences'),
                default_status=schedule_data.get('default_status', 'scheduled'),
                default_notes=schedule_data.get('default_notes'),
                transport_fee=schedule_data.get('transport_fee'),
                transport_fee_for_tutor=schedule_data.get('transport_fee_for_tutor', True),
                is_subscription_based=schedule_data.get('is_subscription_based', False),
                subscription_id=schedule_data.get('subscription_id'),
                is_active=True,
                created_by=schedule_data.get('created_by')
            )
            
            db.session.add(schedule)
            db.session.flush()  # Get the ID
            
            current_app.logger.info(f"Created recurring schedule {schedule.schedule_id} for tutor {schedule.tutor_id}")
            
            return schedule, None
            
        except Exception as e:
            db.session.rollback()
            error_msg = f"Error creating recurring schedule: {str(e)}"
            current_app.logger.error(error_msg)
            return None, error_msg

    @staticmethod
    def generate_appointments_for_schedule(schedule_id, end_date=None, max_appointments=50):
        """
        Generate appointments for a recurring schedule up to a specified date.
        
        Args:
            schedule_id (int): ID of the recurring schedule
            end_date (date): Generate appointments up to this date (default: 8 weeks from now)
            max_appointments (int): Maximum number of appointments to generate
            
        Returns:
            tuple: (generated_appointments, error_message)
        """
        try:
            schedule = AppointmentRecurringSchedule.query.get(schedule_id)
            if not schedule:
                return [], "Recurring schedule not found"
            
            if not schedule.is_active:
                return [], "Recurring schedule is not active"
            
            # Default end date to 8 weeks from now
            if end_date is None:
                end_date = datetime.now().date() + timedelta(weeks=8)
            
            generated_appointments = []
            current_date = schedule.last_generated_date or schedule.pattern_start_date
            
            # Start from the day after last generated date
            if schedule.last_generated_date:
                current_date = schedule.last_generated_date + timedelta(days=1)
            
            appointment_count = 0
            
            while current_date <= end_date and appointment_count < max_appointments:
                # Check if we've exceeded the pattern end date
                if schedule.pattern_end_date and current_date > schedule.pattern_end_date:
                    break
                
                # Check if we've reached the maximum occurrences
                if schedule.pattern_occurrences:
                    existing_count = schedule.generated_appointments.count()
                    if existing_count >= schedule.pattern_occurrences:
                        break
                
                # Check if this date matches the recurring pattern
                if RecurringAppointmentService._should_generate_appointment_on_date(schedule, current_date):
                    # Create appointment for this date
                    appointment_start = datetime.combine(current_date, schedule.start_time)
                    appointment_end = appointment_start + timedelta(minutes=schedule.duration_minutes)
                    
                    # Check for conflicts
                    if RecurringAppointmentService._check_appointment_availability(
                        schedule.tutor_id, appointment_start, appointment_end):
                        
                        appointment = Appointment(
                            recurring_schedule_id=schedule.schedule_id,
                            tutor_id=schedule.tutor_id,
                            client_id=schedule.client_id,
                            dependant_id=schedule.dependant_id,
                            tutor_service_id=schedule.tutor_service_id,
                            start_time=appointment_start,
                            end_time=appointment_end,
                            status=schedule.default_status,
                            notes=schedule.default_notes,
                            transport_fee=schedule.transport_fee,
                            transport_fee_for_tutor=schedule.transport_fee_for_tutor,
                            is_subscription_based=schedule.is_subscription_based,
                            subscription_id=schedule.subscription_id,
                            created_by=schedule.created_by
                        )
                        
                        db.session.add(appointment)
                        generated_appointments.append(appointment)
                        appointment_count += 1
                        
                        # Update last generated date
                        schedule.last_generated_date = current_date
                    else:
                        current_app.logger.warning(
                            f"Skipping appointment generation for {current_date} due to tutor conflict"
                        )
                
                current_date += timedelta(days=1)
            
            db.session.commit()
            
            current_app.logger.info(
                f"Generated {len(generated_appointments)} appointments for schedule {schedule_id}"
            )
            
            return generated_appointments, None
            
        except Exception as e:
            db.session.rollback()
            error_msg = f"Error generating appointments for schedule {schedule_id}: {str(e)}"
            current_app.logger.error(error_msg)
            return [], error_msg

    @staticmethod
    def _should_generate_appointment_on_date(schedule, target_date):
        """
        Check if an appointment should be generated on the target date based on the schedule pattern.
        
        Args:
            schedule (AppointmentRecurringSchedule): The recurring schedule
            target_date (date): The date to check
            
        Returns:
            bool: True if appointment should be generated
        """
        if target_date < schedule.pattern_start_date:
            return False
        
        if schedule.frequency == 'weekly':
            return target_date.weekday() == schedule.day_of_week
        
        elif schedule.frequency == 'biweekly':
            # Check if it's the right day and the right week (every 2 weeks from start)
            weeks_diff = (target_date - schedule.pattern_start_date).days // 7
            return (target_date.weekday() == schedule.day_of_week and weeks_diff % 2 == 0)
        
        elif schedule.frequency == 'monthly':
            # Monthly logic - same day of week in the specified week of month
            if target_date.weekday() != schedule.day_of_week:
                return False
            
            # Calculate which week of the month this is
            week_of_month = (target_date.day - 1) // 7 + 1
            return week_of_month == schedule.week_of_month
        
        return False

    @staticmethod
    def _check_appointment_availability(tutor_id, start_time, end_time):
        """
        Check if the tutor is available during the specified time.
        
        Args:
            tutor_id (int): ID of the tutor
            start_time (datetime): Start time of the appointment
            end_time (datetime): End time of the appointment
            
        Returns:
            bool: True if tutor is available
        """
        conflicts = Appointment.query.filter(
            Appointment.tutor_id == tutor_id,
            Appointment.status != 'cancelled',
            or_(
                # Case 1: New appointment starts during existing appointment
                and_(
                    Appointment.start_time <= start_time,
                    Appointment.end_time > start_time
                ),
                # Case 2: New appointment ends during existing appointment
                and_(
                    Appointment.start_time < end_time,
                    Appointment.end_time >= end_time
                ),
                # Case 3: New appointment completely overlaps existing appointment
                and_(
                    Appointment.start_time >= start_time,
                    Appointment.end_time <= end_time
                )
            )
        ).first()
        
        return conflicts is None

    @staticmethod
    def generate_appointments_for_all_active_schedules(days_ahead=56):
        """
        Generate appointments for all active recurring schedules.
        
        Args:
            days_ahead (int): Number of days ahead to generate appointments (default: 8 weeks)
            
        Returns:
            dict: Summary of generation results
        """
        try:
            end_date = datetime.now().date() + timedelta(days=days_ahead)
            active_schedules = AppointmentRecurringSchedule.query.filter_by(is_active=True).all()
            
            results = {
                'total_schedules': len(active_schedules),
                'successful_schedules': 0,
                'failed_schedules': 0,
                'total_appointments_generated': 0,
                'errors': []
            }
            
            for schedule in active_schedules:
                appointments, error = RecurringAppointmentService.generate_appointments_for_schedule(
                    schedule.schedule_id, end_date
                )
                
                if error:
                    results['failed_schedules'] += 1
                    results['errors'].append(f"Schedule {schedule.schedule_id}: {error}")
                else:
                    results['successful_schedules'] += 1
                    results['total_appointments_generated'] += len(appointments)
            
            current_app.logger.info(
                f"Batch generation complete: {results['total_appointments_generated']} appointments "
                f"generated from {results['successful_schedules']} schedules"
            )
            
            return results
            
        except Exception as e:
            error_msg = f"Error in batch appointment generation: {str(e)}"
            current_app.logger.error(error_msg)
            return {
                'total_schedules': 0,
                'successful_schedules': 0,
                'failed_schedules': 0,
                'total_appointments_generated': 0,
                'errors': [error_msg]
            }

    @staticmethod
    def deactivate_recurring_schedule(schedule_id, reason=None):
        """
        Deactivate a recurring schedule and optionally cancel future appointments.
        
        Args:
            schedule_id (int): ID of the recurring schedule
            reason (str): Optional reason for deactivation
            
        Returns:
            tuple: (success, error_message)
        """
        try:
            schedule = AppointmentRecurringSchedule.query.get(schedule_id)
            if not schedule:
                return False, "Recurring schedule not found"
            
            schedule.is_active = False
            schedule.modification_date = datetime.utcnow()
            
            # Optionally cancel future appointments
            future_appointments = schedule.generated_appointments.filter(
                Appointment.start_time > datetime.utcnow(),
                Appointment.status.in_(['scheduled', 'confirmed'])
            ).all()
            
            cancelled_count = 0
            for appointment in future_appointments:
                appointment.status = 'cancelled'
                if reason:
                    appointment.notes = f"{appointment.notes or ''}\nCancelled: {reason}".strip()
                cancelled_count += 1
            
            db.session.commit()
            
            current_app.logger.info(
                f"Deactivated recurring schedule {schedule_id} and cancelled {cancelled_count} future appointments"
            )
            
            return True, None
            
        except Exception as e:
            db.session.rollback()
            error_msg = f"Error deactivating recurring schedule {schedule_id}: {str(e)}"
            current_app.logger.error(error_msg)
            return False, error_msg

    @staticmethod
    def get_schedule_summary(schedule_id):
        """
        Get a summary of a recurring schedule including generated appointments.
        
        Args:
            schedule_id (int): ID of the recurring schedule
            
        Returns:
            dict: Schedule summary or None if not found
        """
        try:
            schedule = AppointmentRecurringSchedule.query.get(schedule_id)
            if not schedule:
                return None
            
            total_appointments = schedule.generated_appointments.count()
            upcoming_appointments = schedule.generated_appointments.filter(
                Appointment.start_time > datetime.utcnow()
            ).count()
            completed_appointments = schedule.generated_appointments.filter(
                Appointment.status == 'completed'
            ).count()
            
            return {
                'schedule_id': schedule.schedule_id,
                'tutor_name': f"{schedule.tutor.first_name} {schedule.tutor.last_name}" if schedule.tutor else "Unknown",
                'client_name': schedule.appointment_subject_name,
                'frequency': schedule.frequency,
                'start_time': schedule.start_time.strftime('%H:%M'),
                'duration_minutes': schedule.duration_minutes,
                'pattern_start_date': schedule.pattern_start_date.isoformat(),
                'pattern_end_date': schedule.pattern_end_date.isoformat() if schedule.pattern_end_date else None,
                'is_active': schedule.is_active,
                'total_appointments': total_appointments,
                'upcoming_appointments': upcoming_appointments,
                'completed_appointments': completed_appointments,
                'last_generated_date': schedule.last_generated_date.isoformat() if schedule.last_generated_date else None
            }
            
        except Exception as e:
            current_app.logger.error(f"Error getting schedule summary for {schedule_id}: {str(e)}")
            return None