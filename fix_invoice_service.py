from app import create_app
from app.extensions import db
from app.models.invoice import Invoice, InvoiceItem
from app.models.client import Client
from app.models.appointment import Appointment
from app.models.service import TutorService
from app.models.invoice_generation import InvoiceGenerationSettings
from sqlalchemy import text

def fix_invoice_service():
    """Fix the invoice service by updating the database schema to match the model."""
    app = create_app()
    
    with app.app_context():
        print("Checking database schema...")
        
        # Check if the invoice table has invoice_id as primary key
        result = db.session.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'invoices' AND column_name = 'invoice_id'
        """))
        
        if result.fetchone():
            print("✓ Invoice table has invoice_id column")
        else:
            print("✗ Invoice table does not have invoice_id column")
            
        # Check if the client table has client_id as primary key
        result = db.session.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'clients' AND column_name = 'client_id'
        """))
        
        if result.fetchone():
            print("✓ Client table has client_id column")
        else:
            print("✗ Client table does not have client_id column")
            
        # Check if the appointment table has appointment_id as primary key
        result = db.session.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'appointments' AND column_name = 'appointment_id'
        """))
        
        if result.fetchone():
            print("✓ Appointment table has appointment_id column")
        else:
            print("✗ Appointment table does not have appointment_id column")
            
        # Check if the tutor table has tutor_id as primary key
        result = db.session.execute(text("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'tutors' AND column_name = 'tutor_id'
        """))
        
        if result.fetchone():
            print("✓ Tutor table has tutor_id column")
        else:
            print("✗ Tutor table does not have tutor_id column")
            
        # Test a simple query to verify the models are working correctly
        try:
            clients_count = Client.query.count()
            print(f"✓ Successfully queried clients: {clients_count} found")
        except Exception as e:
            print(f"✗ Error querying clients: {str(e)}")
            
        try:
            invoices_count = Invoice.query.count()
            print(f"✓ Successfully queried invoices: {invoices_count} found")
        except Exception as e:
            print(f"✗ Error querying invoices: {str(e)}")
            
        try:
            appointments_count = Appointment.query.count()
            print(f"✓ Successfully queried appointments: {appointments_count} found")
        except Exception as e:
            print(f"✗ Error querying appointments: {str(e)}")
            
        print("\nDatabase schema check complete.")

if __name__ == "__main__":
    fix_invoice_service()