#!/usr/bin/env python3
"""
Test script to verify that program-related models are correctly defined
without triggering SQLAlchemy relationship resolution.
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

def test_program_models_definition():
    """Test that program-related models are correctly defined."""
    
    try:
        # Import SQLAlchemy components directly
        from sqlalchemy import Column, Integer, String, Text, Boolean, DateTime, Date, Time, Numeric, ForeignKey
        from sqlalchemy.ext.declarative import declarative_base
        
        print("✅ SQLAlchemy components imported successfully")
        
        # Read the program.py file and check the model definitions
        with open('app/models/program.py', 'r') as f:
            content = f.read()
        
        print("✅ Program models file read successfully")
        
        # Check that all required models are defined with correct primary keys
        model_checks = [
            ('class Program(db.Model):', 'program_id = db.Column(db.Integer, primary_key=True)'),
            ('class ProgramModule(db.Model):', 'module_id = db.Column(db.Integer, primary_key=True)'),
            ('class Enrollment(db.Model):', 'enrollment_id = db.Column(db.Integer, primary_key=True)'),
            ('class ModuleProgress(db.Model):', 'progress_id = db.Column(db.Integer, primary_key=True)'),
            ('class ModuleSession(db.Model):', 'session_id = db.Column(db.Integer, primary_key=True)'),
            ('class ProgramPricing(db.Model):', 'pricing_id = db.Column(db.Integer, primary_key=True)'),
            ('class GroupSession(db.Model):', 'group_session_id = db.Column(db.Integer, primary_key=True)'),
            ('class GroupSessionParticipant(db.Model):', 'participant_id = db.Column(db.Integer, primary_key=True)')
        ]
        
        print("\n🔍 Checking model definitions:")
        for model_def, pk_def in model_checks:
            if model_def in content and pk_def in content:
                model_name = model_def.split('class ')[1].split('(')[0]
                pk_name = pk_def.split(' = ')[0].strip()
                print(f"✅ {model_name}: {pk_name}")
            else:
                model_name = model_def.split('class ')[1].split('(')[0]
                print(f"❌ {model_name}: Definition not found correctly")
                return False
        
        # Check foreign key references
        print("\n🔍 Checking foreign key references:")
        
        fk_checks = [
            ("ProgramModule", "db.ForeignKey('programs.program_id'"),
            ("Enrollment", "db.ForeignKey('clients.client_id'"),
            ("Enrollment", "db.ForeignKey('programs.program_id'"),
            ("ModuleProgress", "db.ForeignKey('enrollments.enrollment_id'"),
            ("ModuleProgress", "db.ForeignKey('program_modules.module_id'"),
            ("ModuleSession", "db.ForeignKey('module_progress.progress_id'"),
            ("ModuleSession", "db.ForeignKey('appointments.appointment_id'"),
            ("ProgramPricing", "db.ForeignKey('programs.program_id'"),
            ("GroupSession", "db.ForeignKey('programs.program_id'"),
            ("GroupSession", "db.ForeignKey('program_modules.module_id'"),
            ("GroupSession", "db.ForeignKey('tutors.tutor_id'"),
            ("GroupSessionParticipant", "db.ForeignKey('group_sessions.group_session_id'"),
            ("GroupSessionParticipant", "db.ForeignKey('enrollments.enrollment_id'")
        ]
        
        for model_name, fk_ref in fk_checks:
            if fk_ref in content:
                print(f"✅ {model_name}: {fk_ref}")
            else:
                print(f"❌ {model_name}: {fk_ref} not found")
                return False
        
        # Check __repr__ methods use correct primary keys
        print("\n🔍 Checking __repr__ methods:")
        
        repr_checks = [
            ("Program", "f'<Program {self.program_id}"),
            ("ProgramModule", "f'<ProgramModule {self.module_id}"),
            ("Enrollment", "f'<Enrollment {self.enrollment_id}"),
            ("ModuleProgress", "f'<ModuleProgress {self.progress_id}"),
            ("ModuleSession", "f'<ModuleSession {self.session_id}"),
            ("ProgramPricing", "f'<ProgramPricing {self.pricing_id}"),
            ("GroupSession", "f'<GroupSession {self.group_session_id}"),
            ("GroupSessionParticipant", "f'<GroupSessionParticipant {self.participant_id}")
        ]
        
        for model_name, repr_check in repr_checks:
            if repr_check in content:
                print(f"✅ {model_name}: __repr__ uses correct primary key")
            else:
                print(f"❌ {model_name}: __repr__ method issue")
                return False
        
        print("\n🎉 Task 3 - Program-Related Models: VERIFICATION COMPLETE!")
        print("✅ All 8 program-related models are correctly defined")
        print("✅ All models use descriptive primary key names")
        print("✅ All foreign key references use descriptive names")
        print("✅ All __repr__ methods use correct primary keys")
        print("✅ Task requirements fully satisfied")
        
        return True
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_program_models_definition()
    sys.exit(0 if success else 1)