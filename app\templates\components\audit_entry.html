<!-- Individual Audit Entry Component -->
<!-- This template represents a single audit entry with full before/after comparison -->
<div class="audit-entry mb-3" data-entry-id="{{ entry.id }}" data-action="{{ entry.action }}">
    <div class="audit-entry-card card border-start border-3" data-action="{{ entry.action }}">
        <div class="card-body py-3">
            <div class="row align-items-start">
                <!-- Action Icon -->
                <div class="col-auto">
                    <div class="audit-action-icon" data-action="{{ entry.action }}">
                        {% if entry.action == 'create' %}
                            <i class="fas fa-plus-circle text-success audit-icon" title="Created"></i>
                        {% elif entry.action == 'update' %}
                            <i class="fas fa-edit text-primary audit-icon" title="Updated"></i>
                        {% elif entry.action == 'delete' %}
                            <i class="fas fa-trash-alt text-danger audit-icon" title="Deleted"></i>
                        {% elif entry.action == 'cancel' %}
                            <i class="fas fa-times-circle text-warning audit-icon" title="Cancelled"></i>
                        {% else %}
                            <i class="fas fa-eye text-info audit-icon" title="Viewed"></i>
                        {% endif %}
                    </div>
                </div>

                <!-- Entry Content -->
                <div class="col">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <div class="flex-grow-1">
                            <h6 class="mb-1 audit-action-title">
                                {% if entry.action == 'create' %}
                                    Appointment Created
                                {% elif entry.action == 'update' %}
                                    Appointment Updated
                                {% elif entry.action == 'delete' %}
                                    Appointment Deleted
                                {% elif entry.action == 'cancel' %}
                                    Appointment Cancelled
                                {% else %}
                                    Appointment Accessed
                                {% endif %}
                            </h6>
                            
                            <!-- User and Timestamp Information -->
                            <div class="audit-meta text-muted mb-2">
                                <div class="d-flex flex-wrap align-items-center gap-3">
                                    <!-- User Information -->
                                    <div class="user-info d-flex align-items-center">
                                        <i class="fas fa-user me-1"></i>
                                        <span class="audit-user fw-medium">{{ entry.user_name or 'System' }}</span>
                                        {% if entry.user_role %}
                                            <span class="badge badge-role ms-2">{{ entry.user_role.title() }}</span>
                                        {% endif %}
                                    </div>
                                    
                                    <!-- Timestamp -->
                                    <div class="timestamp-info d-flex align-items-center">
                                        <i class="fas fa-clock me-1"></i>
                                        <span class="audit-timestamp" data-utc="{{ entry.timestamp_utc }}">
                                            {{ entry.timestamp_est }}
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- Quick Summary -->
                            <div class="audit-summary">
                                {% if entry.changes_summary %}
                                    <p class="mb-0 text-muted">{{ entry.changes_summary }}</p>
                                {% else %}
                                    <p class="mb-0 text-muted">
                                        {% if entry.action == 'create' %}
                                            Initial appointment creation
                                        {% elif entry.action == 'update' %}
                                            Appointment details modified
                                        {% elif entry.action == 'delete' %}
                                            Appointment removed from system
                                        {% elif entry.action == 'cancel' %}
                                            Appointment cancelled
                                        {% endif %}
                                    </p>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Details Toggle Button -->
                        {% if entry.has_changes or entry.action in ['create', 'update', 'cancel'] %}
                        <button class="btn btn-sm btn-outline-secondary audit-details-toggle ms-3" 
                                type="button" 
                                data-bs-toggle="collapse" 
                                data-bs-target="#auditDetails{{ entry.id }}"
                                aria-expanded="false"
                                aria-controls="auditDetails{{ entry.id }}">
                            <i class="fas fa-chevron-down"></i>
                            <span class="ms-1 d-none d-sm-inline">Details</span>
                        </button>
                        {% endif %}
                    </div>

                    <!-- Collapsible Details Section -->
                    {% if entry.has_changes or entry.action in ['create', 'update', 'cancel'] %}
                    <div class="collapse audit-details" id="auditDetails{{ entry.id }}">
                        <div class="card card-body bg-light border-0 mt-2">
                            <div class="audit-changes">
                                {% if entry.action == 'create' and entry.changes_detail %}
                                    <!-- Creation Details -->
                                    <div class="creation-details">
                                        <h6 class="mb-3 text-success">
                                            <i class="fas fa-plus-circle me-2"></i>
                                            Initial Values
                                        </h6>
                                        {% for change in entry.changes_detail %}
                                        {% if change.change_type == 'created' %}
                                        <div class="change-item mb-2">
                                            <div class="row align-items-center">
                                                <div class="col-md-3">
                                                    <strong class="change-field">{{ change.field_display }}</strong>
                                                </div>
                                                <div class="col-md-9">
                                                    <span class="badge bg-success">{{ change.new_value_display or 'Not set' }}</span>
                                                </div>
                                            </div>
                                        </div>
                                        {% endif %}
                                        {% endfor %}
                                    </div>
                                {% elif entry.action in ['update', 'cancel'] and entry.changes_detail %}
                                    <!-- Update/Cancel Details with Before/After Comparison -->
                                    <div class="update-details">
                                        <h6 class="mb-3 {% if entry.action == 'cancel' %}text-warning{% else %}text-primary{% endif %}">
                                            {% if entry.action == 'cancel' %}
                                                <i class="fas fa-times-circle me-2"></i>
                                                Cancellation Details
                                            {% else %}
                                                <i class="fas fa-edit me-2"></i>
                                                Changes Made
                                            {% endif %}
                                        </h6>
                                        {% for change in entry.changes_detail %}
                                        {% if change.change_type == 'updated' %}
                                        <div class="change-item mb-3" {% if change.field_category %}data-field-category="{{ change.field_category }}"{% endif %}>
                                            <div class="row">
                                                <div class="col-md-3">
                                                    <!-- Visual indicator for change type -->
                                                    {% if change.visual_indicator %}
                                                    <div class="change-indicator 
                                                        {% if change.field == 'status' %}status-change
                                                        {% elif 'time' in change.field %}time-change
                                                        {% elif change.field in ['tutor_id', 'client_id', 'dependant_id'] %}person-change
                                                        {% elif 'fee' in change.field or 'rate' in change.field %}financial-change
                                                        {% endif %}">
                                                        <i class="{{ change.visual_indicator.icon or 'fas fa-edit' }}"></i>
                                                        <span>
                                                            {% if change.field == 'status' %}Status
                                                            {% elif 'time' in change.field %}Time
                                                            {% elif change.field in ['tutor_id', 'client_id', 'dependant_id'] %}Person
                                                            {% elif 'fee' in change.field or 'rate' in change.field %}Financial
                                                            {% endif %}
                                                        </span>
                                                    </div>
                                                    {% endif %}
                                                    
                                                    <strong class="change-field">{{ change.field_display }}</strong>
                                                    
                                                    {% if change.description %}
                                                    <div class="text-muted small mt-1">{{ change.description }}</div>
                                                    {% endif %}
                                                </div>
                                                <div class="col-md-9">
                                                    <div class="change-comparison">
                                                        <!-- Before Value -->
                                                        <div class="old-value">
                                                            <div class="d-flex align-items-center">
                                                                <small class="text-muted me-2">Before:</small>
                                                                <span class="badge 
                                                                    {% if change.field == 'status' %}field-status
                                                                    {% elif 'time' in change.field %}field-time
                                                                    {% elif change.field in ['tutor_id', 'client_id', 'dependant_id'] %}field-person
                                                                    {% elif change.field == 'transport_fee' %}field-money
                                                                    {% endif %}">
                                                                    {{ change.old_value_display or 'Not set' }}
                                                                </span>
                                                            </div>
                                                        </div>
                                                        
                                                        <!-- Arrow Indicator -->
                                                        <div class="change-arrow">
                                                            <i class="fas fa-arrow-down"></i>
                                                        </div>
                                                        
                                                        <!-- After Value -->
                                                        <div class="new-value">
                                                            <div class="d-flex align-items-center">
                                                                <small class="text-muted me-2">After:</small>
                                                                <span class="badge 
                                                                    {% if change.field == 'status' %}field-status
                                                                    {% elif 'time' in change.field %}field-time
                                                                    {% elif change.field in ['tutor_id', 'client_id', 'dependant_id'] %}field-person
                                                                    {% elif change.field == 'transport_fee' %}field-money
                                                                    {% endif %}">
                                                                    {{ change.new_value_display or 'Not set' }}
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {% endif %}
                                        {% endfor %}
                                    </div>
                                {% elif entry.action == 'delete' %}
                                    <!-- Deletion Details -->
                                    <div class="deletion-details">
                                        <h6 class="mb-3 text-danger">
                                            <i class="fas fa-trash-alt me-2"></i>
                                            Deleted Information
                                        </h6>
                                        <div class="alert alert-danger">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            This appointment was permanently removed from the system.
                                        </div>
                                        {% if entry.changes_detail %}
                                        <div class="deleted-values mt-3">
                                            <small class="text-muted">Final values before deletion:</small>
                                            {% for change in entry.changes_detail %}
                                            <div class="change-item mb-1">
                                                <div class="row align-items-center">
                                                    <div class="col-md-3">
                                                        <small class="change-field">{{ change.field_display }}</small>
                                                    </div>
                                                    <div class="col-md-9">
                                                        <small class="text-muted">{{ change.old_value_display or 'Not set' }}</small>
                                                    </div>
                                                </div>
                                            </div>
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                    </div>
                                {% endif %}

                                <!-- Additional Context Information -->
                                {% if entry.context_info %}
                                <div class="context-info mt-3 pt-3 border-top">
                                    <h6 class="mb-2 text-muted">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Additional Context
                                    </h6>
                                    {% for key, value in entry.context_info.items() %}
                                    <div class="context-item mb-1">
                                        <small>
                                            <strong>{{ key|title|replace('_', ' ') }}:</strong> {{ value }}
                                        </small>
                                    </div>
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>