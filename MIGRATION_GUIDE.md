# TECFÉE Prospect System Migration Guide

This guide will help you safely migrate your PostgreSQL database to support the TECFÉE prospect system.

## 🚨 Important Notes

- **These scripts are safe to run multiple times** - they use `IF NOT EXISTS` and `ON CONFLICT` clauses
- **No single large transaction** - each operation is separate to avoid transaction abort errors
- **Graceful error handling** - missing foreign key tables are handled safely

## 📋 Prerequisites

1. PostgreSQL database running
2. Database user with CREATE TABLE privileges
3. Existing `clients` and `tutors` tables (if you want foreign key constraints)

## 🔧 Migration Steps

### Step 1: Run the Main Migration

This creates all the tables, indexes, and triggers:

```sql
-- Copy and paste the contents of app/migrations/safe_prospect_migration.sql
-- OR run it from a file:
\i app/migrations/safe_prospect_migration.sql
```

### Step 2: Insert TECFÉE Program Data

This adds the TECFÉE program with all modules and pricing:

```sql
-- Copy and paste the contents of app/migrations/safe_tecfee_data.sql
-- OR run it from a file:
\i app/migrations/safe_tecfee_data.sql
```

### Step 3: Verify the Migration

Run this query to check everything was created:

```sql
SELECT 'Programs' as table_name, COUNT(*) as count FROM programs WHERE code = 'TECFEE'
UNION ALL
SELECT 'Program Modules', COUNT(*) FROM program_modules WHERE program_id = (SELECT id FROM programs WHERE code = 'TECFEE')
UNION ALL
SELECT 'Program Pricing', COUNT(*) FROM program_pricing WHERE program_id = (SELECT id FROM programs WHERE code = 'TECFEE')
UNION ALL
SELECT 'Prospects', COUNT(*) FROM prospects
UNION ALL
SELECT 'Prospect Notes', COUNT(*) FROM prospect_notes;
```

Expected results:
- Programs: 1
- Program Modules: 9
- Program Pricing: 2
- Prospects: 3 (sample data)
- Prospect Notes: 2 (sample data)

## 🗂️ Tables Created

### Core Tables
- `prospects` - Store prospect information
- `prospect_notes` - Communication tracking
- `programs` - Program definitions
- `program_modules` - Program modules/lessons
- `program_pricing` - Pricing options

### Enrollment Tables
- `enrollments` - Client program enrollments
- `module_progress` - Progress through modules
- `group_sessions` - Group session scheduling
- `group_session_participants` - Session participants

## 🔗 Foreign Key Relationships

The migration handles missing tables gracefully:

- `prospects.converted_client_id` → `clients.id` (if clients table exists)
- `enrollments.client_id` → `clients.id` (if clients table exists)
- `group_sessions.tutor_id` → `tutors.id` (if tutors table exists)

## 🚀 After Migration

1. **Update your .env file** with correct database credentials
2. **Restart your Flask application**
3. **Test prospect enrollment** at `/public/tecfee/enrollment`
4. **Check manager dashboard** for TECFÉE management

## 🐛 Troubleshooting

### Error: "relation does not exist"
- Make sure you're connected to the correct database
- Check that you have CREATE privileges

### Error: "current transaction is aborted"
- Run `ROLLBACK;` first, then try again
- The new scripts avoid this issue by not using large transactions

### Error: "duplicate key value violates unique constraint"
- This is normal if running the script multiple times
- The scripts handle this gracefully with `ON CONFLICT` clauses

### Foreign Key Errors
- If you don't have `clients` or `tutors` tables yet, that's fine
- The foreign key constraints will be added when those tables exist
- You can manually add them later with:

```sql
-- Add foreign key constraints later if needed
ALTER TABLE prospects ADD CONSTRAINT fk_prospects_converted_client 
FOREIGN KEY (converted_client_id) REFERENCES clients(id) ON DELETE SET NULL;

ALTER TABLE enrollments ADD CONSTRAINT fk_enrollments_client 
FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE;

ALTER TABLE group_sessions ADD CONSTRAINT fk_group_sessions_tutor 
FOREIGN KEY (tutor_id) REFERENCES tutors(id) ON DELETE CASCADE;
```

## 📊 Sample Data

The migration includes sample prospect data for testing:
- 3 sample prospects interested in TECFÉE
- 2 sample communication notes
- Complete TECFÉE program with 9 modules
- 2 pricing options ($44.99 per session, $395 full package)

## 🔄 Re-running the Migration

These scripts are designed to be **idempotent** - you can run them multiple times safely:

- Tables use `CREATE TABLE IF NOT EXISTS`
- Data uses `ON CONFLICT DO UPDATE` or `ON CONFLICT DO NOTHING`
- Indexes use `CREATE INDEX IF NOT EXISTS`
- Triggers are dropped and recreated

## ✅ Success Indicators

After successful migration, you should be able to:

1. Visit `/public/tecfee/enrollment` without errors
2. See the TECFÉE program in your database
3. Register as a prospect and proceed to payment
4. Access manager views for prospect management

## 📞 Support

If you encounter issues:
1. Check the error message carefully
2. Verify database connection settings in `.env`
3. Ensure PostgreSQL version compatibility (9.5+)
4. Check that you have sufficient database privileges
