<!-- app/templates/client/related_clients.html -->
{% extends "base.html" %}

{% block title %}{{ t('clients.related.title') }} - TutorAide Inc.{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">{{ t('clients.related.title') }}</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('client.add_related_client') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> {{ t('clients.related.add_new') }}
        </a>
    </div>
</div>

{% if related_clients %}
    <div class="row">
        {% for relationship in related_clients %}
            {% set related = relationship.related_client %}
            <div class="col-md-4 mb-4">
                <div class="card shadow h-100">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">{{ related.first_name }} {{ related.last_name }}</h5>
                        <span class="badge bg-light text-primary">
                            {{ t('clients.relationship.' + relationship.relationship_type) }}
                        </span>
                    </div>
                    <div class="card-body">
                        <ul class="list-group list-group-flush">
                            {% if related.individual_clients and related.individual_clients.date_of_birth %}
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-birthday-cake me-2"></i> {{ t('clients.date_of_birth') }}</span>
                                    <span>{{ related.individual_clients.date_of_birth.strftime('%Y-%m-%d') }}</span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-user me-2"></i> {{ t('clients.age') }}</span>
                                    <span>{{ related.individual_clients.age }}</span>
                                </li>
                            {% endif %}

                            {% if relationship.is_primary %}
                                <li class="list-group-item">
                                    <span class="badge bg-success">{{ t('clients.primary') }}</span>
                                    <small class="text-muted ms-2">{{ t('clients.primary_explanation') }}</small>
                                </li>
                            {% endif %}

                            <!-- Program Enrollments -->
                            {% if related.enrollments.count() > 0 %}
                                <li class="list-group-item">
                                    <h6 class="mb-2">{{ t('programs.enrollments') }}</h6>
                                    {% for enrollment in related.enrollments %}
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span>{{ enrollment.program.name }}</span>
                                            <div>
                                                <div class="progress" style="width: 100px; height: 10px;">
                                                    <div class="progress-bar" role="progressbar"
                                                         style="width: {{ enrollment.completion_percentage or 0 }}%"></div>
                                                </div>
                                                <small class="text-muted">{{ (enrollment.completion_percentage or 0)|round|int }}%</small>
                                            </div>
                                        </div>
                                    {% endfor %}
                                </li>
                            {% endif %}

                            <!-- Upcoming Appointments -->
                            {% if related.appointments.filter_by(status='scheduled').count() > 0 %}
                                <li class="list-group-item">
                                    <h6 class="mb-2">{{ t('appointments.upcoming') }}</h6>
                                    {% for appointment in related.appointments.filter_by(status='scheduled').order_by('start_time').limit(3) %}
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span>{{ appointment.start_time.strftime('%b %d') }}</span>
                                            <span>{{ appointment.tutor_service.service.name }}</span>
                                        </div>
                                    {% endfor %}
                                </li>
                            {% endif %}
                        </ul>

                        {% if related.individual_clients and related.individual_clients.notes %}
                            <div class="mt-3">
                                <h6>{{ t('clients.notes') }}:</h6>
                                <p class="text-muted">{{ related.individual_clients.notes }}</p>
                            </div>
                        {% endif %}
                    </div>
                    <div class="card-footer d-flex justify-content-between">
                        <a href="{{ url_for('client.edit_related_client', id=related.id) }}" class="btn btn-outline-primary">
                            <i class="fas fa-edit"></i> {{ t('buttons.edit') }}
                        </a>
                        <a href="{{ url_for('client.appointments', client_id=related.id, status='upcoming') }}" class="btn btn-outline-info">
                            <i class="fas fa-calendar"></i> {{ t('buttons.view_schedule') }}
                        </a>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
{% else %}
    <div class="card shadow">
        <div class="card-body text-center p-5">
            <i class="fas fa-users fa-4x text-muted mb-3"></i>
            <h4 class="mb-3">{{ t('clients.related.none') }}</h4>
            <p class="mb-4">{{ t('clients.related.none_description') }}</p>
            <a href="{{ url_for('client.add_related_client') }}" class="btn btn-lg btn-primary">
                <i class="fas fa-plus"></i> {{ t('clients.related.add_first') }}
            </a>
        </div>
    </div>
{% endif %}
{% endblock %}
