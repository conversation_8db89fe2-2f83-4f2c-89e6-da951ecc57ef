#!/usr/bin/env python3
"""
Test script to verify API endpoints are working correctly.
Run this script to test the client/dependant search functionality.
"""

import requests
import json
import sys

def test_api_endpoint(base_url, endpoint, params=None):
    """Test an API endpoint and return the response."""
    url = f"{base_url}{endpoint}"
    
    try:
        print(f"\n🔍 Testing: {url}")
        if params:
            print(f"📋 Parameters: {params}")
        
        response = requests.get(url, params=params)
        
        print(f"📊 Status Code: {response.status_code}")
        print(f"📄 Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"✅ Success! Response: {json.dumps(data, indent=2)}")
                return True, data
            except json.JSONDecodeError:
                print(f"❌ Invalid JSON response: {response.text}")
                return False, response.text
        else:
            print(f"❌ Error: {response.status_code} - {response.text}")
            return False, response.text
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return False, str(e)

def main():
    # Default base URL - change this to match your application
    base_url = "http://localhost:5000/api"
    
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    
    print(f"🚀 Testing API endpoints at: {base_url}")
    print("=" * 60)
    
    # Test endpoints
    endpoints_to_test = [
        ("/clients-and-dependants/search", {"q": "da"}),
        ("/clients-and-dependants/search", {"q": "test"}),
        ("/dependants/search", {"q": "da"}),
        ("/clients/search", {"q": "da"}),
        ("/tutors", None),
    ]
    
    results = []
    
    for endpoint, params in endpoints_to_test:
        success, response = test_api_endpoint(base_url, endpoint, params)
        results.append((endpoint, success, response))
    
    print("\n" + "=" * 60)
    print("📋 SUMMARY:")
    print("=" * 60)
    
    for endpoint, success, response in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {endpoint}")
        if not success:
            print(f"    Error: {response}")
    
    print("\n💡 TROUBLESHOOTING TIPS:")
    print("- Make sure your Flask application is running")
    print("- Check if you're logged in as a manager")
    print("- Verify the base URL is correct")
    print("- Check the browser's developer console for errors")

if __name__ == "__main__":
    main()
