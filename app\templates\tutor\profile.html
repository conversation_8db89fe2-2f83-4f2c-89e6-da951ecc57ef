<!-- app/templates/tutor/profile.html -->
{% extends "base.html" %}

{% block title %}{{ t('tutor.profile.page_title') }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h2 class="mb-3">{{ t('tutor.profile.title') }}</h2>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">{{ t('tutor.profile.profile_information') }}</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('tutor.profile') }}">
                    {{ form.csrf_token }}

                    <div class="mb-3">
                        <label class="form-label">{{ t('tutor.profile.email_address') }}</label>
                        <input type="email" class="form-control" value="{{ current_user.email }}" disabled>
                        <div class="form-text">{{ t('tutor.profile.email_change_note') }}</div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">{{ t('tutor.profile.name') }}</label>
                        <input type="text" class="form-control" value="{{ tutor.first_name }} {{ tutor.last_name }}" disabled>
                        <div class="form-text">{{ t('tutor.profile.name_change_note') }}</div>
                    </div>

                    <div class="mb-3">
                        <label for="phone" class="form-label">{{ t('tutor.profile.phone_number') }}</label>
                        {{ form.phone(class="form-control" + (" is-invalid" if form.phone.errors else "")) }}
                        {% if form.phone.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.phone.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Address Information -->
                    <h5 class="mb-3 mt-4">{{ t('tutor.profile.address_information') }}</h5>

                    <div class="mb-3">
                        <label for="street_address" class="form-label">{{ t('tutor.profile.street_address') }}</label>
                        {{ form.street_address(class="form-control" + (" is-invalid" if form.street_address.errors else "")) }}
                        {% if form.street_address.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.street_address.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="city" class="form-label">{{ t('tutor.profile.city') }}</label>
                            {{ form.city(class="form-control" + (" is-invalid" if form.city.errors else "")) }}
                            {% if form.city.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.city.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="province" class="form-label">{{ t('tutor.profile.province_state') }}</label>
                            {{ form.province(class="form-control" + (" is-invalid" if form.province.errors else "")) }}
                            {% if form.province.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.province.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="zip_code" class="form-label">{{ t('tutor.profile.zip_postal_code') }}</label>
                            {{ form.zip_code(class="form-control" + (" is-invalid" if form.zip_code.errors else "")) }}
                            {% if form.zip_code.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.zip_code.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="country" class="form-label">{{ t('tutor.profile.country') }}</label>
                            {{ form.country(class="form-control" + (" is-invalid" if form.country.errors else "")) }}
                            {% if form.country.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.country.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Personal Information -->
                    <h5 class="mb-3 mt-4">{{ t('tutor.profile.personal_information') }}</h5>

                    <div class="mb-3">
                        <label for="birthdate" class="form-label">{{ t('tutor.profile.birthdate') }}</label>
                        {{ form.birthdate(class="form-control", type="date") }}
                        {% if form.birthdate.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.birthdate.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <!-- Banking Information -->
                    <h5 class="mb-3 mt-4">{{ t('tutor.profile.banking_information') }}</h5>

                    <div class="mb-3">
                        <label for="bank_transit_number" class="form-label">{{ t('tutor.profile.bank_transit_number') }}</label>
                        {{ form.bank_transit_number(class="form-control" + (" is-invalid" if form.bank_transit_number.errors else "")) }}
                        {% if form.bank_transit_number.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.bank_transit_number.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">{{ t('tutor.profile.bank_transit_help') }}</div>
                    </div>

                    <div class="mb-3">
                        <label for="bank_institution_number" class="form-label">{{ t('tutor.profile.bank_institution_number') }}</label>
                        {{ form.bank_institution_number(class="form-control" + (" is-invalid" if form.bank_institution_number.errors else "")) }}
                        {% if form.bank_institution_number.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.bank_institution_number.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">{{ t('tutor.profile.bank_institution_help') }}</div>
                    </div>

                    <div class="mb-3">
                        <label for="bank_account_number" class="form-label">{{ t('tutor.profile.bank_account_number') }}</label>
                        {{ form.bank_account_number(class="form-control" + (" is-invalid" if form.bank_account_number.errors else "")) }}
                        {% if form.bank_account_number.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.bank_account_number.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">{{ t('tutor.profile.bank_account_help') }}</div>
                    </div>

                    <!-- Professional Information -->
                    <h5 class="mb-3 mt-4">{{ t('tutor.profile.professional_information') }}</h5>

                    <div class="mb-3">
                        <label for="bio" class="form-label">{{ t('tutor.profile.bio') }}</label>
                        {{ form.bio(class="form-control", rows=5) }}
                        <div class="form-text">{{ t('tutor.profile.bio_help') }}</div>
                    </div>

                    <div class="mb-3">
                        <label for="qualifications" class="form-label">{{ t('tutor.profile.qualifications') }}</label>
                        {{ form.qualifications(class="form-control", rows=3) }}
                        <div class="form-text">{{ t('tutor.profile.qualifications_help') }}</div>
                    </div>

                    <hr class="my-4">

                    <h5 class="mb-3">{{ t('tutor.profile.change_password') }}</h5>
                    <div class="mb-3">
                        <label for="password" class="form-label">{{ t('tutor.profile.new_password') }}</label>
                        {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else "")) }}
                        {% if form.password.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.password.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">{{ t('tutor.profile.password_blank_note') }}</div>
                    </div>

                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">{{ t('tutor.profile.confirm_new_password') }}</label>
                        {{ form.confirm_password(class="form-control" + (" is-invalid" if form.confirm_password.errors else "")) }}
                        {% if form.confirm_password.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.confirm_password.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="d-grid">
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}