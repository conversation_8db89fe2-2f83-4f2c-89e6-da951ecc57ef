<!-- Design Showcase for Feminine Client Interface -->
{% extends "base.html" %}

{% block title %}Design Showcase - TutorAide Inc.{% endblock %}

{% block content %}
<!-- Welcome Section -->
<div class="dashboard-welcome fade-in-up">
    <div class="row align-items-center">
        <div class="col-md-8">
            <h2 class="mb-3">Feminine & Sophisticated Design ✨</h2>
            <p class="text-muted">Experience the new elegant, pastel-themed client interface</p>
        </div>
        <div class="col-md-4 text-end">
            <i class="fas fa-palette" style="font-size: 3rem; color: var(--primary-blush-dark); opacity: 0.3;"></i>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="stat-card fade-in-up">
            <div class="stat-number">12</div>
            <div class="stat-label">Appointments</div>
            <i class="fas fa-calendar-heart" style="color: var(--lavender-dark); font-size: 1.5rem; margin-top: 8px;"></i>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stat-card fade-in-up" style="animation-delay: 0.1s;">
            <div class="stat-number">3</div>
            <div class="stat-label">Pending Invoices</div>
            <i class="fas fa-file-invoice" style="color: var(--peach-dark); font-size: 1.5rem; margin-top: 8px;"></i>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stat-card fade-in-up" style="animation-delay: 0.2s;">
            <div class="stat-number">2</div>
            <div class="stat-label">Active Plans</div>
            <i class="fas fa-star" style="color: var(--mint-dark); font-size: 1.5rem; margin-top: 8px;"></i>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stat-card fade-in-up" style="animation-delay: 0.3s;">
            <div class="stat-number">5</div>
            <div class="stat-label">Family Members</div>
            <i class="fas fa-users" style="color: var(--dusty-rose); font-size: 1.5rem; margin-top: 8px;"></i>
        </div>
    </div>
</div>

<!-- Color Palette Showcase -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-paint-brush"></i>
                    Pastel Color Palette
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2 mb-3 text-center">
                        <div style="height: 80px; background: var(--primary-blush); border-radius: 15px; margin-bottom: 8px;"></div>
                        <small class="fw-bold">Blush Pink</small>
                    </div>
                    <div class="col-md-2 mb-3 text-center">
                        <div style="height: 80px; background: var(--lavender); border-radius: 15px; margin-bottom: 8px;"></div>
                        <small class="fw-bold">Lavender</small>
                    </div>
                    <div class="col-md-2 mb-3 text-center">
                        <div style="height: 80px; background: var(--mint); border-radius: 15px; margin-bottom: 8px;"></div>
                        <small class="fw-bold">Mint Green</small>
                    </div>
                    <div class="col-md-2 mb-3 text-center">
                        <div style="height: 80px; background: var(--peach); border-radius: 15px; margin-bottom: 8px;"></div>
                        <small class="fw-bold">Soft Peach</small>
                    </div>
                    <div class="col-md-2 mb-3 text-center">
                        <div style="height: 80px; background: var(--rose-gold); border-radius: 15px; margin-bottom: 8px;"></div>
                        <small class="fw-bold">Rose Gold</small>
                    </div>
                    <div class="col-md-2 mb-3 text-center">
                        <div style="height: 80px; background: var(--cream); border-radius: 15px; margin-bottom: 8px;"></div>
                        <small class="fw-bold">Cream</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Button Showcase -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-mouse-pointer"></i>
                    Button Styles
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex flex-wrap gap-2 mb-3">
                    <button class="btn btn-primary">Primary Button</button>
                    <button class="btn btn-success">Success Button</button>
                    <button class="btn btn-outline-primary">Outline Button</button>
                </div>
                <div class="d-flex flex-wrap gap-2">
                    <button class="btn btn-primary btn-sm">Small Button</button>
                    <button class="btn btn-success btn-lg">Large Button</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Form Elements -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit"></i>
                    Form Elements
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">Sample Input</label>
                    <input type="text" class="form-control" placeholder="Enter text here...">
                </div>
                <div class="mb-3">
                    <label class="form-label">Select Option</label>
                    <select class="form-control">
                        <option>Choose an option</option>
                        <option>Option 1</option>
                        <option>Option 2</option>
                    </select>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Alert Showcase -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle"></i>
                    Alert Messages
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <strong>Success!</strong> Your appointment has been scheduled successfully.
                </div>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Information:</strong> Your next session is tomorrow at 2:00 PM.
                </div>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Warning:</strong> Your subscription expires in 3 days.
                </div>
                <div class="alert alert-danger">
                    <i class="fas fa-times-circle"></i>
                    <strong>Error:</strong> Payment could not be processed. Please try again.
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Badge Showcase -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-tags"></i>
                    Status Badges
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex flex-wrap gap-2">
                    <span class="badge bg-success">Completed</span>
                    <span class="badge bg-warning">Pending</span>
                    <span class="badge bg-danger">Cancelled</span>
                    <span class="badge" style="background: var(--lavender-dark);">Scheduled</span>
                    <span class="badge" style="background: var(--mint-dark);">Active</span>
                    <span class="badge" style="background: var(--peach-dark); color: var(--charcoal);">In Progress</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Typography Showcase -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-font"></i>
                    Typography
                </h5>
            </div>
            <div class="card-body">
                <h1 style="font-family: var(--font-elegant); color: var(--charcoal);">Elegant Heading</h1>
                <h3 style="font-family: var(--font-modern); color: var(--warm-gray);">Modern Subheading</h3>
                <p style="font-family: var(--font-accent); color: var(--primary-blush-dark); font-size: 1.2rem;">Accent Script Text</p>
                <p>Regular body text with modern font family for excellent readability and professional appearance.</p>
            </div>
        </div>
    </div>
</div>

<!-- Sample Table -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-table"></i>
                    Sample Data Table
                </h5>
            </div>
            <div class="card-body p-0">
                <table class="table mb-0">
                    <thead>
                        <tr>
                            <th>Appointment</th>
                            <th>Date</th>
                            <th>Tutor</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Mathematics Tutoring</td>
                            <td>Dec 15, 2024</td>
                            <td>Sarah Johnson</td>
                            <td><span class="badge bg-success">Completed</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary">View</button>
                            </td>
                        </tr>
                        <tr>
                            <td>French Conversation</td>
                            <td>Dec 18, 2024</td>
                            <td>Marie Dubois</td>
                            <td><span class="badge bg-warning">Pending</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary">View</button>
                            </td>
                        </tr>
                        <tr>
                            <td>TECFÉE Preparation</td>
                            <td>Dec 20, 2024</td>
                            <td>Jean-Pierre Martin</td>
                            <td><span class="badge" style="background: var(--lavender-dark);">Scheduled</span></td>
                            <td>
                                <button class="btn btn-sm btn-outline-primary">View</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Design Features -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-sparkles"></i>
                    Design Features
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="text-center">
                            <i class="fas fa-palette fa-2x" style="color: var(--primary-blush-dark); margin-bottom: 12px;"></i>
                            <h6>Pastel Color Palette</h6>
                            <p class="small text-muted">Soft, feminine colors that create a calming and sophisticated atmosphere</p>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="text-center">
                            <i class="fas fa-magic fa-2x" style="color: var(--lavender-dark); margin-bottom: 12px;"></i>
                            <h6>Elegant Animations</h6>
                            <p class="small text-muted">Smooth transitions and gentle hover effects for enhanced user experience</p>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="text-center">
                            <i class="fas fa-heart fa-2x" style="color: var(--dusty-rose); margin-bottom: 12px;"></i>
                            <h6>Thoughtful Details</h6>
                            <p class="small text-muted">Rounded corners, soft shadows, and refined typography for a premium feel</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
