<!-- app/templates/manager/tutor_detail.html -->
{% extends "base.html" %}

{% block title %}Tutor Details - Tutoring Appointment System{% endblock %}

{% block content %}
<div class="page-header fade-in-up">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>{{ tutor.first_name }} {{ tutor.last_name }}</h1>
            <p>Tutor Profile and Management</p>
        </div>
        <div class="action-buttons">
            <a href="{{ url_for('manager.tutors_list') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Tutors
            </a>
            <a href="{{ url_for('manager.edit_tutor', id=tutor.tutor_id) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
            <div class="dropdown d-inline-block">
                <button class="btn btn-secondary dropdown-toggle" type="button" id="manageDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-cog"></i> Manage
                </button>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="manageDropdown">
                    <li><a class="dropdown-item" href="{{ url_for('manager.new_tutor_service') }}?tutor_id={{ tutor.tutor_id }}">
                        <i class="fas fa-plus-circle"></i> Add Service
                    </a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item" href="#availability-section">
                        <i class="fas fa-calendar-alt"></i> View Availability
                    </a></li>
                    <li><a class="dropdown-item" href="#service-rates-section">
                        <i class="fas fa-dollar-sign"></i> View Service Rates
                    </a></li>
                </ul>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Tutor Information -->
    <div class="col-md-6 mb-4">
        <div class="card h-100 fade-in-up">
            <div class="card-header">
                <h5 class="mb-0">Tutor Information</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6 class="text-muted mb-1">Email</h6>
                    <p>{{ tutor.user.email }}</p>
                </div>

                <div class="mb-3">
                    <h6 class="text-muted mb-1">Phone</h6>
                    <p>{{ tutor.phone }}</p>
                </div>

                <!-- Address Information -->
                {% if tutor.street_address or tutor.city or tutor.province or tutor.zip_code or tutor.country %}
                <div class="mb-3">
                    <h6 class="text-muted mb-1">Address</h6>
                    <p>
                        {% if tutor.street_address %}{{ tutor.street_address }}<br>{% endif %}
                        {% if tutor.city or tutor.province or tutor.zip_code %}
                            {% if tutor.city %}{{ tutor.city }}{% endif %}
                            {% if tutor.province %}{% if tutor.city %}, {% endif %}{{ tutor.province }}{% endif %}
                            {% if tutor.zip_code %} {{ tutor.zip_code }}{% endif %}<br>
                        {% endif %}
                        {% if tutor.country %}{{ tutor.country }}{% endif %}
                    </p>
                </div>
                {% endif %}

                <!-- Personal Information -->
                {% if tutor.birthdate %}
                <div class="mb-3">
                    <h6 class="text-muted mb-1">Birthdate</h6>
                    <p>{{ tutor.birthdate.strftime('%B %d, %Y') }}</p>
                </div>
                {% endif %}

                <!-- Banking Information -->
                {% if tutor.bank_transit_number_encrypted or tutor.bank_institution_number_encrypted or tutor.bank_account_number_encrypted %}
                <div class="mb-3">
                    <h6 class="text-muted mb-1">Banking Information</h6>
                    <p>
                        {% if tutor.bank_transit_number_encrypted %}
                            <strong>Transit #:</strong> {{ tutor.get_bank_transit_number() }}<br>
                        {% endif %}
                        {% if tutor.bank_institution_number_encrypted %}
                            <strong>Institution #:</strong> {{ tutor.get_bank_institution_number() }}<br>
                        {% endif %}
                        {% if tutor.bank_account_number_encrypted %}
                            <strong>Account #:</strong> {{ tutor.get_bank_account_number() }}
                        {% endif %}
                    </p>
                </div>
                {% endif %}

                <div class="mb-3">
                    <h6 class="text-muted mb-1">Status</h6>
                    <p>
                        <span class="badge bg-{{ 'success' if tutor.is_active else 'danger' }} p-2">
                            {{ 'Active' if tutor.is_active else 'Inactive' }}
                        </span>
                    </p>
                </div>

                {% if tutor.bio %}
                    <div class="mb-3">
                        <h6 class="text-muted mb-1">Bio</h6>
                        <p>{{ tutor.bio }}</p>
                    </div>
                {% endif %}

                {% if tutor.qualifications %}
                    <div class="mb-3">
                        <h6 class="text-muted mb-1">Qualifications</h6>
                        <p>{{ tutor.qualifications }}</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Tutor Services -->
    <div class="col-md-6 mb-4">
        <div class="card shadow h-100">
            <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Services Offered</h5>
                <a href="{{ url_for('manager.new_tutor_service') }}?tutor_id={{ tutor.tutor_id }}" class="btn btn-sm btn-light">
                    <i class="fas fa-plus"></i> Add Service
                </a>
            </div>
            <div class="card-body">
                {% if tutor_services %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Service</th>
                                    <th>Tutor Rate</th>
                                    <th>Client Rate</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for ts in tutor_services %}
                                    <tr>
                                        <td>{{ ts.service.name }}</td>
                                        <td>${{ "%.2f"|format(ts.custom_rate or 0) }}</td>
                                        <td>${{ "%.2f"|format(ts.custom_rate or 0) }}</td>
                                        <td>
                                            <span class="badge bg-{{ 'success' if ts.is_active else 'danger' }}">
                                                {{ 'Active' if ts.is_active else 'Inactive' }}
                                            </span>
                                        </td>
                                        <td>
                                            <a href="{{ url_for('manager.edit_tutor_service', id=ts.id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center my-4">No services assigned to this tutor yet.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Tutor Availability -->
<div class="row" id="availability-section">
    <div class="col-md-6 mb-4">
        <div class="card shadow h-100">
            <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Availability</h5>
                <a href="{{ url_for('manager_tutor_profile.tutor_availability', tutor_id=tutor.tutor_id) }}" class="btn btn-sm btn-light">
                    <i class="fas fa-edit"></i> Manage
                </a>
            </div>
            <div class="card-body">
                {% if availabilities %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Day</th>
                                    <th>Time</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for availability in availabilities %}
                                    <tr>
                                        <td>{{ availability.day_name }}</td>
                                        <td>{{ availability.time_range }}</td>
                                        <td>
                                            <span class="badge bg-{{ 'success' if availability.is_active else 'secondary' }}">
                                                {{ 'Active' if availability.is_active else 'Inactive' }}
                                            </span>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center my-4">No availability set for this tutor.</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Tutor Service Rates -->
    <div class="col-md-6 mb-4" id="service-rates-section">
        <div class="card shadow h-100">
            <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Service Rates</h5>
                <a href="{{ url_for('manager_tutor_profile.tutor_service_rates', tutor_id=tutor.tutor_id) }}" class="btn btn-sm btn-light">
                    <i class="fas fa-edit"></i> Manage
                </a>
            </div>
            <div class="card-body">
                {% if service_rates %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Service</th>
                                    <th>Tutor Rate</th>
                                    <th>Client Rate</th>
                                    <th>Transport Fee</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for rate in service_rates %}
                                    <tr>
                                        <td>{{ rate.service.name }}</td>
                                        <td>${{ "%.2f"|format(rate.custom_rate or 0) }}</td>
                                        <td>${{ "%.2f"|format(rate.custom_rate or 0) }}</td>
                                        <td>
                                            {% if rate.transport_fee and rate.transport_fee > 0 %}
                                                ${{ "%.2f"|format(rate.transport_fee) }}
                                                {% if rate.transport_fee_description %}
                                                    <small class="d-block text-muted">{{ rate.transport_fee_description }}</small>
                                                {% endif %}
                                            {% else %}
                                                -
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center my-4">No service rates set for this tutor.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Upcoming Appointments -->
<div class="card shadow mb-4">
    <div class="card-header bg-light">
        <h5 class="mb-0">Upcoming Appointments</h5>
    </div>
    <div class="card-body">
        {% if upcoming_appointments %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Date & Time</th>
                            <th>Client</th>
                            <th>Service</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for appointment in upcoming_appointments %}
                            <tr>
                                <td>
                                    {{ appointment.start_time.strftime('%Y-%m-%d') }}<br>
                                    <small>{{ appointment.start_time.strftime('%I:%M %p') }} - {{ appointment.end_time.strftime('%I:%M %p') }}</small>
                                </td>
                                <td>{{ appointment.client.first_name }} {{ appointment.client.last_name }}</td>
                                <td>
                                    {% if appointment.tutor_service and appointment.tutor_service.service %}
                                        {{ appointment.tutor_service.service.name }}
                                    {% else %}
                                        N/A
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'primary' if appointment.status == 'scheduled' else 'success' if appointment.status == 'completed' else 'danger' if appointment.status == 'cancelled' else 'warning' }}">
                                        {{ appointment.status | capitalize }}
                                    </span>
                                </td>
                                <td>
                                    <div class="d-flex flex-column">
                                        <div class="btn-group mb-1">
                                            <a href="{{ url_for('manager.edit_appointment', id=appointment.id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i> Edit
                                            </a>
                                            <a href="{{ url_for('manager.view_appointment', id=appointment.id) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                        </div>
                                        {% if appointment.status == 'scheduled' %}
                                        <form method="POST" action="{{ url_for('manager.take_attendance', id=appointment.id) }}">
                                            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                            <button type="submit" class="btn btn-sm btn-success w-100">
                                                <i class="fas fa-check-circle"></i> Take Attendance
                                            </button>
                                        </form>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <p class="text-muted text-center my-4">No upcoming appointments for this tutor.</p>
        {% endif %}
    </div>
</div>
{% endblock %}