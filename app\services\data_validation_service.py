#!/usr/bin/env python3
"""
Data Validation Service for TutorAide Application
Comprehensive validation and integrity checking for all database entities
"""

from datetime import datetime, date, timedelta
from typing import Dict, List, Tuple, Any, Optional
from sqlalchemy import text, and_, or_
from sqlalchemy.exc import SQLAlchemyError
from app.extensions import db
import logging

class DataValidationService:
    """Service for comprehensive data validation and integrity checking."""
    
    @staticmethod
    def validate_all_data() -> Dict[str, Any]:
        """
        Run comprehensive validation on all database entities.
        Returns detailed report of all validation issues found.
        """
        validation_report = {
            'timestamp': datetime.utcnow().isoformat(),
            'summary': {
                'total_checks': 0,
                'passed_checks': 0,
                'failed_checks': 0,
                'warnings': 0,
                'critical_issues': 0
            },
            'results': {},
            'recommendations': []
        }
        
        try:
            # Core entity validations
            validation_report['results']['users'] = DataValidationService._validate_users()
            validation_report['results']['clients'] = DataValidationService._validate_clients()
            validation_report['results']['tutors'] = DataValidationService._validate_tutors()
            validation_report['results']['dependants'] = DataValidationService._validate_dependants()
            validation_report['results']['services'] = DataValidationService._validate_services()
            validation_report['results']['appointments'] = DataValidationService._validate_appointments()
            validation_report['results']['recurring_schedules'] = DataValidationService._validate_recurring_schedules()
            validation_report['results']['subscriptions'] = DataValidationService._validate_subscriptions()
            validation_report['results']['invoices'] = DataValidationService._validate_invoices()
            validation_report['results']['programs'] = DataValidationService._validate_programs()
            
            # Relationship integrity validations
            validation_report['results']['foreign_keys'] = DataValidationService._validate_foreign_key_integrity()
            validation_report['results']['business_rules'] = DataValidationService._validate_business_rules()
            validation_report['results']['data_consistency'] = DataValidationService._validate_data_consistency()
            
            # Calculate summary statistics
            DataValidationService._calculate_summary_statistics(validation_report)
            
            # Generate recommendations
            DataValidationService._generate_recommendations(validation_report)
            
        except Exception as e:
            validation_report['error'] = f"Validation failed with error: {str(e)}"
            logging.error(f"Data validation failed: {e}")
        
        return validation_report
    
    @staticmethod
    def _validate_users() -> Dict[str, Any]:
        """Validate users table data integrity."""
        issues = []
        warnings = []
        
        try:
            # Check for duplicate emails
            duplicate_emails = db.session.execute(text("""
                SELECT email, COUNT(*) as count 
                FROM users 
                GROUP BY email 
                HAVING COUNT(*) > 1
            """)).fetchall()
            
            if duplicate_emails:
                issues.append({
                    'type': 'duplicate_emails',
                    'severity': 'critical',
                    'count': len(duplicate_emails),
                    'details': [{'email': row[0], 'count': row[1]} for row in duplicate_emails]
                })
            
            # Check for invalid email formats
            invalid_emails = db.session.execute(text("""
                SELECT user_id, email 
                FROM users 
                WHERE email !~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$'
            """)).fetchall()
            
            if invalid_emails:
                issues.append({
                    'type': 'invalid_email_format',
                    'severity': 'high',
                    'count': len(invalid_emails),
                    'details': [{'user_id': row[0], 'email': row[1]} for row in invalid_emails]
                })
            
            # Check for invalid roles
            invalid_roles = db.session.execute(text("""
                SELECT user_id, role 
                FROM users 
                WHERE role NOT IN ('manager', 'tutor', 'client', 'parent')
            """)).fetchall()
            
            if invalid_roles:
                issues.append({
                    'type': 'invalid_roles',
                    'severity': 'high',
                    'count': len(invalid_roles),
                    'details': [{'user_id': row[0], 'role': row[1]} for row in invalid_roles]
                })
            
            # Check for users without password hash
            missing_passwords = db.session.execute(text("""
                SELECT user_id, email 
                FROM users 
                WHERE password_hash IS NULL OR password_hash = ''
            """)).fetchall()
            
            if missing_passwords:
                issues.append({
                    'type': 'missing_password_hash',
                    'severity': 'critical',
                    'count': len(missing_passwords),
                    'details': [{'user_id': row[0], 'email': row[1]} for row in missing_passwords]
                })
            
            # Check for inactive users with recent activity
            inactive_with_activity = db.session.execute(text("""
                SELECT u.user_id, u.email, u.modification_date
                FROM users u
                WHERE u.is_active = FALSE 
                AND u.modification_date > CURRENT_DATE - INTERVAL '30 days'
            """)).fetchall()
            
            if inactive_with_activity:
                warnings.append({
                    'type': 'inactive_users_recent_activity',
                    'severity': 'medium',
                    'count': len(inactive_with_activity),
                    'details': [{'user_id': row[0], 'email': row[1], 'last_modified': row[2]} for row in inactive_with_activity]
                })
            
        except Exception as e:
            issues.append({
                'type': 'validation_error',
                'severity': 'critical',
                'message': f"Error validating users: {str(e)}"
            })
        
        return {
            'table': 'users',
            'issues': issues,
            'warnings': warnings,
            'total_issues': len(issues),
            'total_warnings': len(warnings)
        }
    
    @staticmethod
    def _validate_clients() -> Dict[str, Any]:
        """Validate clients table data integrity."""
        issues = []
        warnings = []
        
        try:
            # Check for clients without associated users
            orphaned_clients = db.session.execute(text("""
                SELECT c.client_id, c.first_name, c.last_name
                FROM clients c
                LEFT JOIN users u ON c.user_id = u.user_id
                WHERE u.user_id IS NULL
            """)).fetchall()
            
            if orphaned_clients:
                issues.append({
                    'type': 'orphaned_clients',
                    'severity': 'high',
                    'count': len(orphaned_clients),
                    'details': [{'client_id': row[0], 'name': f"{row[1]} {row[2]}"} for row in orphaned_clients]
                })
            
            # Check for missing required fields
            missing_names = db.session.execute(text("""
                SELECT client_id, first_name, last_name
                FROM clients
                WHERE first_name IS NULL OR first_name = '' 
                OR last_name IS NULL OR last_name = ''
            """)).fetchall()
            
            if missing_names:
                issues.append({
                    'type': 'missing_required_names',
                    'severity': 'high',
                    'count': len(missing_names),
                    'details': [{'client_id': row[0], 'first_name': row[1], 'last_name': row[2]} for row in missing_names]
                })
            
            # Check for invalid client types
            invalid_types = db.session.execute(text("""
                SELECT client_id, client_type
                FROM clients
                WHERE client_type NOT IN ('individual', 'institutional')
            """)).fetchall()
            
            if invalid_types:
                issues.append({
                    'type': 'invalid_client_types',
                    'severity': 'medium',
                    'count': len(invalid_types),
                    'details': [{'client_id': row[0], 'client_type': row[1]} for row in invalid_types]
                })
            
            # Check for invalid phone number formats
            invalid_phones = db.session.execute(text("""
                SELECT client_id, phone
                FROM clients
                WHERE phone IS NOT NULL 
                AND phone != ''
                AND phone !~ '^[\\+]?[1-9][\\d]{0,15}$'
            """)).fetchall()
            
            if invalid_phones:
                warnings.append({
                    'type': 'invalid_phone_formats',
                    'severity': 'low',
                    'count': len(invalid_phones),
                    'details': [{'client_id': row[0], 'phone': row[1]} for row in invalid_phones]
                })
            
            # Check for future birth dates
            future_births = db.session.execute(text("""
                SELECT client_id, first_name, last_name, date_of_birth
                FROM clients
                WHERE date_of_birth > CURRENT_DATE
            """)).fetchall()
            
            if future_births:
                issues.append({
                    'type': 'future_birth_dates',
                    'severity': 'medium',
                    'count': len(future_births),
                    'details': [{'client_id': row[0], 'name': f"{row[1]} {row[2]}", 'birth_date': row[3]} for row in future_births]
                })
            
        except Exception as e:
            issues.append({
                'type': 'validation_error',
                'severity': 'critical',
                'message': f"Error validating clients: {str(e)}"
            })
        
        return {
            'table': 'clients',
            'issues': issues,
            'warnings': warnings,
            'total_issues': len(issues),
            'total_warnings': len(warnings)
        }
    
    @staticmethod
    def _validate_tutors() -> Dict[str, Any]:
        """Validate tutors table data integrity."""
        issues = []
        warnings = []
        
        try:
            # Check for tutors without associated users
            orphaned_tutors = db.session.execute(text("""
                SELECT t.tutor_id, t.first_name, t.last_name
                FROM tutors t
                LEFT JOIN users u ON t.user_id = u.user_id
                WHERE u.user_id IS NULL
            """)).fetchall()
            
            if orphaned_tutors:
                issues.append({
                    'type': 'orphaned_tutors',
                    'severity': 'high',
                    'count': len(orphaned_tutors),
                    'details': [{'tutor_id': row[0], 'name': f"{row[1]} {row[2]}"} for row in orphaned_tutors]
                })
            
            # Check for negative hourly rates
            negative_rates = db.session.execute(text("""
                SELECT tutor_id, first_name, last_name, hourly_rate
                FROM tutors
                WHERE hourly_rate < 0
            """)).fetchall()
            
            if negative_rates:
                issues.append({
                    'type': 'negative_hourly_rates',
                    'severity': 'high',
                    'count': len(negative_rates),
                    'details': [{'tutor_id': row[0], 'name': f"{row[1]} {row[2]}", 'rate': row[3]} for row in negative_rates]
                })
            
            # Check for extremely high hourly rates (potential data entry errors)
            high_rates = db.session.execute(text("""
                SELECT tutor_id, first_name, last_name, hourly_rate
                FROM tutors
                WHERE hourly_rate > 500
            """)).fetchall()
            
            if high_rates:
                warnings.append({
                    'type': 'unusually_high_rates',
                    'severity': 'low',
                    'count': len(high_rates),
                    'details': [{'tutor_id': row[0], 'name': f"{row[1]} {row[2]}", 'rate': row[3]} for row in high_rates]
                })
            
            # Check for active tutors without any services
            tutors_no_services = db.session.execute(text("""
                SELECT t.tutor_id, t.first_name, t.last_name
                FROM tutors t
                LEFT JOIN tutor_services ts ON t.tutor_id = ts.tutor_id AND ts.is_active = TRUE
                WHERE t.is_active = TRUE AND ts.tutor_id IS NULL
            """)).fetchall()
            
            if tutors_no_services:
                warnings.append({
                    'type': 'active_tutors_no_services',
                    'severity': 'medium',
                    'count': len(tutors_no_services),
                    'details': [{'tutor_id': row[0], 'name': f"{row[1]} {row[2]}"} for row in tutors_no_services]
                })
            
        except Exception as e:
            issues.append({
                'type': 'validation_error',
                'severity': 'critical',
                'message': f"Error validating tutors: {str(e)}"
            })
        
        return {
            'table': 'tutors',
            'issues': issues,
            'warnings': warnings,
            'total_issues': len(issues),
            'total_warnings': len(warnings)
        }
    
    @staticmethod
    def _validate_dependants() -> Dict[str, Any]:
        """Validate dependants table data integrity."""
        issues = []
        warnings = []
        
        try:
            # Check for dependants without associated clients
            orphaned_dependants = db.session.execute(text("""
                SELECT d.dependant_id, d.first_name, d.last_name
                FROM dependants d
                LEFT JOIN clients c ON d.client_id = c.client_id
                WHERE c.client_id IS NULL
            """)).fetchall()
            
            if orphaned_dependants:
                issues.append({
                    'type': 'orphaned_dependants',
                    'severity': 'high',
                    'count': len(orphaned_dependants),
                    'details': [{'dependant_id': row[0], 'name': f"{row[1]} {row[2]}"} for row in orphaned_dependants]
                })
            
            # Check for future birth dates
            future_births = db.session.execute(text("""
                SELECT dependant_id, first_name, last_name, date_of_birth
                FROM dependants
                WHERE date_of_birth > CURRENT_DATE
            """)).fetchall()
            
            if future_births:
                issues.append({
                    'type': 'future_birth_dates',
                    'severity': 'medium',
                    'count': len(future_births),
                    'details': [{'dependant_id': row[0], 'name': f"{row[1]} {row[2]}", 'birth_date': row[3]} for row in future_births]
                })
            
            # Check for very old birth dates (potential data entry errors)
            old_births = db.session.execute(text("""
                SELECT dependant_id, first_name, last_name, date_of_birth
                FROM dependants
                WHERE date_of_birth < CURRENT_DATE - INTERVAL '25 years'
            """)).fetchall()
            
            if old_births:
                warnings.append({
                    'type': 'unusually_old_dependants',
                    'severity': 'low',
                    'count': len(old_births),
                    'details': [{'dependant_id': row[0], 'name': f"{row[1]} {row[2]}", 'birth_date': row[3]} for row in old_births]
                })
            
        except Exception as e:
            issues.append({
                'type': 'validation_error',
                'severity': 'critical',
                'message': f"Error validating dependants: {str(e)}"
            })
        
        return {
            'table': 'dependants',
            'issues': issues,
            'warnings': warnings,
            'total_issues': len(issues),
            'total_warnings': len(warnings)
        }
    
    @staticmethod
    def _validate_services() -> Dict[str, Any]:
        """Validate services and tutor_services table data integrity."""
        issues = []
        warnings = []
        
        try:
            # Check for services with negative rates
            negative_rates = db.session.execute(text("""
                SELECT service_id, name, base_rate
                FROM services
                WHERE base_rate < 0
            """)).fetchall()
            
            if negative_rates:
                issues.append({
                    'type': 'negative_service_rates',
                    'severity': 'high',
                    'count': len(negative_rates),
                    'details': [{'service_id': row[0], 'name': row[1], 'rate': row[2]} for row in negative_rates]
                })
            
            # Check for tutor services with invalid references
            invalid_tutor_services = db.session.execute(text("""
                SELECT ts.tutor_service_id, ts.tutor_id, ts.service_id
                FROM tutor_services ts
                LEFT JOIN tutors t ON ts.tutor_id = t.tutor_id
                LEFT JOIN services s ON ts.service_id = s.service_id
                WHERE t.tutor_id IS NULL OR s.service_id IS NULL
            """)).fetchall()
            
            if invalid_tutor_services:
                issues.append({
                    'type': 'invalid_tutor_service_references',
                    'severity': 'high',
                    'count': len(invalid_tutor_services),
                    'details': [{'tutor_service_id': row[0], 'tutor_id': row[1], 'service_id': row[2]} for row in invalid_tutor_services]
                })
            
            # Check for negative transport fees
            negative_transport = db.session.execute(text("""
                SELECT tutor_service_id, tutor_id, service_id, transport_fee
                FROM tutor_services
                WHERE transport_fee < 0
            """)).fetchall()
            
            if negative_transport:
                issues.append({
                    'type': 'negative_transport_fees',
                    'severity': 'medium',
                    'count': len(negative_transport),
                    'details': [{'tutor_service_id': row[0], 'tutor_id': row[1], 'service_id': row[2], 'transport_fee': row[3]} for row in negative_transport]
                })
            
        except Exception as e:
            issues.append({
                'type': 'validation_error',
                'severity': 'critical',
                'message': f"Error validating services: {str(e)}"
            })
        
        return {
            'table': 'services',
            'issues': issues,
            'warnings': warnings,
            'total_issues': len(issues),
            'total_warnings': len(warnings)
        }
    
    @staticmethod
    def _validate_appointments() -> Dict[str, Any]:
        """Validate appointments table data integrity."""
        issues = []
        warnings = []
        
        try:
            # Check for appointments with end_time before start_time
            invalid_times = db.session.execute(text("""
                SELECT appointment_id, start_time, end_time
                FROM appointments
                WHERE end_time <= start_time
            """)).fetchall()
            
            if invalid_times:
                issues.append({
                    'type': 'invalid_appointment_times',
                    'severity': 'critical',
                    'count': len(invalid_times),
                    'details': [{'appointment_id': row[0], 'start_time': row[1], 'end_time': row[2]} for row in invalid_times]
                })
            
            # Check for appointments with invalid foreign key references
            invalid_references = db.session.execute(text("""
                SELECT a.appointment_id, a.client_id, a.tutor_id, a.tutor_service_id
                FROM appointments a
                LEFT JOIN clients c ON a.client_id = c.client_id
                LEFT JOIN tutors t ON a.tutor_id = t.tutor_id
                LEFT JOIN tutor_services ts ON a.tutor_service_id = ts.tutor_service_id
                WHERE c.client_id IS NULL OR t.tutor_id IS NULL OR ts.tutor_service_id IS NULL
            """)).fetchall()
            
            if invalid_references:
                issues.append({
                    'type': 'invalid_appointment_references',
                    'severity': 'critical',
                    'count': len(invalid_references),
                    'details': [{'appointment_id': row[0], 'client_id': row[1], 'tutor_id': row[2], 'tutor_service_id': row[3]} for row in invalid_references]
                })
            
            # Check for appointments with invalid status values
            invalid_status = db.session.execute(text("""
                SELECT appointment_id, status
                FROM appointments
                WHERE status NOT IN ('scheduled', 'confirmed', 'completed', 'cancelled', 'no-show', 'awaiting_confirmation')
            """)).fetchall()
            
            if invalid_status:
                issues.append({
                    'type': 'invalid_appointment_status',
                    'severity': 'high',
                    'count': len(invalid_status),
                    'details': [{'appointment_id': row[0], 'status': row[1]} for row in invalid_status]
                })
            
            # Check for overlapping appointments for the same tutor
            overlapping_appointments = db.session.execute(text("""
                SELECT a1.appointment_id as appt1_id, a2.appointment_id as appt2_id, 
                       a1.tutor_id, a1.start_time, a1.end_time, a2.start_time, a2.end_time
                FROM appointments a1
                JOIN appointments a2 ON a1.tutor_id = a2.tutor_id 
                    AND a1.appointment_id < a2.appointment_id
                    AND a1.status NOT IN ('cancelled', 'no-show')
                    AND a2.status NOT IN ('cancelled', 'no-show')
                WHERE (a1.start_time, a1.end_time) OVERLAPS (a2.start_time, a2.end_time)
            """)).fetchall()
            
            if overlapping_appointments:
                issues.append({
                    'type': 'overlapping_tutor_appointments',
                    'severity': 'high',
                    'count': len(overlapping_appointments),
                    'details': [{'appointment_1': row[0], 'appointment_2': row[1], 'tutor_id': row[2]} for row in overlapping_appointments]
                })
            
            # Check for appointments in the far past that are still scheduled
            old_scheduled = db.session.execute(text("""
                SELECT appointment_id, start_time, status
                FROM appointments
                WHERE status = 'scheduled' 
                AND start_time < CURRENT_TIMESTAMP - INTERVAL '7 days'
            """)).fetchall()
            
            if old_scheduled:
                warnings.append({
                    'type': 'old_scheduled_appointments',
                    'severity': 'medium',
                    'count': len(old_scheduled),
                    'details': [{'appointment_id': row[0], 'start_time': row[1], 'status': row[2]} for row in old_scheduled]
                })
            
            # Check for appointments with dependant_id but no valid dependant reference
            invalid_dependant_refs = db.session.execute(text("""
                SELECT a.appointment_id, a.dependant_id
                FROM appointments a
                LEFT JOIN dependants d ON a.dependant_id = d.dependant_id
                WHERE a.dependant_id IS NOT NULL AND d.dependant_id IS NULL
            """)).fetchall()
            
            if invalid_dependant_refs:
                issues.append({
                    'type': 'invalid_dependant_references',
                    'severity': 'high',
                    'count': len(invalid_dependant_refs),
                    'details': [{'appointment_id': row[0], 'dependant_id': row[1]} for row in invalid_dependant_refs]
                })
            
        except Exception as e:
            issues.append({
                'type': 'validation_error',
                'severity': 'critical',
                'message': f"Error validating appointments: {str(e)}"
            })
        
        return {
            'table': 'appointments',
            'issues': issues,
            'warnings': warnings,
            'total_issues': len(issues),
            'total_warnings': len(warnings)
        }
    
    @staticmethod
    def _validate_recurring_schedules() -> Dict[str, Any]:
        """Validate appointment_recurring_schedules table data integrity."""
        issues = []
        warnings = []
        
        try:
            # Check for invalid frequency values
            invalid_frequency = db.session.execute(text("""
                SELECT schedule_id, frequency
                FROM appointment_recurring_schedules
                WHERE frequency NOT IN ('weekly', 'biweekly', 'monthly')
            """)).fetchall()
            
            if invalid_frequency:
                issues.append({
                    'type': 'invalid_frequency_values',
                    'severity': 'high',
                    'count': len(invalid_frequency),
                    'details': [{'schedule_id': row[0], 'frequency': row[1]} for row in invalid_frequency]
                })
            
            # Check for weekly/biweekly schedules without day_of_week
            missing_day_of_week = db.session.execute(text("""
                SELECT schedule_id, frequency
                FROM appointment_recurring_schedules
                WHERE frequency IN ('weekly', 'biweekly') AND day_of_week IS NULL
            """)).fetchall()
            
            if missing_day_of_week:
                issues.append({
                    'type': 'missing_day_of_week',
                    'severity': 'high',
                    'count': len(missing_day_of_week),
                    'details': [{'schedule_id': row[0], 'frequency': row[1]} for row in missing_day_of_week]
                })
            
            # Check for monthly schedules without week_of_month
            missing_week_of_month = db.session.execute(text("""
                SELECT schedule_id, frequency
                FROM appointment_recurring_schedules
                WHERE frequency = 'monthly' AND (day_of_week IS NULL OR week_of_month IS NULL)
            """)).fetchall()
            
            if missing_week_of_month:
                issues.append({
                    'type': 'missing_monthly_pattern',
                    'severity': 'high',
                    'count': len(missing_week_of_month),
                    'details': [{'schedule_id': row[0], 'frequency': row[1]} for row in missing_week_of_month]
                })
            
            # Check for invalid day_of_week values
            invalid_day_of_week = db.session.execute(text("""
                SELECT schedule_id, day_of_week
                FROM appointment_recurring_schedules
                WHERE day_of_week IS NOT NULL AND (day_of_week < 0 OR day_of_week > 6)
            """)).fetchall()
            
            if invalid_day_of_week:
                issues.append({
                    'type': 'invalid_day_of_week',
                    'severity': 'high',
                    'count': len(invalid_day_of_week),
                    'details': [{'schedule_id': row[0], 'day_of_week': row[1]} for row in invalid_day_of_week]
                })
            
            # Check for schedules with pattern_end_date before pattern_start_date
            invalid_date_range = db.session.execute(text("""
                SELECT schedule_id, pattern_start_date, pattern_end_date
                FROM appointment_recurring_schedules
                WHERE pattern_end_date IS NOT NULL AND pattern_end_date < pattern_start_date
            """)).fetchall()
            
            if invalid_date_range:
                issues.append({
                    'type': 'invalid_date_range',
                    'severity': 'high',
                    'count': len(invalid_date_range),
                    'details': [{'schedule_id': row[0], 'start_date': row[1], 'end_date': row[2]} for row in invalid_date_range]
                })
            
            # Check for schedules without end date or occurrence limit
            no_end_constraint = db.session.execute(text("""
                SELECT schedule_id
                FROM appointment_recurring_schedules
                WHERE pattern_end_date IS NULL AND pattern_occurrences IS NULL
            """)).fetchall()
            
            if no_end_constraint:
                warnings.append({
                    'type': 'no_end_constraint',
                    'severity': 'medium',
                    'count': len(no_end_constraint),
                    'details': [{'schedule_id': row[0]} for row in no_end_constraint]
                })
            
        except Exception as e:
            issues.append({
                'type': 'validation_error',
                'severity': 'critical',
                'message': f"Error validating recurring schedules: {str(e)}"
            })
        
        return {
            'table': 'appointment_recurring_schedules',
            'issues': issues,
            'warnings': warnings,
            'total_issues': len(issues),
            'total_warnings': len(warnings)
        }
    
    @staticmethod
    def _validate_subscriptions() -> Dict[str, Any]:
        """Validate subscriptions table data integrity."""
        issues = []
        warnings = []
        
        try:
            # Check for subscriptions with end_date before start_date
            invalid_dates = db.session.execute(text("""
                SELECT subscription_id, start_date, end_date
                FROM subscriptions
                WHERE end_date < start_date
            """)).fetchall()
            
            if invalid_dates:
                issues.append({
                    'type': 'invalid_subscription_dates',
                    'severity': 'high',
                    'count': len(invalid_dates),
                    'details': [{'subscription_id': row[0], 'start_date': row[1], 'end_date': row[2]} for row in invalid_dates]
                })
            
            # Check for negative hours remaining
            negative_hours = db.session.execute(text("""
                SELECT subscription_id, hours_remaining
                FROM subscriptions
                WHERE hours_remaining < 0
            """)).fetchall()
            
            if negative_hours:
                issues.append({
                    'type': 'negative_hours_remaining',
                    'severity': 'high',
                    'count': len(negative_hours),
                    'details': [{'subscription_id': row[0], 'hours_remaining': row[1]} for row in negative_hours]
                })
            
            # Check for active subscriptions that have expired
            expired_active = db.session.execute(text("""
                SELECT subscription_id, end_date, status
                FROM subscriptions
                WHERE status = 'active' AND end_date < CURRENT_DATE
            """)).fetchall()
            
            if expired_active:
                warnings.append({
                    'type': 'expired_active_subscriptions',
                    'severity': 'medium',
                    'count': len(expired_active),
                    'details': [{'subscription_id': row[0], 'end_date': row[1], 'status': row[2]} for row in expired_active]
                })
            
        except Exception as e:
            issues.append({
                'type': 'validation_error',
                'severity': 'critical',
                'message': f"Error validating subscriptions: {str(e)}"
            })
        
        return {
            'table': 'subscriptions',
            'issues': issues,
            'warnings': warnings,
            'total_issues': len(issues),
            'total_warnings': len(warnings)
        }
    
    @staticmethod
    def _validate_invoices() -> Dict[str, Any]:
        """Validate invoices table data integrity."""
        issues = []
        warnings = []
        
        try:
            # Check for invoices with due_date before issue_date
            invalid_dates = db.session.execute(text("""
                SELECT invoice_id, issue_date, due_date
                FROM invoices
                WHERE due_date < issue_date
            """)).fetchall()
            
            if invalid_dates:
                issues.append({
                    'type': 'invalid_invoice_dates',
                    'severity': 'high',
                    'count': len(invalid_dates),
                    'details': [{'invoice_id': row[0], 'issue_date': row[1], 'due_date': row[2]} for row in invalid_dates]
                })
            
            # Check for negative amounts
            negative_amounts = db.session.execute(text("""
                SELECT invoice_id, total_amount, tax_amount
                FROM invoices
                WHERE total_amount < 0 OR tax_amount < 0
            """)).fetchall()
            
            if negative_amounts:
                issues.append({
                    'type': 'negative_invoice_amounts',
                    'severity': 'high',
                    'count': len(negative_amounts),
                    'details': [{'invoice_id': row[0], 'total_amount': row[1], 'tax_amount': row[2]} for row in negative_amounts]
                })
            
            # Check for paid invoices without payment_date
            paid_no_date = db.session.execute(text("""
                SELECT invoice_id, status, payment_date
                FROM invoices
                WHERE status = 'paid' AND payment_date IS NULL
            """)).fetchall()
            
            if paid_no_date:
                issues.append({
                    'type': 'paid_invoices_no_payment_date',
                    'severity': 'medium',
                    'count': len(paid_no_date),
                    'details': [{'invoice_id': row[0], 'status': row[1]} for row in paid_no_date]
                })
            
        except Exception as e:
            issues.append({
                'type': 'validation_error',
                'severity': 'critical',
                'message': f"Error validating invoices: {str(e)}"
            })
        
        return {
            'table': 'invoices',
            'issues': issues,
            'warnings': warnings,
            'total_issues': len(issues),
            'total_warnings': len(warnings)
        }
    
    @staticmethod
    def _validate_programs() -> Dict[str, Any]:
        """Validate programs and related tables data integrity."""
        issues = []
        warnings = []
        
        try:
            # Check for programs with invalid session counts
            invalid_sessions = db.session.execute(text("""
                SELECT program_id, code, total_sessions
                FROM programs
                WHERE total_sessions <= 0
            """)).fetchall()
            
            if invalid_sessions:
                issues.append({
                    'type': 'invalid_session_counts',
                    'severity': 'medium',
                    'count': len(invalid_sessions),
                    'details': [{'program_id': row[0], 'code': row[1], 'total_sessions': row[2]} for row in invalid_sessions]
                })
            
            # Check for program modules with invalid order
            invalid_module_order = db.session.execute(text("""
                SELECT module_id, program_id, module_order
                FROM program_modules
                WHERE module_order <= 0
            """)).fetchall()
            
            if invalid_module_order:
                issues.append({
                    'type': 'invalid_module_order',
                    'severity': 'medium',
                    'count': len(invalid_module_order),
                    'details': [{'module_id': row[0], 'program_id': row[1], 'module_order': row[2]} for row in invalid_module_order]
                })
            
        except Exception as e:
            issues.append({
                'type': 'validation_error',
                'severity': 'critical',
                'message': f"Error validating programs: {str(e)}"
            })
        
        return {
            'table': 'programs',
            'issues': issues,
            'warnings': warnings,
            'total_issues': len(issues),
            'total_warnings': len(warnings)
        }
    
    @staticmethod
    def _validate_foreign_key_integrity() -> Dict[str, Any]:
        """Validate foreign key integrity across all tables."""
        issues = []
        warnings = []
        
        try:
            # This would be a comprehensive check of all foreign key relationships
            # For now, we'll check a few critical ones
            
            # Check for broken user references in clients
            broken_client_users = db.session.execute(text("""
                SELECT COUNT(*) FROM clients c
                LEFT JOIN users u ON c.user_id = u.user_id
                WHERE c.user_id IS NOT NULL AND u.user_id IS NULL
            """)).scalar()
            
            if broken_client_users > 0:
                issues.append({
                    'type': 'broken_client_user_references',
                    'severity': 'critical',
                    'count': broken_client_users
                })
            
            # Check for broken user references in tutors
            broken_tutor_users = db.session.execute(text("""
                SELECT COUNT(*) FROM tutors t
                LEFT JOIN users u ON t.user_id = u.user_id
                WHERE t.user_id IS NOT NULL AND u.user_id IS NULL
            """)).scalar()
            
            if broken_tutor_users > 0:
                issues.append({
                    'type': 'broken_tutor_user_references',
                    'severity': 'critical',
                    'count': broken_tutor_users
                })
            
        except Exception as e:
            issues.append({
                'type': 'validation_error',
                'severity': 'critical',
                'message': f"Error validating foreign keys: {str(e)}"
            })
        
        return {
            'table': 'foreign_keys',
            'issues': issues,
            'warnings': warnings,
            'total_issues': len(issues),
            'total_warnings': len(warnings)
        }
    
    @staticmethod
    def _validate_business_rules() -> Dict[str, Any]:
        """Validate business logic rules."""
        issues = []
        warnings = []
        
        try:
            # Check for appointments scheduled outside business hours
            outside_hours = db.session.execute(text("""
                SELECT appointment_id, start_time
                FROM appointments
                WHERE EXTRACT(hour FROM start_time) < 6 OR EXTRACT(hour FROM start_time) > 22
            """)).fetchall()
            
            if outside_hours:
                warnings.append({
                    'type': 'appointments_outside_business_hours',
                    'severity': 'low',
                    'count': len(outside_hours),
                    'details': [{'appointment_id': row[0], 'start_time': row[1]} for row in outside_hours]
                })
            
            # Check for very short appointments (less than 15 minutes)
            short_appointments = db.session.execute(text("""
                SELECT appointment_id, start_time, end_time,
                       EXTRACT(epoch FROM (end_time - start_time))/60 as duration_minutes
                FROM appointments
                WHERE end_time - start_time < INTERVAL '15 minutes'
            """)).fetchall()
            
            if short_appointments:
                warnings.append({
                    'type': 'very_short_appointments',
                    'severity': 'medium',
                    'count': len(short_appointments),
                    'details': [{'appointment_id': row[0], 'duration_minutes': row[3]} for row in short_appointments]
                })
            
        except Exception as e:
            issues.append({
                'type': 'validation_error',
                'severity': 'critical',
                'message': f"Error validating business rules: {str(e)}"
            })
        
        return {
            'table': 'business_rules',
            'issues': issues,
            'warnings': warnings,
            'total_issues': len(issues),
            'total_warnings': len(warnings)
        }
    
    @staticmethod
    def _validate_data_consistency() -> Dict[str, Any]:
        """Validate data consistency across related tables."""
        issues = []
        warnings = []
        
        try:
            # Check for subscription usage that exceeds subscription hours
            excessive_usage = db.session.execute(text("""
                SELECT s.subscription_id, s.hours_remaining, 
                       COALESCE(SUM(su.hours_used), 0) as total_used
                FROM subscriptions s
                LEFT JOIN subscription_usage su ON s.subscription_id = su.subscription_id
                GROUP BY s.subscription_id, s.hours_remaining
                HAVING s.hours_remaining < 0 OR COALESCE(SUM(su.hours_used), 0) > (
                    SELECT sp.max_hours FROM subscription_plans sp 
                    WHERE sp.plan_id = s.plan_id
                )
            """)).fetchall()
            
            if excessive_usage:
                issues.append({
                    'type': 'excessive_subscription_usage',
                    'severity': 'high',
                    'count': len(excessive_usage),
                    'details': [{'subscription_id': row[0], 'hours_remaining': row[1], 'total_used': row[2]} for row in excessive_usage]
                })
            
        except Exception as e:
            issues.append({
                'type': 'validation_error',
                'severity': 'critical',
                'message': f"Error validating data consistency: {str(e)}"
            })
        
        return {
            'table': 'data_consistency',
            'issues': issues,
            'warnings': warnings,
            'total_issues': len(issues),
            'total_warnings': len(warnings)
        }
    
    @staticmethod
    def _calculate_summary_statistics(validation_report: Dict[str, Any]) -> None:
        """Calculate summary statistics for the validation report."""
        total_checks = 0
        passed_checks = 0
        failed_checks = 0
        warnings = 0
        critical_issues = 0
        
        for table_name, table_results in validation_report['results'].items():
            total_checks += 1
            
            if table_results['total_issues'] == 0 and table_results['total_warnings'] == 0:
                passed_checks += 1
            else:
                failed_checks += 1
            
            warnings += table_results['total_warnings']
            
            # Count critical issues
            for issue in table_results['issues']:
                if issue.get('severity') == 'critical':
                    critical_issues += 1
        
        validation_report['summary'].update({
            'total_checks': total_checks,
            'passed_checks': passed_checks,
            'failed_checks': failed_checks,
            'warnings': warnings,
            'critical_issues': critical_issues
        })
    
    @staticmethod
    def _generate_recommendations(validation_report: Dict[str, Any]) -> None:
        """Generate recommendations based on validation results."""
        recommendations = []
        
        # Check for critical issues
        if validation_report['summary']['critical_issues'] > 0:
            recommendations.append({
                'priority': 'high',
                'category': 'data_integrity',
                'message': f"Found {validation_report['summary']['critical_issues']} critical data integrity issues that require immediate attention."
            })
        
        # Check for foreign key issues
        fk_results = validation_report['results'].get('foreign_keys', {})
        if fk_results.get('total_issues', 0) > 0:
            recommendations.append({
                'priority': 'high',
                'category': 'referential_integrity',
                'message': "Foreign key integrity issues detected. Run cleanup procedures to fix orphaned records."
            })
        
        # Check for appointment issues
        appt_results = validation_report['results'].get('appointments', {})
        if appt_results.get('total_issues', 0) > 0:
            recommendations.append({
                'priority': 'medium',
                'category': 'appointments',
                'message': "Appointment data issues detected. Review scheduling logic and data entry procedures."
            })
        
        # General recommendations
        if validation_report['summary']['warnings'] > 10:
            recommendations.append({
                'priority': 'low',
                'category': 'data_quality',
                'message': f"Found {validation_report['summary']['warnings']} warnings. Consider implementing data validation at input level."
            })
        
        validation_report['recommendations'] = recommendations