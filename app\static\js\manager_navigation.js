/**
 * Enhanced Navigation Functionality for Manager Interface
 */

document.addEventListener('DOMContentLoaded', function() {
    // Only run for manager role
    if (!document.querySelector('.navbar-nav li a[href*="manager"]')) {
        return;
    }

    // Set active navigation item based on current URL
    setActiveNavItem();

    // Generate breadcrumbs based on current page
    generateBreadcrumbs();

    // Setup global search keyboard shortcut
    setupSearchShortcut();
});

/**
 * Sets the active navigation item based on the current URL
 */
function setActiveNavItem() {
    const currentPath = window.location.pathname;

    // Find all nav links
    const navLinks = document.querySelectorAll('.navbar-nav .nav-link');

    // Check each link against the current path
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href && currentPath.includes(href) && href !== '/' && href !== '/manager') {
            // If this is a dropdown toggle, add active class to parent
            if (link.classList.contains('dropdown-toggle')) {
                link.parentElement.classList.add('active');
            }

            // Add active class to the link itself
            link.classList.add('active');
        }
    });

    // Also check dropdown items
    const dropdownItems = document.querySelectorAll('.dropdown-item');
    dropdownItems.forEach(item => {
        const href = item.getAttribute('href');
        if (href && currentPath.includes(href) && href !== '/' && href !== '/manager') {
            // Add active class to the item
            item.classList.add('active');
            item.setAttribute('aria-current', 'page');

            // Also activate the parent dropdown
            const dropdownMenu = item.closest('.dropdown-menu');
            if (dropdownMenu) {
                const dropdownToggle = dropdownMenu.previousElementSibling;
                if (dropdownToggle && dropdownToggle.classList.contains('dropdown-toggle')) {
                    dropdownToggle.classList.add('active');
                    dropdownToggle.parentElement.classList.add('active');
                }
            }
        }
    });
}

/**
 * Generates breadcrumbs based on the current page
 */
function generateBreadcrumbs() {
    const breadcrumbContainer = document.querySelector('.breadcrumb');
    if (!breadcrumbContainer) return;

    // Clear existing breadcrumbs except the first one (Dashboard)
    while (breadcrumbContainer.children.length > 1) {
        breadcrumbContainer.removeChild(breadcrumbContainer.lastChild);
    }

    // Get page title
    const pageTitle = document.title.split(' - ')[0];

    // Get current path segments
    const pathSegments = window.location.pathname.split('/').filter(segment => segment);

    // Map of path segments to readable names
    const pathMap = {
        'manager': 'Dashboard',
        'appointments': 'Appointments',
        'schedule': 'Calendar',
        'recurring-appointments': 'Recurring Appointments',
        'parents': 'Parents',
        'students': 'Students',
        'tutors': 'Tutors',
        'services': 'Services',
        'invoices': 'Invoices',
        'subscriptions': 'Subscriptions',
        'subscription-plans': 'Subscription Plans',
        'tutor-payments': 'Tutor Payments',
        'time-off-requests': 'Time-Off Requests',
        'notifications': 'Notifications',
        'search': 'Search Results',
        'tecfee': 'TECFÉE Program',
        'enrollments': 'Enrollments',
        'enroll': 'Enroll Client',
        'group-sessions': 'Group Sessions',
        'new': 'New',
        'edit': 'Edit',
        'view': 'View'
    };

    // Build breadcrumbs based on path segments
    if (pathSegments.length > 1) {
        // Skip the first segment (manager) as it's already in the first breadcrumb
        for (let i = 1; i < pathSegments.length; i++) {
            const segment = pathSegments[i];

            // Skip numeric segments (likely IDs)
            if (!isNaN(segment)) continue;

            // Get readable name for the segment
            const readableSegment = pathMap[segment] || segment.charAt(0).toUpperCase() + segment.slice(1).replace(/-/g, ' ');

            // For the last segment, add as active breadcrumb
            if (i === pathSegments.length - 1 || (!isNaN(pathSegments[i+1]) && i === pathSegments.length - 2)) {
                const li = document.createElement('li');
                li.className = 'breadcrumb-item active';
                li.setAttribute('aria-current', 'page');
                li.textContent = readableSegment;
                breadcrumbContainer.appendChild(li);
            } else {
                // For intermediate segments, add as links
                const li = document.createElement('li');
                li.className = 'breadcrumb-item';

                const a = document.createElement('a');
                a.href = '/' + pathSegments.slice(0, i + 1).join('/');
                a.textContent = readableSegment;

                li.appendChild(a);
                breadcrumbContainer.appendChild(li);
            }
        }
    }
}

/**
 * Setup keyboard shortcut (Ctrl+K or Cmd+K) for global search
 */
function setupSearchShortcut() {
    // Get the search input
    const searchInput = document.querySelector('form.d-flex input[type="search"]');
    if (!searchInput) return;

    // Add keyboard shortcut
    document.addEventListener('keydown', function(e) {
        // Check for Ctrl+K or Cmd+K
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            searchInput.focus();

            // Show a tooltip or indicator that the shortcut was activated
            const searchForm = searchInput.closest('form');
            if (searchForm) {
                searchForm.classList.add('search-active');
                setTimeout(() => {
                    searchForm.classList.remove('search-active');
                }, 500);
            }
        }
    });

    // Add placeholder text to indicate the shortcut
    const originalPlaceholder = searchInput.getAttribute('placeholder');
    searchInput.setAttribute('placeholder', originalPlaceholder + ' (Ctrl+K)');

    // Add tooltip to search button
    const searchButton = searchInput.nextElementSibling;
    if (searchButton) {
        searchButton.setAttribute('title', 'Search (Ctrl+K)');
        searchButton.setAttribute('data-bs-toggle', 'tooltip');
        searchButton.setAttribute('data-bs-placement', 'bottom');
    }
}
