#!/usr/bin/env python3
"""
Test script to verify audit entry display components work correctly.
This script tests the AuditService formatting and template rendering.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timezone
from app.services.audit_service import AuditService

# Mock audit entry data for testing
class MockAuditEntry:
    def __init__(self, **kwargs):
        self.id = kwargs.get('id', 1)
        self.appointment_id = kwargs.get('appointment_id', 123)
        self.action = kwargs.get('action', 'create')
        self.action_description = kwargs.get('action_description', 'Appointment Created')
        self.timestamp = kwargs.get('timestamp', datetime.now(timezone.utc))
        self.user_id = kwargs.get('user_id', 1)
        self.user_email = kwargs.get('user_email', '<EMAIL>')
        self.user_role = kwargs.get('user_role', 'manager')
        self.old_values = kwargs.get('old_values', {})
        self.new_values = kwargs.get('new_values', {})
        self.changes_summary = kwargs.get('changes_summary', '')
        self.notes = kwargs.get('notes', '')
        self.user = None  # Mock no user relationship

def test_create_entry_formatting():
    """Test formatting of create audit entry."""
    print("Testing CREATE entry formatting...")
    
    create_entry = MockAuditEntry(
        action='create',
        action_description='Appointment Created',
        new_values={
            'client_id': 1,
            'tutor_id': 2,
            'status': 'scheduled',
            'start_time': '2024-12-15T19:30:00Z',
            'duration_minutes': 60
        },
        changes_summary='Initial appointment creation with client John Doe'
    )
    
    formatted = AuditService.format_audit_entry_for_display(create_entry)
    
    print(f"✓ Entry ID: {formatted['id']}")
    print(f"✓ Action: {formatted['action']}")
    print(f"✓ User: {formatted['user_name']}")
    print(f"✓ Timestamp EST: {formatted['timestamp_est']}")
    print(f"✓ Has changes: {formatted['has_changes']}")
    print(f"✓ Changes count: {len(formatted['changes_detail'])}")
    
    # Verify changes detail structure
    for change in formatted['changes_detail']:
        print(f"  - {change['field_display']}: {change['new_value_display']}")
    
    print("CREATE entry test passed!\n")

def test_update_entry_formatting():
    """Test formatting of update audit entry."""
    print("Testing UPDATE entry formatting...")
    
    update_entry = MockAuditEntry(
        action='update',
        action_description='Appointment Updated',
        old_values={
            'status': 'scheduled',
            'notes': ''
        },
        new_values={
            'status': 'confirmed',
            'notes': 'Client confirmed attendance'
        },
        changes_summary='Status changed from scheduled to confirmed'
    )
    
    formatted = AuditService.format_audit_entry_for_display(update_entry)
    
    print(f"✓ Entry ID: {formatted['id']}")
    print(f"✓ Action: {formatted['action']}")
    print(f"✓ User: {formatted['user_name']}")
    print(f"✓ Has changes: {formatted['has_changes']}")
    print(f"✓ Changes count: {len(formatted['changes_detail'])}")
    
    # Verify changes detail structure
    for change in formatted['changes_detail']:
        print(f"  - {change['field_display']}: {change['old_value_display']} → {change['new_value_display']}")
    
    print("UPDATE entry test passed!\n")

def test_cancel_entry_formatting():
    """Test formatting of cancel audit entry."""
    print("Testing CANCEL entry formatting...")
    
    cancel_entry = MockAuditEntry(
        action='cancel',
        action_description='Appointment Cancelled',
        old_values={
            'status': 'confirmed',
            'notes': 'Client confirmed attendance'
        },
        new_values={
            'status': 'cancelled',
            'notes': 'Cancelled due to scheduling conflict'
        },
        changes_summary='Client cancelled appointment due to scheduling conflict'
    )
    
    formatted = AuditService.format_audit_entry_for_display(cancel_entry)
    
    print(f"✓ Entry ID: {formatted['id']}")
    print(f"✓ Action: {formatted['action']}")
    print(f"✓ User: {formatted['user_name']}")
    print(f"✓ Has changes: {formatted['has_changes']}")
    print(f"✓ Changes count: {len(formatted['changes_detail'])}")
    
    # Verify changes detail structure
    for change in formatted['changes_detail']:
        print(f"  - {change['field_display']}: {change['old_value_display']} → {change['new_value_display']}")
    
    print("CANCEL entry test passed!\n")

def test_delete_entry_formatting():
    """Test formatting of delete audit entry."""
    print("Testing DELETE entry formatting...")
    
    delete_entry = MockAuditEntry(
        action='delete',
        action_description='Appointment Deleted',
        old_values={
            'client_id': 1,
            'tutor_id': 2,
            'status': 'cancelled'
        },
        changes_summary='Appointment permanently removed due to cancellation'
    )
    
    formatted = AuditService.format_audit_entry_for_display(delete_entry)
    
    print(f"✓ Entry ID: {formatted['id']}")
    print(f"✓ Action: {formatted['action']}")
    print(f"✓ User: {formatted['user_name']}")
    print(f"✓ Has changes: {formatted['has_changes']}")
    print(f"✓ Changes count: {len(formatted['changes_detail'])}")
    
    print("DELETE entry test passed!\n")

def test_field_value_formatting():
    """Test field value formatting."""
    print("Testing field value formatting...")
    
    # Test various field types
    test_cases = [
        ('status', 'scheduled', 'Scheduled'),
        ('start_time', '2024-12-15T19:30:00Z', 'formatted_time'),
        ('duration_minutes', 90, '1h 30m'),
        ('duration_minutes', 60, '1h'),
        ('duration_minutes', 45, '45m'),
        ('transport_fee', 25.50, '$25.50'),
        ('notes', '', 'Empty'),
        ('notes', None, 'Not set')
    ]
    
    for field, value, expected_type in test_cases:
        formatted = AuditService._format_field_value(field, value)
        print(f"✓ {field}: {value} → {formatted}")
    
    print("Field value formatting test passed!\n")

def test_field_display_names():
    """Test field display name mapping."""
    print("Testing field display names...")
    
    test_fields = [
        'status', 'tutor_id', 'client_id', 'start_time', 
        'end_time', 'notes', 'transport_fee', 'duration_minutes'
    ]
    
    for field in test_fields:
        display_name = AuditService._get_field_display_name(field)
        print(f"✓ {field} → {display_name}")
    
    print("Field display names test passed!\n")

if __name__ == '__main__':
    print("=== Audit Entry Display Components Test ===\n")
    
    try:
        test_create_entry_formatting()
        test_update_entry_formatting()
        test_cancel_entry_formatting()
        test_delete_entry_formatting()
        test_field_value_formatting()
        test_field_display_names()
        
        print("🎉 All tests passed! Audit entry display components are working correctly.")
        
    except Exception as e:
        print(f"❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)