<!-- app/templates/tutor/appointment_update.html -->
{% extends "base.html" %}

{% block title %}{{ t('tutor.appointment_update.page_title') }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">{{ t('tutor.appointment_update.title') }}</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('tutor.view_appointment', id=appointment.id) }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left"></i> {{ t('tutor.appointment_update.back_to_appointment') }}
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">{{ t('tutor.appointment_update.session_details') }}</h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6>
                            {% if appointment.dependant_id %}
                                {{ t('tutor.appointment_update.student') }}
                            {% else %}
                                {{ t('tutor.appointment_update.client') }}
                            {% endif %}
                        </h6>
                        <p>
                            {% if appointment.dependant_id and appointment.dependant %}
                                {{ appointment.dependant.first_name }} {{ appointment.dependant.last_name }}
                                <br>
                                <small class="text-muted">
                                    {{ t('tutor.appointment_update.client') }}: {{ appointment.client.first_name }} {{ appointment.client.last_name }}
                                </small>
                            {% else %}
                                {{ appointment.client.first_name }} {{ appointment.client.last_name }}
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6>{{ t('tutor.appointment_update.date_time') }}</h6>
                        <p>
                            {{ appointment.start_time.strftime('%Y-%m-%d') }}<br>
                            {{ appointment.start_time.strftime('%I:%M %p') }} - {{ appointment.end_time.strftime('%I:%M %p') }}
                        </p>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6>{{ t('tutor.appointment_update.service') }}</h6>
                        <p>
                            {% if appointment.tutor_service and appointment.tutor_service.service %}
                                {{ appointment.tutor_service.service.name }}
                            {% else %}
                                N/A
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6>{{ t('tutor.appointment_update.duration') }}</h6>
                        <p>{{ appointment.duration_minutes }} {{ t('tutor.appointment_update.minutes') }}</p>
                    </div>
                </div>

                <form method="POST" action="{{ url_for('tutor.update_appointment', id=appointment.id) }}">
                    {{ form.csrf_token }}

                    <div class="mb-3">
                        <label for="status" class="form-label">{{ t('tutor.appointment_update.status') }}</label>
                        {{ form.status(class="form-control" + (" is-invalid" if form.status.errors else "")) }}
                        {% if form.status.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.status.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">{{ t('tutor.appointment_update.session_notes') }}</label>
                        {{ form.notes(class="form-control", rows=5) }}
                        <div class="form-text">
                            {{ t('tutor.appointment_update.session_notes_help') }}
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        {{ form.submit(class="btn btn-primary") }}
                        <a href="{{ url_for('tutor.view_appointment', id=appointment.id) }}" class="btn btn-outline-secondary">{{ t('tutor.appointment_update.cancel') }}</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}