@echo off
REM Script to remove the appointment audit trigger
REM This fixes the duplicate audit log entries issue

echo Removing appointment audit trigger to fix duplicate entries...
echo ==============================================

REM Database connection details from environment or defaults
set DB_HOST=%DATABASE_HOST%
if "%DB_HOST%"=="" set DB_HOST=localhost

set DB_PORT=%DATABASE_PORT%
if "%DB_PORT%"=="" set DB_PORT=5432

set DB_NAME=%DATABASE_NAME%
set DB_USER=%DATABASE_USER%
set DB_PASSWORD=%DATABASE_PASSWORD%

REM Check if required variables are set
if "%DB_NAME%"=="" (
    echo Error: DATABASE_NAME not set in environment variables.
    echo Please set it in your .env file or system environment.
    pause
    exit /b 1
)

if "%DB_USER%"=="" (
    echo Error: DATABASE_USER not set in environment variables.
    echo Please set it in your .env file or system environment.
    pause
    exit /b 1
)

REM Set PGPASSWORD environment variable
set PGPASSWORD=%DB_PASSWORD%

REM Run the migration
psql -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -f "remove_appointment_audit_trigger.sql"

if %errorlevel% neq 0 (
    echo X Failed to remove audit trigger
    pause
    exit /b 1
)

echo ✓ Audit trigger removed successfully
echo.
echo The duplicate audit log entries issue has been fixed.
echo Future appointment changes will only create one audit entry.
pause