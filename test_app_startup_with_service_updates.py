#!/usr/bin/env python3
"""
Test that the application can start up with the updated models from Task 2.
"""

import sys
import os

def test_app_startup():
    """Test that the Flask app can start with the updated models."""
    try:
        # Import Flask app
        from app import create_app
        from app.extensions import db
        
        # Create app instance
        app = create_app()
        
        with app.app_context():
            # Try to import all Task 2 models
            from app.models.tutor_payment import TutorPayment
            from app.models.tutor_availability import TutorAvailability
            from app.models.time_off import TimeOff
            
            print("✓ Successfully imported all Task 2 models in app context")
            
            # Check that models are registered with SQLAlchemy
            model_names = [table.name for table in db.metadata.tables.values()]
            
            expected_tables = ['tutor_payments', 'tutor_availabilities', 'time_off_requests']
            
            print("\nTable registration check:")
            for table_name in expected_tables:
                if table_name in model_names:
                    print(f"  ✓ {table_name} table registered")
                else:
                    print(f"  ✗ {table_name} table NOT registered")
            
            print("\n✓ Application startup test completed successfully!")
            return True
            
    except Exception as e:
        print(f"✗ Application startup failed: {e}")
        return False

if __name__ == '__main__':
    print("Testing Application Startup with Task 2 Models")
    print("=" * 50)
    
    success = test_app_startup()
    
    if success:
        print("\n" + "=" * 50)
        print("✓ Task 2 models work correctly with the application!")
    else:
        print("\n" + "=" * 50)
        print("✗ Task 2 models have issues with application startup")
    
    sys.exit(0 if success else 1)