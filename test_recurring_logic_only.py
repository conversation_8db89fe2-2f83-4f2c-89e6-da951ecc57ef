#!/usr/bin/env python3
"""
Test the recurring appointment generation logic without database dependencies.
This tests the core algorithms and validation logic.
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

from datetime import datetime, timedelta, date, time
from app.services.recurring_appointment_service import RecurringAppointmentService
from app.services.scheduling_service import SchedulingService

def test_parameter_validation():
    """Test parameter validation logic."""
    print("Testing parameter validation...")
    
    # Test missing required fields
    invalid_data = {
        'tutor_id': 1,
        # Missing required fields
    }
    
    is_valid, errors = RecurringAppointmentService.validate_recurring_schedule_parameters(invalid_data)
    assert not is_valid, "Should reject incomplete data"
    assert len(errors) > 0, "Should return validation errors"
    print(f"✓ Correctly rejected incomplete data with {len(errors)} errors")
    
    # Test invalid frequency
    invalid_frequency = {
        'tutor_id': 1,
        'client_id': 1,
        'tutor_service_id': 1,
        'start_time': time(10, 0),
        'duration_minutes': 60,
        'frequency': 'invalid',
        'pattern_start_date': date.today()
    }
    
    is_valid, errors = RecurringAppointmentService.validate_recurring_schedule_parameters(invalid_frequency)
    assert not is_valid, "Should reject invalid frequency"
    print("✓ Correctly rejected invalid frequency")
    
    # Test valid weekly data
    valid_weekly = {
        'tutor_id': 1,
        'client_id': 1,
        'tutor_service_id': 1,
        'start_time': time(10, 0),
        'duration_minutes': 60,
        'frequency': 'weekly',
        'day_of_week': 1,
        'pattern_start_date': date.today()
    }
    
    is_valid, errors = RecurringAppointmentService.validate_recurring_schedule_parameters(valid_weekly)
    assert is_valid, f"Should accept valid weekly data, got errors: {errors}"
    print("✓ Correctly accepted valid weekly data")
    
    # Test valid monthly data
    valid_monthly = {
        'tutor_id': 1,
        'client_id': 1,
        'tutor_service_id': 1,
        'start_time': time(14, 0),
        'duration_minutes': 90,
        'frequency': 'monthly',
        'day_of_week': 2,
        'week_of_month': 2,
        'pattern_start_date': date.today()
    }
    
    is_valid, errors = RecurringAppointmentService.validate_recurring_schedule_parameters(valid_monthly)
    assert is_valid, f"Should accept valid monthly data, got errors: {errors}"
    print("✓ Correctly accepted valid monthly data")
    
    print("✅ Parameter validation tests passed\n")

def test_date_pattern_logic():
    """Test the date pattern matching logic."""
    print("Testing date pattern matching logic...")
    
    # Create a mock schedule object for testing
    class MockSchedule:
        def __init__(self):
            self.frequency = 'weekly'
            self.day_of_week = 1  # Tuesday
            self.week_of_month = None
            self.pattern_start_date = date(2024, 1, 2)  # A Tuesday
    
    mock_schedule = MockSchedule()
    
    # Test weekly pattern
    tuesday_date = date(2024, 1, 9)  # Next Tuesday
    should_generate = RecurringAppointmentService._should_generate_appointment_on_date(mock_schedule, tuesday_date)
    assert should_generate, "Should generate on correct weekday"
    print("✓ Weekly pattern correctly identifies matching day")
    
    wednesday_date = date(2024, 1, 10)  # Wednesday
    should_not_generate = RecurringAppointmentService._should_generate_appointment_on_date(mock_schedule, wednesday_date)
    assert not should_not_generate, "Should not generate on wrong weekday"
    print("✓ Weekly pattern correctly rejects wrong day")
    
    # Test biweekly pattern
    mock_schedule.frequency = 'biweekly'
    
    # First week after start (should generate)
    first_biweekly = date(2024, 1, 2)  # Start date
    should_generate = RecurringAppointmentService._should_generate_appointment_on_date(mock_schedule, first_biweekly)
    assert should_generate, "Should generate on biweekly start date"
    print("✓ Biweekly pattern works for start date")
    
    # Second week (should not generate)
    second_week = date(2024, 1, 9)  # One week later
    should_not_generate = RecurringAppointmentService._should_generate_appointment_on_date(mock_schedule, second_week)
    assert not should_not_generate, "Should not generate on off-week"
    print("✓ Biweekly pattern correctly skips off-weeks")
    
    # Third week (should generate)
    third_week = date(2024, 1, 16)  # Two weeks later
    should_generate = RecurringAppointmentService._should_generate_appointment_on_date(mock_schedule, third_week)
    assert should_generate, "Should generate on biweekly interval"
    print("✓ Biweekly pattern works for correct intervals")
    
    # Test monthly pattern
    mock_schedule.frequency = 'monthly'
    mock_schedule.week_of_month = 2  # Second week
    mock_schedule.day_of_week = 1   # Tuesday
    
    # Second Tuesday of January 2024
    second_tuesday_jan = date(2024, 1, 9)
    should_generate = RecurringAppointmentService._should_generate_appointment_on_date(mock_schedule, second_tuesday_jan)
    assert should_generate, "Should generate on second Tuesday"
    print("✓ Monthly pattern works for correct week and day")
    
    # First Tuesday of January (should not generate)
    first_tuesday_jan = date(2024, 1, 2)
    should_not_generate = RecurringAppointmentService._should_generate_appointment_on_date(mock_schedule, first_tuesday_jan)
    assert not should_not_generate, "Should not generate on wrong week"
    print("✓ Monthly pattern correctly rejects wrong week")
    
    print("✅ Date pattern logic tests passed\n")

def test_appointment_timing_validation():
    """Test appointment timing validation."""
    print("Testing appointment timing validation...")
    
    # Test valid future appointment
    future_start = datetime.now() + timedelta(days=1, hours=2)
    future_end = future_start + timedelta(hours=1)
    
    is_valid, errors = SchedulingService.validate_appointment_timing(future_start, future_end)
    if is_valid:
        print("✓ Valid future appointment accepted")
    else:
        print(f"! Valid appointment rejected: {errors}")
    
    # Test past appointment (should be rejected)
    past_start = datetime.now() - timedelta(hours=1)
    past_end = past_start + timedelta(hours=1)
    
    is_valid, errors = SchedulingService.validate_appointment_timing(past_start, past_end)
    assert not is_valid, "Should reject past appointments"
    print("✓ Correctly rejected past appointment")
    
    # Test too short appointment
    short_start = datetime.now() + timedelta(days=1)
    short_end = short_start + timedelta(minutes=5)
    
    is_valid, errors = SchedulingService.validate_appointment_timing(short_start, short_end)
    assert not is_valid, "Should reject appointments shorter than 15 minutes"
    print("✓ Correctly rejected too-short appointment")
    
    # Test too long appointment
    long_start = datetime.now() + timedelta(days=1)
    long_end = long_start + timedelta(hours=10)
    
    is_valid, errors = SchedulingService.validate_appointment_timing(long_start, long_end)
    assert not is_valid, "Should reject appointments longer than 8 hours"
    print("✓ Correctly rejected too-long appointment")
    
    # Test outside business hours
    early_start = datetime.now().replace(hour=6, minute=0, second=0, microsecond=0) + timedelta(days=1)
    early_end = early_start + timedelta(hours=1)
    
    is_valid, errors = SchedulingService.validate_appointment_timing(early_start, early_end)
    assert not is_valid, "Should reject appointments outside business hours"
    print("✓ Correctly rejected appointment outside business hours")
    
    print("✅ Appointment timing validation tests passed\n")

def test_service_method_signatures():
    """Test that all required service methods exist with correct signatures."""
    print("Testing service method signatures...")
    
    # Test RecurringAppointmentService methods
    assert hasattr(RecurringAppointmentService, 'validate_recurring_schedule_parameters'), "Missing validation method"
    assert hasattr(RecurringAppointmentService, 'create_recurring_schedule'), "Missing create method"
    assert hasattr(RecurringAppointmentService, 'generate_appointments_for_schedule'), "Missing generation method"
    assert hasattr(RecurringAppointmentService, 'generate_appointments_for_all_active_schedules'), "Missing batch method"
    assert hasattr(RecurringAppointmentService, 'deactivate_recurring_schedule'), "Missing deactivation method"
    assert hasattr(RecurringAppointmentService, 'get_schedule_summary'), "Missing summary method"
    print("✓ RecurringAppointmentService has all required methods")
    
    # Test SchedulingService methods
    assert hasattr(SchedulingService, 'create_recurring_schedule_with_initial_appointments'), "Missing workflow method"
    assert hasattr(SchedulingService, 'get_tutor_availability_conflicts'), "Missing conflict method"
    assert hasattr(SchedulingService, 'find_available_time_slots'), "Missing availability method"
    assert hasattr(SchedulingService, 'reschedule_appointment'), "Missing reschedule method"
    assert hasattr(SchedulingService, 'validate_appointment_timing'), "Missing timing validation method"
    assert hasattr(SchedulingService, 'get_scheduling_statistics'), "Missing statistics method"
    print("✓ SchedulingService has all required methods")
    
    print("✅ Service method signature tests passed\n")

def test_edge_cases():
    """Test edge cases and boundary conditions."""
    print("Testing edge cases...")
    
    # Test validation with edge values
    edge_case_data = {
        'tutor_id': 1,
        'client_id': 1,
        'tutor_service_id': 1,
        'start_time': time(0, 0),  # Midnight
        'duration_minutes': 1,     # Minimum duration
        'frequency': 'weekly',
        'day_of_week': 0,          # Monday (minimum)
        'pattern_start_date': date.today()
    }
    
    is_valid, errors = RecurringAppointmentService.validate_recurring_schedule_parameters(edge_case_data)
    assert is_valid, f"Should accept edge case values, got errors: {errors}"
    print("✓ Edge case values accepted")
    
    # Test maximum values
    max_case_data = {
        'tutor_id': 1,
        'client_id': 1,
        'tutor_service_id': 1,
        'start_time': time(23, 59),  # Almost midnight
        'duration_minutes': 480,     # Maximum duration (8 hours)
        'frequency': 'monthly',
        'day_of_week': 6,            # Sunday (maximum)
        'week_of_month': 5,          # Fifth week (maximum)
        'pattern_start_date': date.today()
    }
    
    is_valid, errors = RecurringAppointmentService.validate_recurring_schedule_parameters(max_case_data)
    assert is_valid, f"Should accept maximum values, got errors: {errors}"
    print("✓ Maximum values accepted")
    
    # Test boundary violations
    invalid_duration = {
        'tutor_id': 1,
        'client_id': 1,
        'tutor_service_id': 1,
        'start_time': time(10, 0),
        'duration_minutes': 481,     # Over maximum
        'frequency': 'weekly',
        'day_of_week': 1,
        'pattern_start_date': date.today()
    }
    
    is_valid, errors = RecurringAppointmentService.validate_recurring_schedule_parameters(invalid_duration)
    assert not is_valid, "Should reject duration over maximum"
    print("✓ Correctly rejected over-maximum duration")
    
    print("✅ Edge case tests passed\n")

def run_all_tests():
    """Run all logic-only tests."""
    print("=" * 60)
    print("RECURRING APPOINTMENT LOGIC TESTS")
    print("=" * 60)
    print()
    
    try:
        test_parameter_validation()
        test_date_pattern_logic()
        test_appointment_timing_validation()
        test_service_method_signatures()
        test_edge_cases()
        
        print("=" * 60)
        print("✅ ALL LOGIC TESTS PASSED!")
        print("=" * 60)
        print()
        print("SUMMARY:")
        print("- Parameter validation: ✓ Working correctly")
        print("- Date pattern matching: ✓ All frequencies supported")
        print("- Appointment timing validation: ✓ Business rules enforced")
        print("- Service method signatures: ✓ All required methods present")
        print("- Edge cases: ✓ Boundary conditions handled")
        print()
        print("The recurring appointment generation logic is fully implemented")
        print("and ready for integration with the database layer.")
        
        return True
        
    except Exception as e:
        print(f"❌ TEST FAILED: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)