#!/usr/bin/env python3
"""
Run the recurring appointments dependant_id migration
"""
import os
import sys
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.extensions import db

def run_migration():
    """Run the migration to add dependant_id to recurring_appointments"""
    app = create_app()
    
    with app.app_context():
        try:
            # Check if column already exists
            result = db.session.execute(db.text("PRAGMA table_info(recurring_appointments)"))
            columns = [row[1] for row in result.fetchall()]
            
            if 'dependant_id' in columns:
                print("Column 'dependant_id' already exists in recurring_appointments table")
                return True
            
            print("Adding dependant_id column to recurring_appointments table...")
            
            # Add the dependant_id column
            db.session.execute(db.text("""
                ALTER TABLE recurring_appointments 
                ADD COLUMN dependant_id INTEGER REFERENCES dependants(id)
            """))
            
            print("✓ Added dependant_id column")
            
            # Add index for better performance
            db.session.execute(db.text("""
                CREATE INDEX IF NOT EXISTS idx_recurring_appointments_dependant_id 
                ON recurring_appointments(dependant_id)
            """))
            
            print("✓ Added index on dependant_id")
            
            db.session.commit()
            print("Migration completed successfully!")
            
        except Exception as e:
            print(f"Migration failed: {e}")
            db.session.rollback()
            return False
    
    return True

if __name__ == '__main__':
    success = run_migration()
    sys.exit(0 if success else 1)
