# app/utils/email_validator.py
import re
import dns.resolver
from typing import Tuple, Optional
import requests
from flask import current_app

class EmailValidator:
    """Advanced email validation to prevent fake emails and database pollution."""
    
    # Common fake/test email patterns
    FAKE_EMAIL_PATTERNS = [
        r'^test@test\..*',
        r'^fake@fake\..*',
        r'^asdf@asdf\..*',
        r'^admin@admin\..*',
        r'^user@user\..*',
        r'^email@email\..*',
        r'^mail@mail\..*',
        r'^[a-z]{1,3}@[a-z]{1,3}\.(com|net|org)$',  # <EMAIL>
        r'^nobody@.*',
        r'^noreply@.*',
        r'^donotreply@.*',
    ]
    
    # Known disposable email domains (partial list)
    DISPOSABLE_DOMAINS = {
        'tempmail.com', 'guerrillamail.com', '10minutemail.com',
        'throwaway.email', 'yopmail.com', 'mailinator.com',
        'temp-mail.org', 'trashmail.com', 'sharklasers.com',
        'guerrillamail.info', 'grr.la', 'guerrillamail.biz',
        'guerrillamail.org', 'guerrillamailblock.com', 'pokemail.net',
        'spam4.me', 'tempemail.net', 'tempinbox.com',
        'fakeinbox.com', 'emailondeck.com', 'getnada.com',
        'mailnesia.com', 'mintemail.com', 'throwemail.com',
        'tmpmail.net', 'moakt.com', 'dispostable.com',
        'mailcatch.com', 'mailnull.com', 'temp-inbox.com'
    }
    
    # Trusted email providers
    TRUSTED_PROVIDERS = {
        'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
        'live.com', 'msn.com', 'icloud.com', 'me.com',
        'aol.com', 'protonmail.com', 'tutanota.com',
        'yahoo.ca', 'hotmail.ca', 'gmail.ca',
        'videotron.ca', 'bell.net', 'rogers.com',
        'shaw.ca', 'telus.net', 'sympatico.ca'
    }
    
    @classmethod
    def validate_email(cls, email: str) -> Tuple[bool, Optional[str]]:
        """
        Comprehensive email validation.
        Returns: (is_valid, error_message)
        """
        email = email.lower().strip()
        
        # 1. Basic format validation
        if not cls._is_valid_format(email):
            return False, "Format d'email invalide"
        
        # 2. Check for fake patterns
        if cls._is_fake_pattern(email):
            return False, "Cette adresse email semble être factice"
        
        # 3. Extract domain
        domain = email.split('@')[1]
        
        # 4. Check disposable domains
        if cls._is_disposable_domain(domain):
            return False, "Les adresses email temporaires ne sont pas acceptées"
        
        # 5. Check domain existence (DNS)
        if not cls._domain_exists(domain):
            return False, "Le domaine de cette adresse email n'existe pas"
        
        # 6. Additional checks for non-trusted providers
        if domain not in cls.TRUSTED_PROVIDERS:
            # Could add more strict validation here
            pass
        
        return True, None
    
    @classmethod
    def _is_valid_format(cls, email: str) -> bool:
        """Check basic email format."""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @classmethod
    def _is_fake_pattern(cls, email: str) -> bool:
        """Check if email matches known fake patterns."""
        for pattern in cls.FAKE_EMAIL_PATTERNS:
            if re.match(pattern, email, re.IGNORECASE):
                return True
        return False
    
    @classmethod
    def _is_disposable_domain(cls, domain: str) -> bool:
        """Check if domain is a known disposable email service."""
        return domain.lower() in cls.DISPOSABLE_DOMAINS
    
    @classmethod
    def _domain_exists(cls, domain: str) -> bool:
        """Check if domain has valid MX records."""
        try:
            mx_records = dns.resolver.resolve(domain, 'MX')
            return len(mx_records) > 0
        except Exception:
            # If DNS lookup fails, check if it's a trusted provider
            return domain in cls.TRUSTED_PROVIDERS
    
    @classmethod
    def suggest_correction(cls, email: str) -> Optional[str]:
        """Suggest corrections for common typos."""
        typo_fixes = {
            'gmial.com': 'gmail.com',
            'gmai.com': 'gmail.com',
            'gnail.com': 'gmail.com',
            'hotmial.com': 'hotmail.com',
            'hotmal.com': 'hotmail.com',
            'yahooo.com': 'yahoo.com',
            'yaho.com': 'yahoo.com',
            'outloook.com': 'outlook.com',
        }
        
        domain = email.split('@')[1].lower()
        if domain in typo_fixes:
            username = email.split('@')[0]
            return f"{username}@{typo_fixes[domain]}"
        
        return None
    
    @classmethod
    def check_disposable_api(cls, email: str) -> bool:
        """
        Check against online disposable email detection API.
        Only use this for suspicious emails to avoid API limits.
        """
        try:
            # Example using a free API (replace with your preferred service)
            response = requests.get(
                f"https://disposable.debounce.io/?email={email}",
                timeout=5
            )
            data = response.json()
            return data.get('disposable', False)
        except Exception:
            # If API fails, continue with local validation
            return False


# Convenience function for use in views
def validate_enrollment_email(email: str) -> Tuple[bool, Optional[str]]:
    """Validate email for TECFÉE enrollment with strict rules."""
    # First, use our validator
    is_valid, error = EmailValidator.validate_email(email)
    
    if not is_valid:
        return False, error
    
    # Additional TECFÉE-specific rules
    # Require certain length to avoid lazy entries
    username = email.split('@')[0]
    if len(username) < 3:
        return False, "L'adresse email semble trop courte"
    
    # Check for repetitive patterns (<EMAIL>)
    if len(set(username.replace('.', '').replace('_', ''))) < 3:
        return False, "L'adresse email semble invalide"
    
    return True, None