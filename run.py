#!/usr/bin/env python
"""
Application entry point for the Tutoring Appointment System.
This file is used to run the Flask application.
"""
import os
from dotenv import load_dotenv
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Load environment variables from .env file
load_dotenv()

# Log environment variables (excluding sensitive ones)
logger.info(f"FLASK_APP: {os.getenv('FLASK_APP')}")
logger.info(f"FLASK_ENV: {os.getenv('FLASK_ENV')}")
logger.info(f"DATABASE_URL: {'[SET]' if os.getenv('DATABASE_URL') else '[NOT SET]'}")
logger.info(f"TIMEZONE: {os.getenv('TIMEZONE', 'America/New_York')}")

from app import create_app
from app.extensions import db
from app.models import User, Manager, Client, IndividualClient, InstitutionalClient, Tutor, Service, TutorService, Appointment, Invoice, InvoiceItem

# Create application instance with the appropriate configuration
app = create_app(os.getenv('FLASK_CONFIG', 'development'))

@app.shell_context_processor
def make_shell_context():
    """
    Add database models to the Flask shell context.
    This allows for easier testing and debugging in the shell.
    """
    return {
        'db': db,
        'User': User,
        'Manager': Manager,
        'Client': Client,
        'IndividualClient': IndividualClient,
        'InstitutionalClient': InstitutionalClient,
        'Tutor': Tutor,
        'Service': Service,
        'TutorService': TutorService,
        'Appointment': Appointment,
        'Invoice': Invoice,
        'InvoiceItem': InvoiceItem
    }

@app.cli.command("create-admin")
def create_admin():
    """
    Flask CLI command to create an admin user.
    Usage: flask create-admin
    """
    from werkzeug.security import generate_password_hash

    email = input("Enter admin email: ")
    password = input("Enter admin password: ")
    first_name = input("Enter admin first name: ")
    last_name = input("Enter admin last name: ")

    # Check if user already exists
    existing_user = User.query.filter_by(email=email).first()
    if existing_user:
        print(f"User with email {email} already exists.")
        return

    # Create user with manager role
    try:
        user = User(
            email=email,
            password=password,
            role='manager'
        )
        db.session.add(user)
        db.session.flush()  # Get user ID without committing

        # Create manager profile
        manager = Manager(
            user_id=user.id,
            first_name=first_name,
            last_name=last_name
        )
        db.session.add(manager)
        db.session.commit()

        print(f"Admin user {email} created successfully.")
    except Exception as e:
        db.session.rollback()
        print(f"Error creating admin user: {str(e)}")

@app.cli.command("test-db-connection")
def test_db_connection():
    """
    Flask CLI command to test the database connection.
    Usage: flask test-db-connection
    """
    try:
        # Try to execute a simple query
        result = db.session.execute('SELECT 1').scalar()
        if result == 1:
            print("✅ Database connection successful!")
        else:
            print("❌ Database connection test returned unexpected result.")
    except Exception as e:
        print(f"❌ Database connection failed: {str(e)}")

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 8080))
    app.run(host='0.0.0.0', port=port, debug=os.environ.get('FLASK_DEBUG', 'false').lower() == 'true')