# Database Naming Conventions

## Context
Consistent and descriptive naming conventions improve code readability, reduce confusion, and make database relationships clearer.

## Primary Key Naming Convention

### Standard Format
Use descriptive primary key names that include the table name:
- `appointment_id` instead of `id` in appointments table
- `tutor_id` instead of `id` in tutors table  
- `client_id` instead of `id` in clients table
- `user_id` instead of `id` in users table

### Benefits
- **Self-documenting**: Column names clearly indicate what they reference
- **Reduces confusion**: No ambiguity about which ID is being referenced
- **Better joins**: Foreign key relationships are immediately clear
- **Easier debugging**: SQL queries are more readable

### Examples

**Before (Generic):**
```sql
SELECT * FROM appointments a 
JOIN tutors t ON a.tutor_id = t.id 
JOIN clients c ON a.client_id = c.id;
```

**After (Descriptive):**
```sql
SELECT * FROM appointments a 
JOIN tutors t ON a.tutor_id = t.tutor_id 
JOIN clients c ON a.client_id = c.client_id;
```

## Implementation Guidelines

### For New Tables
- Always use descriptive primary key names
- Format: `{table_name_singular}_id`
- Examples: `appointment_id`, `tutor_id`, `service_id`

### For Existing Tables (Development)
- Update schema.sql with new naming convention
- Recreate database using development approach
- Update all model classes to match new column names
- Update any existing queries or code references

### Foreign Key Consistency
- Foreign keys should reference the descriptive primary key name
- Example: `tutor_id` in appointments table references `tutor_id` in tutors table

## Application to Current Project

Since we're in development mode with mock data, we should:
1. Update the main schema.sql with descriptive primary key names
2. Recreate the database from scratch
3. Update all SQLAlchemy models to match
4. Verify all relationships work correctly

This approach maintains consistency and improves long-term maintainability.