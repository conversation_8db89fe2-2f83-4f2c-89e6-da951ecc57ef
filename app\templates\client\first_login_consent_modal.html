<!-- app/templates/client/first_login_consent_modal.html -->
<div class="modal fade" id="consentModal" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="consentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="consentModalLabel">{{ t('consent.title') }}</h5>
            </div>
            <div class="modal-body">
                <div class="mb-4">
                    <h6>{{ t('consent.welcome') }}</h6>
                    <p>{{ t('consent.intro') }}</p>
                </div>

                <div class="mb-4">
                    <h6>{{ t('consent.data_collection_title') }}</h6>
                    <p>{{ t('consent.data_collection_text') }}</p>
                    <ul>
                        <li>{{ t('consent.personal_info') }}</li>
                        <li>{{ t('consent.contact_info') }}</li>
                        <li>{{ t('consent.payment_info') }}</li>
                        <li>{{ t('consent.appointment_info') }}</li>
                    </ul>
                </div>

                <div class="mb-4">
                    <h6>{{ t('consent.data_use_title') }}</h6>
                    <p>{{ t('consent.data_use_text') }}</p>
                    <ul>
                        <li>{{ t('consent.scheduling') }}</li>
                        <li>{{ t('consent.billing') }}</li>
                        <li>{{ t('consent.communication') }}</li>
                        <li>{{ t('consent.service_improvement') }}</li>
                    </ul>
                </div>

                <div class="mb-4">
                    <h6>{{ t('consent.data_sharing_title') }}</h6>
                    <p>{{ t('consent.data_sharing_text') }}</p>
                </div>

                <div class="mb-4">
                    <h6>{{ t('consent.rights_title') }}</h6>
                    <p>{{ t('consent.rights_text') }}</p>
                </div>

                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" id="consentCheckbox">
                    <label class="form-check-label" for="consentCheckbox">
                        {{ t('consent.checkbox_label') }}
                    </label>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" id="consentButton" class="btn btn-primary" disabled>{{ t('consent.agree') }}</button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Show the modal when the page loads
        const consentModal = new bootstrap.Modal(document.getElementById('consentModal'));
        consentModal.show();

        // Enable the consent button when the checkbox is checked
        const consentCheckbox = document.getElementById('consentCheckbox');
        const consentButton = document.getElementById('consentButton');

        consentCheckbox.addEventListener('change', function() {
            consentButton.disabled = !this.checked;
        });

        // Handle the consent button click
        consentButton.addEventListener('click', function() {
            // Send consent to the server
            fetch("{{ url_for('client.save_consent') }}", {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': "{{ csrf_token() }}"
                },
                body: JSON.stringify({
                    consent: true
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Hide the modal
                    consentModal.hide();
                } else {
                    alert('{{ t("consent.error") }}');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('{{ t("consent.error") }}');
            });
        });
    });
</script>
