<!-- app/templates/manager/schedule.html -->
{% extends "base.html" %}

{% block title %}{{ t('manager.schedule.title_full') }}{% endblock %}

{% block styles %}
<style>
    .calendar-container {
        overflow-x: auto;
    }
    .calendar-header {
        position: sticky;
        top: 0;
        background-color: #fff;
        z-index: 100;
    }
    .time-column {
        width: 80px;
        min-width: 80px;
    }
    .tutor-column {
        width: 200px;
        min-width: 200px;
    }
    .appointment {
        position: absolute;
        width: calc(100% - 10px);
        border-radius: 4px;
        padding: 5px;
        overflow: hidden;
        cursor: pointer;
        color: white;
    }
    .appointment.status-scheduled {
        background-color: #0d6efd;
    }
    .appointment.status-completed {
        background-color: #198754;
    }
    .appointment.status-cancelled {
        background-color: #dc3545;
        text-decoration: line-through;
    }
    .appointment.status-no-show {
        background-color: #fd7e14;
    }
    .appointment.status-awaiting_confirmation {
        background-color: #6f42c1;
        border: 2px solid #ffc107;
    }
    /* Group session styles - pastel colors */
    .appointment.group-session {
        background-color: #e8f5e8;  /* Light pastel green */
        color: #2d5a2d;
        border: 2px solid #90c695;
        font-weight: 500;
    }
    .appointment.group-session.status-scheduled {
        background-color: #e8f5e8;  /* Light pastel green */
        color: #2d5a2d;
    }
    .appointment.group-session.status-completed {
        background-color: #d4edda;  /* Slightly darker green */
        color: #155724;
    }
    .appointment.group-session.status-cancelled {
        background-color: #f8d7da;  /* Light pastel red */
        color: #721c24;
        text-decoration: line-through;
    }
    .appointment.group-session.status-confirmed {
        background-color: #cce7ff;  /* Light pastel blue */
        color: #004085;
    }
    .time-slot {
        height: 60px;
        border-top: 1px solid #ddd;
        position: relative;
    }
    .day-header {
        height: 30px;
        text-align: center;
        border-bottom: 1px solid #ddd;
        font-weight: bold;
    }
    .current-time-indicator {
        position: absolute;
        height: 2px;
        background-color: red;
        width: 100%;
        z-index: 50;
    }
    
    /* Audit timeline styles */
    .audit-timeline {
        position: relative;
        padding-left: 30px;
        max-height: 400px;
        overflow-y: auto;
    }
    
    .audit-timeline::before {
        content: '';
        position: absolute;
        left: 10px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #dee2e6;
    }
    
    .audit-timeline .audit-entry {
        position: relative;
        margin-bottom: 20px;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 5px;
        border: 1px solid #dee2e6;
    }
    
    .audit-timeline .audit-entry::before {
        content: '';
        position: absolute;
        left: -25px;
        top: 20px;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background: #6c757d;
        border: 2px solid #fff;
    }
    
    .audit-timeline .audit-entry.action-create::before {
        background: #28a745;
    }
    
    .audit-timeline .audit-entry.action-update::before {
        background: #17a2b8;
    }
    
    .audit-timeline .audit-entry.action-cancel::before {
        background: #ffc107;
    }
    
    .audit-timeline .audit-entry.action-delete::before {
        background: #dc3545;
    }
    
    .audit-timeline .changes-table {
        font-size: 0.9rem;
    }
    
    .audit-timeline .changes-table td {
        padding: 0.25rem 0.5rem;
    }
    
    /* Context menu styles */
    .appointment-context-menu {
        position: fixed;
        background: white;
        border: 1px solid #ccc;
        border-radius: 4px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        z-index: 1000;
        min-width: 150px;
        display: none;
    }
    
    .appointment-context-menu .menu-item {
        padding: 8px 12px;
        cursor: pointer;
        border-bottom: 1px solid #eee;
    }
    
    .appointment-context-menu .menu-item:hover {
        background-color: #f5f5f5;
    }
    
    .appointment-context-menu .menu-item:last-child {
        border-bottom: none;
    }
    
    .appointment-context-menu .menu-item i {
        margin-right: 8px;
        width: 16px;
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header fade-in-up">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>{{ t('manager.schedule.title') }}</h1>
            <p>{{ t('manager.schedule.subtitle', 'View and manage appointments schedule') }}</p>
        </div>
        <div>
            <button type="button" class="btn btn-primary" onclick="openAppointmentModal()">
                <i class="fas fa-plus"></i> {{ t('manager.schedule.new_appointment') }}
            </button>
        </div>
    </div>
</div>

<!-- Legend -->
<div class="row mb-3">
    <div class="col-12">
        <div class="card fade-in-up">
            <div class="card-body py-2">
                <div class="d-flex align-items-center justify-content-center flex-wrap">
                    <span class="me-3"><strong>{{ t('manager.schedule.legend.title') }}</strong></span>
                    <span class="badge me-2 px-3 py-2" style="background-color: #0d6efd; color: white;">{{ t('manager.schedule.legend.regular_appointments') }}</span>
                    <span class="badge me-2 px-3 py-2" style="background-color: #e8f5e8; color: #2d5a2d; border: 1px solid #90c695;">{{ t('manager.schedule.legend.tecfee_group_sessions') }}</span>
                    <span class="badge me-2 px-3 py-2" style="background-color: #198754; color: white;">{{ t('manager.schedule.legend.completed') }}</span>
                    <span class="badge me-2 px-3 py-2" style="background-color: #dc3545; color: white;">{{ t('manager.schedule.legend.cancelled') }}</span>
                    <span class="badge px-3 py-2" style="background-color: #6f42c1; color: white; border: 2px solid #ffc107;">{{ t('manager.schedule.legend.awaiting_confirmation') }}</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Calendar Controls -->
<div class="row mb-4">
    <div class="col-md-4">
        <!-- View Type Selector -->
        <div class="btn-group" role="group">
            <a href="{{ url_for('manager.schedule', view='day', start_date=start_date.strftime('%Y-%m-%d')) }}"
               class="btn btn-outline-primary {{ 'active' if view_type == 'day' else '' }}">{{ t('manager.schedule.views.day') }}</a>
            <a href="{{ url_for('manager.schedule', view='week', start_date=start_date.strftime('%Y-%m-%d')) }}"
               class="btn btn-outline-primary {{ 'active' if view_type == 'week' else '' }}">{{ t('manager.schedule.views.week') }}</a>
            <a href="{{ url_for('manager.schedule', view='month', start_date=start_date.strftime('%Y-%m-%d')) }}"
               class="btn btn-outline-primary {{ 'active' if view_type == 'month' else '' }}">{{ t('manager.schedule.views.month') }}</a>
        </div>
    </div>
    <div class="col-md-4 text-center">
        <!-- Date Navigation -->
        <div class="btn-group" role="group">
            <a href="{{ url_for('manager.schedule', view=view_type, start_date=prev_date.strftime('%Y-%m-%d')) }}"
               class="btn btn-outline-secondary">
                <i class="fas fa-chevron-left"></i>
            </a>
            <button class="btn btn-outline-secondary" id="current-date-btn">
                {% if view_type == 'day' %}
                    {{ start_date.strftime('%B %d, %Y') }}
                {% elif view_type == 'week' %}
                    {{ t('manager.schedule.views.week_of') }} {{ start_date.strftime('%B %d, %Y') }}
                {% elif view_type == 'month' %}
                    {{ start_date.strftime('%B %Y') }}
                {% endif %}
            </button>
            <a href="{{ url_for('manager.schedule', view=view_type, start_date=next_date.strftime('%Y-%m-%d')) }}"
               class="btn btn-outline-secondary">
                <i class="fas fa-chevron-right"></i>
            </a>
        </div>
    </div>
    <div class="col-md-4 text-end">
        <!-- Tutor Filter -->
        <select class="form-select" id="tutor-filter">
            <option value="">{{ t('manager.schedule.views.all_tutors') }}</option>
            {% for tutor in tutors %}
                <option value="{{ tutor.id }}">{{ tutor.full_name }}</option>
            {% endfor %}
        </select>
    </div>
</div>

<!-- Calendar View -->
<div class="card fade-in-up">
    <div class="card-body p-0">
        <div class="calendar-container" id="calendar-container">
            {% if view_type == 'day' or view_type == 'week' %}
                <!-- Day/Week View -->
                <table class="table table-bordered m-0">
                    <thead class="calendar-header">
                        <tr>
                            <th class="time-column"></th>
                            {% if view_type == 'day' %}
                                {% for tutor in tutors %}
                                    <th class="tutor-column">{{ tutor.full_name }}</th>
                                {% endfor %}
                            {% elif view_type == 'week' %}
                                {% for i in range(7) %}
                                    {% set day = start_date + timedelta(days=i) %}
                                    <th class="text-center">
                                        {{ day.strftime('%a') }}<br>
                                        {{ day.strftime('%m/%d') }}
                                    </th>
                                {% endfor %}
                            {% endif %}
                        </tr>
                    </thead>
                    <tbody>
                        {% for hour in range(8, 22) %}
                            <tr>
                                <td class="time-column">
                                    {{ '{0}{1}'.format(hour % 12 if hour % 12 else 12, 'AM' if hour < 12 else 'PM') }}
                                </td>
                                {% if view_type == 'day' %}
                                    {% for tutor in tutors %}
                                        <td class="time-slot" data-tutor-id="{{ tutor.id }}" data-hour="{{ hour }}"></td>
                                    {% endfor %}
                                {% elif view_type == 'week' %}
                                    {% for i in range(7) %}
                                        <td class="time-slot" data-day="{{ i }}" data-hour="{{ hour }}"></td>
                                    {% endfor %}
                                {% endif %}
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% elif view_type == 'month' %}
                <!-- Month View -->
                <table class="table table-bordered m-0">
                    <thead class="calendar-header">
                        <tr>
                            <th class="text-center">{{ t('manager.schedule.days.sunday') }}</th>
                            <th class="text-center">{{ t('manager.schedule.days.monday') }}</th>
                            <th class="text-center">{{ t('manager.schedule.days.tuesday') }}</th>
                            <th class="text-center">{{ t('manager.schedule.days.wednesday') }}</th>
                            <th class="text-center">{{ t('manager.schedule.days.thursday') }}</th>
                            <th class="text-center">{{ t('manager.schedule.days.friday') }}</th>
                            <th class="text-center">{{ t('manager.schedule.days.saturday') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% set first_day = start_date %}
                        {% set last_day = next_date - timedelta(days=1) %}
                        {% set first_day_weekday = first_day.weekday() %}
                        {% set days_in_month = (last_day - first_day).days + 1 %}
                        {% set total_weeks = (days_in_month + first_day_weekday + 6) // 7 %}

                        {% for week in range(total_weeks) %}
                            <tr style="height: 120px;">
                                {% for weekday in range(7) %}
                                    {% set day_offset = week * 7 + weekday - first_day_weekday %}
                                    {% set current_day = first_day + timedelta(days=day_offset) %}

                                    {% if current_day.month == first_day.month %}
                                        <td class="position-relative" data-date="{{ current_day.strftime('%Y-%m-%d') }}">
                                            <div class="d-flex justify-content-between mb-2">
                                                <span class="fw-bold">{{ current_day.day }}</span>
                                                <a href="{{ url_for('manager.schedule', view='day', start_date=current_day.strftime('%Y-%m-%d')) }}"
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-calendar-day"></i>
                                                </a>
                                            </div>
                                            <div class="appointment-container" style="height: 90px; overflow-y: auto;"></div>
                                        </td>
                                    {% else %}
                                        <td class="bg-light text-muted">
                                            <span class="fw-light">{{ current_day.day }}</span>
                                        </td>
                                    {% endif %}
                                {% endfor %}
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% endif %}
        </div>
    </div>
</div>

<!-- Appointment Details Modal -->
<div class="modal fade" id="appointmentModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ t('manager.schedule.modal.appointment_details') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center" id="loadingSpinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">{{ t('manager.schedule.modal.loading') }}</span>
                    </div>
                </div>
                
                <!-- Tab navigation -->
                <ul class="nav nav-tabs" id="appointmentTabs" role="tablist" style="display: none;">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="details-tab" data-bs-toggle="tab" data-bs-target="#details" type="button" role="tab" aria-controls="details" aria-selected="true">
                            <i class="fas fa-info-circle"></i> Details
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="history-tab" data-bs-toggle="tab" data-bs-target="#history" type="button" role="tab" aria-controls="history" aria-selected="false">
                            <i class="fas fa-history"></i> Change History
                        </button>
                    </li>
                </ul>
                
                <!-- Tab content -->
                <div class="tab-content" id="appointmentTabContent" style="display: none;">
                    <!-- Details Tab -->
                    <div class="tab-pane fade show active pt-3" id="details" role="tabpanel" aria-labelledby="details-tab">
                        <div id="appointmentDetails">
                            <div class="mb-3">
                                <label class="fw-bold">{{ t('manager.schedule.modal.student') }}</label>
                                <span id="appointmentStudent"></span>
                            </div>
                            <div class="mb-3">
                                <label class="fw-bold">{{ t('manager.schedule.modal.tutor') }}</label>
                                <span id="appointmentTutor"></span>
                            </div>
                            <div class="mb-3">
                                <label class="fw-bold">{{ t('manager.schedule.modal.service') }}</label>
                                <span id="appointmentService"></span>
                            </div>
                            <div class="mb-3">
                                <label class="fw-bold">{{ t('manager.schedule.modal.date_time') }}</label>
                                <span id="appointmentDateTime"></span>
                            </div>
                            <div class="mb-3">
                                <label class="fw-bold">{{ t('manager.schedule.modal.status') }}</label>
                                <span id="appointmentStatus"></span>
                            </div>
                            <div class="mb-3">
                                <label class="fw-bold">{{ t('manager.schedule.modal.notes') }}</label>
                                <p id="appointmentNotes" class="text-muted"></p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- History Tab -->
                    <div class="tab-pane fade pt-3" id="history" role="tabpanel" aria-labelledby="history-tab">
                        <div id="auditHistory" class="audit-timeline">
                            <!-- Audit logs will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer flex-column">
                <!-- Status-specific action buttons -->
                <div class="w-100 mb-2" id="statusActionButtons">
                    <!-- Buttons will be dynamically populated based on appointment status -->
                </div>
                <div class="w-100 d-flex justify-content-between">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ t('manager.schedule.modal.close') }}</button>
                    <div class="btn-group">
                        <a href="#" id="editAppointmentBtn" class="btn btn-primary">{{ t('manager.schedule.modal.edit') }}</a>
                        <a href="#" id="viewAppointmentBtn" class="btn btn-info">{{ t('manager.schedule.modal.view') }}</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Date Picker Modal -->
<div class="modal fade" id="datePickerModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ t('manager.schedule.date_picker.select_date') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <input type="date" id="datePicker" class="form-control">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ t('manager.schedule.modal.close') }}</button>
                <button type="button" class="btn btn-primary" id="goToDateBtn">{{ t('manager.schedule.date_picker.go_to_date') }}</button>
            </div>
        </div>
    </div>
</div>

<!-- Appointment Form Modal -->
<div class="modal fade" id="appointmentFormModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="appointmentFormTitle">{{ t('manager.appointments.form.title_new') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="appointmentModalForm" method="POST">
                <div class="modal-body">
                    <div id="appointmentFormErrors" class="alert alert-danger d-none"></div>
                    
                    <!-- Hidden fields -->
                    <input type="hidden" id="modal_appointment_id" name="appointment_id" value="">
                    <input type="hidden" id="modal_selected_client_id" name="client_id" value="">
                    <input type="hidden" id="modal_selected_client_type" name="client_type" value="">
                    <input type="hidden" id="modal_selected_dependant_id" name="dependant_id" value="">
                    
                    <!-- Recurring Toggle Section -->
                    <div class="card mb-3" id="modal-recurring-toggle-section">
                        <div class="card-body">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="modal_is_recurring" name="is_recurring">
                                <label class="form-check-label" for="modal_is_recurring">
                                    <strong>{{ t('manager.appointments.form.make_recurring') }}</strong>
                                </label>
                                <div class="form-text">{{ t('manager.appointments.form.make_recurring_help') }}</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="modal_tutor_search" class="form-label">{{ t('manager.appointments.form.tutor') }}</label>
                            <input type="hidden" id="modal_tutor_id" name="tutor_id" value="" required>
                            <div id="modal_tutor_edit_mode" class="alert alert-info d-none">
                                <div>
                                    <strong id="modal_selected_tutor_name_display"></strong><br>
                                    <small class="text-muted" id="modal_selected_tutor_details_display"></small>
                                </div>
                            </div>
                            <div id="modal_tutor_search_mode">
                                <div class="position-relative">
                                    <input type="text" id="modal_tutor_search" class="form-control" 
                                           placeholder="{{ t('manager.appointments.form.select_tutor') }}" autocomplete="off">
                                    <div id="modal_tutor_search_results" class="dropdown-menu w-100" style="display: none; max-height: 300px; overflow-y: auto;"></div>
                                </div>
                                
                                <div id="modal_selected_tutor_display" class="mt-2" style="display: none;">
                                    <div class="alert alert-info d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong id="modal_selected_tutor_name"></strong><br>
                                            <small id="modal_selected_tutor_details" class="text-muted"></small>
                                        </div>
                                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearModalTutorSelection()">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="invalid-feedback d-block" id="modal_tutor_error"></div>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="modal_tutor_service_id" class="form-label">{{ t('manager.appointments.form.service') }}</label>
                            <select class="form-control" id="modal_tutor_service_id" name="tutor_service_id" required disabled>
                                <option value="">{{ t('manager.appointments.form.select_service') }}</option>
                            </select>
                            <div class="form-text">{{ t('manager.appointments.form.select_tutor_first') }}</div>
                            <div class="invalid-feedback"></div>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="modal_client_search" class="form-label">{{ t('manager.appointments.form.client_or_dependant') }}</label>
                            <div id="modal_client_edit_mode" class="alert alert-info d-none">
                                <div>
                                    <strong id="modal_selected_client_name_display"></strong><br>
                                    <small class="text-muted" id="modal_selected_client_details_display"></small><br>
                                    <small class="text-warning"><i class="fas fa-lock"></i> {{ t('manager.appointments.form.cannot_change_client') }}</small>
                                </div>
                            </div>
                            <div id="modal_client_search_mode">
                                <div class="position-relative">
                                    <input type="text" id="modal_client_search" class="form-control" 
                                           placeholder="{{ t('manager.appointments.form.search_placeholder') }}" autocomplete="off">
                                    <div id="modal_client_search_results" class="dropdown-menu w-100" style="display: none; max-height: 300px; overflow-y: auto;"></div>
                                </div>
                                
                                <div id="modal_selected_client_display" class="mt-2" style="display: none;">
                                    <div class="alert alert-info d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong id="modal_selected_client_name"></strong><br>
                                            <small id="modal_selected_client_details" class="text-muted"></small>
                                        </div>
                                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearModalClientSelection()">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="invalid-feedback d-block" id="modal_client_error"></div>
                        </div>
                    </div>
                    
                    <!-- Regular Appointment Date/Time Section -->
                    <div class="row mb-3" id="modal-regular-datetime-section">
                        <div class="col-md-4">
                            <label for="modal_start_date" class="form-label">{{ t('manager.appointments.form.date') }}</label>
                            <input type="date" class="form-control" id="modal_start_date" name="start_date" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-4">
                            <label for="modal_start_time" class="form-label">{{ t('manager.appointments.form.start_time') }}</label>
                            <select class="form-control" id="modal_start_time" name="start_time" required>
                                {% for hour in range(8, 21) %}
                                    {% for minute in [0, 30] %}
                                        <option value="{{ '%02d:%02d'|format(hour, minute) }}">{{ '%02d:%02d'|format(hour, minute) }}</option>
                                    {% endfor %}
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback"></div>
                        </div>
                        <div class="col-md-4">
                            <label for="modal_end_time" class="form-label">{{ t('manager.appointments.form.end_time') }}</label>
                            <input type="time" class="form-control" id="modal_end_time" name="end_time" readonly>
                            <div class="form-text">{{ t('manager.appointments.form.based_on_duration') }}</div>
                            <div class="invalid-feedback"></div>
                        </div>
                    </div>
                    
                    <!-- Recurring Pattern Section (hidden by default) -->
                    <div id="modal-recurring-pattern-section" style="display: none;">
                        <div class="card mb-3">
                            <div class="card-body">
                                <h6 class="card-title">{{ t('manager.appointments.form.recurring_pattern') }}</h6>
                                
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="modal_recurring_start_time" class="form-label">{{ t('manager.appointments.form.start_time') }}</label>
                                        <select class="form-control" id="modal_recurring_start_time" name="recurring_start_time">
                                            {% for hour in range(8, 21) %}
                                                {% for minute in [0, 30] %}
                                                    <option value="{{ '%02d:%02d'|format(hour, minute) }}">{{ '%02d:%02d'|format(hour, minute) }}</option>
                                                {% endfor %}
                                            {% endfor %}
                                        </select>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="modal_frequency" class="form-label">{{ t('manager.appointments.form.frequency') }}</label>
                                        <select class="form-control" id="modal_frequency" name="frequency">
                                            <option value="weekly">{{ t('manager.appointments.form.frequency_weekly') }}</option>
                                            <option value="biweekly">{{ t('manager.appointments.form.frequency_biweekly') }}</option>
                                            <option value="monthly">{{ t('manager.appointments.form.frequency_monthly') }}</option>
                                        </select>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="modal_day_of_week" class="form-label">{{ t('manager.appointments.form.day_of_week') }}</label>
                                        <select class="form-control" id="modal_day_of_week" name="day_of_week">
                                            <option value="0">{{ t('manager.appointments.form.monday') }}</option>
                                            <option value="1">{{ t('manager.appointments.form.tuesday') }}</option>
                                            <option value="2">{{ t('manager.appointments.form.wednesday') }}</option>
                                            <option value="3">{{ t('manager.appointments.form.thursday') }}</option>
                                            <option value="4">{{ t('manager.appointments.form.friday') }}</option>
                                            <option value="5">{{ t('manager.appointments.form.saturday') }}</option>
                                            <option value="6">{{ t('manager.appointments.form.sunday') }}</option>
                                        </select>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                    <div class="col-md-6" id="modal-week-of-month-field" style="display: none;">
                                        <label for="modal_week_of_month" class="form-label">{{ t('manager.appointments.form.week_of_month') }}</label>
                                        <select class="form-control" id="modal_week_of_month" name="week_of_month">
                                            <option value="1">{{ t('manager.appointments.form.first') }}</option>
                                            <option value="2">{{ t('manager.appointments.form.second') }}</option>
                                            <option value="3">{{ t('manager.appointments.form.third') }}</option>
                                            <option value="4">{{ t('manager.appointments.form.fourth') }}</option>
                                            <option value="5">{{ t('manager.appointments.form.last') }}</option>
                                        </select>
                                        <div class="form-text">{{ t('manager.appointments.form.required_for_monthly') }}</div>
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="modal_pattern_start_date" class="form-label">{{ t('manager.appointments.form.start_date') }}</label>
                                        <input type="date" class="form-control" id="modal_pattern_start_date" name="pattern_start_date">
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">{{ t('manager.appointments.form.end_pattern') }}</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="end_type" id="modal_end_type_never" value="never" checked>
                                        <label class="form-check-label" for="modal_end_type_never">
                                            {{ t('manager.appointments.form.never') }}
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="end_type" id="modal_end_type_date" value="date">
                                        <label class="form-check-label" for="modal_end_type_date">
                                            {{ t('manager.appointments.form.end_by_date') }}
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="end_type" id="modal_end_type_occurrences" value="occurrences">
                                        <label class="form-check-label" for="modal_end_type_occurrences">
                                            {{ t('manager.appointments.form.end_after_occurrences') }}
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="row mb-3">
                                    <div class="col-md-6" id="modal-end-date-field" style="display: none;">
                                        <label for="modal_pattern_end_date" class="form-label">{{ t('manager.appointments.form.end_date') }}</label>
                                        <input type="date" class="form-control" id="modal_pattern_end_date" name="pattern_end_date">
                                        <div class="invalid-feedback"></div>
                                    </div>
                                    <div class="col-md-6" id="modal-occurrences-field" style="display: none;">
                                        <label for="modal_pattern_occurrences" class="form-label">{{ t('manager.appointments.form.number_of_occurrences') }}</label>
                                        <input type="number" class="form-control" id="modal_pattern_occurrences" name="pattern_occurrences" min="1" max="100">
                                        <div class="invalid-feedback"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Availability Check -->
                    <div class="mb-3">
                        <div class="d-flex align-items-center mb-2">
                            <button id="modal-check-availability" type="button" class="btn btn-outline-primary me-3">
                                <i class="fas fa-calendar-check"></i> {{ t('manager.appointments.form.check_availability') }}
                            </button>
                            <span id="modal-availability-indicator"></span>
                        </div>
                    </div>
                    
                    <!-- Subscription Section -->
                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="modal_is_subscription_based" name="is_subscription_based">
                                <label class="form-check-label" for="modal_is_subscription_based">
                                    {{ t('manager.appointments.form.use_subscription') }}
                                </label>
                            </div>
                            
                            <div id="modal-subscription-fields" class="mb-3" style="display: none;">
                                <label for="modal_subscription_id" class="form-label">{{ t('manager.appointments.form.select_subscription') }}</label>
                                <select class="form-control" id="modal_subscription_id" name="subscription_id">
                                    <option value="0">{{ t('manager.appointments.form.no_subscription') }}</option>
                                </select>
                                <div class="form-text">{{ t('manager.appointments.form.select_client_first') }}</div>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Transport Fee Section -->
                    <div class="card mb-3" id="modal-transport-fee-section">
                        <div class="card-body">
                            <h5 class="card-title">{{ t('manager.appointments.form.transport_fee') }}</h5>
                            
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="modal_apply_transport_fee" name="apply_transport_fee">
                                <label class="form-check-label" for="modal_apply_transport_fee">
                                    {{ t('manager.appointments.form.apply_transport_fee') }}
                                </label>
                            </div>
                            
                            <div id="modal-fee-amount-field" class="mb-3" style="display: none;">
                                <label for="modal_transport_fee" class="form-label">{{ t('manager.appointments.form.transport_fee_amount') }}</label>
                                <input type="number" class="form-control" id="modal_transport_fee" name="transport_fee" step="0.01" min="0" value="0.00">
                                <div class="form-text">{{ t('manager.appointments.form.transport_fee_auto') }}</div>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Status field -->
                    <div class="mb-3" id="modal-status-section">
                        <label for="modal_status" class="form-label">{{ t('manager.appointments.form.status') }}</label>
                        <select class="form-control" id="modal_status" name="status">
                            <option value="scheduled">{{ t('manager.appointments.form.status_scheduled') }}</option>
                            <option value="completed">{{ t('manager.appointments.form.status_completed') }}</option>
                            <option value="cancelled">{{ t('manager.appointments.form.status_cancelled') }}</option>
                            <option value="no-show">{{ t('manager.appointments.form.status_no_show') }}</option>
                            <option value="awaiting_confirmation">{{ t('manager.appointments.form.status_awaiting_confirmation') }}</option>
                        </select>
                        <div class="invalid-feedback"></div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="modal_notes" class="form-label">{{ t('manager.appointments.form.notes') }}</label>
                        <textarea class="form-control" id="modal_notes" name="notes" rows="3"></textarea>
                        <div class="form-text">{{ t('manager.appointments.form.optional_notes') }}</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ t('manager.appointments.form.cancel') }}</button>
                    <button type="submit" class="btn btn-primary" id="modal-submit-btn">
                        <span id="modal-submit-text">{{ t('manager.appointments.form.create') }}</span>
                        <span id="modal-submit-spinner" class="spinner-border spinner-border-sm ms-2 d-none" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

<!-- Context Menu for Appointments -->
<div id="appointmentContextMenu" class="appointment-context-menu">
    <div class="menu-item" onclick="showAppointmentDetailsFromContext()">
        <i class="fas fa-info-circle"></i>
        View Details
    </div>
    <div class="menu-item" onclick="showAuditTrailFromContext()">
        <i class="fas fa-history"></i>
        View Audit Trail
    </div>
    <div class="menu-item" onclick="editAppointmentFromContext()">
        <i class="fas fa-edit"></i>
        Edit Appointment
    </div>
</div>

<!-- Include Audit Trail Modal -->
{% include 'components/audit_trail_modal.html' %}

{% block scripts %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/audit_trail_modal.css') }}">
<script src="{{ url_for('static', filename='js/audit_trail_modal.js') }}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Calendar data from backend
        const calendarData = {{ calendar_data | tojson }};
        const viewType = '{{ view_type }}';

        // Populate calendar with appointments
        renderCalendar(calendarData);

        // Show appointment modal when clicking on an appointment
        document.querySelectorAll('.appointment').forEach(function(appointment) {
            appointment.addEventListener('click', function(e) {
                const appointmentId = this.dataset.id;
                const appointmentType = this.dataset.type;

                // Check if user clicked on audit trail indicator
                if (e.target.closest('.audit-summary-text') || e.target.classList.contains('fa-history')) {
                    e.stopPropagation();
                    if (appointmentType !== 'group_session') {
                        // Show audit trail modal
                        if (window.auditTrailModal) {
                            window.auditTrailModal.showAuditTrail(appointmentId);
                        }
                    }
                    return;
                }

                if (appointmentType === 'group_session') {
                    // Redirect to group session details page
                    const groupSessionId = appointmentId.replace('group_', '');
                    window.location.href = `/manager/tecfee/group-sessions/${groupSessionId}`;
                } else {
                    showAppointmentDetails(appointmentId);
                }
            });

            // Add right-click context menu for audit trail access
            appointment.addEventListener('contextmenu', function(e) {
                const appointmentType = this.dataset.type;
                if (appointmentType !== 'group_session') {
                    e.preventDefault();
                    window.showAppointmentContextMenu(e, this.dataset.id);
                }
            });
        });

        // Date picker functionality
        document.getElementById('current-date-btn').addEventListener('click', function() {
            const datePickerModal = new bootstrap.Modal(document.getElementById('datePickerModal'));
            datePickerModal.show();
        });

        document.getElementById('goToDateBtn').addEventListener('click', function() {
            const date = document.getElementById('datePicker').value;
            if (date) {
                window.location.href = "{{ url_for('manager.schedule', view=view_type) }}" + "&start_date=" + date;
            }
        });

        // Tutor filter functionality
        document.getElementById('tutor-filter').addEventListener('change', function() {
            const tutorId = this.value;
            filterAppointmentsByTutor(tutorId);
        });

        // Mark current time on the calendar
        if (viewType === 'day') {
            markCurrentTime();
        }

        // Context menu functionality
        let currentContextAppointmentId = null;

        window.showAppointmentContextMenu = function(event, appointmentId) {
            currentContextAppointmentId = appointmentId;
            const contextMenu = document.getElementById('appointmentContextMenu');
            
            // Position the context menu
            contextMenu.style.left = event.pageX + 'px';
            contextMenu.style.top = event.pageY + 'px';
            contextMenu.style.display = 'block';
            
            // Hide context menu when clicking elsewhere
            document.addEventListener('click', hideContextMenu);
        };

        function hideContextMenu() {
            const contextMenu = document.getElementById('appointmentContextMenu');
            contextMenu.style.display = 'none';
            document.removeEventListener('click', hideContextMenu);
            currentContextAppointmentId = null;
        }

        window.showAppointmentDetailsFromContext = function() {
            if (currentContextAppointmentId) {
                showAppointmentDetails(currentContextAppointmentId);
                hideContextMenu();
            }
        };

        window.showAuditTrailFromContext = function() {
            if (currentContextAppointmentId && window.auditTrailModal) {
                window.auditTrailModal.showAuditTrail(currentContextAppointmentId);
                hideContextMenu();
            }
        };

        window.editAppointmentFromContext = function() {
            if (currentContextAppointmentId) {
                window.location.href = `/manager/appointments/${currentContextAppointmentId}/edit`;
                hideContextMenu();
            }
        };
    });

    function renderCalendar(data) {
        const appointments = data.appointments;
        const viewType = data.view_type;

        // Debug logging
        console.log('Calendar data:', data);
        console.log('Appointments:', appointments);

        if (viewType === 'day' || viewType === 'week') {
            appointments.forEach(function(appointment) {
                // Debug each appointment
                console.log('Rendering appointment:', appointment);
                console.log('Student name:', appointment.student_name);
                console.log('Client name:', appointment.client_name);
                const startTime = new Date(appointment.start_time);
                const endTime = new Date(appointment.end_time);

                // Calculate position and height
                const startHour = startTime.getHours() + startTime.getMinutes() / 60;
                const endHour = endTime.getHours() + endTime.getMinutes() / 60;
                const duration = endHour - startHour;

                // Create appointment element
                const appointmentEl = document.createElement('div');
                const isGroupSession = appointment.type === 'group_session';
                appointmentEl.className = `appointment status-${appointment.status}${isGroupSession ? ' group-session' : ''}`;
                appointmentEl.dataset.id = appointment.id;
                appointmentEl.dataset.type = appointment.type || 'appointment';

                if (isGroupSession) {
                    appointmentEl.innerHTML = `
                        <div class="fw-bold">${appointment.client_name}</div>
                        <div class="small">${appointment.student_name}</div>
                        <div class="small">${formatTime(startTime)} - ${formatTime(endTime)}</div>
                    `;
                } else {
                    appointmentEl.innerHTML = `
                        <div class="fw-bold">${appointment.student_name}</div>
                        <div class="small">${appointment.service_name}</div>
                        <div class="small">${formatTime(startTime)} - ${formatTime(endTime)}</div>
                        <div class="small text-muted mt-1">
                            <i class="fas fa-history" style="font-size: 0.7em;"></i>
                            <span class="audit-summary-text">Click for audit trail</span>
                        </div>
                    `;
                }

                if (viewType === 'day') {
                    // Place in tutor column
                    const tutorCells = document.querySelectorAll(`td[data-tutor-id="${appointment.tutor_id}"]`);
                    let targetCell = null;

                    for (let i = 0; i < tutorCells.length; i++) {
                        const cellHour = parseInt(tutorCells[i].dataset.hour);
                        if (cellHour === Math.floor(startHour)) {
                            targetCell = tutorCells[i];
                            break;
                        }
                    }

                    if (targetCell) {
                        appointmentEl.style.top = `${(startHour - Math.floor(startHour)) * 60}px`;
                        appointmentEl.style.height = `${duration * 60}px`;
                        targetCell.appendChild(appointmentEl);
                    }
                } else if (viewType === 'week') {
                    // Place in day column
                    const dayOfWeek = startTime.getDay();
                    const dayCells = document.querySelectorAll(`td[data-day="${dayOfWeek}"]`);
                    let targetCell = null;

                    for (let i = 0; i < dayCells.length; i++) {
                        const cellHour = parseInt(dayCells[i].dataset.hour);
                        if (cellHour === Math.floor(startHour)) {
                            targetCell = dayCells[i];
                            break;
                        }
                    }

                    if (targetCell) {
                        appointmentEl.style.top = `${(startHour - Math.floor(startHour)) * 60}px`;
                        appointmentEl.style.height = `${duration * 60}px`;
                        targetCell.appendChild(appointmentEl);
                    }
                }
            });
        } else if (viewType === 'month') {
            appointments.forEach(function(appointment) {
                const startTime = new Date(appointment.start_time);
                const dateStr = startTime.toISOString().split('T')[0];
                const cell = document.querySelector(`td[data-date="${dateStr}"]`);

                if (cell) {
                    const container = cell.querySelector('.appointment-container');
                    const appointmentEl = document.createElement('div');
                    const isGroupSession = appointment.type === 'group_session';
                    appointmentEl.className = `p-1 mb-1 rounded status-${appointment.status}${isGroupSession ? ' group-session' : ''}`;
                    appointmentEl.dataset.id = appointment.id;
                    appointmentEl.dataset.type = appointment.type || 'appointment';

                    if (isGroupSession) {
                        appointmentEl.innerHTML = `
                            <small>${formatTime(startTime)}: ${appointment.client_name}</small>
                        `;
                    } else {
                        appointmentEl.innerHTML = `
                            <small>${formatTime(startTime)}: ${appointment.student_name}</small>
                            <div class="d-flex justify-content-between align-items-center mt-1">
                                <small class="text-muted" style="font-size: 0.7em;">
                                    <i class="fas fa-history"></i> Audit
                                </small>
                            </div>
                        `;
                    }
                    container.appendChild(appointmentEl);
                }
            });
        }
    }

    function showAppointmentDetails(appointmentId) {
        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('appointmentModal'));
        modal.show();

        // Reset to first tab
        const detailsTab = document.getElementById('details-tab');
        const historyTab = document.getElementById('history-tab');
        const detailsPane = document.getElementById('details');
        const historyPane = document.getElementById('history');
        
        detailsTab.classList.add('active');
        historyTab.classList.remove('active');
        detailsPane.classList.add('show', 'active');
        historyPane.classList.remove('show', 'active');

        // Hide tabs and content, show spinner
        document.getElementById('appointmentTabs').style.display = 'none';
        document.getElementById('appointmentTabContent').style.display = 'none';
        document.getElementById('loadingSpinner').style.display = 'block';

        // Set up edit link
        document.getElementById('editAppointmentBtn').href = `/manager/appointments/${appointmentId}/edit`;

        // Set up view link
        document.getElementById('viewAppointmentBtn').href = `/manager/appointments/${appointmentId}`;

        // Status action buttons will be set up after fetching appointment data

        // Fetch appointment details and audit logs in parallel
        Promise.all([
            fetch(`/api/appointment/${appointmentId}`).then(r => r.json()),
            fetch(`/api/appointment/${appointmentId}/audit`).then(r => r.json())
        ])
        .then(([appointmentData, auditData]) => {
            // Populate appointment details
            document.getElementById('appointmentStudent').textContent = appointmentData.student.name;
            document.getElementById('appointmentTutor').textContent = appointmentData.tutor.name;
            document.getElementById('appointmentService').textContent = appointmentData.service.name;
            document.getElementById('appointmentDateTime').textContent =
                `${formatDateTime(new Date(appointmentData.start_time))} - ${formatTime(new Date(appointmentData.end_time))}`;

            // Format status
            const statusEl = document.getElementById('appointmentStatus');
            statusEl.textContent = appointmentData.status.charAt(0).toUpperCase() + appointmentData.status.slice(1);
            statusEl.className = '';
            statusEl.classList.add(`text-${getStatusColor(appointmentData.status)}`);

            // Notes
            document.getElementById('appointmentNotes').textContent = appointmentData.notes || '{{ t('manager.schedule.modal.no_notes') }}';

            // Populate audit history using the correct structure
            renderAuditHistory(auditData.audit_entries || []);

            // Hide spinner, show tabs and content
            document.getElementById('loadingSpinner').style.display = 'none';
            document.getElementById('appointmentTabs').style.display = 'flex';
            document.getElementById('appointmentTabContent').style.display = 'block';

            // Populate status action buttons based on appointment status
            populateStatusActionButtons(appointmentData.status, appointmentId);
        })
        .catch(error => {
            console.error('Error fetching appointment details:', error);
            document.getElementById('loadingSpinner').style.display = 'none';
            document.getElementById('appointmentTabContent').innerHTML = '<div class="alert alert-danger">{{ t('manager.schedule.modal.error_loading') }}</div>';
        });
    }

    function renderAuditHistory(auditEntries) {
        const auditContainer = document.getElementById('auditHistory');
        
        if (!auditEntries || auditEntries.length === 0) {
            auditContainer.innerHTML = '<div class="alert alert-info">No change history available for this appointment.</div>';
            return;
        }

        let html = '';
        
        auditEntries.forEach(entry => {
            const actionClass = `action-${entry.action}`;
            const badgeClass = {
                'create': 'success',
                'update': 'info',
                'cancel': 'warning',
                'delete': 'danger'
            }[entry.action] || 'secondary';

            html += `
                <div class="audit-entry ${actionClass}">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <div>
                            <span class="badge bg-${badgeClass}">${entry.action_description || entry.action}</span>
                            <strong class="ms-2">${entry.timestamp_est || entry.timestamp_utc}</strong>
                        </div>
                        <small class="text-muted">
                            By: ${entry.user_name || 'System'} ${entry.user_role ? `(${entry.user_role})` : ''}
                        </small>
                    </div>
                    
                    <p class="mb-2"><strong>Summary:</strong> ${entry.changes_summary || 'No summary available'}</p>
            `;

            // Show detailed changes for updates
            if (entry.action === 'update' && entry.changes_detail && entry.changes_detail.length > 0) {
                html += `
                    <h6 class="mt-3">Changes:</h6>
                    <table class="table table-sm changes-table">
                        <thead>
                            <tr>
                                <th>Field</th>
                                <th>Old Value</th>
                                <th>New Value</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                
                entry.changes_detail.forEach(change => {
                    html += `
                        <tr>
                            <td>${change.field_display || change.field}</td>
                            <td><span class="text-muted">${change.old_value_display || change.old_value || 'Not set'}</span></td>
                            <td><strong>${change.new_value_display || change.new_value || 'Not set'}</strong></td>
                        </tr>
                    `;
                });
                
                html += `
                        </tbody>
                    </table>
                `;
            }

            if (entry.notes) {
                html += `<p class="mb-0"><strong>Notes:</strong> ${entry.notes}</p>`;
            }

            html += '</div>';
        });

        auditContainer.innerHTML = html;
    }

    function populateStatusActionButtons(status, appointmentId) {
        const container = document.getElementById('statusActionButtons');
        container.innerHTML = ''; // Clear existing buttons

        if (status === 'scheduled') {
            // Scheduled appointments: Mark Awaiting Confirmation + Cancel
            container.innerHTML = `
                <div class="d-grid gap-2 d-md-block">
                    <button type="button" class="btn btn-warning btn-lg me-2 mb-2" onclick="quickStatusChange('${appointmentId}', 'awaiting_confirmation')">
                        <i class="fas fa-clock"></i> {{ t('manager.schedule.status_actions.mark_awaiting_confirmation') }}
                    </button>
                    <button type="button" class="btn btn-danger btn-lg mb-2" onclick="quickStatusChange('${appointmentId}', 'cancelled')">
                        <i class="fas fa-times-circle"></i> {{ t('manager.schedule.status_actions.cancel') }}
                    </button>
                </div>
            `;
        } else if (status === 'awaiting_confirmation' || status === 'awaiting_confirm') {
            // Awaiting confirmation: Mark Completed + Cancel
            container.innerHTML = `
                <div class="d-grid gap-2 d-md-block">
                    <button type="button" class="btn btn-success btn-lg me-2 mb-2" onclick="quickStatusChange('${appointmentId}', 'completed')">
                        <i class="fas fa-check-circle"></i> {{ t('manager.schedule.status_actions.mark_completed') }}
                    </button>
                    <button type="button" class="btn btn-danger btn-lg mb-2" onclick="quickStatusChange('${appointmentId}', 'cancelled')">
                        <i class="fas fa-times-circle"></i> {{ t('manager.schedule.status_actions.cancel') }}
                    </button>
                </div>
            `;
        } else if (status === 'completed') {
            // Completed appointments: Cancel only
            container.innerHTML = `
                <div class="d-grid gap-2 d-md-block">
                    <button type="button" class="btn btn-outline-danger btn-lg mb-2" onclick="quickStatusChange('${appointmentId}', 'cancelled')">
                        <i class="fas fa-times-circle"></i> {{ t('manager.schedule.status_actions.cancel') }}
                    </button>
                </div>
            `;
        } else if (status === 'cancelled' || status === 'no-show') {
            // Cancelled/No-show appointments: No action buttons
            container.innerHTML = `
                <div class="alert alert-info mb-0">
                    <i class="fas fa-info-circle"></i> {{ t('manager.schedule.status_actions.no_show_message') }}.
                </div>
            `;
        }
    }

    function quickStatusChange(appointmentId, newStatus) {
        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/manager/appointments/${appointmentId}/quick-status-change`;

        // Add CSRF token
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'csrf_token';
        csrfInput.value = getCsrfToken();
        form.appendChild(csrfInput);

        // Add status
        const statusInput = document.createElement('input');
        statusInput.type = 'hidden';
        statusInput.name = 'status';
        statusInput.value = newStatus;
        form.appendChild(statusInput);

        // Submit form
        document.body.appendChild(form);
        form.submit();
    }



    function filterAppointmentsByTutor(tutorId) {
        if (tutorId) {
            // Hide all appointments not from this tutor
            document.querySelectorAll('.appointment').forEach(function(appointment) {
                const appointmentData = calendarData.appointments.find(a => a.id == appointment.dataset.id);
                if (appointmentData && appointmentData.tutor_id != tutorId) {
                    appointment.style.display = 'none';
                } else {
                    appointment.style.display = 'block';
                }
            });
        } else {
            // Show all appointments
            document.querySelectorAll('.appointment').forEach(function(appointment) {
                appointment.style.display = 'block';
            });
        }
    }

    function markCurrentTime() {
        const now = new Date();
        const currentHour = now.getHours();
        const currentMinutes = now.getMinutes();

        // Only mark time if within business hours (8AM-9PM)
        if (currentHour >= 8 && currentHour < 21) {
            const hourCells = document.querySelectorAll(`td[data-hour="${currentHour}"]`);

            hourCells.forEach(function(cell) {
                const indicator = document.createElement('div');
                indicator.className = 'current-time-indicator';
                indicator.style.top = `${currentMinutes}px`;
                cell.appendChild(indicator);
            });
        }
    }

    function formatTime(date) {
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }

    function formatDateTime(date) {
        return date.toLocaleDateString() + ' ' + formatTime(date);
    }

    function getStatusColor(status) {
        switch (status) {
            case 'completed': return 'success';
            case 'scheduled': return 'primary';
            case 'cancelled': return 'danger';
            case 'no-show': return 'warning';
            case 'awaiting_confirmation': return 'info';
            default: return 'secondary';
        }
    }

    function getCsrfToken() {
        const csrfMeta = document.querySelector('meta[name="csrf-token"]');
        if (!csrfMeta) {
            console.error('CSRF token meta tag not found');
            return '';
        }
        const token = csrfMeta.getAttribute('content');
        if (!token) {
            console.error('CSRF token content is empty');
            return '';
        }
        return token;
    }

    // Appointment Form Modal Functions
    let appointmentModal = null;
    let isEditMode = false;
    let modalSearchTimeout = null;

    function openAppointmentModal(appointmentId = null) {
        isEditMode = !!appointmentId;
        
        // Get or create modal instance
        if (!appointmentModal) {
            appointmentModal = new bootstrap.Modal(document.getElementById('appointmentFormModal'));
        }
        
        // Reset form
        resetAppointmentForm();
        
        // Load tutors
        loadTutors();
        
        // Update modal title and button text
        document.getElementById('appointmentFormTitle').textContent = isEditMode 
            ? '{{ t('manager.appointments.form.title_edit') }}' 
            : '{{ t('manager.appointments.form.title_new') }}';
        document.getElementById('modal-submit-text').textContent = isEditMode 
            ? '{{ t('manager.appointments.form.update') }}' 
            : '{{ t('manager.appointments.form.create') }}';
        
        if (isEditMode) {
            // Load appointment data for editing
            document.getElementById('modal_appointment_id').value = appointmentId;
            loadAppointmentData(appointmentId);
        } else {
            // Set default date and time for new appointments
            setDefaultDateTime();
        }
        
        // Show modal
        appointmentModal.show();
    }

    function resetAppointmentForm() {
        const form = document.getElementById('appointmentModalForm');
        form.reset();
        
        // Clear hidden fields
        document.getElementById('modal_appointment_id').value = '';
        document.getElementById('modal_selected_client_id').value = '';
        document.getElementById('modal_selected_client_type').value = '';
        document.getElementById('modal_selected_dependant_id').value = '';
        
        // Clear error messages
        document.getElementById('appointmentFormErrors').classList.add('d-none');
        document.getElementById('appointmentFormErrors').innerHTML = '';
        document.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
        document.querySelectorAll('.invalid-feedback').forEach(el => el.textContent = '');
        
        // Reset client display
        document.getElementById('modal_client_edit_mode').classList.add('d-none');
        document.getElementById('modal_client_search_mode').classList.remove('d-none');
        document.getElementById('modal_client_search').value = '';
        document.getElementById('modal_selected_client_display').style.display = 'none';
        document.getElementById('modal_client_search_results').style.display = 'none';
        
        // Reset tutor display
        document.getElementById('modal_tutor_id').value = '';
        document.getElementById('modal_tutor_edit_mode').classList.add('d-none');
        document.getElementById('modal_tutor_search_mode').classList.remove('d-none');
        document.getElementById('modal_tutor_search').value = '';
        document.getElementById('modal_selected_tutor_display').style.display = 'none';
        document.getElementById('modal_tutor_search_results').style.display = 'none';
        
        // Reset service dropdown
        document.getElementById('modal_tutor_service_id').innerHTML = '<option value="">{{ t('manager.appointments.form.select_service') }}</option>';
        document.getElementById('modal_tutor_service_id').disabled = true;
        
        // Reset recurring sections
        document.getElementById('modal_is_recurring').checked = false;
        toggleModalRecurringFields();
        
        // Reset subscription
        document.getElementById('modal_is_subscription_based').checked = false;
        toggleModalSubscriptionFields();
        
        // Reset transport fee
        document.getElementById('modal_apply_transport_fee').checked = false;
        toggleModalTransportFeeInput();
        
        // Reset status to scheduled
        document.getElementById('modal_status').value = 'scheduled';
    }

    function setDefaultDateTime() {
        const now = new Date();
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const day = String(now.getDate()).padStart(2, '0');
        document.getElementById('modal_start_date').value = `${year}-${month}-${day}`;
        
        // Set default time to next 30-minute increment
        const currentHour = now.getHours();
        const currentMinute = now.getMinutes();
        let nextHour = currentHour;
        let nextMinute = currentMinute <= 30 ? 30 : 0;
        
        if (currentMinute > 30) {
            nextHour += 1;
        }
        
        // Ensure within business hours
        if (nextHour < 8) {
            nextHour = 8;
            nextMinute = 0;
        } else if (nextHour >= 21) {
            nextHour = 8;
            nextMinute = 0;
            // Set to tomorrow
            const tomorrow = new Date(now);
            tomorrow.setDate(tomorrow.getDate() + 1);
            const tYear = tomorrow.getFullYear();
            const tMonth = String(tomorrow.getMonth() + 1).padStart(2, '0');
            const tDay = String(tomorrow.getDate()).padStart(2, '0');
            document.getElementById('modal_start_date').value = `${tYear}-${tMonth}-${tDay}`;
        }
        
        const timeValue = `${String(nextHour).padStart(2, '0')}:${String(nextMinute).padStart(2, '0')}`;
        document.getElementById('modal_start_time').value = timeValue;
    }

    let tutorsData = [];
    let modalTutorSearchTimeout = null;
    
    function loadTutors() {
        return fetch('/api/tutors')
            .then(response => response.json())
            .then(tutors => {
                tutorsData = tutors;
                setupTutorSearch();
                return tutors;
            })
            .catch(error => {
                console.error('Error loading tutors:', error);
                throw error;
            });
    }
    
    function setupTutorSearch() {
        const searchInput = document.getElementById('modal_tutor_search');
        const searchResults = document.getElementById('modal_tutor_search_results');
        
        // Remove existing event listeners
        searchInput.removeEventListener('input', handleTutorSearchInput);
        searchInput.removeEventListener('focus', handleTutorSearchFocus);
        document.removeEventListener('click', handleTutorClickOutside);
        
        // Add event listeners
        searchInput.addEventListener('input', handleTutorSearchInput);
        searchInput.addEventListener('focus', handleTutorSearchFocus);
        document.addEventListener('click', handleTutorClickOutside);
    }
    
    function handleTutorSearchInput(e) {
        clearTimeout(modalTutorSearchTimeout);
        modalTutorSearchTimeout = setTimeout(() => {
            searchTutors(e.target.value);
        }, 300);
    }
    
    function handleTutorSearchFocus(e) {
        if (e.target.value.trim()) {
            searchTutors(e.target.value);
        }
    }
    
    function handleTutorClickOutside(e) {
        const searchResults = document.getElementById('modal_tutor_search_results');
        const searchInput = document.getElementById('modal_tutor_search');
        if (!searchInput.contains(e.target) && !searchResults.contains(e.target)) {
            searchResults.style.display = 'none';
        }
    }
    
    function searchTutors(query) {
        const searchResults = document.getElementById('modal_tutor_search_results');
        
        if (!query.trim()) {
            searchResults.style.display = 'none';
            return;
        }
        
        const filteredTutors = tutorsData.filter(tutor => {
            const fullName = `${tutor.first_name} ${tutor.last_name}`.toLowerCase();
            const email = (tutor.email || '').toLowerCase();
            const searchTerm = query.toLowerCase();
            return fullName.includes(searchTerm) || email.includes(searchTerm);
        });
        
        searchResults.innerHTML = '';
        
        if (filteredTutors.length === 0) {
            const noResultsItem = document.createElement('div');
            noResultsItem.className = 'dropdown-item disabled';
            noResultsItem.textContent = 'No tutors found';
            searchResults.appendChild(noResultsItem);
        } else {
            filteredTutors.forEach(tutor => {
                const item = document.createElement('a');
                item.className = 'dropdown-item';
                item.href = '#';
                item.onclick = (e) => {
                    e.preventDefault();
                    selectModalTutor(tutor);
                };
                
                const nameSpan = document.createElement('strong');
                nameSpan.textContent = `${tutor.first_name} ${tutor.last_name}`;
                
                const detailsSpan = document.createElement('small');
                detailsSpan.className = 'd-block text-muted';
                detailsSpan.textContent = tutor.email || 'No email';
                
                item.appendChild(nameSpan);
                item.appendChild(detailsSpan);
                searchResults.appendChild(item);
            });
        }
        
        searchResults.style.display = 'block';
    }
    
    function selectModalTutor(tutor) {
        document.getElementById('modal_tutor_id').value = tutor.id;
        document.getElementById('modal_tutor_search').value = '';
        document.getElementById('modal_tutor_search_results').style.display = 'none';
        
        // Show selected tutor display
        document.getElementById('modal_selected_tutor_name').textContent = `${tutor.first_name} ${tutor.last_name}`;
        document.getElementById('modal_selected_tutor_details').textContent = tutor.email || 'No email';
        document.getElementById('modal_selected_tutor_display').style.display = 'block';
        
        // Load services for the selected tutor
        loadTutorServices(tutor.id);
        
        // Clear any error messages
        document.getElementById('modal_tutor_error').textContent = '';
        document.getElementById('modal_tutor_search').classList.remove('is-invalid');
    }
    
    function clearModalTutorSelection() {
        document.getElementById('modal_tutor_id').value = '';
        document.getElementById('modal_selected_tutor_display').style.display = 'none';
        document.getElementById('modal_tutor_search').value = '';
        
        // Clear services
        document.getElementById('modal_tutor_service_id').innerHTML = '<option value="">{{ t('manager.appointments.form.select_service') }}</option>';
        document.getElementById('modal_tutor_service_id').disabled = true;
    }

    function loadAppointmentData(appointmentId) {
        fetch(`/api/appointment/${appointmentId}`)
            .then(response => response.json())
            .then(data => {
                // Set tutor - show in edit mode
                document.getElementById('modal_tutor_edit_mode').classList.remove('d-none');
                document.getElementById('modal_tutor_search_mode').classList.add('d-none');
                document.getElementById('modal_tutor_id').value = data.tutor.id;
                document.getElementById('modal_selected_tutor_name_display').textContent = data.tutor.name;
                document.getElementById('modal_selected_tutor_details_display').textContent = 'No email';
                
                // Load services for the tutor and then set the service
                loadTutorServices(data.tutor.id, data.tutor_service_id);
                
                // Set client/dependant - show in edit mode
                document.getElementById('modal_client_edit_mode').classList.remove('d-none');
                document.getElementById('modal_client_search_mode').classList.add('d-none');
                
                if (data.dependant_id) {
                    document.getElementById('modal_selected_client_id').value = `d_${data.dependant_id}`;
                    document.getElementById('modal_selected_client_type').value = 'dependant';
                    document.getElementById('modal_selected_dependant_id').value = data.dependant_id;
                    document.getElementById('modal_selected_client_name_display').textContent = data.dependant.name;
                    document.getElementById('modal_selected_client_details_display').textContent = 
                        `${data.dependant.email || 'No email'} • ${data.dependant.phone || 'No phone'} • Dependant`;
                } else {
                    document.getElementById('modal_selected_client_id').value = `c_${data.client.id}`;
                    document.getElementById('modal_selected_client_type').value = 'client';
                    document.getElementById('modal_selected_client_name_display').textContent = data.client.name;
                    document.getElementById('modal_selected_client_details_display').textContent = 
                        `${data.client.email || 'No email'} • ${data.client.phone || 'No phone'} • Client`;
                }
                
                // Set date and time
                const startDate = new Date(data.start_time);
                document.getElementById('modal_start_date').value = startDate.toISOString().split('T')[0];
                document.getElementById('modal_start_time').value = 
                    `${String(startDate.getHours()).padStart(2, '0')}:${String(startDate.getMinutes()).padStart(2, '0')}`;
                
                // Set status
                document.getElementById('modal_status').value = data.status;
                
                // Set notes
                document.getElementById('modal_notes').value = data.notes || '';
                
                // Load subscriptions for the client
                updateModalSubscriptions();
                
                // Set subscription if used
                if (data.is_subscription_based) {
                    document.getElementById('modal_is_subscription_based').checked = true;
                    toggleModalSubscriptionFields();
                    document.getElementById('modal_subscription_id').value = data.subscription_id || '0';
                }
                
                // Set transport fee
                if (data.apply_transport_fee) {
                    document.getElementById('modal_apply_transport_fee').checked = true;
                    toggleModalTransportFeeInput();
                    document.getElementById('modal_transport_fee').value = data.transport_fee || '0.00';
                }
            })
            .catch(error => {
                console.error('Error loading appointment data:', error);
                showModalError('Error loading appointment data');
            });
    }

    function loadTutorServices(tutorId, selectedServiceId = null) {
        const serviceSelect = document.getElementById('modal_tutor_service_id');
        
        if (!tutorId) {
            serviceSelect.innerHTML = '<option value="">{{ t('manager.appointments.form.select_tutor_first') }}</option>';
            serviceSelect.disabled = true;
            return;
        }
        
        serviceSelect.innerHTML = '<option value="">Loading services...</option>';
        serviceSelect.disabled = true;
        
        fetch(`/api/tutor-services/${tutorId}`)
            .then(response => response.json())
            .then(services => {
                serviceSelect.innerHTML = '<option value="">{{ t('manager.appointments.form.select_service') }}</option>';
                
                services.forEach(service => {
                    const option = document.createElement('option');
                    option.value = service.id;
                    option.textContent = `${service.name} ($${service.client_rate.toFixed(2)}/hr) (${service.duration_minutes} minutes)`;
                    option.dataset.transportFee = service.transport_fee || 0;
                    
                    if (selectedServiceId && service.id == selectedServiceId) {
                        option.selected = true;
                    }
                    
                    serviceSelect.appendChild(option);
                });
                
                serviceSelect.disabled = false;
                
                // Update end time if service is selected
                if (serviceSelect.value) {
                    updateModalEndTime();
                }
            })
            .catch(error => {
                console.error('Error fetching services:', error);
                serviceSelect.innerHTML = '<option value="">Error loading services</option>';
            });
    }

    function updateModalEndTime() {
        const serviceSelect = document.getElementById('modal_tutor_service_id');
        const startTimeInput = document.getElementById('modal_start_time');
        const endTimeInput = document.getElementById('modal_end_time');
        
        if (serviceSelect.value && startTimeInput.value) {
            const selectedOption = serviceSelect.options[serviceSelect.selectedIndex];
            const serviceText = selectedOption.text;
            
            // Extract duration from service text
            const durationMatch = serviceText.match(/\((\d+) minutes\)/);
            if (durationMatch && durationMatch[1]) {
                const durationMinutes = parseInt(durationMatch[1]);
                
                // Parse start time
                const [startHours, startMinutes] = startTimeInput.value.split(':').map(Number);
                
                // Calculate end time
                let endMinutes = startMinutes + durationMinutes;
                let endHours = startHours + Math.floor(endMinutes / 60);
                endMinutes = endMinutes % 60;
                
                // Check if end time is after 9 PM
                if (endHours > 21 || (endHours === 21 && endMinutes > 0)) {
                    alert('Warning: The appointment would end after 9:00 PM. Please select an earlier start time.');
                    return;
                }
                
                // Format end time
                const formattedEndHours = endHours.toString().padStart(2, '0');
                const formattedEndMinutes = endMinutes.toString().padStart(2, '0');
                
                endTimeInput.value = `${formattedEndHours}:${formattedEndMinutes}`;
            }
        }
    }

    function toggleModalRecurringFields() {
        const isRecurring = document.getElementById('modal_is_recurring').checked;
        const regularSection = document.getElementById('modal-regular-datetime-section');
        const recurringSection = document.getElementById('modal-recurring-pattern-section');
        const statusSection = document.getElementById('modal-status-section');
        const availabilityButton = document.getElementById('modal-check-availability');
        
        if (isRecurring) {
            regularSection.style.display = 'none';
            recurringSection.style.display = 'block';
            statusSection.style.display = 'none';
            availabilityButton.style.display = 'none';
        } else {
            regularSection.style.display = 'block';
            recurringSection.style.display = 'none';
            statusSection.style.display = 'block';
            availabilityButton.style.display = 'block';
        }
    }

    function toggleModalSubscriptionFields() {
        const isSubscriptionBased = document.getElementById('modal_is_subscription_based').checked;
        const subscriptionFields = document.getElementById('modal-subscription-fields');
        const transportFeeSection = document.getElementById('modal-transport-fee-section');
        
        subscriptionFields.style.display = isSubscriptionBased ? 'block' : 'none';
        
        if (isSubscriptionBased) {
            transportFeeSection.style.display = 'none';
            document.getElementById('modal_transport_fee').value = '0.00';
            document.getElementById('modal_apply_transport_fee').checked = false;
        } else {
            transportFeeSection.style.display = 'block';
        }
    }

    function toggleModalTransportFeeInput() {
        const applyFee = document.getElementById('modal_apply_transport_fee').checked;
        const feeAmountField = document.getElementById('modal-fee-amount-field');
        
        feeAmountField.style.display = applyFee ? 'block' : 'none';
        
        if (!applyFee) {
            document.getElementById('modal_transport_fee').value = '0.00';
        } else {
            updateModalTransportFee();
        }
    }

    function updateModalTransportFee() {
        const tutorId = document.getElementById('modal_tutor_id').value;
        const clientId = document.getElementById('modal_selected_client_id').value;
        const serviceSelect = document.getElementById('modal_tutor_service_id');
        
        if (!tutorId || !clientId) return;
        
        // Try to get fee from service data first
        if (serviceSelect.value) {
            const selectedOption = serviceSelect.options[serviceSelect.selectedIndex];
            if (selectedOption && selectedOption.dataset.transportFee) {
                const transportFee = parseFloat(selectedOption.dataset.transportFee);
                document.getElementById('modal_transport_fee').value = transportFee.toFixed(2);
                return;
            }
        }
        
        // Fallback to API call
        let actualClientId = clientId;
        if (clientId.startsWith('c_')) {
            actualClientId = clientId.substring(2);
        } else if (clientId.startsWith('d_')) {
            actualClientId = clientId.substring(2);
        }
        
        fetch(`/api/transport-fee/${tutorId}/${actualClientId}`)
            .then(response => response.json())
            .then(data => {
                if (data && data.fee_amount !== undefined) {
                    document.getElementById('modal_transport_fee').value = data.fee_amount.toFixed(2);
                }
            })
            .catch(error => {
                console.error('Error fetching transport fee:', error);
            });
    }

    function updateModalSubscriptions() {
        const clientId = document.getElementById('modal_selected_client_id').value;
        const clientType = document.getElementById('modal_selected_client_type').value;
        const subscriptionSelect = document.getElementById('modal_subscription_id');
        const subscriptionCheckbox = document.getElementById('modal_is_subscription_based');
        
        if (!clientId) {
            subscriptionSelect.innerHTML = '<option value="0">{{ t('manager.appointments.form.select_client_first') }}</option>';
            subscriptionSelect.disabled = true;
            subscriptionCheckbox.disabled = true;
            return;
        }
        
        let actualId = clientId;
        if (clientId.startsWith('c_')) {
            actualId = clientId.substring(2);
        } else if (clientId.startsWith('d_')) {
            actualId = clientId.substring(2);
        }
        
        const apiEndpoint = clientType === 'client' 
            ? `/api/client/${actualId}/subscriptions`
            : `/api/dependant/${actualId}/subscriptions`;
        
        subscriptionSelect.innerHTML = '<option value="0">Loading subscriptions...</option>';
        subscriptionSelect.disabled = true;
        
        fetch(apiEndpoint)
            .then(response => response.json())
            .then(data => {
                subscriptionSelect.innerHTML = '<option value="0">No Subscription</option>';
                
                if (data && data.length > 0) {
                    data.forEach(subscription => {
                        const option = document.createElement('option');
                        option.value = subscription.id;
                        const ownerInfo = clientType === 'dependant' ? ` (${subscription.owner_name}'s subscription)` : '';
                        option.textContent = `${subscription.plan_name} (${subscription.hours_remaining} hours remaining)${ownerInfo}`;
                        subscriptionSelect.appendChild(option);
                    });
                    
                    subscriptionCheckbox.disabled = false;
                    subscriptionSelect.disabled = false;
                } else {
                    subscriptionCheckbox.checked = false;
                    subscriptionCheckbox.disabled = true;
                    
                    if (clientType === 'dependant') {
                        subscriptionSelect.innerHTML = '<option value="0">No parent subscriptions available</option>';
                    }
                }
            })
            .catch(error => {
                console.error('Error fetching subscriptions:', error);
                subscriptionSelect.innerHTML = '<option value="0">Error loading subscriptions</option>';
            });
    }

    function clearModalClientSelection() {
        document.getElementById('modal_selected_client_id').value = '';
        document.getElementById('modal_selected_client_type').value = '';
        document.getElementById('modal_selected_dependant_id').value = '';
        document.getElementById('modal_client_search').value = '';
        document.getElementById('modal_selected_client_display').style.display = 'none';
        document.getElementById('modal_client_search_results').style.display = 'none';
        
        // Reset subscription if exists
        const subscriptionSelect = document.getElementById('modal_subscription_id');
        if (subscriptionSelect) {
            subscriptionSelect.innerHTML = '<option value="0">Select a client first</option>';
            subscriptionSelect.disabled = true;
        }
    }

    function searchModalClients(query) {
        if (query.length < 2) {
            document.getElementById('modal_client_search_results').style.display = 'none';
            return;
        }
        
        fetch(`/api/clients-and-dependants/search?q=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                displayModalSearchResults(data);
            })
            .catch(error => {
                console.error('Error searching clients and dependants:', error);
                document.getElementById('modal_client_search_results').innerHTML = '<div class="dropdown-item text-danger">Error loading results</div>';
                document.getElementById('modal_client_search_results').style.display = 'block';
            });
    }
    
    function displayModalSearchResults(results) {
        const resultsContainer = document.getElementById('modal_client_search_results');
        
        if (results.length === 0) {
            resultsContainer.innerHTML = '<div class="dropdown-item text-muted">No results found</div>';
            resultsContainer.style.display = 'block';
            return;
        }
        
        let html = '';
        results.forEach(result => {
            const typeLabel = result.type === 'client' ? 'Client' : 'Dependant';
            const typeBadge = result.type === 'client' ? 'bg-primary' : 'bg-success';
            
            html += `
                <button type="button" class="dropdown-item" onclick="selectModalClientOrDependant('${result.id}', '${result.name}', '${result.email}', '${result.phone}', '${result.type}')">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>${result.name}</strong><br>
                            <small class="text-muted">${result.email} • ${result.phone}</small>
                        </div>
                        <span class="badge ${typeBadge}">${typeLabel}</span>
                    </div>
                </button>
            `;
        });
        
        resultsContainer.innerHTML = html;
        resultsContainer.style.display = 'block';
    }
    
    function selectModalClientOrDependant(id, name, email, phone, type) {
        document.getElementById('modal_selected_client_type').value = type;
        document.getElementById('modal_selected_client_name').textContent = name;
        document.getElementById('modal_selected_client_details').textContent = `${email} • ${phone} • ${type === 'client' ? 'Client' : 'Dependant'}`;
        
        if (type === 'client') {
            document.getElementById('modal_selected_client_id').value = `c_${id}`;
            document.getElementById('modal_selected_dependant_id').value = '';
        } else {
            document.getElementById('modal_selected_client_id').value = `d_${id}`;
            document.getElementById('modal_selected_dependant_id').value = id;
        }
        
        document.getElementById('modal_client_search').value = name;
        document.getElementById('modal_client_search_results').style.display = 'none';
        document.getElementById('modal_selected_client_display').style.display = 'block';
        
        // Update subscriptions
        updateModalSubscriptions();
    }

    function displayModalSearchResults(results) {
        const resultsDiv = document.getElementById('modal_client_search_results');
        
        if (results.length === 0) {
            resultsDiv.innerHTML = '<div class="dropdown-item text-muted">No results found</div>';
            resultsDiv.style.display = 'block';
            return;
        }
        
        let html = '';
        results.forEach(result => {
            const typeLabel = result.type === 'client' ? 'Client' : 'Dependant';
            const typeBadge = result.type === 'client' ? 'bg-primary' : 'bg-success';
            
            html += `
                <button type="button" class="dropdown-item" onclick="selectModalClient('${result.id}', '${result.name}', '${result.email}', '${result.phone}', '${result.type}')">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>${result.name}</strong><br>
                            <small class="text-muted">${result.email} • ${result.phone}</small>
                        </div>
                        <span class="badge ${typeBadge}">${typeLabel}</span>
                    </div>
                </button>
            `;
        });
        
        resultsDiv.innerHTML = html;
        resultsDiv.style.display = 'block';
    }

    function selectModalClient(id, name, email, phone, type) {
        document.getElementById('modal_selected_client_type').value = type;
        document.getElementById('modal_selected_client_name').textContent = name;
        document.getElementById('modal_selected_client_details').textContent = `${email} • ${phone} • ${type === 'client' ? 'Client' : 'Dependant'}`;
        
        if (type === 'client') {
            document.getElementById('modal_selected_client_id').value = `c_${id}`;
            document.getElementById('modal_selected_dependant_id').value = '';
        } else {
            document.getElementById('modal_selected_client_id').value = `d_${id}`;
            document.getElementById('modal_selected_dependant_id').value = id;
        }
        
        document.getElementById('modal_client_search').value = name;
        document.getElementById('modal_client_search_results').style.display = 'none';
        document.getElementById('modal_selected_client_display').style.display = 'block';
        
        // Update subscriptions and transport fee
        updateModalSubscriptions();
        updateModalTransportFee();
    }

    function clearModalClientSelection() {
        document.getElementById('modal_selected_client_id').value = '';
        document.getElementById('modal_selected_client_type').value = '';
        document.getElementById('modal_selected_dependant_id').value = '';
        document.getElementById('modal_client_search').value = '';
        document.getElementById('modal_selected_client_display').style.display = 'none';
        document.getElementById('modal_client_search_results').style.display = 'none';
        
        // Clear subscriptions
        updateModalSubscriptions();
    }

    function showModalError(message) {
        const errorDiv = document.getElementById('appointmentFormErrors');
        errorDiv.innerHTML = message;
        errorDiv.classList.remove('d-none');
    }

    function clearModalErrors() {
        document.getElementById('appointmentFormErrors').classList.add('d-none');
        document.getElementById('appointmentFormErrors').innerHTML = '';
        document.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
        document.querySelectorAll('.invalid-feedback').forEach(el => el.textContent = '');
    }

    function submitAppointmentForm(event) {
        event.preventDefault();
        
        clearModalErrors();
        
        // Show loading state
        document.getElementById('modal-submit-btn').disabled = true;
        document.getElementById('modal-submit-spinner').classList.remove('d-none');
        
        // Collect form data
        const formData = new FormData(event.target);
        const data = {};
        
        // Convert FormData to object
        for (let [key, value] of formData.entries()) {
            if (key === 'is_recurring' || key === 'is_subscription_based' || key === 'apply_transport_fee') {
                data[key] = value === 'on';
            } else if (key === 'end_type') {
                data[key] = value;
            } else {
                data[key] = value;
            }
        }
        
        // Handle recurring time field
        if (data.is_recurring && data.recurring_start_time) {
            data.start_time = data.recurring_start_time;
        }
        
        // Determine endpoint
        const appointmentId = document.getElementById('modal_appointment_id').value;
        const endpoint = appointmentId 
            ? `/api/appointments/${appointmentId}/update`
            : '/api/appointments/create';
        
        // Send request
        fetch(endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                // Close modal and refresh calendar
                appointmentModal.hide();
                location.reload();
            } else {
                // Show errors
                if (result.errors) {
                    // Field-specific errors
                    for (let field in result.errors) {
                        const fieldElement = document.getElementById(`modal_${field}`);
                        if (fieldElement) {
                            fieldElement.classList.add('is-invalid');
                            const feedbackElement = fieldElement.nextElementSibling;
                            if (feedbackElement && feedbackElement.classList.contains('invalid-feedback')) {
                                feedbackElement.textContent = result.errors[field].join(', ');
                            }
                        }
                    }
                }
                
                if (result.message) {
                    showModalError(result.message);
                }
            }
        })
        .catch(error => {
            console.error('Error submitting appointment:', error);
            showModalError('An error occurred while saving the appointment.');
        })
        .finally(() => {
            // Reset loading state
            document.getElementById('modal-submit-btn').disabled = false;
            document.getElementById('modal-submit-spinner').classList.add('d-none');
        });
    }

    // Event listeners for appointment modal
    document.addEventListener('DOMContentLoaded', function() {
        // Form submission
        document.getElementById('appointmentModalForm').addEventListener('submit', submitAppointmentForm);
        
        // Note: Tutor change is now handled by the selectModalTutor function
        
        // Service change
        document.getElementById('modal_tutor_service_id').addEventListener('change', function() {
            updateModalEndTime();
            updateModalTransportFee();
        });
        
        // Start time change
        document.getElementById('modal_start_time').addEventListener('change', updateModalEndTime);
        
        // Client search
        const clientSearchInput = document.getElementById('modal_client_search');
        clientSearchInput.addEventListener('input', function() {
            clearTimeout(modalSearchTimeout);
            const query = this.value.trim();
            
            if (query.length < 2) {
                document.getElementById('modal_client_search_results').style.display = 'none';
                return;
            }
            
            modalSearchTimeout = setTimeout(() => {
                searchModalClients(query);
            }, 300);
        });
        
        clientSearchInput.addEventListener('focus', function() {
            if (this.value.length >= 2) {
                searchModalClients(this.value);
            }
        });
        
        // Hide search results when clicking outside
        document.addEventListener('click', function(event) {
            if (!clientSearchInput.contains(event.target) && 
                !document.getElementById('modal_client_search_results').contains(event.target)) {
                document.getElementById('modal_client_search_results').style.display = 'none';
            }
        });
        
        // Recurring toggle
        document.getElementById('modal_is_recurring').addEventListener('change', toggleModalRecurringFields);
        
        // Frequency change
        document.getElementById('modal_frequency').addEventListener('change', function() {
            const weekOfMonthField = document.getElementById('modal-week-of-month-field');
            weekOfMonthField.style.display = this.value === 'monthly' ? 'block' : 'none';
        });
        
        // End type change
        document.querySelectorAll('input[name="end_type"]').forEach(radio => {
            radio.addEventListener('change', function() {
                const endDateField = document.getElementById('modal-end-date-field');
                const occurrencesField = document.getElementById('modal-occurrences-field');
                
                if (this.value === 'date') {
                    endDateField.style.display = 'block';
                    occurrencesField.style.display = 'none';
                } else if (this.value === 'occurrences') {
                    endDateField.style.display = 'none';
                    occurrencesField.style.display = 'block';
                } else {
                    endDateField.style.display = 'none';
                    occurrencesField.style.display = 'none';
                }
            });
        });
        
        // Subscription toggle
        document.getElementById('modal_is_subscription_based').addEventListener('change', toggleModalSubscriptionFields);
        
        // Transport fee toggle
        document.getElementById('modal_apply_transport_fee').addEventListener('change', toggleModalTransportFeeInput);
        
        // Availability check
        document.getElementById('modal-check-availability').addEventListener('click', function() {
            const tutorId = document.getElementById('modal_tutor_id').value;
            const date = document.getElementById('modal_start_date').value;
            const startTime = document.getElementById('modal_start_time').value;
            const endTime = document.getElementById('modal_end_time').value;
            const appointmentId = document.getElementById('modal_appointment_id').value;
            
            if (!tutorId || !date || !startTime || !endTime) {
                alert('Please select tutor, date, and time first.');
                return;
            }
            
            const indicator = document.getElementById('modal-availability-indicator');
            indicator.innerHTML = '<span class="text-muted"><i class="fas fa-spinner fa-spin"></i> Checking...</span>';
            
            fetch('/api/check-availability', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCsrfToken()
                },
                body: JSON.stringify({
                    tutor_id: tutorId,
                    date: date,
                    start_time: startTime,
                    end_time: endTime,
                    appointment_id: appointmentId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.available) {
                    indicator.innerHTML = '<span class="text-success"><i class="fas fa-check-circle"></i> Available</span>';
                } else {
                    indicator.innerHTML = '<span class="text-danger"><i class="fas fa-times-circle"></i> Not Available</span>';
                }
            })
            .catch(error => {
                console.error('Error checking availability:', error);
                indicator.innerHTML = '<span class="text-danger"><i class="fas fa-exclamation-triangle"></i> Error</span>';
            });
        });
        
        // Update edit button to use modal
        document.getElementById('editAppointmentBtn').addEventListener('click', function(e) {
            e.preventDefault();
            const appointmentId = this.href.match(/\/appointments\/(\d+)\/edit/)[1];
            if (appointmentModal) {
                appointmentModal.hide();
            }
            openAppointmentModal(appointmentId);
        });
    });
</script>
{% endblock %}
