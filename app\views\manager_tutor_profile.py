# app/views/manager_tutor_profile.py
from flask import Blueprint, render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from app.extensions import db
from app.models.tutor import Tutor
from app.models.service import Service
from app.models.tutor_availability import TutorAvailability
from app.models.tutor_service_rate import TutorServiceRate
from app.forms.availability_forms import TutorAvailabilityForm, TutorServiceRateForm

# Create a blueprint for manager tutor profile management
manager_tutor_profile = Blueprint('manager_tutor_profile', __name__)

# Require manager role for all routes in this blueprint
@manager_tutor_profile.before_request
def check_manager():
    if not current_user.is_authenticated or current_user.role != 'manager':
        flash('You must be a manager to access this area.', 'danger')
        return redirect(url_for('auth.login'))

@manager_tutor_profile.route('/<int:tutor_id>/availability', methods=['GET', 'POST'])
@login_required
def tutor_availability(tutor_id):
    """Manage tutor availability as a manager."""
    tutor = Tutor.query.get_or_404(tutor_id)

    # Get existing availability slots
    availabilities = TutorAvailability.query.filter_by(tutor_id=tutor.id).order_by(
        TutorAvailability.day_of_week, TutorAvailability.start_time
    ).all()

    # Handle form submission for new availability
    form = TutorAvailabilityForm()
    if form.validate_on_submit():
        # Check if we're editing an existing slot
        if form.id.data:
            availability = TutorAvailability.query.get_or_404(form.id.data)
            if availability.tutor_id != tutor.id:
                flash('Invalid availability slot.', 'danger')
                return redirect(url_for('manager_tutor_profile.tutor_availability', tutor_id=tutor.id))
        else:
            # Create new availability slot
            availability = TutorAvailability(tutor_id=tutor.id)

        # Update availability data
        availability.day_of_week = form.day_of_week.data
        availability.start_time = form.start_time.data
        availability.end_time = form.end_time.data
        availability.is_active = form.is_active.data

        db.session.add(availability)
        db.session.commit()

        flash('Availability saved successfully.', 'success')
        return redirect(url_for('manager_tutor_profile.tutor_availability', tutor_id=tutor.id))

    return render_template('manager/tutor_availability.html',
                          availabilities=availabilities,
                          form=form,
                          tutor=tutor)

@manager_tutor_profile.route('/<int:tutor_id>/availability/<int:id>/edit', methods=['GET'])
@login_required
def edit_tutor_availability(tutor_id, id):
    """Edit an existing availability slot as a manager."""
    tutor = Tutor.query.get_or_404(tutor_id)
    availability = TutorAvailability.query.get_or_404(id)

    # Ensure the availability belongs to this tutor
    if availability.tutor_id != tutor.id:
        flash('Invalid availability slot.', 'danger')
        return redirect(url_for('manager_tutor_profile.tutor_availability', tutor_id=tutor.id))

    # Populate form with existing data
    form = TutorAvailabilityForm(obj=availability)

    return render_template('manager/tutor_availability_form.html',
                          form=form,
                          availability=availability,
                          tutor=tutor)

@manager_tutor_profile.route('/tutors/<int:tutor_id>/availability/<int:id>/delete', methods=['POST'])
@login_required
def delete_tutor_availability(tutor_id, id):
    """Delete an availability slot as a manager."""
    tutor = Tutor.query.get_or_404(tutor_id)
    availability = TutorAvailability.query.get_or_404(id)

    # Ensure the availability belongs to this tutor
    if availability.tutor_id != tutor.id:
        flash('Invalid availability slot.', 'danger')
        return redirect(url_for('manager_tutor_profile.tutor_availability', tutor_id=tutor.id))

    db.session.delete(availability)
    db.session.commit()

    flash('Availability slot deleted.', 'success')
    return redirect(url_for('manager_tutor_profile.tutor_availability', tutor_id=tutor.id))

@manager_tutor_profile.route('/<int:tutor_id>/service-rates', methods=['GET', 'POST'])
@login_required
def tutor_service_rates(tutor_id):
    """Manage tutor service rates as a manager."""
    tutor = Tutor.query.get_or_404(tutor_id)

    # Get all services
    services = Service.query.filter_by(is_active=True).all()

    # Get existing service rates
    rates = TutorServiceRate.query.filter_by(tutor_id=tutor.id).all()

    # Create form for adding/editing rates
    form = TutorServiceRateForm()

    # Populate service choices
    form.service_id.choices = [(s.id, s.name) for s in services]

    if form.validate_on_submit():
        # Check if we're editing an existing rate
        if form.id.data:
            rate = TutorServiceRate.query.get_or_404(form.id.data)
            if rate.tutor_id != tutor.id:
                flash('Invalid service rate.', 'danger')
                return redirect(url_for('manager_tutor_profile.tutor_service_rates', tutor_id=tutor.id))
        else:
            # Check if rate already exists for this service
            existing_rate = TutorServiceRate.query.filter_by(
                tutor_id=tutor.id,
                service_id=form.service_id.data
            ).first()

            if existing_rate:
                rate = existing_rate
            else:
                # Create new rate
                rate = TutorServiceRate(tutor_id=tutor.id)

        # Update rate data
        rate.service_id = form.service_id.data
        rate.tutor_rate = form.tutor_rate.data
        rate.client_rate = form.client_rate.data
        rate.transport_fee = form.transport_fee.data or 0.00
        rate.transport_fee_description = form.transport_fee_description.data
        rate.is_active = form.is_active.data

        db.session.add(rate)
        db.session.commit()

        flash('Service rate saved successfully.', 'success')
        return redirect(url_for('manager_tutor_profile.tutor_service_rates', tutor_id=tutor.id))

    return render_template('manager/tutor_service_rates.html',
                          rates=rates,
                          form=form,
                          services=services,
                          tutor=tutor)

@manager_tutor_profile.route('/<int:tutor_id>/service-rates/<int:id>/edit', methods=['GET'])
@login_required
def edit_tutor_service_rate(tutor_id, id):
    """Edit an existing service rate as a manager."""
    tutor = Tutor.query.get_or_404(tutor_id)
    rate = TutorServiceRate.query.get_or_404(id)

    # Ensure the rate belongs to this tutor
    if rate.tutor_id != tutor.id:
        flash('Invalid service rate.', 'danger')
        return redirect(url_for('manager_tutor_profile.tutor_service_rates', tutor_id=tutor.id))

    # Get all services
    services = Service.query.filter_by(is_active=True).all()

    # Populate form with existing data
    form = TutorServiceRateForm(obj=rate)
    form.service_id.choices = [(s.id, s.name) for s in services]

    return render_template('manager/tutor_service_rate_form.html',
                          form=form,
                          rate=rate,
                          tutor=tutor)

@manager_tutor_profile.route('/<int:tutor_id>/service-rates/<int:id>/delete', methods=['POST'])
@login_required
def delete_tutor_service_rate(tutor_id, id):
    """Delete a service rate as a manager."""
    tutor = Tutor.query.get_or_404(tutor_id)
    rate = TutorServiceRate.query.get_or_404(id)

    # Ensure the rate belongs to this tutor
    if rate.tutor_id != tutor.id:
        flash('Invalid service rate.', 'danger')
        return redirect(url_for('manager_tutor_profile.tutor_service_rates', tutor_id=tutor.id))

    db.session.delete(rate)
    db.session.commit()

    flash('Service rate deleted.', 'success')
    return redirect(url_for('manager_tutor_profile.tutor_service_rates', tutor_id=tutor.id))
