from sqlalchemy import create_engine, inspect, text
from app import create_app

def check_table_schema(table_name):
    """Check the schema of a specific table"""
    app = create_app()
    
    with app.app_context():
        # Get database URL from Flask app config
        db_url = app.config['SQLALCHEMY_DATABASE_URI']
        
        # Create SQLAlchemy engine
        engine = create_engine(db_url)
        
        # Get inspector
        inspector = inspect(engine)
        
        # Get columns
        columns = inspector.get_columns(table_name)
        
        print(f"=== Schema for table '{table_name}' ===")
        for column in columns:
            print(f"- {column['name']}: {column['type']} (nullable: {column['nullable']})")
        
        # Get primary key
        pk = inspector.get_pk_constraint(table_name)
        print(f"\nPrimary key: {pk['constrained_columns']}")
        
        # Get foreign keys
        fks = inspector.get_foreign_keys(table_name)
        if fks:
            print("\nForeign keys:")
            for fk in fks:
                print(f"- {fk['constrained_columns']} -> {fk['referred_table']}.{fk['referred_columns']}")
        
        # Get indexes
        indexes = inspector.get_indexes(table_name)
        if indexes:
            print("\nIndexes:")
            for idx in indexes:
                print(f"- {idx['name']}: {idx['column_names']} (unique: {idx['unique']})")

if __name__ == "__main__":
    check_table_schema('tutor_service_rates')