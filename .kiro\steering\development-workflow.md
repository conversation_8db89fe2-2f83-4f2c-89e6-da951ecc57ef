---
inclusion: always
---

# Development Workflow

## Adding New Features

1. Create/update models in `app/models/`
2. Add service logic in `app/services/`
3. Create forms in `app/forms/`
4. Add routes in `app/views/`
5. Create templates in `app/templates/`
6. Add translations in `app/locales/`
7. Run migrations if database changes needed

## Important Development Guidelines

⚠️ **NO MOCKS POLICY**: All new development should avoid using mocks. Instead:
- Use real database connections (test database)
- Use Stripe test mode for payment testing
- Use actual SMTP servers (can use services like Mailtrap for testing)
- Implement real integrations, not mock services
- For external APIs, use their sandbox/test environments

This ensures code works with real services and reduces integration issues in production.

⚠️ **ERROR HANDLING APPROACH**: When encountering errors, don't take shortcuts. Instead:
- **Investigate thoroughly** - Understand the root cause, not just the symptom
- **Check full context** - Review related files, dependencies, configurations, and database state
- **Provide complete solutions** - Fix underlying issues, not just surface problems
- **Consider side effects** - Ensure fixes don't break existing functionality
- **Document the resolution** - Explain what was wrong and why the solution works
- **Test comprehensively** - Verify the fix in different scenarios
- **Update related code** - If the error reveals a pattern, fix it everywhere

Example: If a database query fails, don't just add a try-catch. Investigate why it fails, check the schema, verify migrations, ensure proper indexes, and handle edge cases properly.

⚠️ **DATABASE DESIGN PRINCIPLE**: When implementing new features:
- **First check existing tables** - Can you add columns to existing tables rather than creating new ones?
- **Reuse when possible** - Extend existing models/tables before creating new ones
- **Maintain relationships** - Ensure proper foreign keys and relationships
- **Keep it simple** - Avoid unnecessary complexity in the schema
- **Document changes** - Add comments explaining why new tables were necessary

Example: For audit logs, first check if you can add audit columns to existing tables before creating a separate audit table.

⚠️ **FULL-STACK IMPLEMENTATION**: Always implement complete features:
- **Backend + Frontend** - Never implement backend without corresponding UI
- **Complete workflow** - Ensure users can actually use the feature
- **Navigation** - Add menu items/links to access new features
- **Forms and validation** - Create proper forms with client and server validation
- **Error handling** - Show user-friendly error messages
- **Success feedback** - Confirm actions with appropriate messages
- **Testing** - Verify the complete flow works end-to-end

Example: When adding audit logs, also create the manager view, add menu item, implement filters, and ensure proper display of audit data.

⚠️ **INTERNATIONALIZATION (i18n)**: All user-facing text must be translated:
- **Use translation function** - Always use `{{ t('key.path') }}` in templates, never hardcode text
- **Add to both languages** - Every text key must exist in BOTH:
  - `/app/locales/en/common.json` (English)
  - `/app/locales/fr/common.json` (French)
- **Maintain consistency** - Keys should have the same structure in both files
- **Check before committing** - Verify all new text has translations in both languages
- **Use descriptive keys** - Follow the existing naming pattern (e.g., `manager.appointments.form.title`)

Example: When adding a new button:
```html
<!-- Wrong -->
<button>Create Appointment</button>

<!-- Correct -->
<button>{{ t('manager.appointments.create_button') }}</button>
```

Then add to both JSON files:
- English: `"create_button": "Create Appointment"`
- French: `"create_button": "Créer un rendez-vous"`

## Testing Procedures

1. Unit test services and models
2. Integration test views and forms
3. Test payment flows in Stripe test mode
4. Verify email notifications
5. Check multi-language support