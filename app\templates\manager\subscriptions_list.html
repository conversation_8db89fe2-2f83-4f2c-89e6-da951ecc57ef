<!-- app/templates/manager/subscriptions_list.html -->
{% extends "base.html" %}

{% block title %}{{ t('manager.subscriptions.title_full') }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">{{ t('manager.subscriptions.title') }}</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('manager.new_subscription') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> {{ t('manager.subscriptions.new_subscription') }}
        </a>
    </div>
</div>

<!-- Filter Form -->
<div class="card shadow mb-4">
    <div class="card-body">
        <form method="GET" action="{{ url_for('manager.subscriptions_list') }}" class="row g-3 filter-form">
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select name="status" id="status" class="form-select">
                    <option value="all" {% if request.args.get('status') == 'all' %}selected{% endif %}>All Statuses</option>
                    <option value="active" {% if request.args.get('status') == 'active' %}selected{% endif %}>Active</option>
                    <option value="expired" {% if request.args.get('status') == 'expired' %}selected{% endif %}>Expired</option>
                    <option value="cancelled" {% if request.args.get('status') == 'cancelled' %}selected{% endif %}>Cancelled</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="parent_id" class="form-label">Parent</label>
                <select name="parent_id" id="parent_id" class="form-select">
                    <option value="">All Parents</option>
                    {% for parent in parents %}
                        <option value="{{ parent.id }}" {% if request.args.get('parent_id') == parent.id|string %}selected{% endif %}>
                            {{ parent.first_name }} {{ parent.last_name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="plan_id" class="form-label">Plan</label>
                <select name="plan_id" id="plan_id" class="form-select">
                    <option value="">All Plans</option>
                    {% for plan in plans %}
                        <option value="{{ plan.id }}" {% if request.args.get('plan_id') == plan.id|string %}selected{% endif %}>
                            {{ plan.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary w-100">Filter</button>
            </div>
        </form>
    </div>
</div>

<!-- Subscriptions Table -->
<div class="card shadow">
    <div class="card-body">
        {% if subscriptions %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Client</th>
                            <th>Plan</th>
                            <th>Period</th>
                            <th>Usage</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for subscription in subscriptions %}
                            <tr>
                                <td>
                                    <span class="text-muted" title="Client view coming soon">
                                        {{ subscription.client.first_name }} {{ subscription.client.last_name }}
                                    </span>
                                </td>
                                <td>{{ subscription.plan.name }}</td>
                                <td>
                                    {{ subscription.start_date.strftime('%Y-%m-%d') }} to
                                    {{ subscription.end_date.strftime('%Y-%m-%d') }}
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="progress flex-grow-1 me-2" style="height: 10px;">
                                            <div class="progress-bar {{ 'bg-warning' if subscription.usage_percentage > 75 else 'bg-success' }}"
                                                 role="progressbar"
                                                 style="width: {{ subscription.usage_percentage }}%">
                                            </div>
                                        </div>
                                        <small>{{ "%.1f"|format(subscription.hours_used) }}/{{ subscription.plan.max_hours }} hrs</small>
                                    </div>
                                </td>
                                <td>
                                    {% if subscription.is_active %}
                                        <span class="badge bg-success">Active</span>
                                    {% elif subscription.status == 'cancelled' %}
                                        <span class="badge bg-danger">Cancelled</span>
                                    {% elif subscription.is_expired %}
                                        <span class="badge bg-secondary">Expired</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('manager.view_subscription', id=subscription.id) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    {% if subscription.is_active %}
                                        <button type="button" class="btn btn-sm btn-danger cancel-subscription-btn"
                                                data-bs-toggle="modal"
                                                data-bs-target="#cancelSubscriptionModal"
                                                data-subscription-id="{{ subscription.id }}">
                                            <i class="fas fa-ban"></i> Cancel
                                        </button>
                                    {% endif %}
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center p-5">
                <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                <h5>No subscriptions found</h5>
                <p class="text-muted">No subscriptions match your search criteria or no subscriptions have been created yet.</p>
                <a href="{{ url_for('manager.new_subscription') }}" class="btn btn-primary mt-3">
                    <i class="fas fa-plus"></i> New Subscription
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Cancel Subscription Modal -->
<div class="modal fade" id="cancelSubscriptionModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cancel Subscription</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to cancel this subscription? This action cannot be undone.</p>
                <p class="text-danger">The customer will no longer be able to use their subscription hours after cancellation.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">No, Keep Subscription</button>
                <form id="cancel-subscription-form" method="POST" action="">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">Yes, Cancel Subscription</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle cancel subscription buttons
        const cancelButtons = document.querySelectorAll('.cancel-subscription-btn');
        const cancelForm = document.getElementById('cancel-subscription-form');

        cancelButtons.forEach(function(btn) {
            btn.addEventListener('click', function() {
                const subscriptionId = this.dataset.subscriptionId;
                cancelForm.action = "{{ url_for('manager.cancel_subscription', id=0) }}".replace('0', subscriptionId);
            });
        });
    });
</script>
{% endblock %}