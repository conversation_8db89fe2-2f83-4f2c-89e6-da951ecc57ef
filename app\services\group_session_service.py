# app/services/group_session_service.py
from datetime import datetime, timedelta
from app.extensions import db
from app.models.program import Program, ProgramModule, Enrollment, GroupSession, GroupSessionParticipant, ProgramPricing
from app.models.client import Client
from app.models.tutor import Tutor
from app.models.tutor_payment import TutorPayment
from sqlalchemy import and_, or_

class GroupSessionService:
    """Service class for managing group sessions and program pricing."""

    @staticmethod
    def create_group_session(program_id, module_id, tutor_id, session_date, start_time, end_time,
                           max_participants=10, tutor_payment_rate=15.00, session_notes=None, meeting_link=None):
        """Create a new group session."""
        group_session = GroupSession(
            program_id=program_id,
            module_id=module_id,
            tutor_id=tutor_id,
            session_date=session_date,
            start_time=start_time,
            end_time=end_time,
            max_participants=max_participants,
            tutor_payment_rate=tutor_payment_rate,
            session_notes=session_notes,
            meeting_link=meeting_link
        )
        db.session.add(group_session)
        db.session.commit()
        return group_session

    @staticmethod
    def add_participant_to_session(group_session_id, enrollment_id=None, client_id=None):
        """Add a participant to a group session."""
        group_session = GroupSession.query.get_or_404(group_session_id)

        if enrollment_id:
            enrollment = Enrollment.query.get_or_404(enrollment_id)
            # Verify the enrollment is for the same program
            if enrollment.program_id != group_session.program_id:
                return False, "Enrollment is not for the same program as the group session"
        elif client_id:
            client = Client.query.get_or_404(client_id)
        else:
            return False, "Either enrollment_id or client_id must be provided"

        # Check if session can accept more participants
        if not group_session.can_accept_participants:
            return False, "Group session is full or not accepting participants"

        # Add participant
        success = group_session.add_participant(enrollment_id=enrollment_id, client_id=client_id)
        if success:
            return True, "Participant added successfully"
        else:
            return False, "Participant is already enrolled in this session"

    @staticmethod
    def remove_participant_from_session(group_session_id, enrollment_id):
        """Remove a participant from a group session."""
        group_session = GroupSession.query.get_or_404(group_session_id)
        success = group_session.remove_participant(enrollment_id)
        if success:
            return True, "Participant removed successfully"
        else:
            return False, "Participant not found in this session"

    @staticmethod
    def get_available_sessions_for_program(program_id, tutor_id=None):
        """Get available group sessions for a program that can accept participants."""
        query = GroupSession.query.filter(
            GroupSession.program_id == program_id,
            GroupSession.status == 'scheduled',
            GroupSession.session_date > datetime.now().date()
        )

        if tutor_id:
            query = query.filter(GroupSession.tutor_id == tutor_id)

        # Filter out full sessions by checking participant count
        sessions = query.order_by(GroupSession.session_date).all()
        available_sessions = [s for s in sessions if s.can_accept_participants]

        return available_sessions

    @staticmethod
    def get_sessions_for_tutor(tutor_id, start_date=None, end_date=None):
        """Get group sessions for a specific tutor."""
        query = GroupSession.query.filter(GroupSession.tutor_id == tutor_id)

        if start_date:
            query = query.filter(GroupSession.session_date >= start_date)
        if end_date:
            query = query.filter(GroupSession.session_date <= end_date)

        return query.order_by(GroupSession.session_date).all()



    @staticmethod
    def cancel_session(group_session_id, reason=None):
        """Cancel a group session."""
        try:
            group_session = GroupSession.query.get_or_404(group_session_id)

            # Check if session can be cancelled
            if group_session.status in ['completed', 'cancelled']:
                return False, f"Session is already {group_session.status} and cannot be cancelled"

            # Mark session as cancelled
            group_session.status = 'cancelled'

            # Add cancellation reason to notes if provided
            if reason:
                if group_session.session_notes:
                    group_session.session_notes += f"\n\nCancellation reason: {reason}"
                else:
                    group_session.session_notes = f"Cancellation reason: {reason}"

            db.session.commit()

            # TODO: Send notification emails to participants about cancellation
            # This would be implemented when email functionality is added

            return True, "Session cancelled successfully"

        except Exception as e:
            db.session.rollback()
            return False, f"Error cancelling session: {str(e)}"

    @staticmethod
    def confirm_session_if_minimum_met(group_session_id):
        """Confirm a group session if minimum participants are met."""
        try:
            group_session = GroupSession.query.get_or_404(group_session_id)

            # Check if session can be confirmed
            if group_session.status in ['completed', 'cancelled']:
                return False, f"Session is already {group_session.status} and cannot be confirmed"

            if group_session.status == 'confirmed':
                return False, "Session is already confirmed"

            # Check if minimum participants are met (4 for TECFÉE)
            if not group_session.has_minimum_participants:
                return False, f"Session needs at least 4 participants to be confirmed. Currently has {group_session.current_participants_count}"

            # Mark session as confirmed
            group_session.status = 'confirmed'
            db.session.commit()

            # TODO: Send confirmation emails to participants
            # This would be implemented when email functionality is added

            return True, "Session confirmed successfully"

        except Exception as e:
            db.session.rollback()
            return False, f"Error confirming session: {str(e)}"

    @staticmethod
    def complete_session_and_create_payments(group_session_id):
        """Mark a session as completed and create tutor payments."""
        group_session = GroupSession.query.get_or_404(group_session_id)

        if group_session.status != 'confirmed':
            return False, "Session must be confirmed before completion"

        # Mark session as completed
        group_session.status = 'completed'

        # Create tutor payment based on actual participants
        attended_participants = group_session.participants.filter_by(attendance_status='attended').count()
        if attended_participants == 0:
            # If no attendance recorded, assume all registered participants attended
            attended_participants = group_session.current_participants

        total_payment = float(group_session.tutor_payment_rate) * attended_participants

        # Create tutor payment record
        payment = TutorPayment(
            tutor_id=group_session.tutor_id,
            appointment_id=None,  # Group sessions don't have individual appointments
            service_amount=total_payment,
            transport_amount=0,  # Assuming no transport for online sessions
            total_amount=total_payment,
            status='pending'
        )

        db.session.add(payment)
        db.session.commit()

        return True, f"Session completed. Tutor payment of ${total_payment:.2f} created for {attended_participants} participants"

    @staticmethod
    def mark_attendance(group_session_id, enrollment_id, attendance_status):
        """Mark attendance for a participant in a group session."""
        participant = GroupSessionParticipant.query.filter_by(
            group_session_id=group_session_id,
            enrollment_id=enrollment_id
        ).first()

        if not participant:
            return False, "Participant not found in this session"

        if attendance_status not in ['registered', 'attended', 'absent']:
            return False, "Invalid attendance status"

        participant.attendance_status = attendance_status
        db.session.commit()

        return True, f"Attendance marked as {attendance_status}"

    @staticmethod
    def get_program_pricing(program_id):
        """Get pricing options for a program."""
        return ProgramPricing.query.filter_by(
            program_id=program_id,
            is_active=True
        ).all()

    @staticmethod
    def create_program_pricing(program_id, pricing_type, price, description=None):
        """Create a new pricing option for a program."""
        pricing = ProgramPricing(
            program_id=program_id,
            pricing_type=pricing_type,
            price=price,
            description=description
        )
        db.session.add(pricing)
        db.session.commit()
        return pricing

    @staticmethod
    def get_tecfee_program():
        """Get the TECFÉE program."""
        return Program.query.filter_by(code='TECFEE').first()



    @staticmethod
    def enroll_client_in_tecfee(client_id, pricing_type='full_package', start_date=None):
        """Enroll a client in the TECFÉE program."""
        tecfee_program = GroupSessionService.get_tecfee_program()
        if not tecfee_program:
            return None, "TECFÉE program not found"

        # Check if client is already enrolled
        existing_enrollment = Enrollment.query.filter_by(
            client_id=client_id,
            program_id=tecfee_program.program_id,
            status='active'
        ).first()

        if existing_enrollment:
            return None, "Client is already enrolled in TECFÉE program"

        # Create enrollment
        if not start_date:
            start_date = datetime.now().date()

        # Estimate end date (9 weeks for 9 sessions)
        end_date = start_date + timedelta(weeks=9)

        # Get pricing information to set total_amount
        from app.models.program import ProgramPricing
        pricing = ProgramPricing.query.filter_by(
            program_id=tecfee_program.program_id,
            pricing_type=pricing_type,
            is_active=True
        ).first()

        total_amount = float(pricing.price) if pricing else None

        enrollment = Enrollment(
            client_id=client_id,
            program_id=tecfee_program.program_id,
            pricing_type=pricing_type,  # Fix: Add missing pricing_type
            enrollment_date=datetime.now().date(),
            start_date=start_date,
            end_date=end_date,
            status='active',
            total_amount=total_amount,  # Fix: Add total_amount
            total_sessions=12 if pricing_type == 'full_package' else 0  # Set expected sessions
        )

        db.session.add(enrollment)
        db.session.flush()  # Get enrollment ID

        # Create module progress entries for all modules
        from app.models.program import ModuleProgress
        for module in tecfee_program.modules:
            module_progress = ModuleProgress(
                enrollment_id=enrollment.id,
                module_id=module.id,
                status='not_started'
            )
            db.session.add(module_progress)

        # For per-session pricing, don't auto-enroll in sessions
        # Sessions will be selected separately and enrolled individually
        # For full package, auto-enroll in all available sessions
        if pricing_type == 'full_package':
            # Get available sessions for this program
            available_sessions = GroupSessionService.get_available_sessions_for_program(tecfee_program.program_id)

            # Add client to all available sessions (for full package pricing)
            for session in available_sessions:
                success = session.add_participant(enrollment_id=enrollment.enrollment_id)
                if not success:
                    # Session might be full, continue to next session
                    continue

        db.session.commit()
        return enrollment, "Client enrolled successfully in TECFÉE program"

    @staticmethod
    def get_upcoming_sessions_for_client(client_id, days=30):
        """Get upcoming group sessions for a client's enrollments."""
        now = datetime.now()
        end_date = now + timedelta(days=days)

        # Get client's active enrollments
        enrollments = Enrollment.query.filter_by(
            client_id=client_id,
            status='active'
        ).all()

        if not enrollments:
            return []

        enrollment_ids = [e.id for e in enrollments]

        # Get group sessions where client is enrolled
        sessions = db.session.query(GroupSession, GroupSessionParticipant).\
            join(GroupSessionParticipant, GroupSession.group_session_id == GroupSessionParticipant.group_session_id).\
            filter(
                GroupSessionParticipant.enrollment_id.in_(enrollment_ids),
                GroupSession.session_date >= now,
                GroupSession.session_date <= end_date,
                GroupSession.status.in_(['scheduled', 'confirmed'])
            ).\
            order_by(GroupSession.session_date).all()

        return sessions

    @staticmethod
    def enroll_client_in_session(client_id, session_id, pricing_type='per_session'):
        """Enroll a client in a specific group session."""
        try:
            # Get the session
            session = GroupSession.query.get_or_404(session_id)

            # Check if session can accept participants
            if not session.can_accept_participants:
                return False, "Session is full or not accepting participants"

            # Get or create enrollment for this program
            enrollment = Enrollment.query.filter_by(
                client_id=client_id,
                program_id=session.program_id,
                status='active'
            ).first()

            if not enrollment:
                # Create new enrollment for this client
                enrollment, message = GroupSessionService.enroll_client_in_tecfee(
                    client_id=client_id,
                    pricing_type=pricing_type
                )
                if not enrollment:
                    return False, f"Failed to create enrollment: {message}"

            # Add participant to session
            success = session.add_participant(enrollment_id=enrollment.enrollment_id)
            if success:
                return True, "Successfully enrolled in session"
            else:
                return False, "Already enrolled in this session or session is full"

        except Exception as e:
            db.session.rollback()
            return False, f"Error enrolling in session: {str(e)}"

    @staticmethod
    def remove_client_from_session(client_id, session_id):
        """Remove a client from a specific group session."""
        try:
            # Get the session
            session = GroupSession.query.get_or_404(session_id)

            # Get enrollment for this client and program
            enrollment = Enrollment.query.filter_by(
                client_id=client_id,
                program_id=session.program_id,
                status='active'
            ).first()

            if not enrollment:
                return False, "Client is not enrolled in this program"

            # Remove participant from session
            success = session.remove_participant(enrollment_id=enrollment.enrollment_id)
            if success:
                return True, "Successfully removed from session"
            else:
                return False, "Client is not enrolled in this session"

        except Exception as e:
            db.session.rollback()
            return False, f"Error removing from session: {str(e)}"

    @staticmethod
    def add_participant_to_session(session_id, enrollment_id):
        """Add a participant to a group session by enrollment ID."""
        try:
            # Get the session
            session = GroupSession.query.get_or_404(session_id)

            # Check if session can accept participants
            if not session.can_accept_participants:
                return False, "Session is full or not accepting participants"

            # Get the enrollment
            enrollment = Enrollment.query.get_or_404(enrollment_id)

            # Verify enrollment is for the same program
            if enrollment.program_id != session.program_id:
                return False, "Enrollment is not for the same program as the group session"

            # Add participant to session
            success = session.add_participant(enrollment_id=enrollment.enrollment_id)
            if success:
                return True, "Successfully added participant to session"
            else:
                return False, "Participant is already enrolled in this session or session is full"

        except Exception as e:
            db.session.rollback()
            return False, f"Error adding participant to session: {str(e)}"
