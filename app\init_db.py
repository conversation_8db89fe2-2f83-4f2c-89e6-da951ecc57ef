#!/usr/bin/env python3
"""
Database initialization script for TutorAide application.
This script creates all necessary database tables.
"""

import os
import sys
# Add the parent directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flask import Flask
from app.extensions import db
from app.models import *  # Import all models

def create_app():
    """Create Flask app for database initialization."""
    app = Flask(__name__)

    # Load configuration
    config_name = os.environ.get('FLASK_CONFIG', 'development')
    app.config.from_object(f'app.config.{config_name.capitalize()}Config')

    # Try to load .env file
    try:
        app.config.from_pyfile('../.env', silent=True)
    except:
        pass

    # Initialize database
    db.init_app(app)

    return app

def init_database():
    """Initialize the database with all tables."""
    app = create_app()

    with app.app_context():
        try:
            print("Creating database tables...")

            # Create all tables
            db.create_all()

            print("✅ Database tables created successfully!")

            # Print table information
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            print(f"📊 Created {len(tables)} tables:")
            for table in sorted(tables):
                print(f"  - {table}")

        except Exception as e:
            print(f"❌ Error creating database tables: {e}")
            return False

    return True

if __name__ == '__main__':
    print("🚀 Initializing TutorAide database...")
    success = init_database()
    if success:
        print("🎉 Database initialization completed successfully!")
        sys.exit(0)
    else:
        print("💥 Database initialization failed!")
        sys.exit(1)
