#!/usr/bin/env python3
"""
Data Cleanup Service for TutorAide Application
Automated cleanup procedures for orphaned records and data inconsistencies
"""

from datetime import datetime, date, timedelta
from typing import Dict, List, Tuple, Any, Optional
from sqlalchemy import text, and_, or_
from sqlalchemy.exc import SQLAlchemyError
from app.extensions import db
import logging

class DataCleanupService:
    """Service for automated data cleanup and orphaned record removal."""
    
    @staticmethod
    def cleanup_all_data(dry_run: bool = True) -> Dict[str, Any]:
        """
        Run comprehensive cleanup on all database entities.
        
        Args:
            dry_run: If True, only report what would be cleaned without making changes
            
        Returns:
            Detailed report of cleanup operations performed or planned
        """
        cleanup_report = {
            'timestamp': datetime.utcnow().isoformat(),
            'dry_run': dry_run,
            'summary': {
                'total_operations': 0,
                'successful_operations': 0,
                'failed_operations': 0,
                'records_affected': 0
            },
            'operations': {},
            'errors': []
        }
        
        try:
            # Cleanup operations in dependency order (children first, then parents)
            cleanup_report['operations']['orphaned_dependants'] = DataCleanupService._cleanup_orphaned_dependants(dry_run)
            cleanup_report['operations']['orphaned_appointments'] = DataCleanupService._cleanup_orphaned_appointments(dry_run)
            cleanup_report['operations']['orphaned_tutor_services'] = DataCleanupService._cleanup_orphaned_tutor_services(dry_run)
            cleanup_report['operations']['orphaned_subscription_usage'] = DataCleanupService._cleanup_orphaned_subscription_usage(dry_run)
            cleanup_report['operations']['orphaned_invoice_items'] = DataCleanupService._cleanup_orphaned_invoice_items(dry_run)
            cleanup_report['operations']['invalid_recurring_schedules'] = DataCleanupService._cleanup_invalid_recurring_schedules(dry_run)
            cleanup_report['operations']['expired_subscriptions'] = DataCleanupService._cleanup_expired_subscriptions(dry_run)
            cleanup_report['operations']['old_scheduled_appointments'] = DataCleanupService._cleanup_old_scheduled_appointments(dry_run)
            cleanup_report['operations']['duplicate_relationships'] = DataCleanupService._cleanup_duplicate_relationships(dry_run)
            cleanup_report['operations']['invalid_data_values'] = DataCleanupService._cleanup_invalid_data_values(dry_run)
            
            # Calculate summary statistics
            DataCleanupService._calculate_cleanup_summary(cleanup_report)
            
            if not dry_run:
                db.session.commit()
                logging.info(f"Data cleanup completed successfully. {cleanup_report['summary']['records_affected']} records affected.")
            else:
                logging.info(f"Data cleanup dry run completed. {cleanup_report['summary']['records_affected']} records would be affected.")
            
        except Exception as e:
            if not dry_run:
                db.session.rollback()
            cleanup_report['errors'].append(f"Cleanup failed with error: {str(e)}")
            logging.error(f"Data cleanup failed: {e}")
        
        return cleanup_report
    
    @staticmethod
    def _cleanup_orphaned_dependants(dry_run: bool = True) -> Dict[str, Any]:
        """Remove dependants that reference non-existent clients."""
        operation_result = {
            'operation': 'cleanup_orphaned_dependants',
            'dry_run': dry_run,
            'records_affected': 0,
            'success': True,
            'details': [],
            'error': None
        }
        
        try:
            # Find orphaned dependants
            orphaned_query = text("""
                SELECT d.dependant_id, d.first_name, d.last_name, d.client_id
                FROM dependants d
                LEFT JOIN clients c ON d.client_id = c.client_id
                WHERE c.client_id IS NULL
            """)
            
            orphaned_dependants = db.session.execute(orphaned_query).fetchall()
            
            if orphaned_dependants:
                operation_result['records_affected'] = len(orphaned_dependants)
                operation_result['details'] = [
                    {
                        'dependant_id': row[0],
                        'name': f"{row[1]} {row[2]}",
                        'invalid_client_id': row[3]
                    }
                    for row in orphaned_dependants
                ]
                
                if not dry_run:
                    # Delete orphaned dependants
                    dependant_ids = [row[0] for row in orphaned_dependants]
                    delete_query = text("""
                        DELETE FROM dependants 
                        WHERE dependant_id = ANY(:dependant_ids)
                    """)
                    db.session.execute(delete_query, {'dependant_ids': dependant_ids})
            
        except Exception as e:
            operation_result['success'] = False
            operation_result['error'] = str(e)
        
        return operation_result
    
    @staticmethod
    def _cleanup_orphaned_appointments(dry_run: bool = True) -> Dict[str, Any]:
        """Remove appointments with invalid foreign key references."""
        operation_result = {
            'operation': 'cleanup_orphaned_appointments',
            'dry_run': dry_run,
            'records_affected': 0,
            'success': True,
            'details': [],
            'error': None
        }
        
        try:
            # Find appointments with invalid references
            orphaned_query = text("""
                SELECT a.appointment_id, a.client_id, a.tutor_id, a.tutor_service_id
                FROM appointments a
                LEFT JOIN clients c ON a.client_id = c.client_id
                LEFT JOIN tutors t ON a.tutor_id = t.tutor_id
                LEFT JOIN tutor_services ts ON a.tutor_service_id = ts.tutor_service_id
                WHERE c.client_id IS NULL OR t.tutor_id IS NULL OR ts.tutor_service_id IS NULL
            """)
            
            orphaned_appointments = db.session.execute(orphaned_query).fetchall()
            
            if orphaned_appointments:
                operation_result['records_affected'] = len(orphaned_appointments)
                operation_result['details'] = [
                    {
                        'appointment_id': row[0],
                        'client_id': row[1],
                        'tutor_id': row[2],
                        'tutor_service_id': row[3]
                    }
                    for row in orphaned_appointments
                ]
                
                if not dry_run:
                    # Delete orphaned appointments
                    appointment_ids = [row[0] for row in orphaned_appointments]
                    delete_query = text("""
                        DELETE FROM appointments 
                        WHERE appointment_id = ANY(:appointment_ids)
                    """)
                    db.session.execute(delete_query, {'appointment_ids': appointment_ids})
            
        except Exception as e:
            operation_result['success'] = False
            operation_result['error'] = str(e)
        
        return operation_result
    
    @staticmethod
    def _cleanup_orphaned_tutor_services(dry_run: bool = True) -> Dict[str, Any]:
        """Remove tutor services with invalid tutor or service references."""
        operation_result = {
            'operation': 'cleanup_orphaned_tutor_services',
            'dry_run': dry_run,
            'records_affected': 0,
            'success': True,
            'details': [],
            'error': None
        }
        
        try:
            # Find tutor services with invalid references
            orphaned_query = text("""
                SELECT ts.tutor_service_id, ts.tutor_id, ts.service_id
                FROM tutor_services ts
                LEFT JOIN tutors t ON ts.tutor_id = t.tutor_id
                LEFT JOIN services s ON ts.service_id = s.service_id
                WHERE t.tutor_id IS NULL OR s.service_id IS NULL
            """)
            
            orphaned_tutor_services = db.session.execute(orphaned_query).fetchall()
            
            if orphaned_tutor_services:
                operation_result['records_affected'] = len(orphaned_tutor_services)
                operation_result['details'] = [
                    {
                        'tutor_service_id': row[0],
                        'tutor_id': row[1],
                        'service_id': row[2]
                    }
                    for row in orphaned_tutor_services
                ]
                
                if not dry_run:
                    # Delete orphaned tutor services
                    tutor_service_ids = [row[0] for row in orphaned_tutor_services]
                    delete_query = text("""
                        DELETE FROM tutor_services 
                        WHERE tutor_service_id = ANY(:tutor_service_ids)
                    """)
                    db.session.execute(delete_query, {'tutor_service_ids': tutor_service_ids})
            
        except Exception as e:
            operation_result['success'] = False
            operation_result['error'] = str(e)
        
        return operation_result
    
    @staticmethod
    def _cleanup_orphaned_subscription_usage(dry_run: bool = True) -> Dict[str, Any]:
        """Remove subscription usage records with invalid references."""
        operation_result = {
            'operation': 'cleanup_orphaned_subscription_usage',
            'dry_run': dry_run,
            'records_affected': 0,
            'success': True,
            'details': [],
            'error': None
        }
        
        try:
            # Find subscription usage with invalid references
            orphaned_query = text("""
                SELECT su.usage_id, su.subscription_id, su.appointment_id
                FROM subscription_usage su
                LEFT JOIN subscriptions s ON su.subscription_id = s.subscription_id
                LEFT JOIN appointments a ON su.appointment_id = a.appointment_id
                WHERE s.subscription_id IS NULL OR a.appointment_id IS NULL
            """)
            
            orphaned_usage = db.session.execute(orphaned_query).fetchall()
            
            if orphaned_usage:
                operation_result['records_affected'] = len(orphaned_usage)
                operation_result['details'] = [
                    {
                        'usage_id': row[0],
                        'subscription_id': row[1],
                        'appointment_id': row[2]
                    }
                    for row in orphaned_usage
                ]
                
                if not dry_run:
                    # Delete orphaned subscription usage
                    usage_ids = [row[0] for row in orphaned_usage]
                    delete_query = text("""
                        DELETE FROM subscription_usage 
                        WHERE usage_id = ANY(:usage_ids)
                    """)
                    db.session.execute(delete_query, {'usage_ids': usage_ids})
            
        except Exception as e:
            operation_result['success'] = False
            operation_result['error'] = str(e)
        
        return operation_result
    
    @staticmethod
    def _cleanup_orphaned_invoice_items(dry_run: bool = True) -> Dict[str, Any]:
        """Remove invoice items with invalid invoice or appointment references."""
        operation_result = {
            'operation': 'cleanup_orphaned_invoice_items',
            'dry_run': dry_run,
            'records_affected': 0,
            'success': True,
            'details': [],
            'error': None
        }
        
        try:
            # Find invoice items with invalid references
            orphaned_query = text("""
                SELECT ii.item_id, ii.invoice_id, ii.appointment_id
                FROM invoice_items ii
                LEFT JOIN invoices i ON ii.invoice_id = i.invoice_id
                LEFT JOIN appointments a ON ii.appointment_id = a.appointment_id
                WHERE i.invoice_id IS NULL OR (ii.appointment_id IS NOT NULL AND a.appointment_id IS NULL)
            """)
            
            orphaned_items = db.session.execute(orphaned_query).fetchall()
            
            if orphaned_items:
                operation_result['records_affected'] = len(orphaned_items)
                operation_result['details'] = [
                    {
                        'item_id': row[0],
                        'invoice_id': row[1],
                        'appointment_id': row[2]
                    }
                    for row in orphaned_items
                ]
                
                if not dry_run:
                    # Delete orphaned invoice items
                    item_ids = [row[0] for row in orphaned_items]
                    delete_query = text("""
                        DELETE FROM invoice_items 
                        WHERE item_id = ANY(:item_ids)
                    """)
                    db.session.execute(delete_query, {'item_ids': item_ids})
            
        except Exception as e:
            operation_result['success'] = False
            operation_result['error'] = str(e)
        
        return operation_result
    
    @staticmethod
    def _cleanup_invalid_recurring_schedules(dry_run: bool = True) -> Dict[str, Any]:
        """Fix or remove invalid recurring schedule configurations."""
        operation_result = {
            'operation': 'cleanup_invalid_recurring_schedules',
            'dry_run': dry_run,
            'records_affected': 0,
            'success': True,
            'details': [],
            'error': None
        }
        
        try:
            # Find schedules with invalid configurations
            invalid_query = text("""
                SELECT schedule_id, frequency, day_of_week, week_of_month, 
                       pattern_start_date, pattern_end_date
                FROM appointment_recurring_schedules
                WHERE 
                    -- Invalid frequency
                    frequency NOT IN ('weekly', 'biweekly', 'monthly')
                    -- Weekly/biweekly without day_of_week
                    OR (frequency IN ('weekly', 'biweekly') AND day_of_week IS NULL)
                    -- Monthly without required fields
                    OR (frequency = 'monthly' AND (day_of_week IS NULL OR week_of_month IS NULL))
                    -- Invalid day_of_week
                    OR (day_of_week IS NOT NULL AND (day_of_week < 0 OR day_of_week > 6))
                    -- Invalid week_of_month
                    OR (week_of_month IS NOT NULL AND (week_of_month < 1 OR week_of_month > 5))
                    -- End date before start date
                    OR (pattern_end_date IS NOT NULL AND pattern_end_date < pattern_start_date)
            """)
            
            invalid_schedules = db.session.execute(invalid_query).fetchall()
            
            if invalid_schedules:
                operation_result['records_affected'] = len(invalid_schedules)
                operation_result['details'] = [
                    {
                        'schedule_id': row[0],
                        'frequency': row[1],
                        'day_of_week': row[2],
                        'week_of_month': row[3],
                        'pattern_start_date': row[4],
                        'pattern_end_date': row[5]
                    }
                    for row in invalid_schedules
                ]
                
                if not dry_run:
                    # Deactivate invalid schedules instead of deleting them
                    schedule_ids = [row[0] for row in invalid_schedules]
                    update_query = text("""
                        UPDATE appointment_recurring_schedules 
                        SET is_active = FALSE, 
                            modification_date = CURRENT_TIMESTAMP
                        WHERE schedule_id = ANY(:schedule_ids)
                    """)
                    db.session.execute(update_query, {'schedule_ids': schedule_ids})
            
        except Exception as e:
            operation_result['success'] = False
            operation_result['error'] = str(e)
        
        return operation_result
    
    @staticmethod
    def _cleanup_expired_subscriptions(dry_run: bool = True) -> Dict[str, Any]:
        """Update status of expired subscriptions."""
        operation_result = {
            'operation': 'cleanup_expired_subscriptions',
            'dry_run': dry_run,
            'records_affected': 0,
            'success': True,
            'details': [],
            'error': None
        }
        
        try:
            # Find active subscriptions that have expired
            expired_query = text("""
                SELECT subscription_id, client_id, end_date, status
                FROM subscriptions
                WHERE status = 'active' AND end_date < CURRENT_DATE
            """)
            
            expired_subscriptions = db.session.execute(expired_query).fetchall()
            
            if expired_subscriptions:
                operation_result['records_affected'] = len(expired_subscriptions)
                operation_result['details'] = [
                    {
                        'subscription_id': row[0],
                        'client_id': row[1],
                        'end_date': row[2],
                        'current_status': row[3]
                    }
                    for row in expired_subscriptions
                ]
                
                if not dry_run:
                    # Update expired subscriptions
                    subscription_ids = [row[0] for row in expired_subscriptions]
                    update_query = text("""
                        UPDATE subscriptions 
                        SET status = 'expired', 
                            modification_date = CURRENT_TIMESTAMP
                        WHERE subscription_id = ANY(:subscription_ids)
                    """)
                    db.session.execute(update_query, {'subscription_ids': subscription_ids})
            
        except Exception as e:
            operation_result['success'] = False
            operation_result['error'] = str(e)
        
        return operation_result
    
    @staticmethod
    def _cleanup_old_scheduled_appointments(dry_run: bool = True) -> Dict[str, Any]:
        """Update status of old appointments that are still scheduled."""
        operation_result = {
            'operation': 'cleanup_old_scheduled_appointments',
            'dry_run': dry_run,
            'records_affected': 0,
            'success': True,
            'details': [],
            'error': None
        }
        
        try:
            # Find old scheduled appointments (more than 7 days past)
            old_query = text("""
                SELECT appointment_id, start_time, status
                FROM appointments
                WHERE status = 'scheduled' 
                AND start_time < CURRENT_TIMESTAMP - INTERVAL '7 days'
            """)
            
            old_appointments = db.session.execute(old_query).fetchall()
            
            if old_appointments:
                operation_result['records_affected'] = len(old_appointments)
                operation_result['details'] = [
                    {
                        'appointment_id': row[0],
                        'start_time': row[1],
                        'current_status': row[2]
                    }
                    for row in old_appointments
                ]
                
                if not dry_run:
                    # Update old appointments to 'no-show' status
                    appointment_ids = [row[0] for row in old_appointments]
                    update_query = text("""
                        UPDATE appointments 
                        SET status = 'no-show', 
                            modification_date = CURRENT_TIMESTAMP
                        WHERE appointment_id = ANY(:appointment_ids)
                    """)
                    db.session.execute(update_query, {'appointment_ids': appointment_ids})
            
        except Exception as e:
            operation_result['success'] = False
            operation_result['error'] = str(e)
        
        return operation_result
    
    @staticmethod
    def _cleanup_duplicate_relationships(dry_run: bool = True) -> Dict[str, Any]:
        """Remove duplicate relationship records."""
        operation_result = {
            'operation': 'cleanup_duplicate_relationships',
            'dry_run': dry_run,
            'records_affected': 0,
            'success': True,
            'details': [],
            'error': None
        }
        
        try:
            # Find duplicate dependant relationships
            duplicate_query = text("""
                SELECT client_id, dependant_id, COUNT(*) as count
                FROM dependant_relationships
                GROUP BY client_id, dependant_id
                HAVING COUNT(*) > 1
            """)
            
            duplicates = db.session.execute(duplicate_query).fetchall()
            
            if duplicates:
                total_duplicates = sum(row[2] - 1 for row in duplicates)  # Keep one, remove others
                operation_result['records_affected'] = total_duplicates
                operation_result['details'] = [
                    {
                        'client_id': row[0],
                        'dependant_id': row[1],
                        'duplicate_count': row[2]
                    }
                    for row in duplicates
                ]
                
                if not dry_run:
                    # Remove duplicates, keeping the oldest record
                    for client_id, dependant_id, count in duplicates:
                        delete_query = text("""
                            DELETE FROM dependant_relationships
                            WHERE relationship_id NOT IN (
                                SELECT MIN(relationship_id)
                                FROM dependant_relationships
                                WHERE client_id = :client_id AND dependant_id = :dependant_id
                            )
                            AND client_id = :client_id AND dependant_id = :dependant_id
                        """)
                        db.session.execute(delete_query, {
                            'client_id': client_id,
                            'dependant_id': dependant_id
                        })
            
        except Exception as e:
            operation_result['success'] = False
            operation_result['error'] = str(e)
        
        return operation_result
    
    @staticmethod
    def _cleanup_invalid_data_values(dry_run: bool = True) -> Dict[str, Any]:
        """Fix invalid data values that can be corrected automatically."""
        operation_result = {
            'operation': 'cleanup_invalid_data_values',
            'dry_run': dry_run,
            'records_affected': 0,
            'success': True,
            'details': [],
            'error': None
        }
        
        try:
            fixes_applied = []
            
            # Fix negative hours remaining in subscriptions
            negative_hours_query = text("""
                SELECT subscription_id, hours_remaining
                FROM subscriptions
                WHERE hours_remaining < 0
            """)
            
            negative_hours = db.session.execute(negative_hours_query).fetchall()
            
            if negative_hours and not dry_run:
                # Set negative hours to 0
                subscription_ids = [row[0] for row in negative_hours]
                fix_query = text("""
                    UPDATE subscriptions 
                    SET hours_remaining = 0, 
                        modification_date = CURRENT_TIMESTAMP
                    WHERE subscription_id = ANY(:subscription_ids)
                """)
                db.session.execute(fix_query, {'subscription_ids': subscription_ids})
                
                fixes_applied.append({
                    'type': 'negative_hours_fixed',
                    'count': len(negative_hours),
                    'details': [{'subscription_id': row[0], 'old_value': row[1]} for row in negative_hours]
                })
            
            # Fix negative transport fees
            negative_transport_query = text("""
                SELECT tutor_service_id, transport_fee
                FROM tutor_services
                WHERE transport_fee < 0
            """)
            
            negative_transport = db.session.execute(negative_transport_query).fetchall()
            
            if negative_transport and not dry_run:
                # Set negative transport fees to 0
                tutor_service_ids = [row[0] for row in negative_transport]
                fix_query = text("""
                    UPDATE tutor_services 
                    SET transport_fee = 0, 
                        modification_date = CURRENT_TIMESTAMP
                    WHERE tutor_service_id = ANY(:tutor_service_ids)
                """)
                db.session.execute(fix_query, {'tutor_service_ids': tutor_service_ids})
                
                fixes_applied.append({
                    'type': 'negative_transport_fees_fixed',
                    'count': len(negative_transport),
                    'details': [{'tutor_service_id': row[0], 'old_value': row[1]} for row in negative_transport]
                })
            
            operation_result['records_affected'] = sum(fix['count'] for fix in fixes_applied)
            operation_result['details'] = fixes_applied
            
        except Exception as e:
            operation_result['success'] = False
            operation_result['error'] = str(e)
        
        return operation_result
    
    @staticmethod
    def _calculate_cleanup_summary(cleanup_report: Dict[str, Any]) -> None:
        """Calculate summary statistics for the cleanup report."""
        total_operations = 0
        successful_operations = 0
        failed_operations = 0
        total_records_affected = 0
        
        for operation_name, operation_result in cleanup_report['operations'].items():
            total_operations += 1
            
            if operation_result['success']:
                successful_operations += 1
            else:
                failed_operations += 1
            
            total_records_affected += operation_result['records_affected']
        
        cleanup_report['summary'].update({
            'total_operations': total_operations,
            'successful_operations': successful_operations,
            'failed_operations': failed_operations,
            'records_affected': total_records_affected
        })
    
    @staticmethod
    def cleanup_specific_issues(issue_types: List[str], dry_run: bool = True) -> Dict[str, Any]:
        """
        Clean up specific types of issues.
        
        Args:
            issue_types: List of issue types to clean up
            dry_run: If True, only report what would be cleaned
            
        Returns:
            Cleanup report for the specified issues
        """
        cleanup_methods = {
            'orphaned_dependants': DataCleanupService._cleanup_orphaned_dependants,
            'orphaned_appointments': DataCleanupService._cleanup_orphaned_appointments,
            'orphaned_tutor_services': DataCleanupService._cleanup_orphaned_tutor_services,
            'orphaned_subscription_usage': DataCleanupService._cleanup_orphaned_subscription_usage,
            'orphaned_invoice_items': DataCleanupService._cleanup_orphaned_invoice_items,
            'invalid_recurring_schedules': DataCleanupService._cleanup_invalid_recurring_schedules,
            'expired_subscriptions': DataCleanupService._cleanup_expired_subscriptions,
            'old_scheduled_appointments': DataCleanupService._cleanup_old_scheduled_appointments,
            'duplicate_relationships': DataCleanupService._cleanup_duplicate_relationships,
            'invalid_data_values': DataCleanupService._cleanup_invalid_data_values
        }
        
        cleanup_report = {
            'timestamp': datetime.utcnow().isoformat(),
            'dry_run': dry_run,
            'requested_issues': issue_types,
            'summary': {
                'total_operations': 0,
                'successful_operations': 0,
                'failed_operations': 0,
                'records_affected': 0
            },
            'operations': {},
            'errors': []
        }
        
        try:
            for issue_type in issue_types:
                if issue_type in cleanup_methods:
                    cleanup_report['operations'][issue_type] = cleanup_methods[issue_type](dry_run)
                else:
                    cleanup_report['errors'].append(f"Unknown issue type: {issue_type}")
            
            # Calculate summary
            DataCleanupService._calculate_cleanup_summary(cleanup_report)
            
            if not dry_run:
                db.session.commit()
                logging.info(f"Specific cleanup completed. {cleanup_report['summary']['records_affected']} records affected.")
            
        except Exception as e:
            if not dry_run:
                db.session.rollback()
            cleanup_report['errors'].append(f"Cleanup failed: {str(e)}")
            logging.error(f"Specific cleanup failed: {e}")
        
        return cleanup_report