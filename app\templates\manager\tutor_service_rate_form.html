{% extends 'base.html' %}

{% block title %}Edit Service Rate{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1>Edit Service Rate for {{ tutor.full_name }}</h1>
            <p class="lead">Update the tutor's rate for {{ rate.service.name }}.</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('manager_tutor_profile.tutor_service_rates', tutor_id=tutor.id) }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Service Rates
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Edit Service Rate</h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('manager_tutor_profile.tutor_service_rates', tutor_id=tutor.id) }}">
                        {{ form.csrf_token }}
                        {{ form.id }}
                        <div class="mb-3">
                            {{ form.service_id.label(class="form-label") }}
                            {{ form.service_id(class="form-select") }}
                        </div>
                        <div class="mb-3">
                            {{ form.tutor_rate.label(class="form-label") }}
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                {{ form.tutor_rate(class="form-control") }}
                            </div>
                        </div>
                        <div class="mb-3">
                            {{ form.client_rate.label(class="form-label") }}
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                {{ form.client_rate(class="form-control") }}
                            </div>
                        </div>
                        <div class="mb-3">
                            {{ form.transport_fee.label(class="form-label") }}
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                {{ form.transport_fee(class="form-control") }}
                            </div>
                        </div>
                        <div class="mb-3">
                            {{ form.transport_fee_description.label(class="form-label") }}
                            {{ form.transport_fee_description(class="form-control") }}
                        </div>
                        <div class="mb-3 form-check">
                            {{ form.is_active(class="form-check-input") }}
                            {{ form.is_active.label(class="form-check-label") }}
                        </div>
                        <button type="submit" class="btn btn-primary">Update Service Rate</button>
                        <a href="{{ url_for('manager_tutor_profile.tutor_service_rates', tutor_id=tutor.id) }}" class="btn btn-secondary">Cancel</a>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
