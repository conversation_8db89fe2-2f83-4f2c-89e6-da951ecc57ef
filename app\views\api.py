# app/views/api.py
from flask import Blueprint, request, jsonify, session, current_app
from flask_login import login_required, current_user
from app.extensions import db
from app.models.client import Client
from app.models.tutor import Tutor
from app.models.service import Service, TutorService
from app.models.appointment import Appointment
from app.services.appointment_service import AppointmentService
from datetime import datetime, timedelta
import stripe
import hmac
import hashlib
import time

api = Blueprint('api', __name__, url_prefix='/api')

# Exempt all API routes from CSRF protection
@api.after_request
def after_request(response):
    response.headers.add('Access-Control-Allow-Origin', '*')
    response.headers.add('Access-Control-Allow-Headers', 'Content-Type,Authorization')
    response.headers.add('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE')
    return response

@api.route('/tutor-services/<int:tutor_id>')
@login_required
def get_tutor_services(tutor_id):
    """Get services offered by a specific tutor."""
    # Ensure user is authorized (manager or the tutor themselves)
    if current_user.role not in ['manager', 'tutor'] or \
       (current_user.role == 'tutor' and current_user.tutor.tutor_id != tutor_id):
        return jsonify({'error': 'Unauthorized'}), 403

    tutor_services = TutorService.query.filter_by(tutor_id=tutor_id, is_active=True).all()

    result = []
    for ts in tutor_services:
        service = Service.query.get(ts.service_id)
        if service:  # Make sure service exists
            result.append({
                'id': ts.tutor_service_id,
                'service_id': service.service_id,
                'name': service.name,
                'description': service.description,
                'client_rate': float(ts.custom_rate) if ts.custom_rate else float(service.base_rate) if service.base_rate else 0,
                'duration_minutes': service.duration_minutes,
                'transport_fee': float(ts.transport_fee) if ts.transport_fee else 0,
            })

    return jsonify(result)

# Parent-student model has been migrated to client-dependant model

@api.route('/check-availability', methods=['POST'])
@login_required
def check_availability():
    """Check if a tutor is available for a specific time slot."""
    # Only managers can check availability
    if current_user.role != 'manager':
        return jsonify({'error': 'Unauthorized'}), 403

    data = request.json
    tutor_id = data.get('tutor_id')

    # Parse date and time
    try:
        start_date = datetime.strptime(data.get('date'), '%Y-%m-%d').date()
        start_time = datetime.strptime(data.get('start_time'), '%H:%M').time()
        end_time = datetime.strptime(data.get('end_time'), '%H:%M').time()

        start_datetime = datetime.combine(start_date, start_time)
        end_datetime = datetime.combine(start_date, end_time)

        # If we have an appointment ID, we're checking for an update
        appointment_id = data.get('appointment_id')

        is_available = AppointmentService.check_availability(
            tutor_id, start_datetime, end_datetime, appointment_id
        )

        return jsonify({'available': is_available})

    except (ValueError, TypeError) as e:
        return jsonify({'error': str(e)}), 400

@api.route('/calendar-data')
@login_required
def get_calendar_data():
    """Get calendar data for appointments."""
    view_type = request.args.get('view', 'week')
    start_date_str = request.args.get('start_date', datetime.now().strftime('%Y-%m-%d'))
    tutor_id = request.args.get('tutor_id', type=int)

    try:
        start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
    except ValueError:
        start_date = datetime.now()

    # Get end date based on view type
    if view_type == 'day':
        end_date = start_date + timedelta(days=1)
    elif view_type == 'week':
        # Start from Monday of the current week
        monday = start_date - timedelta(days=start_date.weekday())
        start_date = monday
        end_date = start_date + timedelta(days=7)
    elif view_type == 'month':
        # Start from the 1st of the month
        start_date = start_date.replace(day=1)
        # Go to the 1st of next month
        if start_date.month == 12:
            end_date = start_date.replace(year=start_date.year+1, month=1)
        else:
            end_date = start_date.replace(month=start_date.month+1)

    # Base query for appointments with eager loading of relationships
    from sqlalchemy.orm import joinedload
    query = Appointment.query.options(
        joinedload(Appointment.client),
        joinedload(Appointment.dependant),
        joinedload(Appointment.tutor),
        joinedload(Appointment.tutor_service).joinedload(TutorService.service)
    ).filter(
        Appointment.start_time >= start_date,
        Appointment.start_time < end_date
    )

    # Filter by tutor if specified
    if tutor_id:
        query = query.filter(Appointment.tutor_id == tutor_id)
    # Filter by client's appointments if client is viewing
    elif current_user.role == 'client':
        client = Client.query.filter_by(user_id=current_user.user_id).first()
        if client:
            query = query.filter(Appointment.client_id == client.client_id)
    # Filter by tutor's appointments if tutor is viewing
    elif current_user.role == 'tutor':
        tutor = Tutor.query.filter_by(user_id=current_user.user_id).first()
        if tutor:
            query = query.filter(Appointment.tutor_id == tutor.tutor_id)

    appointments = query.all()

    # Get tutors for display (only for manager view)
    tutors = []
    if current_user.role == 'manager':
        tutors = Tutor.query.filter_by(is_active=True).all()
        tutors = [{'id': t.tutor_id, 'name': f"{t.first_name} {t.last_name}"} for t in tutors]

    # Prepare calendar data
    calendar_data = AppointmentService.prepare_calendar_data(appointments, tutors, start_date, end_date, view_type)

    return jsonify(calendar_data)

@api.route('/appointment/<int:id>', methods=['GET'])
@login_required
def get_appointment(id):
    """Get detailed information for a specific appointment."""
    from sqlalchemy.orm import joinedload
    appointment = Appointment.query.options(
        joinedload(Appointment.client),
        joinedload(Appointment.dependant),
        joinedload(Appointment.tutor),
        joinedload(Appointment.tutor_service).joinedload(TutorService.service)
    ).get_or_404(id)

    # Check authorization
    if current_user.role == 'manager':
        pass  # Managers can access all appointments
    elif current_user.role == 'tutor':
        tutor = Tutor.query.filter_by(user_id=current_user.user_id).first()
        if not tutor or appointment.tutor_id != tutor.tutor_id:
            return jsonify({'error': 'Unauthorized'}), 403
    elif current_user.role == 'client':
        client = Client.query.filter_by(user_id=current_user.user_id).first()
        if not client:
            return jsonify({'error': 'Unauthorized'}), 403

        if appointment.client_id != client.client_id:
            return jsonify({'error': 'Unauthorized'}), 403
    else:
        return jsonify({'error': 'Unauthorized'}), 403

    # Get related data from eager-loaded relationships
    tutor = appointment.tutor
    client = appointment.client
    tutor_service = appointment.tutor_service
    service = tutor_service.service if tutor_service else None
    dependant = appointment.dependant

    # Prepare response data
    result = {
        'id': appointment.appointment_id,
        'tutor': {
            'id': tutor.tutor_id,
            'name': f"{tutor.first_name} {tutor.last_name}"
        } if tutor else None,
        'client': {
            'id': client.client_id,
            'name': f"{client.first_name} {client.last_name}"
        } if client else None,
        'dependant': {
            'id': dependant.dependant_id,
            'name': f"{dependant.first_name} {dependant.last_name}"
        } if dependant else None,
        'student': {
            'id': dependant.dependant_id if dependant else client.client_id,
            'name': f"{dependant.first_name} {dependant.last_name}" if dependant else f"{client.first_name} {client.last_name}"
        } if (dependant or client) else None,
        'service': {
            'id': service.service_id if service else None,
            'name': service.name if service else "Unknown",
            'duration_minutes': service.duration_minutes if service else 0,
            'rate': float(tutor_service.client_rate) if tutor_service else 0
        },
        'start_time': appointment.start_time.strftime('%Y-%m-%d %H:%M'),
        'end_time': appointment.end_time.strftime('%Y-%m-%d %H:%M'),
        'status': appointment.status,
        'notes': appointment.notes,
        'is_for_dependant': appointment.dependant_id is not None
    }

    return jsonify(result)

# Legacy generate invoice endpoint removed - use manager interface instead



# Continuation of app/views/api.py

# Legacy invoice endpoint removed - use manager interface instead

# Subscription available check endpoint
@api.route('/subscription/<int:id>/check-availability', methods=['POST'])
@login_required
def check_subscription_availability(id):
    """Check if a subscription has enough hours for an appointment."""
    from app.services.subscription_service import SubscriptionService

    data = request.json
    if not data or 'duration_minutes' not in data:
        return jsonify({'error': 'Duration minutes required'}), 400

    duration_minutes = data['duration_minutes']
    available = SubscriptionService.check_subscription_available(id, duration_minutes)

    return jsonify({
        'subscription_id': id,
        'duration_minutes': duration_minutes,
        'available': available
    })

@api.route('/manager/pending-time-off-count')
@login_required
def get_pending_time_off_count():
    """Get count of pending time-off requests for badge display."""
    # Only managers can access this
    if current_user.role != 'manager':
        return jsonify({'error': 'Unauthorized'}), 403

    from app.models.time_off import TimeOff
    count = TimeOff.query.filter_by(status='pending').count()

    return jsonify({'count': count})

# Legacy student subscriptions endpoint removed - use client-based subscriptions instead

# Legacy parent consent endpoints removed - use client-based consent system instead

@api.route('/clients/search')
@login_required
def search_clients():
    """Search for clients by name or email for dynamic selection."""
    # Only managers can search clients
    if current_user.role != 'manager':
        return jsonify({'error': 'Unauthorized'}), 403

    query = request.args.get('q', '').strip()
    exclude_id = request.args.get('exclude_id', type=int)

    if not query or len(query) < 2:
        return jsonify([])

    # Search clients by name or email
    search_term = f"%{query}%"
    clients_query = db.session.query(Client).join(Client.user).filter(
        db.or_(
            Client.first_name.ilike(search_term),
            Client.last_name.ilike(search_term),
            db.func.concat(Client.first_name, ' ', Client.last_name).ilike(search_term),
            Client.user.has(email=search_term)
        )
    )

    # Exclude specific client if provided (useful when editing)
    if exclude_id:
        clients_query = clients_query.filter(Client.client_id != exclude_id)

    clients = clients_query.limit(10).all()

    results = []
    for client in clients:
        results.append({
            'id': client.client_id,
            'name': f"{client.first_name} {client.last_name}",
            'email': client.user.email,
            'client_type': client.client_type,
            'phone': client.phone
        })

    return jsonify(results)

@api.route('/dependants/search')
@login_required
def search_dependants():
    """Search for dependants by name, email, or phone."""
    query = request.args.get('q', '').strip()

    if len(query) < 2:
        return jsonify([])

    from app.models.dependant import Dependant
    from sqlalchemy import or_

    # Search dependants
    dependants = Dependant.query.filter(
        or_(
            Dependant.first_name.ilike(f'%{query}%'),
            Dependant.last_name.ilike(f'%{query}%'),
            Dependant.email.ilike(f'%{query}%'),
            Dependant.phone.ilike(f'%{query}%')
        )
    ).limit(10).all()

    results = []
    for dependant in dependants:
        results.append({
            'id': dependant.dependant_id,
            'name': f"{dependant.first_name} {dependant.last_name}",
            'email': dependant.email or 'No email',
            'phone': dependant.phone or 'No phone',
            'type': 'dependant'
        })

    return jsonify(results)


@api.route('/clients-and-dependants/search')
@login_required
def search_clients_and_dependants():
    """Search for both clients and dependants for appointment scheduling."""
    # Only managers can search for appointment scheduling
    if current_user.role != 'manager':
        return jsonify({'error': 'Unauthorized'}), 403

    query = request.args.get('q', '').strip()

    if len(query) < 2:
        return jsonify([])

    from app.models.dependant import Dependant
    from sqlalchemy import or_

    results = []

    # Search clients (individual clients only)
    search_term = f"%{query}%"
    clients = Client.query.filter(
        Client.client_type == 'individual',
        or_(
            Client.first_name.ilike(search_term),
            Client.last_name.ilike(search_term),
            db.func.concat(Client.first_name, ' ', Client.last_name).ilike(search_term),
            Client.email.ilike(search_term)
        )
    ).limit(10).all()

    for client in clients:
        results.append({
            'id': client.client_id,
            'name': f"{client.first_name} {client.last_name}",
            'email': client.email or 'No email',
            'phone': client.phone or 'No phone',
            'type': 'client'
        })

    # Search dependants
    dependants = Dependant.query.filter(
        or_(
            Dependant.first_name.ilike(search_term),
            Dependant.last_name.ilike(search_term),
            Dependant.email.ilike(search_term),
            Dependant.phone.ilike(search_term)
        )
    ).limit(10).all()

    for dependant in dependants:
        results.append({
            'id': dependant.dependant_id,
            'name': f"{dependant.first_name} {dependant.last_name}",
            'email': dependant.email or 'No email',
            'phone': dependant.phone or 'No phone',
            'type': 'dependant'
        })

    # Sort results by name
    results.sort(key=lambda x: x['name'])

    return jsonify(results)


@api.route('/clients/<int:client_id>')
@login_required
def get_client(client_id):
    """Get individual client information."""
    if current_user.role != 'manager':
        return jsonify({'error': 'Unauthorized'}), 403

    client = Client.query.get_or_404(client_id)
    return jsonify({
        'id': client.client_id,
        'first_name': client.first_name,
        'last_name': client.last_name,
        'email': client.email,
        'phone': client.phone,
        'client_type': client.client_type
    })


@api.route('/dependants/<int:dependant_id>')
@login_required
def get_dependant(dependant_id):
    """Get individual dependant information."""
    if current_user.role != 'manager':
        return jsonify({'error': 'Unauthorized'}), 403

    from app.models.dependant import Dependant
    dependant = Dependant.query.get_or_404(dependant_id)
    return jsonify({
        'id': dependant.dependant_id,
        'first_name': dependant.first_name,
        'last_name': dependant.last_name,
        'email': dependant.email,
        'phone': dependant.phone
    })


@api.route('/appointment/<int:id>/audit', methods=['GET'])
@login_required
def get_appointment_audit(id):
    """Get audit history for a specific appointment with comprehensive error handling and user feedback."""
    request_start_time = time.time()
    
    try:
        # Only managers can access audit logs
        if current_user.role != 'manager':
            current_app.logger.warning(f"Unauthorized audit access attempt by user {current_user.id} with role {current_user.role} for appointment {id}")
            return jsonify({
                'error': 'Access Denied', 
                'message': 'Only managers can access audit trails. Please contact your administrator if you believe you should have access.',
                'error_code': 'AUDIT_ACCESS_DENIED',
                'timestamp': datetime.now().isoformat()
            }), 403
        
        from app.services.audit_service import AuditService
        from sqlalchemy.orm import joinedload
        
        # Verify appointment exists and get appointment details
        try:
            appointment = Appointment.query.options(
                joinedload(Appointment.client),
                joinedload(Appointment.dependant),
                joinedload(Appointment.tutor),
                joinedload(Appointment.tutor_service).joinedload(TutorService.service)
            ).get_or_404(id)
        except Exception as e:
            current_app.logger.error(f"Error fetching appointment {id}: {str(e)}")
            return jsonify({
                'error': 'Appointment not found',
                'message': f'Appointment with ID {id} does not exist'
            }), 404
        
        # Get and validate pagination parameters
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        # Validate pagination parameters
        if page < 1:
            page = 1
        if per_page < 1 or per_page > 100:  # Limit max per_page to prevent abuse
            per_page = 20
        
        # Get audit history using the audit service with comprehensive error handling
        use_cache = request.args.get('cache', 'true').lower() == 'true'
        
        try:
            audit_data = AuditService.get_appointment_audit_history(id, page, per_page, use_cache)
            
            # Validate audit data structure
            if not audit_data or not isinstance(audit_data, dict):
                raise ValueError("Invalid audit data structure returned from service")
            
            # Check for required fields in audit data
            required_fields = ['entries', 'pagination', 'total']
            for field in required_fields:
                if field not in audit_data:
                    raise ValueError(f"Missing required field '{field}' in audit data")
            
            # Validate entries is a list
            if not isinstance(audit_data['entries'], list):
                raise ValueError("Audit entries must be a list")
                
        except ValueError as ve:
            current_app.logger.error(f"Data validation error for appointment {id}: {str(ve)}")
            return jsonify({
                'error': 'Data Validation Error',
                'message': 'The audit data structure is invalid. This may indicate a system issue.',
                'error_code': 'AUDIT_DATA_INVALID',
                'timestamp': datetime.now().isoformat(),
                'details': str(ve)
            }), 500
            
        except Exception as e:
            current_app.logger.error(f"Service error fetching audit history for appointment {id}: {str(e)}")
            
            # Determine error type and provide appropriate response
            if 'database' in str(e).lower() or 'connection' in str(e).lower():
                return jsonify({
                    'error': 'Database Connection Error',
                    'message': 'Unable to connect to the database. Please try again in a few moments.',
                    'error_code': 'AUDIT_DB_ERROR',
                    'timestamp': datetime.now().isoformat(),
                    'retry_recommended': True
                }), 503
            elif 'timeout' in str(e).lower():
                return jsonify({
                    'error': 'Request Timeout',
                    'message': 'The request took too long to process. Please try again.',
                    'error_code': 'AUDIT_TIMEOUT',
                    'timestamp': datetime.now().isoformat(),
                    'retry_recommended': True
                }), 504
            else:
                return jsonify({
                    'error': 'Service Error',
                    'message': 'An error occurred while retrieving the audit trail. Please try again.',
                    'error_code': 'AUDIT_SERVICE_ERROR',
                    'timestamp': datetime.now().isoformat(),
                    'retry_recommended': True
                }), 500
        
        # Prepare enhanced appointment summary for the modal
        appointment_summary = {
            'id': appointment.id,
            'client_name': f"{appointment.client.first_name} {appointment.client.last_name}" if appointment.client else 'Unknown Client',
            'tutor_name': f"{appointment.tutor.first_name} {appointment.tutor.last_name}" if appointment.tutor else 'Unknown Tutor',
            'status': appointment.status,
            'start_time': appointment.start_time.isoformat() if appointment.start_time else None,
            'end_time': appointment.end_time.isoformat() if appointment.end_time else None,
            'service_name': appointment.tutor_service.service.name if appointment.tutor_service and appointment.tutor_service.service else 'Unknown Service',
            'notes': appointment.notes
        }
        
        # If there's a dependant, use their name instead of client name
        if appointment.dependant:
            appointment_summary['client_name'] = f"{appointment.dependant.first_name} {appointment.dependant.last_name}"
            appointment_summary['is_for_dependant'] = True
        else:
            appointment_summary['is_for_dependant'] = False
        
        # Calculate request processing time
        request_processing_time = (time.time() - request_start_time) * 1000  # Convert to milliseconds
        
        # Enhanced response with better structure and error handling info
        response_data = {
            'success': True,
            'appointment': appointment_summary,
            'audit_entries': audit_data['entries'],
            'pagination': {
                'current_page': audit_data['pagination']['page'],
                'per_page': audit_data['pagination']['per_page'],
                'total_pages': audit_data['pagination']['total_pages'],
                'total_entries': audit_data['total'],
                'has_previous': audit_data['pagination']['has_prev'],
                'has_next': audit_data['pagination']['has_next'],
                'previous_page': audit_data['pagination']['prev_num'],
                'next_page': audit_data['pagination']['next_num']
            },
            'meta': {
                'request_timestamp': datetime.now().isoformat(),
                'timezone': 'EST',
                'processing_time_ms': round(request_processing_time, 2),
                'from_cache': audit_data.get('from_cache', False)
            }
        }
        
        # Include error info if present (partial failures)
        if 'error_info' in audit_data:
            response_data['warnings'] = {
                'type': audit_data['error_info']['type'],
                'message': audit_data['error_info']['message'],
                'affected_entries': audit_data['error_info'].get('total_errors', 0)
            }
            current_app.logger.warning(f"Partial failure in audit retrieval for appointment {id}: {audit_data['error_info']['message']}")
        
        # Add performance data for frontend optimization
        if request_processing_time > 1000:  # Log slow requests
            current_app.logger.warning(f"Slow audit request for appointment {id}: {request_processing_time}ms")
            response_data['performance'] = {
                'load_time': round(request_processing_time, 2),
                'slow_request': True,
                'optimization_recommended': True
            }
        else:
            response_data['performance'] = {
                'load_time': round(request_processing_time, 2),
                'slow_request': False
            }
        
        current_app.logger.info(f"Audit history retrieved for appointment {id} by manager {current_user.id} in {request_processing_time:.2f}ms")
        return jsonify(response_data)
        
    except Exception as e:
        current_app.logger.error(f"Unexpected error in get_appointment_audit for appointment {id}: {str(e)}")
        return jsonify({
            'error': 'Internal server error',
            'message': 'An unexpected error occurred while processing your request'
        }), 500


@api.route('/appointment/<int:id>/audit/summary', methods=['GET'])
@login_required
def get_appointment_audit_summary(id):
    """Get a quick audit summary for an appointment."""
    try:
        # Only managers can access audit logs
        if current_user.role != 'manager':
            current_app.logger.warning(f"Unauthorized audit summary access attempt by user {current_user.id} with role {current_user.role}")
            return jsonify({
                'error': 'Unauthorized',
                'message': 'Only managers can access audit summaries'
            }), 403
        
        from app.services.audit_service import AuditService
        from sqlalchemy.orm import joinedload
        
        # Verify appointment exists
        try:
            appointment = Appointment.query.options(
                joinedload(Appointment.client),
                joinedload(Appointment.dependant),
                joinedload(Appointment.tutor)
            ).get_or_404(id)
        except Exception as e:
            current_app.logger.error(f"Error fetching appointment {id} for summary: {str(e)}")
            return jsonify({
                'error': 'Appointment not found',
                'message': f'Appointment with ID {id} does not exist'
            }), 404
        
        # Get audit summary using the audit service
        try:
            summary_data = AuditService.get_audit_summary(id)
        except Exception as e:
            current_app.logger.error(f"Error fetching audit summary for appointment {id}: {str(e)}")
            return jsonify({
                'error': 'Failed to load audit summary',
                'message': 'An error occurred while retrieving the audit summary'
            }), 500
        
        # Enhanced response with appointment context
        response_data = {
            'success': True,
            'appointment_id': id,
            'appointment_info': {
                'client_name': f"{appointment.client.first_name} {appointment.client.last_name}" if appointment.client else 'Unknown Client',
                'tutor_name': f"{appointment.tutor.first_name} {appointment.tutor.last_name}" if appointment.tutor else 'Unknown Tutor',
                'status': appointment.status
            },
            'audit_summary': summary_data,
            'meta': {
                'request_timestamp': datetime.now().isoformat(),
                'timezone': 'EST'
            }
        }
        
        # If there's a dependant, use their name instead of client name
        if appointment.dependant:
            response_data['appointment_info']['client_name'] = f"{appointment.dependant.first_name} {appointment.dependant.last_name}"
            response_data['appointment_info']['is_for_dependant'] = True
        else:
            response_data['appointment_info']['is_for_dependant'] = False
        
        current_app.logger.info(f"Audit summary retrieved for appointment {id} by manager {current_user.id}")
        return jsonify(response_data)
        
    except Exception as e:
        current_app.logger.error(f"Unexpected error in get_appointment_audit_summary for appointment {id}: {str(e)}")
        return jsonify({
            'error': 'Internal server error',
            'message': 'An unexpected error occurred while processing your request'
        }), 500


@api.route('/client/<int:client_id>/subscriptions')
@login_required
def get_client_subscriptions(client_id):
    """Get active subscriptions for a specific client."""
    if current_user.role != 'manager':
        return jsonify({'error': 'Unauthorized'}), 403

    from app.services.subscription_service import SubscriptionService
    from app.models.subscription import Subscription

    # Get active subscriptions for this client
    subscriptions = SubscriptionService.get_active_subscriptions_for_client(client_id)

    # Format the response
    subscription_data = []
    for subscription in subscriptions:
        subscription_data.append({
            'id': subscription.id,
            'plan_name': subscription.plan.name,
            'hours_remaining': float(subscription.hours_remaining),
            'hours_used': float(subscription.hours_used),
            'max_hours': subscription.plan.max_hours,
            'start_date': subscription.start_date.isoformat(),
            'end_date': subscription.end_date.isoformat(),
            'status': subscription.status,
            'owner_name': f"{subscription.client.first_name} {subscription.client.last_name}"
        })

    return jsonify(subscription_data)


@api.route('/dependant/<int:dependant_id>/subscriptions')
@login_required
def get_dependant_subscriptions(dependant_id):
    """Get active subscriptions available to a dependant through their client relationships."""
    if current_user.role != 'manager':
        return jsonify({'error': 'Unauthorized'}), 403

    from app.services.subscription_service import SubscriptionService

    # Get active subscriptions available to this dependant
    subscriptions = SubscriptionService.get_active_subscriptions_for_dependant(dependant_id)

    # Format the response
    subscription_data = []
    for subscription in subscriptions:
        subscription_data.append({
            'id': subscription.id,
            'plan_name': subscription.plan.name,
            'hours_remaining': float(subscription.hours_remaining),
            'hours_used': float(subscription.hours_used),
            'max_hours': subscription.plan.max_hours,
            'start_date': subscription.start_date.isoformat(),
            'end_date': subscription.end_date.isoformat(),
            'status': subscription.status,
            'owner_name': f"{subscription.client.first_name} {subscription.client.last_name}"
        })

    return jsonify(subscription_data)


@api.route('/health-check', methods=['GET', 'HEAD'])
def health_check():
    """Simple health check endpoint for network connectivity verification."""
    try:
        # Basic database connectivity check
        db.session.execute(text('SELECT 1'))
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'database': 'connected'
        }), 200
    except Exception as e:
        current_app.logger.error(f"Health check failed: {str(e)}")
        return jsonify({
            'status': 'unhealthy',
            'timestamp': datetime.now().isoformat(),
            'database': 'disconnected',
            'error': str(e)
        }), 503


@api.route('/dependants', methods=['POST'])
@login_required
def create_dependant_api():
    """Create a new dependant via API (for modal creation)."""
    print("=== DEPENDANT CREATION API CALLED ===")
    print(f"Request method: {request.method}")
    print(f"Request URL: {request.url}")
    print(f"Request headers: {dict(request.headers)}")
    print(f"Request content type: {request.content_type}")
    print(f"Raw request data: {request.data}")

    # Only managers can create dependants
    if current_user.role != 'manager':
        return jsonify({'error': 'Unauthorized'}), 403

    # Check CSRF token
    from flask_wtf.csrf import validate_csrf
    try:
        validate_csrf(request.headers.get('X-CSRFToken'))
    except Exception as e:
        print(f"CSRF validation failed: {e}")
        return jsonify({'error': 'CSRF token missing or invalid'}), 400

    data = request.json

    # Debug: Log received data
    print(f"Parsed JSON data: {data}")
    print(f"Data type: {type(data)}")
    if data:
        print("Individual field analysis:")
        for key, value in data.items():
            print(f"  {key}: '{value}' (type: {type(value)}, len: {len(str(value)) if value is not None else 'N/A'})")
    else:
        print("NO DATA RECEIVED!")
    print("=== END DEBUG INFO ===")

    # Check if data is None or empty
    if not data:
        return jsonify({'error': 'No data received'}), 400

    # Check if all critical fields are None (indicates form collection failure)
    if (data.get('first_name') is None and
        data.get('last_name') is None and
        data.get('client_type') is None):
        return jsonify({'error': 'Form data collection failed - all required fields are None. Please check the form and try again.'}), 400

    # Validate required fields (first_name, last_name, and client_type are required)
    # Note: client_type is inherited from parent and should always be provided by the frontend
    required_fields = ['first_name', 'last_name', 'client_type']
    for field in required_fields:
        if field not in data:
            print(f"Field {field} not in data")
            return jsonify({'error': f'Missing field: {field}'}), 400

        field_value = data[field]
        print(f"Validating field {field}: value='{field_value}', type={type(field_value)}")

        if field_value is None:
            print(f"Field {field} is None")
            return jsonify({'error': f'Field {field} cannot be None'}), 400

        if str(field_value).strip() == '':
            print(f"Field {field} is empty after stripping")
            # More specific error message for first_name
            if field == 'first_name':
                return jsonify({'error': 'first name is required and cannot be empty'}), 400
            else:
                return jsonify({'error': f'Empty value for required field: {field}'}), 400

    # Extract and clean the validated data
    first_name = str(data.get('first_name')).strip()
    last_name = str(data.get('last_name')).strip()
    client_type = str(data.get('client_type')).strip()

    print(f"Final processed values:")
    print(f"  first_name: '{first_name}'")
    print(f"  last_name: '{last_name}'")
    print(f"  client_type: '{client_type}'")

    # If email is provided, validate it and check for duplicates
    email = data.get('email', '').strip()
    password = data.get('password', '').strip()

    # If email is provided, password should also be provided (and vice versa)
    if email and not password:
        return jsonify({'error': 'If email is provided, password is also required'}), 400
    if password and not email:
        return jsonify({'error': 'If password is provided, email is also required'}), 400

    # Check if email already exists (only if email is provided)
    user = None
    if email:
        from app.models.user import User
        if User.query.filter_by(email=email).first():
            return jsonify({'error': 'Email already registered'}), 400

    try:
        # Import the Dependant model
        from app.models.dependant import Dependant

        # Create user only if email is provided
        if email:
            from app.models.user import User
            user = User(
                email=email,
                password=password,
                role='client'
            )
            db.session.add(user)
            db.session.flush()  # Get the user ID without committing
            user_id = user.id
        else:
            user_id = None

        # Create dependant record (not client record)
        phone = data.get('phone', '').strip()
        address = data.get('address', '').strip()

        # Create dependant with explicit assignment
        dependant = Dependant()
        dependant.user_id = user_id
        dependant.first_name = first_name
        dependant.last_name = last_name
        dependant.email = email if email else None
        dependant.phone = phone if phone else None
        dependant.address = address if address else None
        dependant.is_active = True
        dependant.preferred_language = 'en'

        # Add type-specific fields for individual dependants
        if client_type == 'individual':
            if data.get('date_of_birth', '').strip():
                from datetime import datetime
                dependant.date_of_birth = datetime.strptime(data['date_of_birth'], '%Y-%m-%d').date()
            dependant.school_grade = data.get('school_grade', '').strip() if data.get('school_grade', '').strip() else None

        # Add notes
        dependant.notes = data.get('notes', '').strip() if data.get('notes', '').strip() else None

        db.session.add(dependant)
        db.session.commit()

        return jsonify({
            'success': True,
            'dependant': {
                'id': dependant.id,
                'name': f"{dependant.first_name} {dependant.last_name}",
                'email': email if email else 'No email provided',
                'type': 'dependant',  # Always return 'dependant' as type
                'phone': dependant.phone if dependant.phone else 'No phone provided'
            }
        })

    except Exception as e:
        db.session.rollback()
        print(f"Exception occurred during client creation: {str(e)}")
        print(f"Exception type: {type(e)}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")

        # Debug: Print the data that was being used when the exception occurred
        print(f"Data being processed when exception occurred:")
        print(f"  first_name: '{first_name}' (length: {len(first_name)})")
        print(f"  last_name: '{last_name}' (length: {len(last_name)})")
        print(f"  client_type: '{client_type}' (length: {len(client_type)})")
        print(f"  email: '{email}' (length: {len(email)})")
        print(f"  phone: '{phone}' (length: {len(phone)})")

        # Provide more specific error messages
        error_message = str(e)
        if 'null value' in error_message.lower():
            if 'first_name' in error_message:
                return jsonify({'error': f'Database constraint violation for first_name. Value was: "{first_name}". Error: {error_message}'}), 400
            elif 'last_name' in error_message:
                return jsonify({'error': f'Database constraint violation for last_name. Value was: "{last_name}". Error: {error_message}'}), 400
            elif 'client_type' in error_message:
                return jsonify({'error': f'Database constraint violation for client_type. Value was: "{client_type}". Error: {error_message}'}), 400
            else:
                return jsonify({'error': f'Database constraint violation: {error_message}'}), 400
        else:
            return jsonify({'error': f'Failed to create client: {error_message}'}), 500

@api.route('/set-language', methods=['POST'])
@login_required
def set_language():
    """Set the user's preferred language."""
    data = request.json

    # Validate required fields
    if 'language' not in data:
        return jsonify({'success': False, 'message': 'Missing required field: language'}), 400

    language = data['language']

    # Validate language code
    if language not in ['en', 'fr']:
        return jsonify({'success': False, 'message': 'Invalid language code. Supported: en, fr'}), 400

    # Store in session
    session['language'] = language

    # If user is a client, store preference in database
    if current_user.role == 'client':
        client = Client.query.filter_by(user_id=current_user.id).first()
        if client:
            # Update preferred_language if the column exists
            try:
                client.preferred_language = language
                db.session.commit()
            except:
                # Column might not exist yet, just use session
                pass

    return jsonify({'success': True})

@api.route('/stripe/webhook', methods=['POST'])
def stripe_webhook():
    """Handle Stripe webhook events with proper verification and race condition protection."""
    payload = request.get_data()
    sig_header = request.headers.get('Stripe-Signature')

    # Verify webhook signature
    webhook_secret = current_app.config.get('STRIPE_WEBHOOK_SECRET')
    if not webhook_secret:
        current_app.logger.error('Stripe webhook secret not configured')
        return jsonify({'error': 'Webhook secret not configured'}), 500

    try:
        # Verify the webhook signature
        stripe.api_key = current_app.config['STRIPE_SECRET_KEY']
        event = stripe.Webhook.construct_event(
            payload, sig_header, webhook_secret
        )
    except ValueError as e:
        # Invalid payload
        current_app.logger.error(f'Invalid payload in Stripe webhook: {str(e)}')
        return jsonify({'error': 'Invalid payload'}), 400
    except stripe.error.SignatureVerificationError as e:
        # Invalid signature
        current_app.logger.error(f'Invalid signature in Stripe webhook: {str(e)}')
        return jsonify({'error': 'Invalid signature'}), 400

    # Process the event using our race-condition-safe service
    try:
        from app.services.stripe_service import StripeService
        success = StripeService.handle_webhook_event(event)

        if success:
            current_app.logger.info(f'Successfully processed Stripe webhook event: {event["type"]}')
            return jsonify({'status': 'success'}), 200
        else:
            current_app.logger.warning(f'Unhandled Stripe webhook event type: {event["type"]}')
            return jsonify({'status': 'ignored'}), 200

    except Exception as e:
        current_app.logger.error(f'Error processing Stripe webhook: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500


@api.route('/tutors')
@login_required
def get_tutors():
    """Get all active tutors."""
    if current_user.role != 'manager':
        return jsonify({'error': 'Unauthorized'}), 403
    
    tutors = Tutor.query.filter_by(is_active=True).order_by(Tutor.last_name).all()
    
    result = []
    for tutor in tutors:
        result.append({
            'id': tutor.tutor_id,
            'first_name': tutor.first_name,
            'last_name': tutor.last_name,
            'email': tutor.user.email if tutor.user else '',
            'phone': tutor.phone
        })
    
    return jsonify(result)


@api.route('/transport-fee/<int:tutor_id>/<int:client_id>')
@login_required
def get_transport_fee(tutor_id, client_id):
    """Get transport fee for a tutor-client combination."""
    if current_user.role != 'manager':
        return jsonify({'error': 'Unauthorized'}), 403
    
    # For now, return a default fee
    # In the future, this could calculate based on distance or other factors
    return jsonify({'fee_amount': 0.00})




@api.route('/appointments/create', methods=['POST'])
@login_required
def create_appointment():
    """Create a new appointment via AJAX."""
    if current_user.role != 'manager':
        return jsonify({'error': 'Unauthorized', 'success': False}), 403
    
    data = request.json
    
    # Validate required fields
    errors = {}
    
    if not data.get('tutor_id'):
        errors['tutor_id'] = ['Tutor is required']
    if not data.get('client_id'):
        errors['client_id'] = ['Client is required']
    if not data.get('tutor_service_id'):
        errors['tutor_service_id'] = ['Service is required']
    
    # For regular appointments, validate date/time
    if not data.get('is_recurring'):
        if not data.get('start_date'):
            errors['start_date'] = ['Date is required']
        if not data.get('start_time'):
            errors['start_time'] = ['Start time is required']
    else:
        # For recurring appointments, validate pattern fields
        if not data.get('frequency'):
            errors['frequency'] = ['Frequency is required']
        if not data.get('day_of_week'):
            errors['day_of_week'] = ['Day of week is required']
        if not data.get('start_time'):
            errors['start_time'] = ['Start time is required']
        if not data.get('pattern_start_date'):
            errors['pattern_start_date'] = ['Start date is required']
    
    if errors:
        return jsonify({
            'success': False,
            'errors': errors,
            'message': 'Please correct the errors and try again.'
        }), 400
    
    try:
        from app.forms.manager_forms import AppointmentForm
        from datetime import datetime
        
        # Create form instance and populate with data
        form = AppointmentForm()
        
        # Manually set form data from JSON
        form.tutor_id.data = int(data.get('tutor_id'))
        form.client_id.data = data.get('client_id')
        form.client_type.data = data.get('client_type', 'client')
        form.dependant_id.data = data.get('dependant_id')
        form.tutor_service_id.data = int(data.get('tutor_service_id'))
        form.is_recurring.data = data.get('is_recurring', False)
        form.is_subscription_based.data = data.get('is_subscription_based', False)
        form.subscription_id.data = int(data.get('subscription_id', 0))
        form.apply_transport_fee.data = data.get('apply_transport_fee', False)
        form.transport_fee.data = float(data.get('transport_fee', 0))
        form.notes.data = data.get('notes', '')
        
        if not form.is_recurring.data:
            # Regular appointment fields
            form.start_date.data = datetime.strptime(data.get('start_date'), '%Y-%m-%d').date()
            form.start_time.data = data.get('start_time')
            form.status.data = data.get('status', 'scheduled')
            
            # Calculate end time if provided
            if data.get('end_time'):
                end_time_str = data.get('end_time')
                if ':' in end_time_str:
                    hours, minutes = map(int, end_time_str.split(':'))
                    form.end_time.data = datetime.strptime(f"{hours:02d}:{minutes:02d}", '%H:%M').time()
        else:
            # Recurring appointment fields
            form.frequency.data = data.get('frequency')
            form.day_of_week.data = int(data.get('day_of_week'))
            form.week_of_month.data = int(data.get('week_of_month', 1))
            form.pattern_start_date.data = datetime.strptime(data.get('pattern_start_date'), '%Y-%m-%d').date()
            form.end_type.data = data.get('end_type', 'never')
            
            if data.get('pattern_end_date'):
                form.pattern_end_date.data = datetime.strptime(data.get('pattern_end_date'), '%Y-%m-%d').date()
            if data.get('pattern_occurrences'):
                form.pattern_occurrences.data = int(data.get('pattern_occurrences'))
        
        # Populate form choices
        AppointmentService.populate_appointment_form_choices(form)
        
        # Disable CSRF for API endpoint
        form.csrf_token.data = request.headers.get('X-CSRFToken', '')
        
        # Create appointment
        if form.is_recurring.data:
            # Create recurring appointment
            recurring_template, generated_appointments = AppointmentService.create_appointment_from_form(form)
            
            if recurring_template:
                return jsonify({
                    'success': True,
                    'message': f'Recurring appointment created successfully! {len(generated_appointments)} appointments have been scheduled.',
                    'appointment_id': recurring_template.id,
                    'appointments_created': len(generated_appointments)
                })
            else:
                return jsonify({
                    'success': False,
                    'message': 'Error creating recurring appointment.'
                }), 400
        else:
            # Create single appointment
            appointment = AppointmentService.create_appointment_from_form(form)
            
            if appointment:
                return jsonify({
                    'success': True,
                    'message': 'Appointment scheduled successfully!',
                    'appointment_id': appointment.id
                })
            else:
                return jsonify({
                    'success': False,
                    'message': 'Error scheduling appointment. Please check for conflicts.'
                }), 400
    
    except Exception as e:
        current_app.logger.error(f'Error creating appointment: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'An error occurred: {str(e)}'
        }), 500


@api.route('/appointments/<int:id>/update', methods=['POST'])
@login_required
def update_appointment(id):
    """Update an existing appointment via AJAX."""
    if current_user.role != 'manager':
        return jsonify({'error': 'Unauthorized', 'success': False}), 403
    
    appointment = Appointment.query.get_or_404(id)
    
    # Don't allow updating recurring templates via this endpoint
    if appointment.is_recurring:
        return jsonify({
            'success': False,
            'message': 'Updating recurring appointment templates is not yet implemented.'
        }), 400
    
    data = request.json
    
    # Validate required fields
    errors = {}
    
    if not data.get('tutor_id'):
        errors['tutor_id'] = ['Tutor is required']
    if not data.get('tutor_service_id'):
        errors['tutor_service_id'] = ['Service is required']
    if not data.get('start_date'):
        errors['start_date'] = ['Date is required']
    if not data.get('start_time'):
        errors['start_time'] = ['Start time is required']
    
    if errors:
        return jsonify({
            'success': False,
            'errors': errors,
            'message': 'Please correct the errors and try again.'
        }), 400
    
    try:
        from app.forms.manager_forms import AppointmentForm
        from datetime import datetime
        
        # Create form instance
        form = AppointmentForm()
        
        # Manually set form data from JSON
        form.tutor_id.data = int(data.get('tutor_id'))
        form.client_id.data = f"c_{appointment.client_id}"  # Maintain existing client
        form.client_type.data = 'dependant' if appointment.dependant_id else 'client'
        form.dependant_id.data = appointment.dependant_id
        form.tutor_service_id.data = int(data.get('tutor_service_id'))
        form.is_subscription_based.data = data.get('is_subscription_based', False)
        form.subscription_id.data = int(data.get('subscription_id', 0))
        form.apply_transport_fee.data = data.get('apply_transport_fee', False)
        form.transport_fee.data = float(data.get('transport_fee', 0))
        form.status.data = data.get('status', 'scheduled')
        form.notes.data = data.get('notes', '')
        
        # Date and time fields
        form.start_date.data = datetime.strptime(data.get('start_date'), '%Y-%m-%d').date()
        form.start_time.data = data.get('start_time')
        
        # Calculate end time if provided
        if data.get('end_time'):
            end_time_str = data.get('end_time')
            if ':' in end_time_str:
                hours, minutes = map(int, end_time_str.split(':'))
                form.end_time.data = datetime.strptime(f"{hours:02d}:{minutes:02d}", '%H:%M').time()
        
        # Populate form choices
        AppointmentService.populate_appointment_form_choices(form)
        
        # Disable CSRF for API endpoint
        form.csrf_token.data = request.headers.get('X-CSRFToken', '')
        
        # Update appointment
        success = AppointmentService.update_appointment_from_form(appointment, form)
        
        if success:
            # Force database refresh
            db.session.refresh(appointment)
            
            return jsonify({
                'success': True,
                'message': 'Appointment updated successfully!',
                'appointment_id': appointment.id
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Error updating appointment. Please check for conflicts.'
            }), 400
    
    except Exception as e:
        current_app.logger.error(f'Error updating appointment: {str(e)}')
        return jsonify({
            'success': False,
            'message': f'An error occurred: {str(e)}'
        }), 500
@api.route('/appointment/<int:id>/audit/performance', methods=['GET'])
@login_required
def get_appointment_audit_performance(id):
    """Get performance metrics for audit queries on a specific appointment."""
    try:
        # Only managers can access performance metrics
        if current_user.role != 'manager':
            return jsonify({
                'error': 'Unauthorized',
                'message': 'Only managers can access performance metrics'
            }), 403
        
        from app.services.audit_service import AuditService
        
        # Verify appointment exists
        appointment = Appointment.query.get_or_404(id)
        
        # Get performance metrics
        try:
            metrics = AuditService.get_audit_performance_metrics(id)
        except Exception as e:
            current_app.logger.error(f"Error getting performance metrics for appointment {id}: {str(e)}")
            return jsonify({
                'error': 'Failed to get performance metrics',
                'message': 'An error occurred while analyzing performance'
            }), 500
        
        current_app.logger.info(f"Performance metrics retrieved for appointment {id} by manager {current_user.id}")
        return jsonify(metrics)
        
    except Exception as e:
        current_app.logger.error(f"Unexpected error in get_appointment_audit_performance for appointment {id}: {str(e)}")
        return jsonify({
            'error': 'Internal server error',
            'message': 'An unexpected error occurred while processing your request'
        }), 500


@api.route('/audit/database/verify-indexes', methods=['GET'])
@login_required
def verify_audit_database_indexes():
    """Verify that proper database indexes exist for optimal audit query performance."""
    try:
        # Only managers can verify database indexes
        if current_user.role != 'manager':
            return jsonify({
                'error': 'Unauthorized',
                'message': 'Only managers can verify database indexes'
            }), 403
        
        from app.services.audit_service import AuditService
        
        # Get index verification results
        try:
            verification_results = AuditService.verify_database_indexes()
        except Exception as e:
            current_app.logger.error(f"Error verifying database indexes: {str(e)}")
            return jsonify({
                'error': 'Failed to verify indexes',
                'message': 'An error occurred while verifying database indexes'
            }), 500
        
        current_app.logger.info(f"Database indexes verified by manager {current_user.id}")
        return jsonify(verification_results)
        
    except Exception as e:
        current_app.logger.error(f"Unexpected error in verify_audit_database_indexes: {str(e)}")
        return jsonify({
            'error': 'Internal server error',
            'message': 'An unexpected error occurred while processing your request'
        }), 500


@api.route('/audit/cache/clear', methods=['POST'])
@login_required
def clear_audit_cache():
    """Clear audit cache for a specific appointment or all cached data."""
    try:
        # Only managers can clear cache
        if current_user.role != 'manager':
            return jsonify({
                'error': 'Unauthorized',
                'message': 'Only managers can clear audit cache'
            }), 403
        
        from app.services.audit_service import AuditService
        
        # Get appointment ID from request data (optional)
        data = request.get_json() or {}
        appointment_id = data.get('appointment_id')
        
        # Clear cache
        try:
            AuditService.clear_audit_cache(appointment_id)
            
            if appointment_id:
                message = f"Cache cleared for appointment {appointment_id}"
                current_app.logger.info(f"Audit cache cleared for appointment {appointment_id} by manager {current_user.id}")
            else:
                message = "All audit cache cleared"
                current_app.logger.info(f"All audit cache cleared by manager {current_user.id}")
            
            return jsonify({
                'success': True,
                'message': message,
                'appointment_id': appointment_id,
                'timestamp': datetime.now().isoformat()
            })
            
        except Exception as e:
            current_app.logger.error(f"Error clearing audit cache: {str(e)}")
            return jsonify({
                'error': 'Failed to clear cache',
                'message': 'An error occurred while clearing the cache'
            }), 500
        
    except Exception as e:
        current_app.logger.error(f"Unexpected error in clear_audit_cache: {str(e)}")
        return jsonify({
            'error': 'Internal server error',
            'message': 'An unexpected error occurred while processing your request'
        }), 500


@api.route('/audit/cache/status', methods=['GET'])
@login_required
def get_audit_cache_status():
    """Get current audit cache status and statistics."""
    try:
        # Only managers can view cache status
        if current_user.role != 'manager':
            return jsonify({
                'error': 'Unauthorized',
                'message': 'Only managers can view cache status'
            }), 403
        
        from app.services.audit_service import AuditService
        
        # Get cache statistics
        cache_size = len(AuditService._audit_cache)
        timestamp_count = len(AuditService._cache_timestamps)
        
        # Calculate cache age statistics
        import time
        current_time = time.time()
        cache_ages = []
        
        for cache_key, timestamp in AuditService._cache_timestamps.items():
            age_seconds = current_time - timestamp
            cache_ages.append({
                'key': cache_key,
                'age_seconds': round(age_seconds, 2),
                'age_minutes': round(age_seconds / 60, 2),
                'is_expired': age_seconds > AuditService._cache_ttl
            })
        
        # Sort by age (oldest first)
        cache_ages.sort(key=lambda x: x['age_seconds'], reverse=True)
        
        # Calculate statistics
        expired_count = sum(1 for entry in cache_ages if entry['is_expired'])
        avg_age = sum(entry['age_seconds'] for entry in cache_ages) / len(cache_ages) if cache_ages else 0
        
        cache_status = {
            'cache_enabled': True,
            'cache_size': cache_size,
            'timestamp_count': timestamp_count,
            'cache_ttl_seconds': AuditService._cache_ttl,
            'expired_entries': expired_count,
            'active_entries': cache_size - expired_count,
            'average_age_seconds': round(avg_age, 2),
            'average_age_minutes': round(avg_age / 60, 2),
            'oldest_entries': cache_ages[:5],  # Show 5 oldest entries
            'memory_usage_estimate': {
                'entries': cache_size,
                'estimated_kb': round(cache_size * 2, 2)  # Rough estimate
            },
            'timestamp': datetime.now().isoformat()
        }
        
        current_app.logger.info(f"Cache status retrieved by manager {current_user.id}")
        return jsonify(cache_status)
        
    except Exception as e:
        current_app.logger.error(f"Unexpected error in get_audit_cache_status: {str(e)}")
        return jsonify({
            'error': 'Internal server error',
            'message': 'An unexpected error occurred while processing your request'
        }), 500