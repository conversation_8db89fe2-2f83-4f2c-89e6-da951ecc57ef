# Development Database Management Approach

## Context
During development phase with mocked/test data, database schema changes should prioritize simplicity and speed over data preservation.

## Guidelines

### Development Mode Database Changes
- **Drop and Recreate**: When making significant schema changes, drop existing tables and recreate them from scratch
- **No Complex Migrations**: Avoid complex migration scripts during development - they add unnecessary complexity
- **Fresh Schema**: Use the main schema.sql file as the single source of truth
- **Mock Data**: All development data is disposable and can be regenerated

### When to Use This Approach
- ✅ Development environment
- ✅ Testing environment  
- ✅ When working with mock/sample data
- ✅ During rapid prototyping phases

### When NOT to Use This Approach
- ❌ Production environment
- ❌ Staging with real user data
- ❌ When preserving existing data is critical

### Implementation Pattern
1. Update the main `schema.sql` file with new table definitions
2. Drop existing database tables
3. Recreate from updated schema
4. Regenerate mock data as needed

### Benefits
- **Faster Development**: No time spent writing complex migrations
- **Cleaner Schema**: Always working with the latest, cleanest schema design
- **Fewer Bugs**: Eliminates migration-related issues during development
- **Easier Testing**: Fresh database state for each test cycle

## Database Recreation Commands

```bash
# Drop all tables (development only!)
python -c "from app import create_app; from app.extensions import db; app = create_app(); app.app_context().push(); db.drop_all()"

# Recreate from schema
python -c "from app import create_app; from app.extensions import db; app = create_app(); app.app_context().push(); db.create_all()"
```

## Note
This approach is specifically for development environments. Production deployments should always use proper migration strategies to preserve data integrity.