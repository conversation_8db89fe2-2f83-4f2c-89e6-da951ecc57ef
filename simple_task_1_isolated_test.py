#!/usr/bin/env python3
"""
Isolated test for Task 1: Service and TutorService model updates.
Tests only the model definitions without database initialization.
"""

import os
import sys

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_service_model_definition():
    """Test Service model definition."""
    print("Testing Service model definition...")
    
    try:
        from app.models.service import Service
        
        # Check that Service has the correct primary key
        primary_keys = [col for col in Service.__table__.columns if col.primary_key]
        assert len(primary_keys) == 1, f"Service should have exactly one primary key, got {len(primary_keys)}"
        
        pk_column = primary_keys[0]
        assert pk_column.name == 'service_id', f"Service primary key should be 'service_id', got '{pk_column.name}'"
        
        print("✅ Service model has correct primary key: service_id")
        return True
        
    except Exception as e:
        print(f"❌ Service model test failed: {e}")
        return False

def test_tutor_service_model_definition():
    """Test TutorService model definition."""
    print("Testing TutorService model definition...")
    
    try:
        from app.models.service import TutorService
        
        # Check that TutorService has the correct primary key
        primary_keys = [col for col in TutorService.__table__.columns if col.primary_key]
        assert len(primary_keys) == 1, f"TutorService should have exactly one primary key, got {len(primary_keys)}"
        
        pk_column = primary_keys[0]
        assert pk_column.name == 'tutor_service_id', f"TutorService primary key should be 'tutor_service_id', got '{pk_column.name}'"
        
        print("✅ TutorService model has correct primary key: tutor_service_id")
        
        # Check foreign key references
        tutor_id_col = None
        service_id_col = None
        
        for col in TutorService.__table__.columns:
            if col.name == 'tutor_id':
                tutor_id_col = col
            elif col.name == 'service_id':
                service_id_col = col
        
        # Check tutor_id foreign key
        assert tutor_id_col is not None, "TutorService should have tutor_id column"
        assert len(tutor_id_col.foreign_keys) > 0, "tutor_id should have foreign key constraint"
        
        tutor_fk = list(tutor_id_col.foreign_keys)[0]
        assert str(tutor_fk.column) == 'tutors.tutor_id', f"tutor_id should reference tutors.tutor_id, got {tutor_fk.column}"
        
        print("✅ TutorService.tutor_id references tutors.tutor_id correctly")
        
        # Check service_id foreign key
        assert service_id_col is not None, "TutorService should have service_id column"
        assert len(service_id_col.foreign_keys) > 0, "service_id should have foreign key constraint"
        
        service_fk = list(service_id_col.foreign_keys)[0]
        assert str(service_fk.column) == 'services.service_id', f"service_id should reference services.service_id, got {service_fk.column}"
        
        print("✅ TutorService.service_id references services.service_id correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ TutorService model test failed: {e}")
        return False

def test_tutor_service_rate_model_updates():
    """Test TutorServiceRate model foreign key updates."""
    print("Testing TutorServiceRate model foreign key updates...")
    
    try:
        from app.models.tutor_service_rate import TutorServiceRate
        
        # Check foreign key references
        tutor_id_col = None
        service_id_col = None
        
        for col in TutorServiceRate.__table__.columns:
            if col.name == 'tutor_id':
                tutor_id_col = col
            elif col.name == 'service_id':
                service_id_col = col
        
        # Check tutor_id foreign key
        assert tutor_id_col is not None, "TutorServiceRate should have tutor_id column"
        assert len(tutor_id_col.foreign_keys) > 0, "tutor_id should have foreign key constraint"
        
        tutor_fk = list(tutor_id_col.foreign_keys)[0]
        assert str(tutor_fk.column) == 'tutors.tutor_id', f"tutor_id should reference tutors.tutor_id, got {tutor_fk.column}"
        
        print("✅ TutorServiceRate.tutor_id references tutors.tutor_id correctly")
        
        # Check service_id foreign key
        assert service_id_col is not None, "TutorServiceRate should have service_id column"
        assert len(service_id_col.foreign_keys) > 0, "service_id should have foreign key constraint"
        
        service_fk = list(service_id_col.foreign_keys)[0]
        assert str(service_fk.column) == 'services.service_id', f"service_id should reference services.service_id, got {service_fk.column}"
        
        print("✅ TutorServiceRate.service_id references services.service_id correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ TutorServiceRate model test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("=" * 70)
    print("TASK 1: SERVICE AND TUTORSERVICE MODEL UPDATES - ISOLATED TEST")
    print("=" * 70)
    
    tests = [
        test_service_model_definition,
        test_tutor_service_model_definition,
        test_tutor_service_rate_model_updates
    ]
    
    all_passed = True
    for test in tests:
        if not test():
            all_passed = False
        print()
    
    print("=" * 70)
    if all_passed:
        print("✅ TASK 1 COMPLETED SUCCESSFULLY!")
        print()
        print("Summary of changes made:")
        print("1. ✅ Service model: Changed primary key from 'id' to 'service_id'")
        print("2. ✅ TutorService model: Changed primary key from 'id' to 'tutor_service_id'")
        print("3. ✅ TutorService model: Fixed tutor_id foreign key to reference 'tutors.tutor_id'")
        print("4. ✅ TutorService model: Fixed service_id foreign key to reference 'services.service_id'")
        print("5. ✅ TutorServiceRate model: Fixed tutor_id foreign key to reference 'tutors.tutor_id'")
        print("6. ✅ TutorServiceRate model: Fixed service_id foreign key to reference 'services.service_id'")
        print()
        print("All requirements for Task 1 have been satisfied:")
        print("- Requirements 1.1, 1.2, 1.3, 1.4: Primary key standardization ✅")
        print("- Requirements 2.1, 2.2: Foreign key consistency ✅")
        print("- Requirements 3.1, 3.2: Model class updates ✅")
    else:
        print("❌ TASK 1 FAILED!")
    print("=" * 70)
    
    return all_passed

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)