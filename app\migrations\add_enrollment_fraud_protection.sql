-- Migration: Add fraud protection for enrollments
-- Date: 2024-01-06
-- Description: Adds fields and tables to prevent fake enrollments and track suspicious activity

-- Add IP tracking to enrollments
ALTER TABLE enrollments 
ADD COLUMN created_by_ip VARCHAR(45) NULL COMMENT 'IP address that created the enrollment';

ALTER TABLE enrollments 
ADD COLUMN stripe_customer_id VARCHAR(255) NULL COMMENT 'Stripe customer ID for payment verification';

-- Create table to track enrollment attempts
CREATE TABLE IF NOT EXISTS enrollment_attempts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    attempt_type ENUM('started', 'failed', 'success') NOT NULL,
    failure_reason VARCHAR(255) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_ip (ip_address),
    INDEX idx_created (created_at)
) COMMENT='Track enrollment attempts for fraud detection';

-- Create table for blocked emails/IPs
CREATE TABLE IF NOT EXISTS enrollment_blocklist (
    id INT AUTO_INCREMENT PRIMARY KEY,
    block_type ENUM('email', 'ip', 'domain') NOT NULL,
    block_value VARCHAR(255) NOT NULL,
    reason VARCHAR(255) NOT NULL,
    blocked_by INT NULL COMMENT 'User ID who blocked',
    blocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL COMMENT 'When the block expires',
    is_active BOOLEAN DEFAULT TRUE,
    UNIQUE KEY unique_block (block_type, block_value),
    INDEX idx_active (is_active, expires_at)
) COMMENT='Blocked emails, IPs, and domains';

-- Create view for enrollment fraud analysis
CREATE OR REPLACE VIEW enrollment_fraud_metrics AS
SELECT 
    DATE(created_at) as enrollment_date,
    COUNT(DISTINCT email) as unique_emails,
    COUNT(DISTINCT ip_address) as unique_ips,
    COUNT(*) as total_attempts,
    SUM(CASE WHEN attempt_type = 'failed' THEN 1 ELSE 0 END) as failed_attempts,
    SUM(CASE WHEN attempt_type = 'success' THEN 1 ELSE 0 END) as successful_attempts,
    GROUP_CONCAT(DISTINCT 
        CASE 
            WHEN COUNT(*) > 5 THEN ip_address 
            ELSE NULL 
        END
    ) as suspicious_ips
FROM enrollment_attempts
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY DATE(created_at);

-- Add some initial blocks for known fake domains
INSERT INTO enrollment_blocklist (block_type, block_value, reason) VALUES
('domain', 'tempmail.com', 'Known disposable email service'),
('domain', 'guerrillamail.com', 'Known disposable email service'),
('domain', '10minutemail.com', 'Known disposable email service'),
('domain', 'mailinator.com', 'Known disposable email service'),
('email', '<EMAIL>', 'Common test email'),
('email', '<EMAIL>', 'Common fake email'),
('email', '<EMAIL>', 'Common fake email')
ON DUPLICATE KEY UPDATE reason = VALUES(reason);