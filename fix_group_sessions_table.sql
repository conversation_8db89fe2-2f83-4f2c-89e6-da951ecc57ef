-- Fix Group Sessions Table Schema to Match SQLAlchemy Model
-- This script fixes the mismatch between the database schema and the GroupSession model

-- ========================================
-- GROUP_SESSIONS TABLE FIXES
-- ========================================

-- Add group_session_id column (rename from session_id if it exists)
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'group_sessions' 
               AND column_name = 'session_id') 
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns 
                       WHERE table_name = 'group_sessions' 
                       AND column_name = 'group_session_id') THEN
        -- Rename session_id to group_session_id
        ALTER TABLE group_sessions RENAME COLUMN session_id TO group_session_id;
        
        RAISE NOTICE 'Renamed session_id to group_session_id in group_sessions table';
    ELSIF EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'group_sessions' 
                  AND column_name = 'id') 
          AND NOT EXISTS (SELECT 1 FROM information_schema.columns 
                          WHERE table_name = 'group_sessions' 
                          AND column_name = 'group_session_id') THEN
        -- Rename id to group_session_id
        ALTER TABLE group_sessions RENAME COLUMN id TO group_session_id;
        
        RAISE NOTICE 'Renamed id to group_session_id in group_sessions table';
    ELSIF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                      WHERE table_name = 'group_sessions' 
                      AND column_name = 'group_session_id') THEN
        -- Add group_session_id column if it doesn't exist
        ALTER TABLE group_sessions ADD COLUMN group_session_id SERIAL PRIMARY KEY;
        
        RAISE NOTICE 'Added group_session_id column to group_sessions table';
    END IF;
END $$;

-- ========================================
-- GROUP_SESSION_PARTICIPANTS TABLE FIXES
-- ========================================

-- Fix group_session_participants table to match the model
DO $$
BEGIN
    -- Add participant_id column (rename from id if it exists)
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'group_session_participants' 
               AND column_name = 'id') 
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns 
                       WHERE table_name = 'group_session_participants' 
                       AND column_name = 'participant_id') THEN
        -- Rename id to participant_id
        ALTER TABLE group_session_participants RENAME COLUMN id TO participant_id;
        
        RAISE NOTICE 'Renamed id to participant_id in group_session_participants table';
    ELSIF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                      WHERE table_name = 'group_session_participants' 
                      AND column_name = 'participant_id') THEN
        -- Add participant_id column if it doesn't exist
        ALTER TABLE group_session_participants ADD COLUMN participant_id SERIAL PRIMARY KEY;
        
        RAISE NOTICE 'Added participant_id column to group_session_participants table';
    END IF;
    
    -- Add missing columns that the SQLAlchemy model expects
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'group_session_participants' 
                   AND column_name = 'is_paid') THEN
        ALTER TABLE group_session_participants ADD COLUMN is_paid BOOLEAN DEFAULT FALSE;
        RAISE NOTICE 'Added is_paid column to group_session_participants table';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'group_session_participants' 
                   AND column_name = 'reserved_until') THEN
        ALTER TABLE group_session_participants ADD COLUMN reserved_until TIMESTAMP;
        RAISE NOTICE 'Added reserved_until column to group_session_participants table';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'group_session_participants' 
                   AND column_name = 'payment_amount') THEN
        ALTER TABLE group_session_participants ADD COLUMN payment_amount DECIMAL(10,2);
        RAISE NOTICE 'Added payment_amount column to group_session_participants table';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'group_session_participants' 
                   AND column_name = 'stripe_payment_intent_id') THEN
        ALTER TABLE group_session_participants ADD COLUMN stripe_payment_intent_id VARCHAR(255);
        RAISE NOTICE 'Added stripe_payment_intent_id column to group_session_participants table';
    END IF;
    
    -- Rename columns to match the model if needed
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'group_session_participants' 
               AND column_name = 'join_date') 
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns 
                       WHERE table_name = 'group_session_participants' 
                       AND column_name = 'insert_date') THEN
        ALTER TABLE group_session_participants RENAME COLUMN join_date TO insert_date;
        RAISE NOTICE 'Renamed join_date to insert_date in group_session_participants table';
    ELSIF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                      WHERE table_name = 'group_session_participants' 
                      AND column_name = 'insert_date') THEN
        ALTER TABLE group_session_participants ADD COLUMN insert_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Added insert_date column to group_session_participants table';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'group_session_participants' 
                   AND column_name = 'modification_date') THEN
        ALTER TABLE group_session_participants ADD COLUMN modification_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Added modification_date column to group_session_participants table';
    END IF;
END $$;

-- ========================================
-- UPDATE FOREIGN KEY CONSTRAINTS
-- ========================================

-- Update foreign key constraints to reference the new column names
DO $$
BEGIN
    -- Drop old foreign key constraints if they exist
    IF EXISTS (SELECT 1 FROM information_schema.table_constraints 
               WHERE constraint_name = 'group_session_participants_group_session_id_fkey' 
               AND table_name = 'group_session_participants') THEN
        ALTER TABLE group_session_participants DROP CONSTRAINT group_session_participants_group_session_id_fkey;
        RAISE NOTICE 'Dropped old foreign key constraint';
    END IF;
    
    -- Add new foreign key constraint
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                   WHERE constraint_name = 'fk_group_session_participants_session' 
                   AND table_name = 'group_session_participants') THEN
        ALTER TABLE group_session_participants 
        ADD CONSTRAINT fk_group_session_participants_session 
        FOREIGN KEY (group_session_id) REFERENCES group_sessions(group_session_id) ON DELETE CASCADE;
        RAISE NOTICE 'Added new foreign key constraint for group_session_id';
    END IF;
EXCEPTION
    WHEN others THEN
        RAISE NOTICE 'Could not update foreign key constraints - this is usually fine';
END $$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_group_sessions_program_id ON group_sessions(program_id);
CREATE INDEX IF NOT EXISTS idx_group_sessions_tutor_id ON group_sessions(tutor_id);
CREATE INDEX IF NOT EXISTS idx_group_sessions_session_date ON group_sessions(session_date);
CREATE INDEX IF NOT EXISTS idx_group_sessions_status ON group_sessions(status);
CREATE INDEX IF NOT EXISTS idx_group_session_participants_group_session_id ON group_session_participants(group_session_id);
CREATE INDEX IF NOT EXISTS idx_group_session_participants_enrollment_id ON group_session_participants(enrollment_id);

-- ========================================
-- VERIFICATION QUERIES
-- ========================================

-- Check group_sessions table structure
SELECT 'GROUP_SESSIONS TABLE COLUMNS:' as info;
SELECT 
    column_name,
    data_type,
    character_maximum_length,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'group_sessions' 
AND column_name IN ('group_session_id', 'session_id', 'id', 'program_id', 'tutor_id')
ORDER BY column_name;

-- Check group_session_participants table structure
SELECT 'GROUP_SESSION_PARTICIPANTS TABLE COLUMNS:' as info;
SELECT 
    column_name,
    data_type,
    character_maximum_length,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'group_session_participants' 
AND column_name IN ('participant_id', 'id', 'group_session_id', 'is_paid', 'reserved_until')
ORDER BY column_name;

-- Final success message
SELECT 'GROUP SESSIONS TABLE FIX COMPLETED!' as status,
       'The group_sessions table schema has been updated to match the SQLAlchemy model.' as message,
       'You can now restart your application.' as next_step;
