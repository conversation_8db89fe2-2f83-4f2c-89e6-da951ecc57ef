# Python bytecode:
__pycache__/
*.py[cod]
*$py.class

# Virtual Environment
venv/
env/
ENV/

# Environment variables
.env
.flaskenv

# Flask instance folder
instance/

# Database files
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Testing
.coverage
htmlcov/
.pytest_cache/
.tox/
.nox/

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Stripe CLI
.stripe/

# Session files
flask_session/

# Deployment settings
Railway.json

# Temporary files
tmp/
.tmp/