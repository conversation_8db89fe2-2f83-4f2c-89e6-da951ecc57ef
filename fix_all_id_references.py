#!/usr/bin/env python3
"""
Comprehensive script to fix all ID reference issues in the codebase
This script updates all .id references to use the proper primary key names
"""

import os
import re
from pathlib import Path

def fix_manager_py():
    """Fix all ID references in manager.py"""
    file_path = 'app/views/manager.py'
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Define specific replacements for manager.py
        replacements = [
            # User model fixes
            (r'\bcurrent_user\.id\b', 'current_user.user_id'),
            (r'\buser\.id\b', 'user.user_id'),
            (r'\bUser\.id\b', 'User.user_id'),
            
            # Client model fixes
            (r'\bclient\.id\b', 'client.client_id'),
            (r'\bClient\.id\b', 'Client.client_id'),
            
            # Tutor model fixes
            (r'\btutor\.id\b', 'tutor.tutor_id'),
            (r'\bTutor\.id\b', 'Tutor.tutor_id'),
            
            # Service model fixes
            (r'\bservice\.id\b', 'service.service_id'),
            (r'\bService\.id\b', 'Service.service_id'),
            
            # Appointment model fixes
            (r'\bappointment\.id\b', 'appointment.appointment_id'),
            (r'\bAppointment\.id\b', 'Appointment.appointment_id'),
            
            # Invoice model fixes
            (r'\binvoice\.id\b', 'invoice.invoice_id'),
            (r'\bInvoice\.id\b', 'Invoice.invoice_id'),
            
            # Subscription model fixes
            (r'\bsubscription\.id\b', 'subscription.subscription_id'),
            (r'\bSubscription\.id\b', 'Subscription.subscription_id'),
            
            # SubscriptionPlan model fixes
            (r'\bplan\.id\b', 'plan.plan_id'),
            (r'\bSubscriptionPlan\.id\b', 'SubscriptionPlan.plan_id'),
            
            # Dependant model fixes
            (r'\bdependant\.id\b', 'dependant.dependant_id'),
            (r'\bDependant\.id\b', 'Dependant.dependant_id'),
            
            # Notification model fixes
            (r'\bnotification\.id\b', 'notification.notification_id'),
            (r'\bNotification\.id\b', 'Notification.notification_id'),
            
            # TimeOff model fixes
            (r'\btime_off\.id\b', 'time_off.time_off_id'),
            (r'\bTimeOff\.id\b', 'TimeOff.time_off_id'),
            
            # GroupSession model fixes
            (r'\bgroup_session\.id\b', 'group_session.group_session_id'),
            (r'\bGroupSession\.id\b', 'GroupSession.group_session_id'),
            
            # Program model fixes
            (r'\btecfee_program\.id\b', 'tecfee_program.program_id'),
            (r'\bProgram\.id\b', 'Program.program_id'),
            
            # Enrollment model fixes
            (r'\benrollment\.id\b', 'enrollment.enrollment_id'),
            (r'\bEnrollment\.id\b', 'Enrollment.enrollment_id'),
            
            # Module model fixes
            (r'\.module_id == Enrollment\.id\b', '.module_id == Enrollment.enrollment_id'),
            
            # Relationship model fixes
            (r'\brelationship\.id\b', 'relationship.relationship_id'),
            (r'\brel\.id\b', 'rel.relationship_id'),
            
            # Generic list comprehension fixes for form choices
            (r'\[\(t\.id,', '[(t.tutor_id,'),
            (r'\[\(c\.id,', '[(c.client_id,'),
            (r'\[\(s\.id,', '[(s.service_id,'),
            (r'\[\(p\.id,', '[(p.plan_id,'),
            (r'\[\(m\.id,', '[(m.module_id,'),
            
            # Join condition fixes
            (r'Client\.user_id == User\.id', 'Client.user_id == User.user_id'),
            (r'Tutor\.user_id == User\.id', 'Tutor.user_id == User.user_id'),
            (r'Appointment\.client_id == Client\.id', 'Appointment.client_id == Client.client_id'),
            (r'Appointment\.tutor_id == Tutor\.id', 'Appointment.tutor_id == Tutor.tutor_id'),
            (r'TutorService\.service_id == Service\.id', 'TutorService.service_id == Service.service_id'),
            (r'Invoice\.client_id == Client\.id', 'Invoice.client_id == Client.client_id'),
            (r'Enrollment\.client_id == Client\.id', 'Enrollment.client_id == Client.client_id'),
            (r'GroupSessionParticipant\.enrollment_id == Enrollment\.id', 'GroupSessionParticipant.enrollment_id == Enrollment.enrollment_id'),
            (r'TutorPayment\.appointment_id', 'TutorPayment.appointment_id'),
            (r'Appointment\.id == TutorPayment\.appointment_id', 'Appointment.appointment_id == TutorPayment.appointment_id'),
        ]
        
        # Apply replacements
        for pattern, replacement in replacements:
            content = re.sub(pattern, replacement, content)
        
        # Only write if content changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ Fixed {file_path}")
            return True
        else:
            print(f"- No changes needed in {file_path}")
            return False
            
    except Exception as e:
        print(f"✗ Error fixing {file_path}: {e}")
        return False

def fix_other_files():
    """Fix ID references in other key files"""
    files_to_fix = [
        'app/views/tutor.py',
        'app/views/client.py', 
        'app/views/api.py',
        'app/services/appointment_service.py',
        'app/services/invoice_service.py',
        'app/services/subscription_service.py',
    ]
    
    # Basic replacements for other files
    replacements = [
        (r'\bcurrent_user\.id\b', 'current_user.user_id'),
        (r'\buser\.id\b', 'user.user_id'),
        (r'\bclient\.id\b', 'client.client_id'),
        (r'\btutor\.id\b', 'tutor.tutor_id'),
        (r'\bservice\.id\b', 'service.service_id'),
        (r'\bappointment\.id\b', 'appointment.appointment_id'),
        (r'\binvoice\.id\b', 'invoice.invoice_id'),
        (r'\bsubscription\.id\b', 'subscription.subscription_id'),
        (r'\bplan\.id\b', 'plan.plan_id'),
    ]
    
    fixed_count = 0
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                original_content = content
                
                # Apply replacements
                for pattern, replacement in replacements:
                    content = re.sub(pattern, replacement, content)
                
                # Only write if content changed
                if content != original_content:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    print(f"✓ Fixed {file_path}")
                    fixed_count += 1
                else:
                    print(f"- No changes needed in {file_path}")
                    
            except Exception as e:
                print(f"✗ Error fixing {file_path}: {e}")
        else:
            print(f"- File not found: {file_path}")
    
    return fixed_count

def main():
    """Main function"""
    print("Comprehensive ID Reference Fix")
    print("=" * 40)
    print()
    
    # Check if we're in the right directory
    if not Path("app").exists():
        print("Error: This script must be run from the project root directory")
        print("Current directory:", os.getcwd())
        return 1
    
    total_fixed = 0
    
    # Fix manager.py (the main problematic file)
    print("Fixing manager.py...")
    if fix_manager_py():
        total_fixed += 1
    
    print("\nFixing other files...")
    total_fixed += fix_other_files()
    
    print("\n" + "=" * 40)
    print(f"Summary: Fixed {total_fixed} files")
    
    if total_fixed > 0:
        print("\n✅ ID reference fixes applied successfully!")
        print("You should now restart your application to see the changes.")
    else:
        print("\n✅ No ID reference issues found or all were already fixed.")
    
    return 0

if __name__ == "__main__":
    exit(main())
