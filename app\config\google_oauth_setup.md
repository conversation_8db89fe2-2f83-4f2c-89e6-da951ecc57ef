# Google OAuth Setup Guide for TutorAide TECFÉE

## 1. Google Cloud Console Setup

### Create Google OAuth Credentials:

1. **Go to Google Cloud Console**: https://console.cloud.google.com/
2. **Create or Select Project**: Create a new project or select existing one
3. **Enable Google+ API**:
   - Go to "APIs & Services" > "Library"
   - Search for "Google+ API" and enable it
4. **Create OAuth 2.0 Credentials**:
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "OAuth 2.0 Client IDs"
   - Choose "Web application"
   - Add authorized redirect URIs:
     - Development: `http://localhost:5000/auth/google-callback`
     - Production: `https://yourdomain.com/auth/google-callback`

## 2. Environment Variables

Add these to your `.env` file or environment configuration:

```bash
# Google OAuth Configuration
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here

# Flask Configuration (if not already set)
FLASK_SECRET_KEY=your_secret_key_here
```

## 3. Flask Configuration

Add to your Flask app configuration:

```python
# In your config.py or app configuration
import os

class Config:
    # Existing configuration...
    
    # Google OAuth
    GOOGLE_CLIENT_ID = os.environ.get('GOOGLE_CLIENT_ID')
    GOOGLE_CLIENT_SECRET = os.environ.get('GOOGLE_CLIENT_SECRET')
    
    # Ensure these are set for OAuth to work
    SECRET_KEY = os.environ.get('FLASK_SECRET_KEY') or 'dev-secret-key'
```

## 4. Install Required Dependencies

Run this command to install Google OAuth dependencies:

```bash
pip install google-auth google-auth-oauthlib google-auth-httplib2 requests PyJWT
```

Or add to your requirements.txt:
```
google-auth==2.23.4
google-auth-oauthlib==1.1.0
google-auth-httplib2==0.1.1
requests==2.31.0
PyJWT==2.8.0
```

## 5. Database Migration

Run the Google authentication migration:

```sql
-- Run this SQL script
psql -d your_database -f app/migrations/add_google_auth_fields.sql
```

## 6. Testing

### Test Google Sign-In Flow:

1. **Registration**: Go to `/tecfee/enrollment` and click "Continuer avec Google"
2. **Login**: Go to `/auth/login` and click "Se connecter avec Google"
3. **Verify**: Check that users are created with `auth_provider = 'google'`

### Verify Database:

```sql
-- Check Google users
SELECT email, auth_provider, google_id, email_verified 
FROM users 
WHERE auth_provider = 'google';
```

## 7. Security Considerations

### Production Setup:
- ✅ Use HTTPS for all OAuth redirects
- ✅ Set proper CORS policies
- ✅ Validate all OAuth tokens
- ✅ Use environment variables for secrets
- ✅ Enable Google API quotas and monitoring

### Privacy Compliance:
- ✅ Update privacy policy to mention Google OAuth
- ✅ Inform users about data collection
- ✅ Provide option to disconnect Google account

## 8. Troubleshooting

### Common Issues:

1. **"redirect_uri_mismatch"**:
   - Check that redirect URI in Google Console matches exactly
   - Include protocol (http/https) and port if needed

2. **"invalid_client"**:
   - Verify GOOGLE_CLIENT_ID and GOOGLE_CLIENT_SECRET are correct
   - Check environment variables are loaded

3. **"access_denied"**:
   - User cancelled OAuth flow
   - Check OAuth consent screen configuration

4. **Database errors**:
   - Ensure migrations are run
   - Check that google_id field allows NULL initially

## 9. Features Enabled

### For TECFÉE Users:
- ✅ One-click registration with Google
- ✅ Automatic email verification
- ✅ Streamlined profile completion
- ✅ Secure authentication without passwords

### For Existing Users:
- ✅ Link Google account to existing account
- ✅ Alternative login method
- ✅ Maintain existing functionality

## 10. Monitoring

### Track Usage:
- Monitor Google OAuth success/failure rates
- Track user preference (Google vs traditional)
- Monitor registration completion rates

### Analytics:
```sql
-- Google OAuth usage statistics
SELECT 
    DATE(insert_date) as date,
    auth_provider,
    COUNT(*) as registrations
FROM users 
WHERE insert_date >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE(insert_date), auth_provider
ORDER BY date DESC;
```
