#!/usr/bin/env python3
"""
Simple test to verify Task 2 models are correctly configured.
"""

def test_task_2_models():
    """Test Task 2 models without importing them to avoid metadata conflicts."""
    
    # Read the model files and check their content
    models_to_check = [
        ('app/models/tutor_payment.py', 'TutorPayment', 'payment_id'),
        ('app/models/tutor_availability.py', 'TutorAvailability', 'availability_id'),
        ('app/models/time_off.py', 'TimeOff', 'time_off_id')
    ]
    
    print("Task 2: Payment and Availability Models Verification")
    print("=" * 55)
    
    all_correct = True
    
    for file_path, model_name, expected_pk in models_to_check:
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            # Check for correct primary key
            pk_line = f"{expected_pk} = db.Column(db.Integer, primary_key=True)"
            if pk_line in content:
                print(f"✓ {model_name}: Uses {expected_pk} as primary key")
            else:
                print(f"✗ {model_name}: Does not use {expected_pk} as primary key")
                all_correct = False
            
            # Check foreign key references
            if model_name == 'TutorPayment':
                if "tutor_id = db.Column(db.Integer, db.ForeignKey('tutors.tutor_id')" in content:
                    print(f"  ✓ {model_name}: tutor_id references tutors.tutor_id")
                else:
                    print(f"  ✗ {model_name}: tutor_id foreign key incorrect")
                    all_correct = False
                
                if "appointment_id = db.Column(db.Integer, db.ForeignKey('appointments.appointment_id')" in content:
                    print(f"  ✓ {model_name}: appointment_id references appointments.appointment_id")
                else:
                    print(f"  ✗ {model_name}: appointment_id foreign key incorrect")
                    all_correct = False
            
            elif model_name in ['TutorAvailability', 'TimeOff']:
                if "tutor_id = db.Column(db.Integer, db.ForeignKey('tutors.tutor_id'" in content:
                    print(f"  ✓ {model_name}: tutor_id references tutors.tutor_id")
                else:
                    print(f"  ✗ {model_name}: tutor_id foreign key incorrect")
                    all_correct = False
                    
        except FileNotFoundError:
            print(f"✗ {model_name}: File {file_path} not found")
            all_correct = False
        except Exception as e:
            print(f"✗ {model_name}: Error reading file - {e}")
            all_correct = False
    
    print("\n" + "=" * 55)
    if all_correct:
        print("✓ Task 2 COMPLETED SUCCESSFULLY!")
        print("\nSummary:")
        print("- TutorPayment model: ✓ payment_id primary key, ✓ correct foreign keys")
        print("- TutorAvailability model: ✓ availability_id primary key, ✓ correct foreign keys")
        print("- TimeOff model: ✓ time_off_id primary key, ✓ correct foreign keys")
        print("\nAll models already follow the correct naming convention!")
    else:
        print("✗ Task 2 has issues that need to be addressed")
    
    return all_correct

if __name__ == '__main__':
    success = test_task_2_models()
    exit(0 if success else 1)