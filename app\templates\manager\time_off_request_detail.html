{% extends 'base.html' %}

{% block title %}Time-Off Request Detail{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1>Time-Off Request Detail</h1>
            <p class="lead">Review and respond to tutor time-off request.</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('manager.time_off_requests') }}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left"></i> Back to Requests
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Request Details</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Tutor:</strong> {{ tutor.first_name }} {{ tutor.last_name }}
                    </div>
                    <div class="mb-3">
                        <strong>Dates:</strong> {{ time_off.start_date.strftime('%Y-%m-%d') }} to {{ time_off.end_date.strftime('%Y-%m-%d') }}
                    </div>
                    <div class="mb-3">
                        <strong>Duration:</strong> {{ time_off.duration_days }} day{% if time_off.duration_days != 1 %}s{% endif %}
                    </div>
                    <div class="mb-3">
                        <strong>Status:</strong>
                        {% if time_off.is_pending %}
                            <span class="badge bg-warning">Pending</span>
                        {% elif time_off.is_approved %}
                            <span class="badge bg-success">Approved</span>
                        {% elif time_off.is_rejected %}
                            <span class="badge bg-danger">Rejected</span>
                        {% endif %}
                    </div>
                    <div class="mb-3">
                        <strong>Requested On:</strong> {{ time_off.insert_date.strftime('%Y-%m-%d %H:%M') }}
                    </div>
                    <div class="mb-3">
                        <strong>Reason:</strong>
                        <p class="mt-2">{{ time_off.reason }}</p>
                    </div>
                    {% if time_off.manager_notes %}
                        <div class="mb-3">
                            <strong>Manager Notes:</strong>
                            <p class="mt-2">{{ time_off.manager_notes }}</p>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Check for conflicts -->
            <div class="card shadow mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">Appointment Conflicts</h5>
                </div>
                <div class="card-body">
                    <!-- This would show any scheduled appointments during the time-off period -->
                    <p>This section would show any scheduled appointments during the requested time-off period.</p>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            {% if time_off.is_pending %}
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Respond to Request</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="{{ url_for('manager.view_time_off_request', id=time_off.id) }}">
                            {{ form.csrf_token }}
                            <div class="mb-3">
                                {{ form.status.label(class="form-label") }}
                                {{ form.status(class="form-select") }}
                            </div>
                            <div class="mb-3">
                                {{ form.manager_notes.label(class="form-label") }}
                                {{ form.manager_notes(class="form-control", rows=4) }}
                                <div class="form-text">Optional notes to explain your decision.</div>
                            </div>
                            <button type="submit" class="btn btn-primary">Submit Response</button>
                        </form>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
