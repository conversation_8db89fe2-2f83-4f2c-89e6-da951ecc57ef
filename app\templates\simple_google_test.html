<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Google Sign-In Buttons - TutorAide Inc.</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500&display=swap" rel="stylesheet">
    
    <style>
        /* Official Google Sign-In Button Styles */
        .google-signin-btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            background-color: #ffffff;
            border: 1px solid #dadce0;
            border-radius: 4px;
            color: #3c4043;
            font-family: 'Roboto', sans-serif;
            font-size: 14px;
            font-weight: 500;
            letter-spacing: 0.25px;
            line-height: 16px;
            padding: 12px 16px;
            text-decoration: none;
            transition: background-color 0.218s, border-color 0.218s, box-shadow 0.218s;
            min-height: 40px;
            width: 100%;
        }

        .google-signin-btn:hover {
            background-color: #f8f9fa;
            border-color: #dadce0;
            box-shadow: 0 1px 3px 0 rgba(60, 64, 67, 0.302), 0 4px 8px 3px rgba(60, 64, 67, 0.149);
            color: #3c4043;
            text-decoration: none;
        }

        .google-signin-btn:focus {
            background-color: #f8f9fa;
            border-color: #4285f4;
            box-shadow: 0 1px 3px 0 rgba(60, 64, 67, 0.302), 0 4px 8px 3px rgba(60, 64, 67, 0.149);
            color: #3c4043;
            outline: none;
            text-decoration: none;
        }

        .google-signin-btn:active {
            background-color: #f1f3f4;
            border-color: #dadce0;
            box-shadow: 0 2px 6px 2px rgba(60, 64, 67, 0.149);
            color: #3c4043;
        }

        .google-logo {
            margin-right: 12px;
            width: 18px;
            height: 18px;
        }

        .google-signin-btn-lg {
            font-size: 16px;
            font-weight: 500;
            padding: 16px 24px;
            min-height: 48px;
        }

        .google-signin-btn-lg .google-logo {
            width: 20px;
            height: 20px;
            margin-right: 16px;
        }

        .google-signin-btn-sm {
            font-size: 13px;
            padding: 8px 12px;
            min-height: 32px;
        }

        .google-signin-btn-sm .google-logo {
            width: 16px;
            height: 16px;
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <h1 class="text-center mb-5">🎯 Google Sign-In Buttons Test</h1>
                
                <!-- Success Message -->
                <div class="alert alert-success mb-4">
                    <h5>✅ Google Buttons Working!</h5>
                    <p class="mb-0">If you can see this page, the template issues are fixed and the Google Sign-In buttons are working properly.</p>
                </div>

                <!-- Standard Button -->
                <div class="card mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Standard Google Sign-In Button</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid">
                            <a href="#" class="google-signin-btn">
                                <svg class="google-logo" viewBox="0 0 24 24">
                                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                                </svg>
                                Se connecter avec Google
                            </a>
                        </div>
                        <p class="small text-muted mt-2">Used on login pages - 40px height</p>
                    </div>
                </div>

                <!-- Large Button -->
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">Large Google Sign-In Button</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid">
                            <a href="#" class="google-signin-btn google-signin-btn-lg">
                                <svg class="google-logo" viewBox="0 0 24 24">
                                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                                </svg>
                                Continuer avec Google
                            </a>
                        </div>
                        <p class="small text-muted mt-2">Used for TECFÉE enrollment - 48px height</p>
                    </div>
                </div>

                <!-- Small Button -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">Small Google Sign-In Button</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid">
                            <a href="#" class="google-signin-btn google-signin-btn-sm">
                                <svg class="google-logo" viewBox="0 0 24 24">
                                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                                </svg>
                                Google
                            </a>
                        </div>
                        <p class="small text-muted mt-2">Used in compact areas - 32px height</p>
                    </div>
                </div>

                <!-- Features -->
                <div class="card mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">✅ Features Implemented</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Design Features:</h6>
                                <ul class="small">
                                    <li>✅ Official Google logo (4-color SVG)</li>
                                    <li>✅ Roboto font (Google's official font)</li>
                                    <li>✅ Authentic colors and styling</li>
                                    <li>✅ Proper hover/focus/active states</li>
                                    <li>✅ Responsive design</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Technical Features:</h6>
                                <ul class="small">
                                    <li>✅ Multiple button sizes</li>
                                    <li>✅ Accessibility compliant</li>
                                    <li>✅ Cross-browser compatible</li>
                                    <li>✅ Mobile-friendly</li>
                                    <li>✅ Professional integration</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Instructions -->
                <div class="card mb-4">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">🚀 How to Enable in Production</h5>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li><strong>Get Google OAuth credentials</strong> from Google Cloud Console</li>
                            <li><strong>Set environment variables:</strong>
                                <pre class="bg-light p-2 mt-2"><code>export GOOGLE_CLIENT_ID="your_client_id"
export GOOGLE_CLIENT_SECRET="your_client_secret"</code></pre>
                            </li>
                            <li><strong>Restart your application</strong></li>
                            <li><strong>Visit /auth/login</strong> - Google button will appear automatically</li>
                        </ol>
                    </div>
                </div>

                <!-- Navigation -->
                <div class="text-center">
                    <a href="/auth/login" class="btn btn-primary me-2">
                        <i class="fas fa-sign-in-alt"></i> Go to Login Page
                    </a>
                    <a href="/auth/login?show_google=1" class="btn btn-success me-2">
                        <i class="fab fa-google"></i> Login with Test Mode
                    </a>
                    <a href="/auth/debug-google-config" class="btn btn-info">
                        <i class="fas fa-cog"></i> Debug Configuration
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
