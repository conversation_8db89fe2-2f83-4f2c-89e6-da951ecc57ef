#!/usr/bin/env python3
"""
Simple integration tests for audit API endpoints.
Tests the enhanced /appointment/<id>/audit and /appointment/<id>/audit/summary endpoints.
"""

import os
import sys
import unittest
from datetime import datetime
from unittest.mock import patch, Mo<PERSON>, MagicMock

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

# Set up Flask app context for testing
os.environ['FLASK_ENV'] = 'testing'

def create_mock_app():
    """Create a mock Flask app for testing."""
    from flask import Flask
    app = Flask(__name__)
    app.config['TESTING'] = True
    app.config['WTF_CSRF_ENABLED'] = False
    app.config['SECRET_KEY'] = 'test-secret-key'
    return app

class TestAuditAPIEndpoints(unittest.TestCase):
    """Test cases for audit API endpoints using mocks."""
    
    def setUp(self):
        """Set up for each test."""
        self.app = create_mock_app()
        self.client = self.app.test_client()
        
        # Mock data
        self.mock_appointment_id = 123
        self.mock_user_id = 1
        
        # Mock audit data
        self.mock_audit_entries = [
            {
                'id': 1,
                'appointment_id': 123,
                'action': 'create',
                'action_description': 'Appointment created',
                'action_icon': '➕',
                'action_color': 'green',
                'user_name': 'Test Manager',
                'user_role': 'manager',
                'user_email': '<EMAIL>',
                'timestamp_est': '2024-01-15 10:30 AM EST',
                'timestamp_est_long': 'January 15, 2024 at 10:30 AM EST',
                'changes_summary': 'Initial appointment creation',
                'changes_detail': [],
                'has_changes': False,
                'notes': 'Created via test'
            },
            {
                'id': 2,
                'appointment_id': 123,
                'action': 'update',
                'action_description': 'Appointment updated',
                'action_icon': '📝',
                'action_color': 'blue',
                'user_name': 'Test Manager',
                'user_role': 'manager',
                'user_email': '<EMAIL>',
                'timestamp_est': '2024-01-15 11:00 AM EST',
                'timestamp_est_long': 'January 15, 2024 at 11:00 AM EST',
                'changes_summary': 'Status changed to confirmed',
                'changes_detail': [
                    {
                        'field': 'status',
                        'field_display': 'Status',
                        'change_type': 'updated',
                        'old_value': 'scheduled',
                        'new_value': 'confirmed',
                        'old_value_display': 'Scheduled',
                        'new_value_display': 'Confirmed'
                    }
                ],
                'has_changes': True,
                'notes': 'Updated via test'
            }
        ]
        
        self.mock_audit_summary = {
            'has_audit_trail': True,
            'total_entries': 2,
            'last_modified': '2024-01-15 11:00 AM EST',
            'last_modified_by': 'Test Manager',
            'last_action': 'Appointment updated',
            'last_changes_summary': 'Status changed to confirmed',
            'created_at': '2024-01-15 10:30 AM EST',
            'created_by': 'Test Manager'
        }
    
    @patch('flask_login.current_user')
    @patch('app.services.audit_service.AuditService')
    @patch('app.models.appointment.Appointment')
    def test_audit_endpoint_manager_access_success(self, mock_appointment_model, mock_audit_service, mock_current_user):
        """Test that managers can successfully access audit endpoint."""
        # Setup mocks
        mock_current_user.role = 'manager'
        mock_current_user.id = self.mock_user_id
        
        # Mock appointment
        mock_appointment = Mock()
        mock_appointment.id = self.mock_appointment_id
        mock_appointment.client.first_name = 'Test'
        mock_appointment.client.last_name = 'Client'
        mock_appointment.tutor.first_name = 'Test'
        mock_appointment.tutor.last_name = 'Tutor'
        mock_appointment.status = 'scheduled'
        mock_appointment.start_time = datetime(2024, 1, 15, 10, 30)
        mock_appointment.end_time = datetime(2024, 1, 15, 11, 30)
        mock_appointment.dependant = None
        mock_appointment.tutor_service.service.name = 'Math Tutoring'
        mock_appointment.notes = 'Test appointment'
        
        mock_appointment_model.query.options.return_value.get_or_404.return_value = mock_appointment
        
        # Mock audit service
        mock_audit_service_instance = mock_audit_service.return_value
        mock_audit_service.get_appointment_audit_history.return_value = {
            'entries': self.mock_audit_entries,
            'pagination': {
                'page': 1,
                'per_page': 20,
                'total_pages': 1,
                'has_prev': False,
                'has_next': False,
                'prev_num': None,
                'next_num': None
            },
            'total': 2
        }
        
        # Import and register the API blueprint
        with self.app.app_context():
            from app.views.api import api
            self.app.register_blueprint(api)
            
            # Make request
            response = self.client.get(f'/api/appointment/{self.mock_appointment_id}/audit')
            
            # Assertions
            self.assertEqual(response.status_code, 200)
            data = response.get_json()
            
            # Check response structure
            self.assertIn('success', data)
            self.assertTrue(data['success'])
            self.assertIn('appointment', data)
            self.assertIn('audit_entries', data)
            self.assertIn('pagination', data)
            self.assertIn('meta', data)
            
            # Check meta information
            meta = data['meta']
            self.assertIn('timezone', meta)
            self.assertEqual(meta['timezone'], 'EST')
    
    @patch('flask_login.current_user')
    def test_audit_endpoint_unauthorized_access(self, mock_current_user):
        """Test that non-managers cannot access audit endpoint."""
        # Setup mocks
        mock_current_user.role = 'client'
        mock_current_user.id = self.mock_user_id
        
        # Import and register the API blueprint
        with self.app.app_context():
            from app.views.api import api
            self.app.register_blueprint(api)
            
            # Make request
            response = self.client.get(f'/api/appointment/{self.mock_appointment_id}/audit')
            
            # Assertions
            self.assertEqual(response.status_code, 403)
            data = response.get_json()
            
            self.assertIn('error', data)
            self.assertEqual(data['error'], 'Unauthorized')
            self.assertIn('message', data)
    
    @patch('flask_login.current_user')
    @patch('app.models.appointment.Appointment')
    def test_audit_endpoint_nonexistent_appointment(self, mock_appointment_model, mock_current_user):
        """Test audit endpoint with non-existent appointment."""
        # Setup mocks
        mock_current_user.role = 'manager'
        mock_current_user.id = self.mock_user_id
        
        # Mock appointment not found
        from werkzeug.exceptions import NotFound
        mock_appointment_model.query.options.return_value.get_or_404.side_effect = NotFound()
        
        # Import and register the API blueprint
        with self.app.app_context():
            from app.views.api import api
            self.app.register_blueprint(api)
            
            # Make request
            response = self.client.get('/api/appointment/99999/audit')
            
            # Assertions
            self.assertEqual(response.status_code, 404)
    
    @patch('flask_login.current_user')
    @patch('app.services.audit_service.AuditService')
    @patch('app.models.appointment.Appointment')
    def test_audit_summary_endpoint_success(self, mock_appointment_model, mock_audit_service, mock_current_user):
        """Test audit summary endpoint success."""
        # Setup mocks
        mock_current_user.role = 'manager'
        mock_current_user.id = self.mock_user_id
        
        # Mock appointment
        mock_appointment = Mock()
        mock_appointment.id = self.mock_appointment_id
        mock_appointment.client.first_name = 'Test'
        mock_appointment.client.last_name = 'Client'
        mock_appointment.tutor.first_name = 'Test'
        mock_appointment.tutor.last_name = 'Tutor'
        mock_appointment.status = 'scheduled'
        mock_appointment.dependant = None
        
        mock_appointment_model.query.options.return_value.get_or_404.return_value = mock_appointment
        
        # Mock audit service
        mock_audit_service.get_audit_summary.return_value = self.mock_audit_summary
        
        # Import and register the API blueprint
        with self.app.app_context():
            from app.views.api import api
            self.app.register_blueprint(api)
            
            # Make request
            response = self.client.get(f'/api/appointment/{self.mock_appointment_id}/audit/summary')
            
            # Assertions
            self.assertEqual(response.status_code, 200)
            data = response.get_json()
            
            # Check response structure
            self.assertIn('success', data)
            self.assertTrue(data['success'])
            self.assertIn('appointment_id', data)
            self.assertIn('appointment_info', data)
            self.assertIn('audit_summary', data)
            self.assertIn('meta', data)
            
            # Check meta information
            meta = data['meta']
            self.assertIn('timezone', meta)
            self.assertEqual(meta['timezone'], 'EST')
    
    @patch('flask_login.current_user')
    def test_audit_summary_endpoint_unauthorized(self, mock_current_user):
        """Test audit summary endpoint unauthorized access."""
        # Setup mocks
        mock_current_user.role = 'client'
        mock_current_user.id = self.mock_user_id
        
        # Import and register the API blueprint
        with self.app.app_context():
            from app.views.api import api
            self.app.register_blueprint(api)
            
            # Make request
            response = self.client.get(f'/api/appointment/{self.mock_appointment_id}/audit/summary')
            
            # Assertions
            self.assertEqual(response.status_code, 403)
            data = response.get_json()
            
            self.assertIn('error', data)
            self.assertEqual(data['error'], 'Unauthorized')
    
    @patch('flask_login.current_user')
    @patch('app.services.audit_service.AuditService')
    @patch('app.models.appointment.Appointment')
    def test_audit_endpoint_service_error_handling(self, mock_appointment_model, mock_audit_service, mock_current_user):
        """Test audit endpoint error handling when service fails."""
        # Setup mocks
        mock_current_user.role = 'manager'
        mock_current_user.id = self.mock_user_id
        
        # Mock appointment
        mock_appointment = Mock()
        mock_appointment_model.query.options.return_value.get_or_404.return_value = mock_appointment
        
        # Mock service to raise an exception
        mock_audit_service.get_appointment_audit_history.side_effect = Exception("Service error")
        
        # Import and register the API blueprint
        with self.app.app_context():
            from app.views.api import api
            self.app.register_blueprint(api)
            
            # Make request
            response = self.client.get(f'/api/appointment/{self.mock_appointment_id}/audit')
            
            # Assertions
            self.assertEqual(response.status_code, 500)
            data = response.get_json()
            
            self.assertIn('error', data)
            self.assertEqual(data['error'], 'Failed to load audit history')
            self.assertIn('message', data)
    
    @patch('flask_login.current_user')
    @patch('app.services.audit_service.AuditService')
    @patch('app.models.appointment.Appointment')
    def test_audit_summary_endpoint_service_error_handling(self, mock_audit_service, mock_appointment_model, mock_current_user):
        """Test audit summary endpoint error handling when service fails."""
        # Setup mocks
        mock_current_user.role = 'manager'
        mock_current_user.id = self.mock_user_id
        
        # Mock appointment
        mock_appointment = Mock()
        mock_appointment_model.query.options.return_value.get_or_404.return_value = mock_appointment
        
        # Mock service to raise an exception
        mock_audit_service.get_audit_summary.side_effect = Exception("Service error")
        
        # Import and register the API blueprint
        with self.app.app_context():
            from app.views.api import api
            self.app.register_blueprint(api)
            
            # Make request
            response = self.client.get(f'/api/appointment/{self.mock_appointment_id}/audit/summary')
            
            # Assertions
            self.assertEqual(response.status_code, 500)
            data = response.get_json()
            
            self.assertIn('error', data)
            self.assertEqual(data['error'], 'Failed to load audit summary')
            self.assertIn('message', data)


def run_tests():
    """Run the audit API endpoint tests."""
    print("=" * 60)
    print("AUDIT API ENDPOINTS SIMPLE TESTS")
    print("=" * 60)
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestAuditAPIEndpoints)
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    if result.wasSuccessful():
        print("✅ All audit API endpoint tests passed!")
        print(f"   Tests run: {result.testsRun}")
        print(f"   Failures: {len(result.failures)}")
        print(f"   Errors: {len(result.errors)}")
        return True
    else:
        print("❌ Some audit API endpoint tests failed!")
        print(f"   Tests run: {result.testsRun}")
        print(f"   Failures: {len(result.failures)}")
        print(f"   Errors: {len(result.errors)}")
        
        if result.failures:
            print("\nFAILURES:")
            for test, traceback in result.failures:
                print(f"- {test}: {traceback}")
        
        if result.errors:
            print("\nERRORS:")
            for test, traceback in result.errors:
                print(f"- {test}: {traceback}")
        
        return False


if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)