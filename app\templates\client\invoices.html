<!-- app/templates/client/invoices.html -->
{% extends "base.html" %}

{% block title %}{{ t('invoices.your_invoices') }} - TutorAide Inc.{% endblock %}

{% block styles %}
<style>
    /* Reset all Bootstrap tab styles */
    .nav-tabs {
        border-bottom: 1px solid #dee2e6 !important;
    }
    
    .nav-tabs .nav-item {
        margin-bottom: -1px !important;
    }
    
    .nav-tabs .nav-link {
        color: #212529 !important;
        font-weight: 500;
        border: 1px solid transparent !important;
        border-top-left-radius: 0 !important;
        border-top-right-radius: 0 !important;
        background: transparent !important;
        padding: 1rem 1.5rem !important;
        margin-bottom: 0 !important;
        position: relative !important;
        transition: all 0.3s ease;
    }
    
    .nav-tabs .nav-link::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        right: 0;
        height: 3px;
        background: transparent;
        transition: background 0.3s ease;
    }
    
    .nav-tabs .nav-link:hover {
        color: #e57373 !important;
        border-color: transparent !important;
        background: transparent !important;
    }
    
    .nav-tabs .nav-link:hover::after {
        background: transparent;
    }
    
    /* Active tab styling */
    .nav-tabs .nav-link.active,
    .nav-tabs .nav-item.show .nav-link {
        color: #e57373 !important;
        background-color: transparent !important;
        border: 1px solid transparent !important;
        border-bottom-color: transparent !important;
        font-weight: 600 !important;
    }
    
    .nav-tabs .nav-link.active::after {
        background: #e57373 !important;
    }
    
    /* Remove ALL focus states */
    .nav-tabs .nav-link:focus,
    .nav-tabs .nav-link:focus-visible,
    .nav-tabs .nav-link.active:focus,
    .nav-tabs .nav-link.active:focus-visible {
        outline: none !important;
        outline-width: 0 !important;
        box-shadow: none !important;
        border-color: transparent !important;
        background: transparent !important;
    }
    
    /* Override Bootstrap's tab-pane.active */
    .tab-content > .active {
        display: block;
    }
    
    /* Ensure no blue appears on click */
    .nav-tabs .nav-link:active,
    .nav-tabs .nav-link.active:active {
        outline: none !important;
        box-shadow: none !important;
        border-color: transparent !important;
    }
    
    .nav-tabs {
        border-bottom: 1px solid #dee2e6;
    }
    
    .card-header {
        background: #f8f9fa !important;
        padding: 0;
    }
    
    .card-header .nav-tabs {
        margin-bottom: -1px;
    }
    
    .nav-tabs .nav-item {
        margin-bottom: 0;
    }
    
    .nav-tabs .nav-link {
        padding: 1rem 1.5rem;
        margin-bottom: 0;
        border-radius: 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h2 class="mb-3">{{ t('invoices.your_invoices') }}</h2>
        <p class="text-muted">{{ t('invoices.view_and_pay') }}</p>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <ul class="nav nav-tabs card-header-tabs" id="invoicesTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="pending-tab" data-bs-toggle="tab" data-bs-target="#pending" type="button" role="tab" aria-controls="pending" aria-selected="true">
                    {{ t('invoices.pending') }}
                    {% if pending_invoices %}
                        <span class="badge bg-danger ms-1">{{ pending_invoices|length }}</span>
                    {% endif %}
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="paid-tab" data-bs-toggle="tab" data-bs-target="#paid" type="button" role="tab" aria-controls="paid" aria-selected="false">
                    {{ t('invoices.paid') }}
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="all-tab" data-bs-toggle="tab" data-bs-target="#all" type="button" role="tab" aria-controls="all" aria-selected="false">
                    {{ t('invoices.all') }}
                </button>
            </li>
        </ul>
    </div>
    <div class="card-body">
        <div class="tab-content" id="invoicesTabsContent">
            <!-- Pending Invoices Tab -->
            <div class="tab-pane fade show active" id="pending" role="tabpanel" aria-labelledby="pending-tab">
                {% if pending_invoices %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{{ t('invoices.invoice_number') }}</th>
                                    <th>{{ t('invoices.date') }}</th>
                                    <th>{{ t('invoices.due_date') }}</th>
                                    <th>{{ t('invoices.amount') }}</th>
                                    <th>{{ t('invoices.status') }}</th>
                                    <th>{{ t('invoices.actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invoice in pending_invoices %}
                                    <tr class="{{ 'table-danger' if invoice.is_overdue else '' }}">
                                        <td>{{ invoice.id }}</td>
                                        <td>{{ invoice.invoice_date.strftime('%Y-%m-%d') }}</td>
                                        <td>{{ invoice.due_date.strftime('%Y-%m-%d') }}</td>
                                        <td>${{ "%.2f"|format(invoice.total_amount) }}</td>
                                        <td>
                                            <span class="badge bg-{{ 'danger' if invoice.is_overdue else 'warning' }}">
                                                {{ t('invoices.overdue') if invoice.is_overdue else t('invoices.' + invoice.status) }}
                                            </span>
                                        </td>
                                        <td>
                                            <a href="{{ url_for('client.view_invoice', id=invoice.id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i> {{ t('invoices.view') }}
                                            </a>
                                            <a href="{{ url_for('client.pay_invoice', id=invoice.id) }}" class="btn btn-sm btn-success">
                                                <i class="fas fa-credit-card"></i> {{ t('invoices.pay') }}
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center my-4">{{ t('invoices.no_pending_invoices') }}</p>
                {% endif %}
            </div>

            <!-- Paid Invoices Tab -->
            <div class="tab-pane fade" id="paid" role="tabpanel" aria-labelledby="paid-tab">
                {% if paid_invoices %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{{ t('invoices.invoice_number') }}</th>
                                    <th>{{ t('invoices.date') }}</th>
                                    <th>{{ t('invoices.paid_date') }}</th>
                                    <th>{{ t('invoices.amount') }}</th>
                                    <th>{{ t('invoices.actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invoice in paid_invoices %}
                                    <tr>
                                        <td>{{ invoice.id }}</td>
                                        <td>{{ invoice.invoice_date.strftime('%Y-%m-%d') }}</td>
                                        <td>{{ invoice.paid_date.strftime('%Y-%m-%d') if invoice.paid_date else '-' }}</td>
                                        <td>${{ "%.2f"|format(invoice.total_amount) }}</td>
                                        <td>
                                            <a href="{{ url_for('client.view_invoice', id=invoice.id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i> {{ t('invoices.view') }}
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center my-4">{{ t('invoices.no_paid_invoices') }}</p>
                {% endif %}
            </div>

            <!-- All Invoices Tab -->
            <div class="tab-pane fade" id="all" role="tabpanel" aria-labelledby="all-tab">
                {% if all_invoices %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{{ t('invoices.invoice_number') }}</th>
                                    <th>{{ t('invoices.date') }}</th>
                                    <th>{{ t('invoices.due_date') }}</th>
                                    <th>{{ t('invoices.amount') }}</th>
                                    <th>{{ t('invoices.status') }}</th>
                                    <th>{{ t('invoices.actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invoice in all_invoices %}
                                    <tr class="{{ 'table-danger' if invoice.is_overdue else '' }}">
                                        <td>{{ invoice.id }}</td>
                                        <td>{{ invoice.invoice_date.strftime('%Y-%m-%d') }}</td>
                                        <td>{{ invoice.due_date.strftime('%Y-%m-%d') }}</td>
                                        <td>${{ "%.2f"|format(invoice.total_amount) }}</td>
                                        <td>
                                            <span class="badge bg-{{ 'success' if invoice.status == 'paid' else 'danger' if invoice.is_overdue else 'warning' }}">
                                                {{ t('invoices.overdue') if invoice.is_overdue else t('invoices.' + invoice.status) }}
                                            </span>
                                        </td>
                                        <td>
                                            <a href="{{ url_for('client.view_invoice', id=invoice.id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i> {{ t('invoices.view') }}
                                            </a>
                                            {% if invoice.status == 'pending' %}
                                                <a href="{{ url_for('client.pay_invoice', id=invoice.id) }}" class="btn btn-sm btn-success">
                                                    <i class="fas fa-credit-card"></i> {{ t('invoices.pay') }}
                                                </a>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center my-4">{{ t('invoices.no_invoices') }}</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
