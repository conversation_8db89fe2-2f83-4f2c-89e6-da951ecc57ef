# app/models/invoice_generation.py
from datetime import datetime
from app.extensions import db

class InvoiceGenerationSettings(db.Model):
    __tablename__ = 'invoice_generation_settings'
    
    setting_id = db.Column(db.Integer, primary_key=True)
    appointment_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON><PERSON>('appointments.appointment_id'), nullable=False, unique=True)
    invoice_generated = db.Column(db.<PERSON><PERSON>, default=False)
    invoice_id = db.Column(db.Integer, db.<PERSON>ey('invoices.invoice_id'), nullable=True)
    insert_date = db.Column(db.DateTime, default=datetime.utcnow)
    modification_date = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    appointment = db.relationship('Appointment', backref='invoice_generation_settings', uselist=False)
    invoice = db.relationship('Invoice', backref='generation_settings', uselist=False)
    
    def __repr__(self):
        return f'<InvoiceGenerationSettings setting_id={self.setting_id} appointment_id={self.appointment_id} invoice_generated={self.invoice_generated}>'
