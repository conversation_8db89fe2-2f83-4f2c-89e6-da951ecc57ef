#!/usr/bin/env python3

"""
Test script for Task 7: Update TutorServiceRate Model
Tests that the TutorServiceRate model foreign key references are correct.
"""

import sys
import os

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_tutor_service_rate_foreign_keys():
    """Test TutorServiceRate model foreign key references."""
    print("Testing TutorServiceRate model foreign key references...")
    
    try:
        from app.models.tutor_service_rate import TutorServiceRate
        
        # Check that the model can be imported
        print("✅ TutorServiceRate model imported successfully")
        
        # Check foreign key references
        tutor_id_col = None
        service_id_col = None
        
        for col in TutorServiceRate.__table__.columns:
            if col.name == 'tutor_id':
                tutor_id_col = col
            elif col.name == 'service_id':
                service_id_col = col
        
        # Check tutor_id foreign key
        assert tutor_id_col is not None, "TutorServiceRate should have tutor_id column"
        assert len(tutor_id_col.foreign_keys) > 0, "tutor_id should have foreign key constraint"
        
        tutor_fk = list(tutor_id_col.foreign_keys)[0]
        assert str(tutor_fk.column) == 'tutors.tutor_id', f"tutor_id should reference tutors.tutor_id, got {tutor_fk.column}"
        
        print("✅ TutorServiceRate.tutor_id references tutors.tutor_id correctly")
        
        # Check service_id foreign key
        assert service_id_col is not None, "TutorServiceRate should have service_id column"
        assert len(service_id_col.foreign_keys) > 0, "service_id should have foreign key constraint"
        
        service_fk = list(service_id_col.foreign_keys)[0]
        assert str(service_fk.column) == 'services.service_id', f"service_id should reference services.service_id, got {service_fk.column}"
        
        print("✅ TutorServiceRate.service_id references services.service_id correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ TutorServiceRate model test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tutor_service_rate_relationships():
    """Test TutorServiceRate model relationships."""
    print("\nTesting TutorServiceRate model relationships...")
    
    try:
        from app.models.tutor_service_rate import TutorServiceRate
        
        # Check relationships exist
        assert hasattr(TutorServiceRate, 'tutor'), "TutorServiceRate should have tutor relationship"
        assert hasattr(TutorServiceRate, 'service'), "TutorServiceRate should have service relationship"
        
        print("✅ TutorServiceRate has tutor and service relationships")
        
        # Check relationship configuration
        tutor_rel = TutorServiceRate.tutor
        service_rel = TutorServiceRate.service
        
        print("✅ TutorServiceRate relationships are properly configured")
        
        return True
        
    except Exception as e:
        print(f"❌ TutorServiceRate relationship test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_model_table_structure():
    """Test TutorServiceRate model table structure."""
    print("\nTesting TutorServiceRate model table structure...")
    
    try:
        from app.models.tutor_service_rate import TutorServiceRate
        
        # Check primary key
        primary_key_cols = [col.name for col in TutorServiceRate.__table__.primary_key.columns]
        assert 'tutor_service_id' in primary_key_cols, f"Expected tutor_service_id as primary key, got {primary_key_cols}"
        
        print("✅ TutorServiceRate has correct primary key: tutor_service_id")
        
        # Check required columns exist
        column_names = [col.name for col in TutorServiceRate.__table__.columns]
        required_columns = ['tutor_service_id', 'tutor_id', 'service_id', 'tutor_rate', 'client_rate']
        
        for col in required_columns:
            assert col in column_names, f"Missing required column: {col}"
        
        print("✅ TutorServiceRate has all required columns")
        
        return True
        
    except Exception as e:
        print(f"❌ TutorServiceRate table structure test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests for Task 7."""
    print("=" * 60)
    print("TASK 7: Update TutorServiceRate Model - Testing")
    print("=" * 60)
    
    tests = [
        test_tutor_service_rate_foreign_keys,
        test_tutor_service_rate_relationships,
        test_model_table_structure
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    print("\n" + "=" * 60)
    print("TASK 7 TEST SUMMARY")
    print("=" * 60)
    
    if all(results):
        print("🎉 ALL TESTS PASSED!")
        print("\nTask 7 Requirements Satisfied:")
        print("✅ Fixed TutorServiceRate model foreign key references to use correct primary key names")
        print("✅ Updated tutor_id foreign key to reference 'tutors.tutor_id'")
        print("✅ Updated service_id foreign key to reference 'services.service_id'")
        print("✅ Tested model relationships work correctly")
        return True
    else:
        print("❌ Some tests failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)