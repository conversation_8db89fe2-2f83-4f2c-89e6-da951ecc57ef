-- Fix Subscription Plans Table Schema to Match SQLAlchemy Model
-- This script fixes the mismatch between the database schema and the SubscriptionPlan model

-- ========================================
-- SUBSCRIPTION_PLANS TABLE FIXES
-- ========================================

-- Add duration_months column (rename from validity_days if it exists)
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'subscription_plans' 
               AND column_name = 'validity_days') 
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns 
                       WHERE table_name = 'subscription_plans' 
                       AND column_name = 'duration_months') THEN
        -- Add duration_months column
        ALTER TABLE subscription_plans ADD COLUMN duration_months INTEGER;
        
        -- Convert validity_days to duration_months (approximate: 30 days = 1 month)
        UPDATE subscription_plans 
        SET duration_months = CASE 
            WHEN validity_days <= 30 THEN 1
            WHEN validity_days <= 60 THEN 2
            WHEN validity_days <= 90 THEN 3
            WHEN validity_days <= 120 THEN 4
            WHEN validity_days <= 180 THEN 6
            WHEN validity_days <= 365 THEN 12
            ELSE ROUND(validity_days / 30.0)
        END;
        
        -- Make duration_months NOT NULL after setting values
        ALTER TABLE subscription_plans ALTER COLUMN duration_months SET NOT NULL;
        
        RAISE NOTICE 'Added duration_months column and migrated data from validity_days';
    ELSIF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                      WHERE table_name = 'subscription_plans' 
                      AND column_name = 'duration_months') THEN
        ALTER TABLE subscription_plans ADD COLUMN duration_months INTEGER NOT NULL DEFAULT 1;
        RAISE NOTICE 'Added duration_months column to subscription_plans table';
    END IF;
END $$;

-- Check if subscriptions table needs fixing too
DO $$
BEGIN
    -- Check if subscriptions table has hours_remaining instead of hours_used
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'subscriptions' 
               AND column_name = 'hours_remaining') 
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns 
                       WHERE table_name = 'subscriptions' 
                       AND column_name = 'hours_used') THEN
        -- Add hours_used column
        ALTER TABLE subscriptions ADD COLUMN hours_used FLOAT DEFAULT 0.0;
        
        -- Calculate hours_used from hours_remaining and max_hours
        UPDATE subscriptions 
        SET hours_used = COALESCE(
            (SELECT sp.max_hours FROM subscription_plans sp WHERE sp.plan_id = subscriptions.plan_id) 
            - subscriptions.hours_remaining, 
            0
        )
        WHERE hours_used = 0;
        
        RAISE NOTICE 'Added hours_used column and calculated from hours_remaining';
    ELSIF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                      WHERE table_name = 'subscriptions' 
                      AND column_name = 'hours_used') THEN
        ALTER TABLE subscriptions ADD COLUMN hours_used FLOAT DEFAULT 0.0;
        RAISE NOTICE 'Added hours_used column to subscriptions table';
    END IF;
    
    -- Add status column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'subscriptions' 
                   AND column_name = 'status') THEN
        ALTER TABLE subscriptions ADD COLUMN status VARCHAR(20) NOT NULL DEFAULT 'active';
        ALTER TABLE subscriptions ADD CONSTRAINT subscriptions_status_check 
        CHECK (status IN ('active', 'cancelled', 'expired'));
        RAISE NOTICE 'Added status column to subscriptions table';
    END IF;
    
    -- Add stripe_subscription_id column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'subscriptions' 
                   AND column_name = 'stripe_subscription_id') THEN
        ALTER TABLE subscriptions ADD COLUMN stripe_subscription_id VARCHAR(255);
        RAISE NOTICE 'Added stripe_subscription_id column to subscriptions table';
    END IF;
END $$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_subscription_plans_is_active ON subscription_plans(is_active);
CREATE INDEX IF NOT EXISTS idx_subscription_plans_duration_months ON subscription_plans(duration_months);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_subscriptions_client_id ON subscriptions(client_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_plan_id ON subscriptions(plan_id);

-- ========================================
-- VERIFICATION QUERIES
-- ========================================

-- Check subscription_plans table structure
SELECT 'SUBSCRIPTION_PLANS TABLE COLUMNS:' as info;
SELECT 
    column_name,
    data_type,
    character_maximum_length,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'subscription_plans' 
AND column_name IN ('duration_months', 'validity_days', 'max_hours', 'price')
ORDER BY column_name;

-- Check subscriptions table structure
SELECT 'SUBSCRIPTIONS TABLE COLUMNS:' as info;
SELECT 
    column_name,
    data_type,
    character_maximum_length,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'subscriptions' 
AND column_name IN ('hours_used', 'hours_remaining', 'status', 'stripe_subscription_id')
ORDER BY column_name;

-- Show current subscription plans data
SELECT 'CURRENT SUBSCRIPTION PLANS:' as info;
SELECT plan_id, name, price, duration_months, max_hours, is_active
FROM subscription_plans
ORDER BY plan_id;

-- Final success message
SELECT 'SUBSCRIPTION PLANS TABLE FIX COMPLETED!' as status,
       'The subscription_plans table schema has been updated to match the SQLAlchemy model.' as message,
       'You can now restart your application.' as next_step;
