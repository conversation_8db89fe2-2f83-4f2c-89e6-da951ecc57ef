# New file: app/services/tutor_payment_service.py

from app.extensions import db
from app.models.tutor_payment import TutorPayment
from app.models.appointment import Appointment
from datetime import datetime
from app.services.stripe_service import StripeService
from flask import current_app
from sqlalchemy.exc import IntegrityError

class TutorPaymentService:

    @staticmethod
    def get_pending_payments(tutor_id=None):
        """Get pending payments for a tutor or all tutors."""
        query = TutorPayment.query.filter(TutorPayment.status.in_(['pending', 'ready']))

        if tutor_id:
            query = query.filter_by(tutor_id=tutor_id)

        return query.all()

    @staticmethod
    def process_payment(payment_id, use_stripe_payout=True):
        """Process a payment with optional Stripe payout and race condition protection."""
        try:
            # Lock the payment record to prevent concurrent processing
            payment = db.session.query(TutorPayment).filter_by(id=payment_id).with_for_update().first()
            if not payment:
                return None, f"Payment {payment_id} not found"

            # Only process payments that are in pending or ready status
            if payment.status not in ['pending', 'ready']:
                return payment, f"Payment {payment_id} is not in a processable status"

            # Check if payment is already being processed
            if payment.status == 'paid':
                current_app.logger.info(f"Payment {payment_id} already processed, skipping")
                return payment, "Payment already processed"

            if use_stripe_payout:
                # Process with Stripe payout
                success, message = TutorPaymentService._process_stripe_payout(payment)
                if not success:
                    return payment, message

            # Update payment status
            payment.status = 'paid'
            payment.payment_date = datetime.now()

            # Commit the transaction
            db.session.commit()
            return payment, "Payment processed successfully"

        except IntegrityError as e:
            # Handle database constraint violations
            db.session.rollback()
            current_app.logger.error(f"Integrity error processing payment {payment_id}: {str(e)}")
            return None, f"Database constraint error: {str(e)}"
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"Error processing payment {payment_id}: {str(e)}")
            return None, f"Error processing payment: {str(e)}"

    @staticmethod
    def _process_stripe_payout(payment):
        """Process Stripe payout for a tutor payment."""
        try:
            tutor = payment.tutor

            # Get decrypted bank information
            transit_number = tutor.get_bank_transit_number()
            institution_number = tutor.get_bank_institution_number()
            account_number = tutor.get_bank_account_number()

            if not all([transit_number, institution_number, account_number]):
                return False, f"Missing bank information for tutor {tutor.full_name}"

            # Format Canadian routing number
            routing_number = StripeService.format_canadian_routing_number(
                transit_number, institution_number
            )

            # Create bank account token
            bank_token = StripeService.create_bank_account_token(
                account_number=account_number,
                routing_number=routing_number,
                account_holder_name=tutor.full_name
            )

            if not bank_token:
                return False, f"Failed to create bank account token for tutor {tutor.full_name}"

            # Create payout
            description = f"Tutoring payment for appointment on {payment.appointment.start_time.strftime('%Y-%m-%d')}"
            metadata = {
                'payment_id': payment.id,
                'tutor_id': tutor.id,
                'appointment_id': payment.appointment_id
            }

            payout = StripeService.create_payout(
                amount=payment.total_amount,
                bank_account_token=bank_token.id,
                description=description,
                metadata=metadata
            )

            if not payout:
                return False, f"Failed to create Stripe payout for tutor {tutor.full_name}"

            # Store payout ID
            payment.stripe_payout_id = payout.id

            return True, "Stripe payout created successfully"

        except Exception as e:
            current_app.logger.error(f"Stripe payout error for payment {payment.id}: {str(e)}")
            return False, f"Stripe payout error: {str(e)}"

    @staticmethod
    def process_multiple_payments(payment_ids, use_stripe_payout=True):
        """Process multiple payments with detailed results."""
        results = {
            'successful': [],
            'failed': [],
            'total_processed': 0,
            'total_amount': 0
        }

        for payment_id in payment_ids:
            try:
                payment, message = TutorPaymentService.process_payment(payment_id, use_stripe_payout)

                if payment and payment.status == 'paid':
                    results['successful'].append({
                        'payment_id': payment_id,
                        'tutor_name': payment.tutor.full_name,
                        'amount': float(payment.total_amount),
                        'stripe_payout_id': payment.stripe_payout_id,
                        'message': message
                    })
                    results['total_processed'] += 1
                    results['total_amount'] += float(payment.total_amount)
                else:
                    # Payment might be None if there was an error
                    results['failed'].append({
                        'payment_id': payment_id,
                        'tutor_name': payment.tutor.full_name if payment else 'Unknown',
                        'amount': float(payment.total_amount) if payment else 0,
                        'error': message
                    })

            except Exception as e:
                results['failed'].append({
                    'payment_id': payment_id,
                    'tutor_name': 'Unknown',
                    'amount': 0,
                    'error': str(e)
                })

        return results

    @staticmethod
    def calculate_tutor_earnings(tutor_id, start_date=None, end_date=None, include_transport=True):
        """Calculate a tutor's earnings for a given period."""
        query = TutorPayment.query.filter_by(tutor_id=tutor_id, status='paid')

        if start_date:
            query = query.filter(TutorPayment.payment_date >= start_date)

        if end_date:
            query = query.filter(TutorPayment.payment_date <= end_date)

        payments = query.all()

        # Calculate totals
        service_total = sum(float(payment.service_amount) for payment in payments)
        transport_total = sum(float(payment.transport_amount) for payment in payments)

        return {
            'service_total': service_total,
            'transport_total': transport_total,
            'grand_total': service_total + transport_total,
            'payment_count': len(payments)
        }