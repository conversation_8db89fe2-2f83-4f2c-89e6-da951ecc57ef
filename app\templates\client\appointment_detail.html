<!-- app/templates/client/appointment_detail.html -->
{% extends "base.html" %}

{% block title %}{{ t('appointments.appointment_details') }} - TutorAide Inc.{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">{{ t('appointments.appointment_details') }}</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('client.appointments') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> {{ t('appointments.back_to_list') }}
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card shadow mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">{{ t('appointments.details') }}</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>{{ t('appointments.date') }}:</strong>
                    </div>
                    <div class="col-md-8">
                        {{ appointment.start_time.strftime('%Y-%m-%d') }}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>{{ t('appointments.time') }}:</strong>
                    </div>
                    <div class="col-md-8">
                        {{ appointment.start_time.strftime('%H:%M') }} - {{ appointment.end_time.strftime('%H:%M') }}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>{{ t('appointments.duration') }}:</strong>
                    </div>
                    <div class="col-md-8">
                        {{ appointment.duration_minutes }} {{ t('appointments.minutes') }}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>{{ t('appointments.client') }}:</strong>
                    </div>
                    <div class="col-md-8">
                        {% if appointment.client %}
                            {{ appointment.client.first_name }} {{ appointment.client.last_name }}
                        {% else %}
                            -
                        {% endif %}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>{{ t('appointments.tutor') }}:</strong>
                    </div>
                    <div class="col-md-8">
                        {{ appointment.tutor.first_name }} {{ appointment.tutor.last_name }}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>{{ t('appointments.service') }}:</strong>
                    </div>
                    <div class="col-md-8">
                        {{ appointment.tutor_service.service.name }}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>{{ t('appointments.location') }}:</strong>
                    </div>
                    <div class="col-md-8">
                        {{ appointment.location or t('appointments.online') }}
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>{{ t('appointments.status') }}:</strong>
                    </div>
                    <div class="col-md-8">
                        <span class="badge bg-{{ 'success' if appointment.status == 'completed' else 'warning' if appointment.status == 'scheduled' else 'danger' }}">
                            {{ appointment.status | capitalize }}
                        </span>
                    </div>
                </div>
                {% if appointment.notes %}
                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>{{ t('appointments.notes') }}:</strong>
                    </div>
                    <div class="col-md-8">
                        {{ appointment.notes|nl2br }}
                    </div>
                </div>
                {% endif %}
                {% if appointment.is_subscription_based %}
                <div class="row mb-3">
                    <div class="col-md-4">
                        <strong>{{ t('appointments.subscription') }}:</strong>
                    </div>
                    <div class="col-md-8">
                        <span class="badge bg-info">{{ t('appointments.subscription_based') }}</span>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card shadow mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">{{ t('appointments.actions') }}</h5>
            </div>
            <div class="card-body">
                {% if appointment.status == 'scheduled' and appointment.start_time > now %}
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('client.cancel_appointment', id=appointment.id) }}" class="btn btn-danger mb-2" onclick="return confirm('{{ t('appointments.confirm_cancel') }}');">
                            <i class="fas fa-times-circle"></i> {{ t('appointments.cancel') }}
                        </a>
                    </div>
                    <p class="text-muted small mt-2">{{ t('appointments.cancel_policy') }}</p>
                {% elif appointment.status == 'completed' %}
                    <p class="text-success">
                        <i class="fas fa-check-circle"></i> {{ t('appointments.completed_message') }}
                    </p>
                {% elif appointment.status == 'cancelled' %}
                    <p class="text-danger">
                        <i class="fas fa-times-circle"></i> {{ t('appointments.cancelled_message') }}
                    </p>
                {% endif %}
            </div>
        </div>

        {% if appointment.invoice_items.count() > 0 %}
        <div class="card shadow">
            <div class="card-header bg-light">
                <h5 class="mb-0">{{ t('appointments.billing') }}</h5>
            </div>
            <div class="card-body">
                <div class="list-group">
                    {% for item in appointment.invoice_items %}
                        <a href="{{ url_for('client.view_invoice', id=item.invoice_id) }}" class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">{{ t('appointments.invoice') }} #{{ item.invoice_id }}</h6>
                                <small>${{ "%.2f"|format(item.amount) }}</small>
                            </div>
                            <p class="mb-1">{{ item.description }}</p>
                            <small class="text-muted">
                                <span class="badge bg-{{ 'success' if item.invoice.status == 'paid' else 'warning' }}">
                                    {{ t('invoices.' + item.invoice.status) }}
                                </span>
                            </small>
                        </a>
                    {% endfor %}
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
