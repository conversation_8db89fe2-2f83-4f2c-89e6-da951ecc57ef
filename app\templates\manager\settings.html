{% extends "base.html" %}

{% block title %}{{ t('navigation.settings') }} - TutorAide Inc.{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">
            <i class="fas fa-cog me-2"></i>
            {{ t('navigation.settings') }}
        </h2>
        <p class="text-muted">{{ t('manager.settings.subtitle') }}</p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('manager.dashboard') }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left me-1"></i> {{ t('navigation.dashboard') }}
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-cog me-2"></i>
                    {{ t('manager.settings.profile_information') }}
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.csrf_token }}

                    <!-- Account Information (Read-only) -->
                    <div class="mb-4">
                        <h6 class="text-muted">{{ t('manager.settings.account_information') }}</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label fw-bold">{{ t('profile.name') }}</label>
                                    <p class="form-control-static">{{ current_user.first_name }} {{ current_user.last_name }}</p>
                                    <small class="text-muted">{{ t('manager.settings.name_change_note') }}</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label class="form-label fw-bold">{{ t('profile.email') }}</label>
                                    <p class="form-control-static">{{ current_user.email }}</p>
                                    <small class="text-muted">{{ t('manager.settings.email_change_note') }}</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="mb-4">
                        <h6 class="text-muted">{{ t('manager.settings.contact_information') }}</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    {{ form.phone.label(class="form-label") }}
                                    {{ form.phone(class="form-control" + (" is-invalid" if form.phone.errors else "")) }}
                                    {% if form.phone.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.phone.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Address Information -->
                    <div class="mb-4">
                        <h6 class="text-muted">{{ t('manager.settings.address_information') }}</h6>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    {{ form.street_address.label(class="form-label") }}
                                    {{ form.street_address(class="form-control" + (" is-invalid" if form.street_address.errors else "")) }}
                                    {% if form.street_address.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.street_address.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    {{ form.city.label(class="form-label") }}
                                    {{ form.city(class="form-control" + (" is-invalid" if form.city.errors else "")) }}
                                    {% if form.city.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.city.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    {{ form.province.label(class="form-label") }}
                                    {{ form.province(class="form-control" + (" is-invalid" if form.province.errors else "")) }}
                                    {% if form.province.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.province.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    {{ form.zip_code.label(class="form-label") }}
                                    {{ form.zip_code(class="form-control" + (" is-invalid" if form.zip_code.errors else "")) }}
                                    {% if form.zip_code.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.zip_code.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    {{ form.country.label(class="form-label") }}
                                    {{ form.country(class="form-control" + (" is-invalid" if form.country.errors else "")) }}
                                    {% if form.country.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.country.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Password Change Section -->
                    <div class="mb-4">
                        <h6 class="text-muted">{{ t('manager.settings.change_password') }}</h6>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            {{ t('manager.settings.password_change_note') }}
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group mb-3">
                                    {{ form.current_password.label(class="form-label") }}
                                    {{ form.current_password(class="form-control" + (" is-invalid" if form.current_password.errors else "")) }}
                                    {% if form.current_password.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.current_password.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="text-muted">{{ t('manager.settings.current_password_help') }}</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    {{ form.password.label(class="form-label") }}
                                    {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else "")) }}
                                    {% if form.password.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.password.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                    <small class="text-muted">{{ t('manager.settings.password_help') }}</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    {{ form.confirm_password.label(class="form-label") }}
                                    {{ form.confirm_password(class="form-control" + (" is-invalid" if form.confirm_password.errors else "")) }}
                                    {% if form.confirm_password.errors %}
                                        <div class="invalid-feedback">
                                            {% for error in form.confirm_password.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('manager.dashboard') }}" class="btn btn-outline-secondary me-md-2">
                            {{ t('buttons.cancel') }}
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Clear password fields after form submission errors
    const passwordField = document.getElementById('password');
    const confirmPasswordField = document.getElementById('confirm_password');
    const currentPasswordField = document.getElementById('current_password');
    
    // Show/hide password change section
    function togglePasswordSection() {
        const passwordSection = document.querySelector('.password-section');
        const showPasswordBtn = document.getElementById('show-password-change');
        
        if (showPasswordBtn) {
            showPasswordBtn.addEventListener('click', function() {
                if (passwordSection.style.display === 'none') {
                    passwordSection.style.display = 'block';
                    this.textContent = 'Hide Password Change';
                } else {
                    passwordSection.style.display = 'none';
                    this.textContent = 'Change Password';
                    // Clear password fields when hiding
                    if (passwordField) passwordField.value = '';
                    if (confirmPasswordField) confirmPasswordField.value = '';
                    if (currentPasswordField) currentPasswordField.value = '';
                }
            });
        }
    }
    
    togglePasswordSection();
    
    // Password strength indicator (optional enhancement)
    if (passwordField) {
        passwordField.addEventListener('input', function() {
            const password = this.value;
            const strengthIndicator = document.getElementById('password-strength');
            
            if (strengthIndicator) {
                let strength = 0;
                if (password.length >= 8) strength++;
                if (/[A-Z]/.test(password)) strength++;
                if (/[a-z]/.test(password)) strength++;
                if (/[0-9]/.test(password)) strength++;
                if (/[^A-Za-z0-9]/.test(password)) strength++;
                
                const strengthText = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'][strength];
                const strengthColor = ['danger', 'warning', 'info', 'success', 'success'][strength];
                
                strengthIndicator.innerHTML = `<small class="text-${strengthColor}">Strength: ${strengthText}</small>`;
            }
        });
    }
});
</script>
{% endblock %}