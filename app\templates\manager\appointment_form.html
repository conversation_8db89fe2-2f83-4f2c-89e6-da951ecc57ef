{% extends "base.html" %}

{% block title %}{{ title }} - Tutoring Appointment System{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">
            {% if appointment %}
                {{ t('manager.appointments.form.title_edit') }}
            {% else %}
                {{ t('manager.appointments.form.title_new') }}
            {% endif %}
        </h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('manager.schedule') }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left"></i> {{ t('manager.appointments.back_to_schedule', 'Back to Schedule') }}
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card shadow">
            <div class="card-body">
                <form method="POST" id="appointment-form" {% if appointment %}action="{{ url_for('manager.edit_appointment', id=appointment.id) }}"{% else %}action="{{ url_for('manager.new_appointment') }}"{% endif %}>
                    {{ form.csrf_token }}

                    {% if appointment %}
                        <input type="hidden" id="appointment_id" value="{{ appointment.id }}">
                        <input type="hidden" id="original_tutor_service_id" value="{{ appointment.tutor_service_id }}">
                        {% if appointment.start_time %}
                            <input type="hidden" id="original_appointment_date" value="{{ appointment.start_time.strftime('%Y-%m-%d') }}">
                        {% endif %}
                        <input type="hidden" id="is_editing_recurring" value="{{ appointment.is_recurring }}">
                    {% endif %}

                    <!-- Recurring Toggle Section -->
                    {% if not appointment or not appointment.is_recurring %}
                    <div class="card mb-3" id="recurring-toggle-section">
                        <div class="card-body">
                            <div class="form-check">
                                {{ form.is_recurring(class="form-check-input", id="is_recurring") }}
                                <label class="form-check-label" for="is_recurring">
                                    <strong>{{ t('manager.appointments.form.make_recurring') }}</strong>
                                </label>
                                <div class="form-text">{{ t('manager.appointments.form.make_recurring_help') }}</div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="tutor_id" class="form-label">{{ t('manager.appointments.form.tutor') }}</label>
                            {{ form.tutor_id(class="form-control" + (" is-invalid" if form.tutor_id.errors else "")) }}
                            {% if form.tutor_id.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.tutor_id.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="col-md-6">
                            <label for="tutor_service_id" class="form-label">{{ t('manager.appointments.form.service') }}</label>
                            {{ form.tutor_service_id(class="form-control" + (" is-invalid" if form.tutor_service_id.errors else "")) }}
                            {% if form.tutor_service_id.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.tutor_service_id.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">{{ t('manager.appointments.form.select_tutor_first') }}</div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-12">
                            <label for="client_search" class="form-label">{{ t('manager.appointments.form.client_or_dependant') }}</label>
                            {% if appointment %}
                                <!-- Editing mode - show selected client/dependant as read-only -->
                                <div class="alert alert-info">
                                    <div>
                                        <strong>
                                            {% if appointment.dependant_id and appointment.dependant %}
                                                {{ appointment.dependant.first_name }} {{ appointment.dependant.last_name }}
                                            {% else %}
                                                {{ appointment.client.first_name }} {{ appointment.client.last_name }}
                                            {% endif %}
                                        </strong><br>
                                        <small class="text-muted">
                                            {% if appointment.dependant_id and appointment.dependant %}
                                                {{ appointment.dependant.email or t('manager.appointments.form.no_email') }} • {{ appointment.dependant.phone or t('manager.appointments.form.no_phone') }} • {{ t('manager.appointments.form.dependant') }}
                                            {% else %}
                                                {{ appointment.client.email or t('manager.appointments.form.no_email') }} • {{ appointment.client.phone or t('manager.appointments.form.no_phone') }} • {{ t('manager.appointments.form.client') }}
                                            {% endif %}
                                        </small><br>
                                        <small class="text-warning"><i class="fas fa-lock"></i> {{ t('manager.appointments.form.cannot_change_client') }}</small>
                                    </div>
                                </div>
                                <!-- Hidden fields to maintain the current selection -->
                                <input type="hidden" id="selected_client_id" name="client_id" value="{{ form.client_id.data or '' }}">
                                <input type="hidden" id="selected_client_type" name="client_type" value="{{ form.client_type.data or '' }}">
                                <input type="hidden" id="selected_dependant_id" name="dependant_id" value="{{ form.dependant_id.data or '' }}">
                            {% else %}
                                <!-- New appointment mode - show search functionality -->
                                <div class="position-relative">
                                    <input type="text" id="client_search" class="form-control" placeholder="{{ t('manager.appointments.form.search_placeholder') }}" autocomplete="off">
                                    <div id="client_search_results" class="dropdown-menu w-100" style="display: none; max-height: 300px; overflow-y: auto;"></div>
                                </div>

                                <!-- Hidden fields to store selected client/dependant info -->
                                <input type="hidden" id="selected_client_id" name="client_id" value="{{ form.client_id.data or '' }}">
                                <input type="hidden" id="selected_client_type" name="client_type" value="{{ form.client_type.data or '' }}">
                                <input type="hidden" id="selected_dependant_id" name="dependant_id" value="{{ form.dependant_id.data or '' }}">

                                <!-- Display selected client/dependant -->
                                <div id="selected_client_display" class="mt-2" style="display: none;">
                                    <div class="alert alert-info d-flex justify-content-between align-items-center">
                                        <div>
                                            <strong id="selected_client_name"></strong><br>
                                            <small id="selected_client_details" class="text-muted"></small>
                                        </div>
                                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearClientSelection()">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                            {% endif %}

                            {% if form.client_id.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.client_id.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Regular Appointment Date/Time Section -->
                    <div class="row mb-3" id="regular-datetime-section">
                        <div class="col-md-4">
                            <label for="start_date" class="form-label">{{ t('manager.appointments.form.date') }}</label>
                            {{ form.start_date(class="form-control" + (" is-invalid" if form.start_date.errors else ""), type="date") }}
                            {% if form.start_date.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.start_date.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-4">
                            <label for="start_time" class="form-label">{{ t('manager.appointments.form.start_time') }}</label>
                            {{ form.start_time(class="form-control" + (" is-invalid" if form.start_time.errors else "")) }}
                            {% if form.start_time.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.start_time.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-4">
                            <label for="end_time" class="form-label">{{ t('manager.appointments.form.end_time') }}</label>
                            <input id="end_time" name="end_time" type="time" class="form-control{{ ' is-invalid' if form.end_time.errors else '' }}" readonly
                                value="{{ form.end_time.data.strftime('%H:%M') if form.end_time.data and form.end_time.data.__class__.__name__ == 'time' else (form.end_time.data if form.end_time.data else '') }}">
                            {% if form.end_time.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.end_time.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">{{ t('manager.appointments.form.based_on_duration') }}</div>
                        </div>
                        <div class="col-12 mt-2">
                            <div class="form-text">
                                {{ t('manager.appointments.form.appointment_hours_note') }}
                            </div>
                        </div>
                    </div>

                    <!-- Recurring Appointment Pattern Section -->
                    <div id="recurring-pattern-section" style="display: none;">
                        <div class="card mb-3">
                            <div class="card-body">
                                <h6 class="card-title">{{ t('manager.appointments.form.recurring_pattern') }}</h6>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="recurring_start_time" class="form-label">{{ t('manager.appointments.form.start_time') }}</label>
                                        {{ form.start_time(class="form-control" + (" is-invalid" if form.start_time.errors else ""), id="recurring_start_time") }}
                                        {% if form.start_time.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.start_time.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6">
                                        <label for="frequency" class="form-label">{{ t('manager.appointments.form.frequency') }}</label>
                                        {{ form.frequency(class="form-control" + (" is-invalid" if form.frequency.errors else "")) }}
                                        {% if form.frequency.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.frequency.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="day_of_week" class="form-label">{{ t('manager.appointments.form.day_of_week') }}</label>
                                        {{ form.day_of_week(class="form-control" + (" is-invalid" if form.day_of_week.errors else "")) }}
                                        {% if form.day_of_week.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.day_of_week.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6" id="week-of-month-field" style="display: none;">
                                        <label for="week_of_month" class="form-label">{{ t('manager.appointments.form.week_of_month') }}</label>
                                        {{ form.week_of_month(class="form-control" + (" is-invalid" if form.week_of_month.errors else "")) }}
                                        {% if form.week_of_month.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.week_of_month.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                        <div class="form-text">{{ t('manager.appointments.form.required_for_monthly') }}</div>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <label for="pattern_start_date" class="form-label">{{ t('manager.appointments.form.start_date') }}</label>
                                        {{ form.pattern_start_date(class="form-control" + (" is-invalid" if form.pattern_start_date.errors else ""), type="date") }}
                                        {% if form.pattern_start_date.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.pattern_start_date.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">{{ t('manager.appointments.form.end_pattern') }}</label>
                                    {% for choice in form.end_type %}
                                        <div class="form-check">
                                            {{ choice(class="form-check-input") }}
                                            {{ choice.label(class="form-check-label") }}
                                        </div>
                                    {% endfor %}
                                    {% if form.end_type.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.end_type.errors %}
                                                {{ error }}
                                            {% endfor %}
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="row mb-3">
                                    <div class="col-md-6" id="end-date-field" style="display: none;">
                                        <label for="pattern_end_date" class="form-label">{{ t('manager.appointments.form.end_date') }}</label>
                                        {{ form.pattern_end_date(class="form-control" + (" is-invalid" if form.pattern_end_date.errors else ""), type="date") }}
                                        {% if form.pattern_end_date.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.pattern_end_date.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                    <div class="col-md-6" id="occurrences-field" style="display: none;">
                                        <label for="pattern_occurrences" class="form-label">{{ t('manager.appointments.form.number_of_occurrences') }}</label>
                                        {{ form.pattern_occurrences(class="form-control" + (" is-invalid" if form.pattern_occurrences.errors else ""), type="number", min="1", max="100") }}
                                        {% if form.pattern_occurrences.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.pattern_occurrences.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="d-flex align-items-center mb-2">
                            <button id="check-availability" type="button" class="btn btn-outline-primary me-3">
                                <i class="fas fa-calendar-check"></i> {{ t('manager.appointments.form.check_availability') }}
                            </button>
                            <span id="availability-indicator"></span>
                        </div>
                    </div>

                    <!-- Subscription Section -->
                    {% if hasattr(form, 'is_subscription_based') %}
                    <div class="card mb-3">
                        <div class="card-body">
                            <div class="mb-3 form-check">
                                {{ form.is_subscription_based(class="form-check-input") }}
                                <label class="form-check-label" for="is_subscription_based">
                                    {{ t('manager.appointments.form.use_subscription') }}
                                </label>
                            </div>

                            <div id="subscription-fields" class="mb-3">
                                <label for="subscription_id" class="form-label">{{ t('manager.appointments.form.select_subscription') }}</label>
                                {{ form.subscription_id(class="form-control" + (" is-invalid" if form.subscription_id.errors else "")) }}
                                {% if form.subscription_id.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.subscription_id.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">{{ t('manager.appointments.form.select_client_first') }}</div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Transport Fee Section -->
                    <div class="card mb-3" id="transport-fee-section">
                        <div class="card-body">
                            <h5 class="card-title">{{ t('manager.appointments.form.transport_fee') }}</h5>

                            <div class="mb-3 form-check">
                                {{ form.apply_transport_fee(class="form-check-input") }}
                                <label class="form-check-label" for="apply_transport_fee">
                                    {{ t('manager.appointments.form.apply_transport_fee') }}
                                </label>
                            </div>

                            <div id="fee-amount-field" class="mb-3">
                                <label for="transport_fee" class="form-label">{{ t('manager.appointments.form.transport_fee_amount') }}</label>
                                {{ form.transport_fee(class="form-control" + (" is-invalid" if form.transport_fee.errors else ""), type="number", step="0.01", min="0") }}
                                {% if form.transport_fee.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.transport_fee.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">{{ t('manager.appointments.form.transport_fee_auto') }}</div>
                            </div>
                        </div>
                    </div>

                    <!-- Status field (only for regular appointments) -->
                    <div class="mb-3" id="status-section">
                        <label for="status" class="form-label">{{ t('manager.appointments.form.status') }}</label>
                        {{ form.status(class="form-control" + (" is-invalid" if form.status.errors else "")) }}
                        {% if form.status.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.status.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">{{ t('manager.appointments.form.notes') }}</label>
                        {{ form.notes(class="form-control", rows=3) }}
                        <div class="form-text">{{ t('manager.appointments.form.optional_notes') }}</div>
                    </div>

                    <div class="d-grid gap-2">
                        {{ form.submit(class="btn btn-primary") }}
                        <a href="{{ url_for('manager.schedule') }}" class="btn btn-outline-secondary">{{ t('manager.appointments.form.cancel') }}</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Client/Dependant search functionality
    let searchTimeout;
    const clientSearchInput = document.getElementById('client_search');
    const clientSearchResults = document.getElementById('client_search_results');
    const selectedClientId = document.getElementById('selected_client_id');
    const selectedClientType = document.getElementById('selected_client_type');
    const selectedClientDisplay = document.getElementById('selected_client_display');
    const selectedClientName = document.getElementById('selected_client_name');
    const selectedClientDetails = document.getElementById('selected_client_details');

    function searchClientsAndDependants(query) {
        if (query.length < 2) {
            clientSearchResults.style.display = 'none';
            return;
        }

        fetch(`/api/clients-and-dependants/search?q=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                displaySearchResults(data);
            })
            .catch(error => {
                console.error('Error searching clients and dependants:', error);
                clientSearchResults.innerHTML = '<div class="dropdown-item text-danger">Error loading results</div>';
                clientSearchResults.style.display = 'block';
            });
    }

    function displaySearchResults(results) {
        if (results.length === 0) {
            clientSearchResults.innerHTML = '<div class="dropdown-item text-muted">No results found</div>';
            clientSearchResults.style.display = 'block';
            return;
        }

        let html = '';
        results.forEach(result => {
            const typeLabel = result.type === 'client' ? 'Client' : 'Dependant';
            const typeBadge = result.type === 'client' ? 'bg-primary' : 'bg-success';

            html += `
                <button type="button" class="dropdown-item" onclick="selectClientOrDependant('${result.id}', '${result.name}', '${result.email}', '${result.phone}', '${result.type}')">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>${result.name}</strong><br>
                            <small class="text-muted">${result.email} • ${result.phone}</small>
                        </div>
                        <span class="badge ${typeBadge}">${typeLabel}</span>
                    </div>
                </button>
            `;
        });

        clientSearchResults.innerHTML = html;
        clientSearchResults.style.display = 'block';
    }

    function selectClientOrDependant(id, name, email, phone, type) {
        selectedClientType.value = type;
        selectedClientName.textContent = name;
        selectedClientDetails.textContent = `${email} • ${phone} • ${type === 'client' ? 'Client' : 'Dependant'}`;

        if (type === 'client') {
            // For clients, use the client ID with 'c_' prefix
            selectedClientId.value = `c_${id}`;
            document.getElementById('selected_dependant_id').value = '';
        } else {
            // For dependants, use the dependant ID with 'd_' prefix
            // The backend will handle finding the appropriate parent client
            selectedClientId.value = `d_${id}`;
            document.getElementById('selected_dependant_id').value = id;
        }

        clientSearchInput.value = name;
        clientSearchResults.style.display = 'none';
        selectedClientDisplay.style.display = 'block';

        // Trigger change event for subscription updates
        selectedClientId.dispatchEvent(new Event('change'));
    }

    function clearClientSelection() {
        selectedClientId.value = '';
        selectedClientType.value = '';
        document.getElementById('selected_dependant_id').value = '';
        clientSearchInput.value = '';
        selectedClientDisplay.style.display = 'none';
        clientSearchResults.style.display = 'none';

        // Trigger change event for subscription updates
        selectedClientId.dispatchEvent(new Event('change'));
    }

    // Event listeners for client search (only for new appointments)
    if (clientSearchInput) {
        clientSearchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();

            if (query.length < 2) {
                clientSearchResults.style.display = 'none';
                return;
            }

            searchTimeout = setTimeout(() => {
                searchClientsAndDependants(query);
            }, 300);
        });

        clientSearchInput.addEventListener('focus', function() {
            if (this.value.length >= 2) {
                searchClientsAndDependants(this.value);
            }
        });

        // Hide results when clicking outside
        document.addEventListener('click', function(event) {
            if (!clientSearchInput.contains(event.target) && !clientSearchResults.contains(event.target)) {
                clientSearchResults.style.display = 'none';
            }
        });
    }

    // Initialize display if client is already selected (for edit forms)
    if (selectedClientId.value) {
        // For edit forms, we need to get the client/dependant info and display it
        const clientIdValue = selectedClientId.value;
        let actualId, entityType;

        // Parse the prefixed ID or handle legacy format
        if (typeof clientIdValue === 'string' && clientIdValue.startsWith('c_')) {
            actualId = clientIdValue.substring(2);
            entityType = 'client';
        } else if (typeof clientIdValue === 'string' && clientIdValue.startsWith('d_')) {
            actualId = clientIdValue.substring(2);
            entityType = 'dependant';
        } else {
            // Legacy format - assume it's a client ID
            actualId = clientIdValue;
            entityType = 'client';
        }

        if (actualId) {
            if (entityType === 'client') {
                // Try to get client info
                fetch(`/api/clients/${actualId}`)
                    .then(response => {
                        if (response.ok) {
                            return response.json();
                        }
                        throw new Error('Not a client');
                    })
                    .then(client => {
                        selectedClientType.value = 'client';
                        selectedClientName.textContent = `${client.first_name} ${client.last_name}`;
                        selectedClientDetails.textContent = `${client.email || 'No email'} • ${client.phone || 'No phone'} • Client`;
                        clientSearchInput.value = `${client.first_name} ${client.last_name}`;
                        selectedClientDisplay.style.display = 'block';
                    })
                .catch(() => {
                    console.error('Error loading client:', actualId);
                });
            } else if (entityType === 'dependant') {
                // Try to get dependant info
                fetch(`/api/dependants/${actualId}`)
                    .then(response => {
                        if (response.ok) {
                            return response.json();
                        }
                        throw new Error('Not a dependant');
                    })
                    .then(dependant => {
                        selectedClientType.value = 'dependant';
                        selectedClientName.textContent = `${dependant.first_name} ${dependant.last_name}`;
                        selectedClientDetails.textContent = `${dependant.email || 'No email'} • ${dependant.phone || 'No phone'} • Dependant`;
                        clientSearchInput.value = `${dependant.first_name} ${dependant.last_name}`;
                        selectedClientDisplay.style.display = 'block';
                    })
                    .catch(() => {
                        console.error('Error loading dependant:', actualId);
                    });
            }
        }
    }

    // Recurring appointment toggle functionality
    function toggleRecurringFields() {
        const isRecurringCheckbox = document.getElementById('is_recurring');
        const regularSection = document.getElementById('regular-datetime-section');
        const recurringSection = document.getElementById('recurring-pattern-section');
        const statusSection = document.getElementById('status-section');
        const availabilityButton = document.getElementById('check-availability');

        if (isRecurringCheckbox && isRecurringCheckbox.checked) {
            // Show recurring fields, hide regular fields
            if (regularSection) regularSection.style.display = 'none';
            if (recurringSection) recurringSection.style.display = 'block';
            if (statusSection) statusSection.style.display = 'none';
            if (availabilityButton) availabilityButton.style.display = 'none';
        } else {
            // Show regular fields, hide recurring fields
            if (regularSection) regularSection.style.display = 'block';
            if (recurringSection) recurringSection.style.display = 'none';
            if (statusSection) statusSection.style.display = 'block';
            if (availabilityButton) availabilityButton.style.display = 'block';
        }
    }

    // Handle frequency change for monthly options
    function handleFrequencyChange() {
        const frequencySelect = document.getElementById('frequency');
        const weekOfMonthField = document.getElementById('week-of-month-field');

        if (frequencySelect && weekOfMonthField) {
            if (frequencySelect.value === 'monthly') {
                weekOfMonthField.style.display = 'block';
            } else {
                weekOfMonthField.style.display = 'none';
            }
        }
    }

    // Handle end type change for recurring appointments
    function handleEndTypeChange() {
        const endTypeRadios = document.querySelectorAll('input[name="end_type"]');
        const endDateField = document.getElementById('end-date-field');
        const occurrencesField = document.getElementById('occurrences-field');

        endTypeRadios.forEach(radio => {
            if (radio.checked) {
                if (radio.value === 'date') {
                    if (endDateField) endDateField.style.display = 'block';
                    if (occurrencesField) occurrencesField.style.display = 'none';
                } else if (radio.value === 'occurrences') {
                    if (endDateField) endDateField.style.display = 'none';
                    if (occurrencesField) occurrencesField.style.display = 'block';
                } else {
                    if (endDateField) endDateField.style.display = 'none';
                    if (occurrencesField) occurrencesField.style.display = 'none';
                }
            }
        });
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Initialize recurring toggle functionality
        const isRecurringCheckbox = document.getElementById('is_recurring');
        if (isRecurringCheckbox) {
            isRecurringCheckbox.addEventListener('change', toggleRecurringFields);
            // Initialize on page load
            toggleRecurringFields();
        }

        // If editing a non-recurring appointment, ensure status section is visible
        const isEditingRecurring = document.getElementById('is_editing_recurring');
        if (isEditingRecurring && isEditingRecurring.value === 'False') {
            const statusSection = document.getElementById('status-section');
            if (statusSection) {
                statusSection.style.display = 'block';
            }
        }

        // Initialize frequency change handler
        const frequencySelect = document.getElementById('frequency');
        if (frequencySelect) {
            frequencySelect.addEventListener('change', handleFrequencyChange);
            // Initialize on page load
            handleFrequencyChange();
        }

        // Initialize end type change handlers
        const endTypeRadios = document.querySelectorAll('input[name="end_type"]');
        endTypeRadios.forEach(radio => {
            radio.addEventListener('change', handleEndTypeChange);
        });
        // Initialize on page load
        handleEndTypeChange();

        // Set default date and time for new appointments
        const dateField = document.getElementById('start_date');
        const timeField = document.getElementById('start_time');
        const originalDateField = document.getElementById('original_appointment_date');

        // Set default date to today if not editing an existing appointment
        if (dateField && !dateField.value && !originalDateField) {
            const today = new Date();
            const year = today.getFullYear();
            const month = String(today.getMonth() + 1).padStart(2, '0');
            const day = String(today.getDate()).padStart(2, '0');
            dateField.value = `${year}-${month}-${day}`;
        }

        // Set default time to nearest future 30-minute increment if not editing
        if (timeField && !timeField.value && !originalDateField) {
            const now = new Date();
            const currentHour = now.getHours();
            const currentMinute = now.getMinutes();

            // Calculate next 30-minute increment
            let nextHour = currentHour;
            let nextMinute = currentMinute <= 30 ? 30 : 0;

            if (currentMinute > 30) {
                nextHour += 1;
            }

            // Ensure we're within business hours (8 AM - 9 PM)
            if (nextHour < 8) {
                nextHour = 8;
                nextMinute = 0;
            } else if (nextHour >= 21) {
                // If it's after 9 PM, set to 8 AM next day
                nextHour = 8;
                nextMinute = 0;
                // Also advance the date by one day
                if (dateField && !originalDateField) {
                    const tomorrow = new Date(today);
                    tomorrow.setDate(tomorrow.getDate() + 1);
                    const year = tomorrow.getFullYear();
                    const month = String(tomorrow.getMonth() + 1).padStart(2, '0');
                    const day = String(tomorrow.getDate()).padStart(2, '0');
                    dateField.value = `${year}-${month}-${day}`;
                }
            }

            const timeValue = `${String(nextHour).padStart(2, '0')}:${String(nextMinute).padStart(2, '0')}`;
            timeField.value = timeValue;

            // Trigger change event to update end time
            timeField.dispatchEvent(new Event('change'));
        }

        // Set the date field from the original appointment date if editing
        if (originalDateField && dateField && !dateField.value) {
            dateField.value = originalDateField.value;
        }

        // Initialize dependent dropdowns - tutor -> services
        const tutorSelect = document.getElementById('tutor_id');
        const serviceSelect = document.getElementById('tutor_service_id');

        // Store the current service ID if we're editing an appointment
        // First check if we have the original service ID in a hidden field
        const originalServiceIdField = document.getElementById('original_tutor_service_id');
        const currentServiceId = originalServiceIdField ? originalServiceIdField.value : serviceSelect.value;

        if (tutorSelect && serviceSelect) {
            tutorSelect.addEventListener('change', function() {
                const tutorId = this.value;

                if (tutorId) {
                    // Clear current options
                    serviceSelect.innerHTML = '<option value="">Loading services...</option>';
                    serviceSelect.disabled = true;

                    // Fetch services for this tutor
                    fetch(`/api/tutor-services/${tutorId}`)
                        .then(response => response.json())
                        .then(data => {
                            serviceSelect.innerHTML = '<option value="">Select a service</option>';

                            data.forEach(function(service) {
                                const option = document.createElement('option');
                                option.value = service.id;
                                option.textContent = `${service.name} ($${service.client_rate.toFixed(2)}/hr) (${service.duration_minutes} minutes)`;

                                // Store transport fee data in the option
                                option.dataset.transportFee = service.transport_fee || 0;

                                // If this is the previously selected service, select it
                                if (service.id == currentServiceId) {
                                    option.selected = true;
                                }

                                serviceSelect.appendChild(option);
                            });

                            serviceSelect.disabled = false;

                            // If we have a current service ID but it wasn't found in the options,
                            // we need to trigger the change event to update the end time
                            if (currentServiceId && !serviceSelect.value) {
                                console.warn("Previously selected service not found in tutor's services");
                            } else if (serviceSelect.value) {
                                // Trigger change event to update end time if a service is selected
                                serviceSelect.dispatchEvent(new Event('change'));

                                // Also trigger the start time change to ensure end time is calculated
                                const startTimeInput = document.getElementById('start_time');
                                if (startTimeInput && startTimeInput.value) {
                                    startTimeInput.dispatchEvent(new Event('change'));
                                }
                            }
                        })
                        .catch(error => {
                            console.error('Error fetching services:', error);
                            serviceSelect.innerHTML = '<option value="">Error loading services</option>';
                            serviceSelect.disabled = true;
                        });
                } else {
                    serviceSelect.innerHTML = '<option value="">Select a tutor first</option>';
                    serviceSelect.disabled = true;
                }
            });

            // Trigger change event if tutor is already selected (like on edit forms)
            if (tutorSelect.value) {
                tutorSelect.dispatchEvent(new Event('change'));
            }
        }
    });

    document.addEventListener('DOMContentLoaded', function() {
        // Get form elements
        const tutorServiceSelect = document.getElementById('tutor_service_id');
        const startDateInput = document.getElementById('start_date');
        const startTimeInput = document.getElementById('start_time');
        const endTimeInput = document.getElementById('end_time');

        // Function to update end time based on start time and service duration
        function updateEndTime() {
            // Only proceed if we have a service selected and a start time
            if (tutorServiceSelect.value && startTimeInput.value) {
                // Get the selected service option text
                const selectedOption = tutorServiceSelect.options[tutorServiceSelect.selectedIndex];
                const serviceText = selectedOption.text;

                // Extract duration from the service text (assuming format like "Service Name (60 minutes)")
                const durationMatch = serviceText.match(/\((\d+) minutes\)/);
                if (durationMatch && durationMatch[1]) {
                    const durationMinutes = parseInt(durationMatch[1]);

                    // Parse the start time
                    const [startHours, startMinutes] = startTimeInput.value.split(':').map(Number);

                    // Calculate end time
                    let endMinutes = startMinutes + durationMinutes;
                    let endHours = startHours + Math.floor(endMinutes / 60);
                    endMinutes = endMinutes % 60;

                    // Check if end time is after 9 PM (21:00)
                    if (endHours > 21 || (endHours === 21 && endMinutes > 0)) {
                        alert('Warning: The appointment would end after 9:00 PM. Please select an earlier start time.');
                        // Don't update the end time, let the user change the start time
                        return;
                    }

                    // Format end time for input (pad with leading zeros if needed)
                    const formattedEndHours = endHours.toString().padStart(2, '0');
                    const formattedEndMinutes = endMinutes.toString().padStart(2, '0');

                    // Set the end time input value
                    endTimeInput.value = `${formattedEndHours}:${formattedEndMinutes}`;
                }
            }
        }

        // Add event listeners to trigger the update
        if (tutorServiceSelect && startTimeInput && endTimeInput) {
            tutorServiceSelect.addEventListener('change', updateEndTime);
            startTimeInput.addEventListener('change', updateEndTime);

            // Also trigger when the form loads if values are already set
            if (tutorServiceSelect.value && startTimeInput.value) {
                updateEndTime();
            }
        }

        // Add validation to prevent selecting start times that would result in end times after 9 PM
        if (tutorServiceSelect && startTimeInput) {
            tutorServiceSelect.addEventListener('change', function() {
                // When service changes, we need to validate the current start time
                if (startTimeInput.value) {
                    updateEndTime();
                }
            });
        }
    });

    document.addEventListener('DOMContentLoaded', function() {
        // Get form elements
        const tutorSelect = document.getElementById('tutor_id');
        const clientSelect = document.getElementById('selected_client_id'); // Updated to use hidden field
        const transportFeeInput = document.getElementById('transport_fee');
        const applyTransportFeeCheckbox = document.getElementById('apply_transport_fee');
        const isSubscriptionBasedCheckbox = document.getElementById('is_subscription_based');
        const transportFeeSection = document.getElementById('transport-fee-section');

        // Function to update transport fee based on selected service
        function updateTransportFee() {
            // Only proceed if we have both tutor and client/dependant selected
            if (tutorSelect.value && clientSelect.value) {
                // If it's subscription-based, hide transport fee section
                if (isSubscriptionBasedCheckbox && isSubscriptionBasedCheckbox.checked) {
                    transportFeeSection.style.display = 'none';
                    transportFeeInput.value = '0.00';
                    if (applyTransportFeeCheckbox) {
                        applyTransportFeeCheckbox.checked = false;
                    }
                    return;
                } else {
                    transportFeeSection.style.display = 'block';
                }

                // Get transport fee from the selected service
                const tutorServiceSelect = document.getElementById('tutor_service_id');
                if (tutorServiceSelect && tutorServiceSelect.value) {
                    const selectedOption = tutorServiceSelect.options[tutorServiceSelect.selectedIndex];
                    if (selectedOption && selectedOption.dataset.transportFee) {
                        const transportFee = parseFloat(selectedOption.dataset.transportFee);
                        transportFeeInput.value = transportFee.toFixed(2);

                        // Automatically check the apply fee checkbox if a fee exists
                        if (applyTransportFeeCheckbox && transportFee > 0) {
                            applyTransportFeeCheckbox.checked = true;
                        } else if (applyTransportFeeCheckbox && transportFee === 0) {
                            applyTransportFeeCheckbox.checked = false;
                        }

                        // Show/hide the fee amount field based on checkbox
                        toggleTransportFeeInput();
                        return;
                    }
                }

                // Fallback to the old method if service doesn't have transport fee data
                // Parse the client ID to get the actual ID
                let actualClientId = clientSelect.value;
                if (typeof actualClientId === 'string' && actualClientId.startsWith('c_')) {
                    actualClientId = actualClientId.substring(2);
                } else if (typeof actualClientId === 'string' && actualClientId.startsWith('d_')) {
                    actualClientId = actualClientId.substring(2);
                }

                fetch(`/api/transport-fee/${tutorSelect.value}/${actualClientId}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data && data.fee_amount !== undefined) {
                            transportFeeInput.value = data.fee_amount.toFixed(2);
                            // Automatically check the apply fee checkbox if a fee exists
                            if (applyTransportFeeCheckbox && parseFloat(data.fee_amount) > 0) {
                                applyTransportFeeCheckbox.checked = true;
                            }
                        } else {
                            transportFeeInput.value = '0.00';
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching transport fee:', error);
                        transportFeeInput.value = '0.00';
                    });
            }
        }

        // Function to toggle transport fee input based on checkbox
        function toggleTransportFeeInput() {
            if (applyTransportFeeCheckbox) {
                document.getElementById('fee-amount-field').style.display =
                    applyTransportFeeCheckbox.checked ? 'block' : 'none';

                if (!applyTransportFeeCheckbox.checked) {
                    transportFeeInput.value = '0.00';
                } else {
                    // Re-fetch the fee
                    updateTransportFee();
                }
            }
        }

        // Add event listeners
        if (tutorSelect && clientSelect && transportFeeInput) {
            tutorSelect.addEventListener('change', updateTransportFee);
            clientSelect.addEventListener('change', updateTransportFee);

            // Add listener for subscription checkbox
            if (isSubscriptionBasedCheckbox) {
                isSubscriptionBasedCheckbox.addEventListener('change', function() {
                    updateTransportFee();
                });
            }

            // Add listener for apply transport fee checkbox
            if (applyTransportFeeCheckbox) {
                applyTransportFeeCheckbox.addEventListener('change', toggleTransportFeeInput);
                // Initial setup
                toggleTransportFeeInput();
            }

            // Trigger update when form loads if values are already set
            if (tutorSelect.value && clientSelect.value) {
                updateTransportFee();
            }
        }

        // Subscription interactions
        if (isSubscriptionBasedCheckbox) {
            const subscriptionFields = document.getElementById('subscription-fields');

            function toggleSubscriptionFields() {
                subscriptionFields.style.display =
                    isSubscriptionBasedCheckbox.checked ? 'block' : 'none';

                // Update transport fee section visibility
                updateTransportFee();
            }

            isSubscriptionBasedCheckbox.addEventListener('change', toggleSubscriptionFields);

            // Initial setup
            toggleSubscriptionFields();
        }
    });

    document.addEventListener('DOMContentLoaded', function() {
        // Get form elements
        const clientSelect = document.getElementById('selected_client_id'); // Updated to use hidden field
        const subscriptionSelect = document.getElementById('subscription_id');
        const isSubscriptionBasedCheckbox = document.getElementById('is_subscription_based');

        // Function to update subscription options based on client/dependant selection
        function updateSubscriptions() {
            const clientIdValue = clientSelect.value;
            const clientType = document.getElementById('selected_client_type').value;

            // Parse the prefixed ID to get the actual ID
            let actualId;
            if (typeof clientIdValue === 'string' && clientIdValue.startsWith('c_')) {
                actualId = clientIdValue.substring(2);
            } else if (typeof clientIdValue === 'string' && clientIdValue.startsWith('d_')) {
                actualId = clientIdValue.substring(2);
            } else {
                // Legacy format
                actualId = clientIdValue;
            }

            // Both clients and dependants can use subscriptions now
            if (actualId && (clientType === 'client' || clientType === 'dependant') && subscriptionSelect) {
                // Clear current options
                subscriptionSelect.innerHTML = '<option value="0">Loading subscriptions...</option>';
                subscriptionSelect.disabled = true;

                // Determine the API endpoint based on type
                const apiEndpoint = clientType === 'client'
                    ? `/api/client/${actualId}/subscriptions`
                    : `/api/dependant/${actualId}/subscriptions`;

                // Fetch subscriptions
                fetch(apiEndpoint)
                    .then(response => response.json())
                    .then(data => {
                        // Clear and add default option
                        subscriptionSelect.innerHTML = '<option value="0">No Subscription</option>';

                        // Add subscription options
                        if (data && data.length > 0) {
                            data.forEach(function(subscription) {
                                const option = document.createElement('option');
                                option.value = subscription.id;

                                // Show owner name for dependant subscriptions
                                const ownerInfo = clientType === 'dependant' ? ` (${subscription.owner_name}'s subscription)` : '';
                                option.textContent = `${subscription.plan_name} (${subscription.hours_remaining} hours remaining)${ownerInfo}`;
                                subscriptionSelect.appendChild(option);
                            });

                            // Enable the subscription checkbox if there are subscriptions
                            if (isSubscriptionBasedCheckbox) {
                                isSubscriptionBasedCheckbox.disabled = false;
                            }
                        } else {
                            // If no subscriptions, disable the checkbox
                            if (isSubscriptionBasedCheckbox) {
                                isSubscriptionBasedCheckbox.checked = false;
                                isSubscriptionBasedCheckbox.disabled = true;
                            }

                            // Show appropriate message
                            if (clientType === 'dependant') {
                                subscriptionSelect.innerHTML = '<option value="0">No parent subscriptions available</option>';
                            }
                        }

                        subscriptionSelect.disabled = false;
                    })
                    .catch(error => {
                        console.error('Error fetching subscriptions:', error);
                        console.error('Client ID:', clientId);
                        console.error('Client Type:', clientType);
                        subscriptionSelect.innerHTML = '<option value="0">Error loading subscriptions</option>';
                        subscriptionSelect.disabled = true;
                    });
            } else if (subscriptionSelect) {
                // If no client/dependant selected
                subscriptionSelect.innerHTML = '<option value="0">Select a client or dependant first</option>';
                subscriptionSelect.disabled = true;

                // Disable the subscription checkbox
                if (isSubscriptionBasedCheckbox) {
                    isSubscriptionBasedCheckbox.checked = false;
                    isSubscriptionBasedCheckbox.disabled = true;
                }
            }
        }

        // Add event listener to client select
        if (clientSelect && subscriptionSelect) {
            clientSelect.addEventListener('change', updateSubscriptions);

            // Trigger change event if client is already selected (like on edit forms)
            if (clientSelect.value) {
                updateSubscriptions();
            }
        }
    });
</script>
{% endblock %}