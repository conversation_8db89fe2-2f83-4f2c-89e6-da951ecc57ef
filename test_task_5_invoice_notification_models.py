#!/usr/bin/env python3
"""
Test script to verify Task 5: Invoice and Notification Models updates
Tests that all models use correct primary key names and foreign key references
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

def test_invoice_notification_models():
    """Test that Invoice and Notification models have correct primary key naming"""
    print("Testing Invoice and Notification Models...")
    
    try:
        # Import models to test their configuration
        from app.models.invoice import Invoice, InvoiceItem
        from app.models.invoice_generation import InvoiceGenerationSettings
        from app.models.notification import Notification
        
        # Test Invoice model
        print("✓ Testing Invoice model...")
        invoice_pk = Invoice.__table__.primary_key.columns.keys()[0]
        assert invoice_pk == 'invoice_id', f"Invoice primary key should be 'invoice_id', got '{invoice_pk}'"
        print(f"  ✓ Invoice primary key: {invoice_pk}")
        
        # Test InvoiceItem model
        print("✓ Testing InvoiceItem model...")
        item_pk = InvoiceItem.__table__.primary_key.columns.keys()[0]
        assert item_pk == 'item_id', f"InvoiceItem primary key should be 'item_id', got '{item_pk}'"
        print(f"  ✓ InvoiceItem primary key: {item_pk}")
        
        # Test foreign key references in InvoiceItem
        invoice_fk = None
        appointment_fk = None
        for fk in InvoiceItem.__table__.foreign_keys:
            if 'invoices' in str(fk.target_fullname):
                invoice_fk = str(fk.target_fullname)
            elif 'appointments' in str(fk.target_fullname):
                appointment_fk = str(fk.target_fullname)
        
        assert invoice_fk == 'invoices.invoice_id', f"InvoiceItem should reference 'invoices.invoice_id', got '{invoice_fk}'"
        assert appointment_fk == 'appointments.appointment_id', f"InvoiceItem should reference 'appointments.appointment_id', got '{appointment_fk}'"
        print(f"  ✓ InvoiceItem foreign keys: {invoice_fk}, {appointment_fk}")
        
        # Test Notification model
        print("✓ Testing Notification model...")
        notification_pk = Notification.__table__.primary_key.columns.keys()[0]
        assert notification_pk == 'notification_id', f"Notification primary key should be 'notification_id', got '{notification_pk}'"
        print(f"  ✓ Notification primary key: {notification_pk}")
        
        # Test foreign key reference in Notification
        user_fk = None
        for fk in Notification.__table__.foreign_keys:
            if 'users' in str(fk.target_fullname):
                user_fk = str(fk.target_fullname)
        
        assert user_fk == 'users.user_id', f"Notification should reference 'users.user_id', got '{user_fk}'"
        print(f"  ✓ Notification foreign key: {user_fk}")
        
        # Test InvoiceGenerationSettings model
        print("✓ Testing InvoiceGenerationSettings model...")
        settings_pk = InvoiceGenerationSettings.__table__.primary_key.columns.keys()[0]
        assert settings_pk == 'setting_id', f"InvoiceGenerationSettings primary key should be 'setting_id', got '{settings_pk}'"
        print(f"  ✓ InvoiceGenerationSettings primary key: {settings_pk}")
        
        # Test foreign key references in InvoiceGenerationSettings
        appointment_fk = None
        invoice_fk = None
        for fk in InvoiceGenerationSettings.__table__.foreign_keys:
            if 'appointments' in str(fk.target_fullname):
                appointment_fk = str(fk.target_fullname)
            elif 'invoices' in str(fk.target_fullname):
                invoice_fk = str(fk.target_fullname)
        
        assert appointment_fk == 'appointments.appointment_id', f"InvoiceGenerationSettings should reference 'appointments.appointment_id', got '{appointment_fk}'"
        assert invoice_fk == 'invoices.invoice_id', f"InvoiceGenerationSettings should reference 'invoices.invoice_id', got '{invoice_fk}'"
        print(f"  ✓ InvoiceGenerationSettings foreign keys: {appointment_fk}, {invoice_fk}")
        
        print("\n✅ All Invoice and Notification models are correctly configured!")
        print("Task 5 requirements verified:")
        print("  ✓ Invoice model uses 'invoice_id' as primary key")
        print("  ✓ InvoiceItem model uses 'item_id' as primary key")
        print("  ✓ Notification model uses 'notification_id' as primary key")
        print("  ✓ InvoiceGenerationSettings model uses 'setting_id' as primary key")
        print("  ✓ All foreign key references point to correct primary keys")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing models: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_invoice_notification_models()
    sys.exit(0 if success else 1)