# app/models/client.py
from datetime import datetime
from app.extensions import db

class Client(db.Model):
    """Base model for all client types (individual and institutional)."""
    __tablename__ = 'clients'

    client_id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>ey('users.user_id'), nullable=True)
    client_type = db.Column(db.String(20), nullable=False)  # 'individual' or 'institutional'
    first_name = db.Column(db.String(100), nullable=False)
    last_name = db.Column(db.String(100), nullable=False)
    phone = db.Column(db.String(20), nullable=True)
    # Note: email is not in the schema.sql, it's accessed through the user relationship
    # address field is not in the schema.sql anymore

    # Structured address fields
    civic_number = db.Column(db.String(20), nullable=True)
    street = db.Column(db.String(255), nullable=True)
    city = db.Column(db.String(100), nullable=True)
    postal_code = db.Column(db.String(10), nullable=True)
    province = db.Column(db.String(50), default='Quebec', nullable=True)
    country = db.Column(db.String(50), default='Canada', nullable=True)

    # These fields are not in the schema.sql file
    # stripe_customer_id = db.Column(db.String(255), nullable=True)
    # is_suspended = db.Column(db.Boolean, default=False, nullable=False)
    # suspension_reason = db.Column(db.String(255), nullable=True)
    # suspended_at = db.Column(db.DateTime, nullable=True)
    # preferred_language = db.Column(db.String(2), default='en', nullable=True)  # 'en' or 'fr'
    insert_date = db.Column(db.DateTime, default=datetime.utcnow)
    modification_date = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    appointments = db.relationship('Appointment', lazy='dynamic',
                                  foreign_keys='Appointment.client_id')
    invoices = db.relationship('Invoice', lazy='dynamic',
                              foreign_keys='Invoice.client_id', overlaps="client_invoices")
    paid_invoices = db.relationship('Invoice', lazy='dynamic',
                                   foreign_keys='Invoice.paid_by_client_id', overlaps="client_paid_invoices")
    subscriptions = db.relationship('Subscription', backref='client', lazy='dynamic',
                                   foreign_keys='Subscription.client_id')
    enrollments = db.relationship('Enrollment', backref='client', lazy='dynamic')

    # Polymorphic identity
    __mapper_args__ = {
        'polymorphic_on': client_type,
        'polymorphic_identity': 'client'
    }

    def __repr__(self):
        return f'<Client {self.client_id} {self.first_name} {self.last_name}>'

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"

    @property
    def formatted_address(self):
        """Get formatted address from structured fields."""
        address_parts = []

        if self.civic_number and self.street:
            address_parts.append(f"{self.civic_number} {self.street}")
        elif self.street:
            address_parts.append(self.street)

        if self.city:
            address_parts.append(self.city)

        if self.province:
            address_parts.append(self.province)

        if self.postal_code:
            address_parts.append(self.postal_code)

        if self.country:
            address_parts.append(self.country)

        return ", ".join(address_parts) if address_parts else None

    # Relationships with other clients
    def get_related_clients(self, relationship_type=None):
        """Get clients related to this client, optionally filtered by relationship type."""
        query = ClientRelationship.query.filter_by(client_id=self.client_id)
        if relationship_type:
            query = query.filter_by(relationship_type=relationship_type)
        relationships = query.all()
        return [rel.related_client for rel in relationships]

    def add_related_client(self, related_client, relationship_type, is_primary=False):
        """Add a relationship to another client."""
        if not ClientRelationship.query.filter_by(
            client_id=self.client_id,
            related_client_id=related_client.client_id,
            relationship_type=relationship_type
        ).first():
            relationship = ClientRelationship(
                client_id=self.client_id,
                related_client_id=related_client.client_id,
                relationship_type=relationship_type,
                is_primary=is_primary
            )
            db.session.add(relationship)
            db.session.commit()
            return True
        return False

    def get_dependant_relationships(self):
        """Get dependant relationships for this client."""
        from app.models.dependant import DependantRelationship
        return DependantRelationship.query.filter_by(client_id=self.client_id).all()

    def get_dependants_count(self):
        """Get the count of dependants for this client."""
        return len(self.get_dependant_relationships())


class IndividualClient(Client):
    """Model for individual clients (students, parents, adult learners)."""
    __tablename__ = 'individual_clients'

    client_id = db.Column(db.Integer, db.ForeignKey('clients.client_id', ondelete='CASCADE'), primary_key=True)
    date_of_birth = db.Column(db.Date, nullable=True)
    school_grade = db.Column(db.String(50), nullable=True)
    notes = db.Column(db.Text, nullable=True)

    # Polymorphic identity
    __mapper_args__ = {
        'polymorphic_identity': 'individual'
    }

    def __repr__(self):
        return f'<IndividualClient {self.client_id} {self.full_name}>'

    @property
    def age(self):
        if self.date_of_birth:
            today = datetime.today()
            return today.year - self.date_of_birth.year - ((today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day))
        return None


class InstitutionalClient(Client):
    """Model for institutional clients (schools, organizations)."""
    __tablename__ = 'institutional_clients'

    client_id = db.Column(db.Integer, db.ForeignKey('clients.client_id', ondelete='CASCADE'), primary_key=True)
    institution_name = db.Column(db.String(255), nullable=False)
    institution_type = db.Column(db.String(100), nullable=True)  # school, university, company, etc.
    contact_person = db.Column(db.String(255), nullable=True)
    tax_id = db.Column(db.String(100), nullable=True)
    billing_address = db.Column(db.Text, nullable=True)
    billing_email = db.Column(db.String(255), nullable=True)
    billing_phone = db.Column(db.String(20), nullable=True)
    contract_start_date = db.Column(db.Date, nullable=True)
    contract_end_date = db.Column(db.Date, nullable=True)
    contract_details = db.Column(db.Text, nullable=True)
    notes = db.Column(db.Text, nullable=True)

    # Polymorphic identity
    __mapper_args__ = {
        'polymorphic_identity': 'institutional'
    }

    def __repr__(self):
        return f'<InstitutionalClient {self.client_id} {self.institution_name}>'

    @property
    def display_name(self):
        return self.institution_name

    @property
    def is_contract_active(self):
        if self.contract_start_date and self.contract_end_date:
            today = datetime.today().date()
            return self.contract_start_date <= today <= self.contract_end_date
        return False


class ClientRelationship(db.Model):
    """Model for tracking relationships between clients."""
    __tablename__ = 'client_relationships'

    relationship_id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('clients.client_id', ondelete='CASCADE'), nullable=False)
    related_client_id = db.Column(db.Integer, db.ForeignKey('clients.client_id', ondelete='CASCADE'), nullable=False)
    relationship_type = db.Column(db.String(50), nullable=False)  # 'parent', 'guardian', 'employee', etc.
    is_primary = db.Column(db.Boolean, default=False)
    insert_date = db.Column(db.DateTime, default=datetime.utcnow)
    modification_date = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    client = db.relationship('Client', foreign_keys=[client_id], backref='outgoing_relationships')
    related_client = db.relationship('Client', foreign_keys=[related_client_id], backref='incoming_relationships')

    __table_args__ = (
        db.UniqueConstraint('client_id', 'related_client_id', 'relationship_type', name='client_relationship_unique'),
    )

    def __repr__(self):
        return f'<ClientRelationship {self.client_id} -> {self.related_client_id} ({self.relationship_type})>'
