{% extends "base.html" %}

{% block title %}{{ t('manager.tecfee.group_sessions.form.title_create') }} - TutorAide Inc.{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="h3 mb-3">
                <i class="fas fa-calendar-plus text-primary"></i>
                {{ t('manager.tecfee.group_sessions.form.title_create') }}
            </h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('manager.dashboard') }}">{{ t('navigation.dashboard') }}</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('manager.tecfee_dashboard') }}">{{ t('base.sidebar.tecfee_dashboard') }}</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('manager.tecfee_group_sessions') }}">{{ t('base.sidebar.group_sessions') }}</a></li>
                    <li class="breadcrumb-item active">{{ t('manager.tecfee.group_sessions.create_new_session') }}</li>
                </ol>
            </nav>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('manager.tecfee_group_sessions') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> {{ t('manager.tecfee.group_sessions.form.back_to_sessions') }}
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Session Form -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">{{ t('manager.tecfee.group_sessions.form.session_details') }}</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        {{ form.hidden_tag() }}

                        <!-- Program (Hidden/Read-only) -->
                        {{ form.program_id() }}
                        <div class="mb-4">
                            <label class="form-label">{{ t('manager.tecfee.group_sessions.form.program') }}</label>
                            <div class="form-control-plaintext bg-light p-2 rounded">
                                <strong>{{ program.name }}</strong><br>
                                <small class="text-muted">{{ program.description }}</small>
                            </div>
                        </div>

                        <!-- Module Selection -->
                        <div class="mb-4">
                            <label for="{{ form.module_id.id }}" class="form-label">
                                {{ form.module_id.label.text }} <span class="text-danger">*</span>
                            </label>
                            {{ form.module_id(class="form-select") }}
                            {% if form.module_id.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.module_id.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                {{ t('manager.tecfee.group_sessions.form.module_help') }}
                            </div>
                        </div>

                        <!-- Tutor Selection -->
                        <div class="mb-4">
                            <label for="{{ form.tutor_id.id }}" class="form-label">
                                {{ form.tutor_id.label.text }} <span class="text-danger">*</span>
                            </label>
                            {{ form.tutor_id(class="form-select") }}
                            {% if form.tutor_id.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.tutor_id.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                {{ t('manager.tecfee.group_sessions.form.tutor_help') }}
                            </div>
                        </div>

                        <!-- Date and Time -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="{{ form.session_date.id }}" class="form-label">
                                    {{ form.session_date.label.text }} <span class="text-danger">*</span>
                                </label>
                                {{ form.session_date(class="form-control") }}
                                {% if form.session_date.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.session_date.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                <label for="{{ form.session_time.id }}" class="form-label">
                                    {{ form.session_time.label.text }} <span class="text-danger">*</span>
                                </label>
                                {{ form.session_time(class="form-control") }}
                                {% if form.session_time.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.session_time.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    {{ t('manager.tecfee.group_sessions.form.session_time_help') }}
                                </div>
                            </div>
                        </div>

                        <!-- Duration -->
                        <div class="mb-4">
                            <label for="{{ form.duration_minutes.id }}" class="form-label">
                                {{ form.duration_minutes.label.text }} <span class="text-danger">*</span>
                            </label>
                            <div class="input-group">
                                {{ form.duration_minutes(class="form-control") }}
                                <span class="input-group-text">{{ t('appointments.minutes') }}</span>
                            </div>
                            {% if form.duration_minutes.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.duration_minutes.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                {{ t('manager.tecfee.group_sessions.form.duration_help') }}
                            </div>
                        </div>

                        <!-- Participant Limits -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="{{ form.min_participants.id }}" class="form-label">
                                    {{ form.min_participants.label.text }} <span class="text-danger">*</span>
                                </label>
                                {{ form.min_participants(class="form-control") }}
                                {% if form.min_participants.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.min_participants.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    {{ t('manager.tecfee.group_sessions.form.min_participants_help') }}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="{{ form.max_participants.id }}" class="form-label">
                                    {{ form.max_participants.label.text }} <span class="text-danger">*</span>
                                </label>
                                {{ form.max_participants(class="form-control") }}
                                {% if form.max_participants.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.max_participants.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    {{ t('manager.tecfee.group_sessions.form.max_participants_help') }}
                                </div>
                            </div>
                        </div>

                        <!-- Tutor Rate -->
                        <div class="mb-4">
                            <label for="{{ form.tutor_rate_per_student.id }}" class="form-label">
                                {{ form.tutor_rate_per_student.label.text }} <span class="text-danger">*</span>
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">$</span>
                                {{ form.tutor_rate_per_student(class="form-control") }}
                                <span class="input-group-text">per student</span>
                            </div>
                            {% if form.tutor_rate_per_student.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.tutor_rate_per_student.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                {{ t('manager.tecfee.group_sessions.form.tutor_rate_help') }}
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="mb-4">
                            <label for="{{ form.notes.id }}" class="form-label">
                                {{ form.notes.label.text }}
                            </label>
                            {{ form.notes(class="form-control", rows="3", placeholder=t('manager.tecfee.group_sessions.form.notes_help')) }}
                            {% if form.notes.errors %}
                                <div class="text-danger mt-1">
                                    {% for error in form.notes.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <!-- Submit Button -->
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('manager.tecfee_group_sessions') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times"></i> {{ t('manager.tecfee.group_sessions.form.cancel') }}
                            </a>
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Session Information Sidebar -->
        <div class="col-lg-4">
            <!-- Session Guidelines -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">{{ t('manager.tecfee.group_sessions.guidelines.title') }}</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6 class="alert-heading">{{ t('manager.tecfee.group_sessions.guidelines.requirements_title') }}</h6>
                        <ul class="mb-0">
                            <li>{{ t('manager.tecfee.group_sessions.guidelines.min_students') }}</li>
                            <li>{{ t('manager.tecfee.group_sessions.guidelines.max_students') }}</li>
                            <li>{{ t('manager.tecfee.group_sessions.guidelines.online_sessions') }}</li>
                            <li>{{ t('manager.tecfee.group_sessions.guidelines.standard_duration') }}</li>
                        </ul>
                    </div>

                    <div class="alert alert-warning">
                        <h6 class="alert-heading">{{ t('manager.tecfee.group_sessions.guidelines.payment_structure_title') }}</h6>
                        <p class="mb-0">
                            {{ t('manager.tecfee.group_sessions.guidelines.payment_structure_text') }}
                        </p>
                    </div>
                </div>
            </div>

            <!-- Payment Calculator -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0">{{ t('manager.tecfee.group_sessions.calculator.title') }}</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border rounded p-2 mb-2">
                                <strong class="text-success" id="min-payment">$60.00</strong><br>
                                <small class="text-muted">{{ t('manager.tecfee.group_sessions.calculator.min_payment') }}</small><br>
                                <small class="text-muted">(4 {{ t('manager.tecfee.group_sessions.calculator.students') }})</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="border rounded p-2 mb-2">
                                <strong class="text-primary" id="max-payment">$150.00</strong><br>
                                <small class="text-muted">{{ t('manager.tecfee.group_sessions.calculator.max_payment') }}</small><br>
                                <small class="text-muted">(10 {{ t('manager.tecfee.group_sessions.calculator.students') }})</small>
                            </div>
                        </div>
                    </div>

                    <div class="mt-3">
                        <label class="form-label">{{ t('manager.tecfee.group_sessions.calculator.estimated_payment') }}</label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            <input type="text" class="form-control" id="estimated-payment" readonly value="105.00">
                        </div>
                        <small class="text-muted" id="estimated-text">Based on 7 students (average)</small>
                    </div>
                </div>
            </div>

            <!-- Module Information -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0">{{ t('manager.tecfee.group_sessions.modules.title') }}</h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        {% for module in program.modules %}
                        <div class="list-group-item px-0">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>Module {{ module.module_number }}</strong><br>
                                    <small class="text-muted">{{ module.name }}</small>
                                </div>
                                <span class="badge bg-secondary">{{ module.duration_minutes }}min</span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set minimum date to today
    const sessionDateInput = document.getElementById('{{ form.session_date.id }}');
    if (sessionDateInput) {
        const today = new Date().toISOString().split('T')[0];
        sessionDateInput.setAttribute('min', today);
    }

    // Update payment calculator when participant limits change
    const minParticipantsInput = document.getElementById('{{ form.min_participants.id }}');
    const maxParticipantsInput = document.getElementById('{{ form.max_participants.id }}');
    const tutorRateInput = document.getElementById('{{ form.tutor_rate_per_student.id }}');

    function updatePaymentCalculator() {
        const minParticipants = parseInt(minParticipantsInput.value) || 4;
        const maxParticipants = parseInt(maxParticipantsInput.value) || 10;
        const tutorRate = parseFloat(tutorRateInput.value) || 15.00;

        const minPayment = minParticipants * tutorRate;
        const maxPayment = maxParticipants * tutorRate;
        const avgParticipants = Math.round((minParticipants + maxParticipants) / 2);
        const estimatedPayment = avgParticipants * tutorRate;

        document.getElementById('min-payment').textContent = `$${minPayment.toFixed(2)}`;
        document.getElementById('max-payment').textContent = `$${maxPayment.toFixed(2)}`;
        document.getElementById('estimated-payment').value = estimatedPayment.toFixed(2);

        // Update the small text
        const estimatedText = document.getElementById('estimated-text');
        estimatedText.textContent = `{{ t('manager.tecfee.group_sessions.calculator.based_on_students') }}`.replace('{count}', avgParticipants);
    }

    if (minParticipantsInput) minParticipantsInput.addEventListener('input', updatePaymentCalculator);
    if (maxParticipantsInput) maxParticipantsInput.addEventListener('input', updatePaymentCalculator);
    if (tutorRateInput) tutorRateInput.addEventListener('input', updatePaymentCalculator);

    // Validate participant limits
    function validateParticipantLimits() {
        const minVal = parseInt(minParticipantsInput.value);
        const maxVal = parseInt(maxParticipantsInput.value);

        if (minVal && maxVal && minVal > maxVal) {
            maxParticipantsInput.setCustomValidity('Maximum must be greater than or equal to minimum');
        } else {
            maxParticipantsInput.setCustomValidity('');
        }
    }

    if (minParticipantsInput) minParticipantsInput.addEventListener('input', validateParticipantLimits);
    if (maxParticipantsInput) maxParticipantsInput.addEventListener('input', validateParticipantLimits);

    // Initialize calculator
    updatePaymentCalculator();
});
</script>
{% endblock %}
