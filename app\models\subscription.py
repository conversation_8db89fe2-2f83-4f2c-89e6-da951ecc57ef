# app/models/subscription.py
from datetime import datetime, timedelta
from app.extensions import db

class SubscriptionPlan(db.Model):
    __tablename__ = 'subscription_plans'

    plan_id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)
    price = db.Column(db.Numeric(10, 2), nullable=False)
    duration_months = db.Column(db.Integer, nullable=False)
    max_hours = db.Column(db.Integer, nullable=False)
    is_active = db.Column(db.<PERSON>, default=True)
    insert_date = db.Column(db.DateTime, default=datetime.utcnow)
    modification_date = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    subscriptions = db.relationship('Subscription', backref='plan', lazy='dynamic')

    def __repr__(self):
        return f'<SubscriptionPlan {self.name}>'

class Subscription(db.Model):
    __tablename__ = 'subscriptions'

    subscription_id = db.Column(db.Integer, primary_key=True)
    client_id = db.Column(db.Integer, db.ForeignKey('clients.client_id'), nullable=False)
    plan_id = db.Column(db.Integer, db.ForeignKey('subscription_plans.plan_id'), nullable=False)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    hours_used = db.Column(db.Float, default=0.0)
    status = db.Column(db.String(20), nullable=False, default='active')  # active, cancelled, expired
    stripe_subscription_id = db.Column(db.String(255), nullable=True)
    insert_date = db.Column(db.DateTime, default=datetime.utcnow)
    modification_date = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    subscription_usage = db.relationship('SubscriptionUsage', backref='subscription', lazy='dynamic')

    def __repr__(self):
        return f'<Subscription {self.subscription_id} client={self.client_id}>'

    @property
    def hours_remaining(self):
        return max(0, self.plan.max_hours - self.hours_used)

    @property
    def is_active(self):
        today = datetime.now().date()
        return self.status == 'active' and self.start_date <= today <= self.end_date

    @property
    def is_expired(self):
        today = datetime.now().date()
        return today > self.end_date

    @property
    def usage_percentage(self):
        if self.plan.max_hours > 0:
            return min(100, (self.hours_used / self.plan.max_hours) * 100)
        return 0

class SubscriptionUsage(db.Model):
    __tablename__ = 'subscription_usage'

    usage_id = db.Column(db.Integer, primary_key=True)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscriptions.subscription_id'), nullable=False)
    appointment_id = db.Column(db.Integer, db.ForeignKey('appointments.appointment_id'), nullable=False)
    hours_used = db.Column(db.Float, nullable=False)
    usage_date = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<SubscriptionUsage {self.usage_id} subscription={self.subscription_id} appointment={self.appointment_id}>'