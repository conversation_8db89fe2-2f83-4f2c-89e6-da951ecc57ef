<!-- app/templates/client/subscriptions.html -->
{% extends "base.html" %}

{% block title %}{{ t('subscriptions.your_subscriptions') }} - TutorAide Inc.{% endblock %}

{% block styles %}
<style>
    /* Reset all Bootstrap tab styles */
    .nav-tabs {
        border-bottom: 1px solid #dee2e6 !important;
    }
    
    .nav-tabs .nav-item {
        margin-bottom: -1px !important;
    }
    
    .nav-tabs .nav-link {
        color: #212529 !important;
        font-weight: 500;
        border: 1px solid transparent !important;
        border-top-left-radius: 0 !important;
        border-top-right-radius: 0 !important;
        background: transparent !important;
        padding: 1rem 1.5rem !important;
        margin-bottom: 0 !important;
        position: relative !important;
        transition: all 0.3s ease;
    }
    
    .nav-tabs .nav-link::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        right: 0;
        height: 3px;
        background: transparent;
        transition: background 0.3s ease;
    }
    
    .nav-tabs .nav-link:hover {
        color: #e57373 !important;
        border-color: transparent !important;
        background: transparent !important;
    }
    
    .nav-tabs .nav-link:hover::after {
        background: transparent;
    }
    
    /* Active tab styling */
    .nav-tabs .nav-link.active,
    .nav-tabs .nav-item.show .nav-link {
        color: #e57373 !important;
        background-color: transparent !important;
        border: 1px solid transparent !important;
        border-bottom-color: transparent !important;
        font-weight: 600 !important;
    }
    
    .nav-tabs .nav-link.active::after {
        background: #e57373 !important;
    }
    
    /* Remove ALL focus states */
    .nav-tabs .nav-link:focus,
    .nav-tabs .nav-link:focus-visible,
    .nav-tabs .nav-link.active:focus,
    .nav-tabs .nav-link.active:focus-visible {
        outline: none !important;
        outline-width: 0 !important;
        box-shadow: none !important;
        border-color: transparent !important;
        background: transparent !important;
    }
    
    /* Override Bootstrap's tab-pane.active */
    .tab-content > .active {
        display: block;
    }
    
    /* Ensure no blue appears on click */
    .nav-tabs .nav-link:active,
    .nav-tabs .nav-link.active:active {
        outline: none !important;
        box-shadow: none !important;
        border-color: transparent !important;
    }
    
    .nav-tabs {
        border-bottom: 1px solid #dee2e6;
    }
    
    .card-header {
        background: #f8f9fa !important;
        padding: 0;
    }
    
    .card-header .nav-tabs {
        margin-bottom: -1px;
    }
    
    .nav-tabs .nav-item {
        margin-bottom: 0;
    }
    
    .nav-tabs .nav-link {
        padding: 1rem 1.5rem;
        margin-bottom: 0;
        border-radius: 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h2 class="mb-3">{{ t('subscriptions.your_subscriptions') }}</h2>
        <p class="text-muted">{{ t('subscriptions.view_and_manage') }}</p>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <ul class="nav nav-tabs card-header-tabs" id="subscriptionsTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="active-tab" data-bs-toggle="tab" data-bs-target="#active" type="button" role="tab" aria-controls="active" aria-selected="true">
                    {{ t('subscriptions.active') }}
                    {% if active_subscriptions %}
                        <span class="badge bg-danger ms-1">{{ active_subscriptions|length }}</span>
                    {% endif %}
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="expired-tab" data-bs-toggle="tab" data-bs-target="#expired" type="button" role="tab" aria-controls="expired" aria-selected="false">
                    {{ t('subscriptions.expired') }}
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="all-tab" data-bs-toggle="tab" data-bs-target="#all" type="button" role="tab" aria-controls="all" aria-selected="false">
                    {{ t('subscriptions.all') }}
                </button>
            </li>
        </ul>
    </div>
    <div class="card-body">
        <div class="tab-content" id="subscriptionsTabsContent">
            <!-- Active Subscriptions Tab -->
            <div class="tab-pane fade show active" id="active" role="tabpanel" aria-labelledby="active-tab">
                {% if active_subscriptions %}
                    <div class="row">
                        {% for subscription in active_subscriptions %}
                            <div class="col-md-6 mb-4">
                                <div class="card h-100">
                                    <div class="card-header">
                                        <h5 class="mb-0">{{ subscription.plan.name }}</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <strong>{{ t('subscriptions.period') }}:</strong>
                                            {{ subscription.start_date.strftime('%Y-%m-%d') }} - {{ subscription.end_date.strftime('%Y-%m-%d') }}
                                        </div>
                                        <div class="mb-3">
                                            <strong>{{ t('subscriptions.hours') }}:</strong>
                                            {{ subscription.plan.max_hours }}
                                        </div>
                                        <div class="mb-3">
                                            <strong>{{ t('subscriptions.usage') }}:</strong>
                                            <div class="progress mt-2" style="height: 20px;">
                                                {% set percentage = (subscription.hours_used / subscription.plan.max_hours) * 100 %}
                                                <div class="progress-bar bg-danger"
                                                     role="progressbar"
                                                     style="width: {{ percentage }}%;"
                                                     aria-valuenow="{{ percentage }}"
                                                     aria-valuemin="0"
                                                     aria-valuemax="100">
                                                    {{ "%.1f"|format(subscription.hours_used) }} / {{ subscription.plan.max_hours }}
                                                </div>
                                            </div>
                                            <div class="text-center mt-1">
                                                <small class="text-muted">{{ "%.1f"|format(subscription.hours_remaining) }} {{ t('subscriptions.hours_remaining') }}</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-footer">
                                        <a href="{{ url_for('client.view_subscription', id=subscription.id) }}" class="btn btn-primary">
                                            <i class="fas fa-eye"></i> {{ t('subscriptions.view_details') }}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted text-center my-4">{{ t('subscriptions.no_active_subscriptions') }}</p>
                {% endif %}
            </div>

            <!-- Expired Subscriptions Tab -->
            <div class="tab-pane fade" id="expired" role="tabpanel" aria-labelledby="expired-tab">
                {% if expired_subscriptions %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{{ t('subscriptions.package') }}</th>
                                    <th>{{ t('subscriptions.period') }}</th>
                                    <th>{{ t('subscriptions.hours') }}</th>
                                    <th>{{ t('subscriptions.usage') }}</th>
                                    <th>{{ t('subscriptions.actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for subscription in expired_subscriptions %}
                                    <tr>
                                        <td>{{ subscription.plan.name }}</td>
                                        <td>{{ subscription.start_date.strftime('%Y-%m-%d') }} - {{ subscription.end_date.strftime('%Y-%m-%d') }}</td>
                                        <td>{{ subscription.plan.max_hours }}</td>
                                        <td>
                                            <div class="progress" style="height: 15px;">
                                                {% set percentage = (subscription.hours_used / subscription.plan.max_hours) * 100 %}
                                                <div class="progress-bar bg-secondary"
                                                     role="progressbar"
                                                     style="width: {{ percentage }}%;"
                                                     aria-valuenow="{{ percentage }}"
                                                     aria-valuemin="0"
                                                     aria-valuemax="100">
                                                    {{ "%.1f"|format(subscription.hours_used) }} / {{ subscription.plan.max_hours }}
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <a href="{{ url_for('client.view_subscription', id=subscription.subscription_id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center my-4">{{ t('subscriptions.no_expired_subscriptions') }}</p>
                {% endif %}
            </div>

            <!-- All Subscriptions Tab -->
            <div class="tab-pane fade" id="all" role="tabpanel" aria-labelledby="all-tab">
                {% if all_subscriptions %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{{ t('subscriptions.package') }}</th>
                                    <th>{{ t('subscriptions.period') }}</th>
                                    <th>{{ t('subscriptions.hours') }}</th>
                                    <th>{{ t('subscriptions.usage') }}</th>
                                    <th>{{ t('subscriptions.status') }}</th>
                                    <th>{{ t('subscriptions.actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for subscription in all_subscriptions %}
                                    <tr>
                                        <td>{{ subscription.plan.name }}</td>
                                        <td>{{ subscription.start_date.strftime('%Y-%m-%d') }} - {{ subscription.end_date.strftime('%Y-%m-%d') }}</td>
                                        <td>{{ subscription.plan.max_hours }}</td>
                                        <td>
                                            <div class="progress" style="height: 15px;">
                                                {% set percentage = (subscription.hours_used / subscription.plan.max_hours) * 100 %}
                                                <div class="progress-bar {{ 'bg-secondary' if not subscription.is_active else 'bg-danger' }}"
                                                     role="progressbar"
                                                     style="width: {{ percentage }}%;"
                                                     aria-valuenow="{{ percentage }}"
                                                     aria-valuemin="0"
                                                     aria-valuemax="100">
                                                    {{ "%.1f"|format(subscription.hours_used) }} / {{ subscription.plan.max_hours }}
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-danger">
                                                {{ t('subscriptions.active') if subscription.is_active else t('subscriptions.expired') }}
                                            </span>
                                        </td>
                                        <td>
                                            <a href="{{ url_for('client.view_subscription', id=subscription.subscription_id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center my-4">{{ t('subscriptions.no_subscriptions') }}</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
