#!/usr/bin/env python3

from app import create_app
from app.extensions import db
from sqlalchemy import text

app = create_app()
with app.app_context():
    # Check current table structure
    query = "SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'appointments' ORDER BY ordinal_position"
    result = db.session.execute(text(query))
    print('Appointments table columns:')
    for row in result:
        print(f'  {row[0]}: {row[1]}')
    
    print('\nChecking for existing recurring appointments...')
    # Check for appointments with recurring data using raw SQL to avoid model issues
    query2 = "SELECT COUNT(*) FROM appointments WHERE is_recurring = true OR recurring_template_id IS NOT NULL OR frequency IS NOT NULL"
    try:
        result2 = db.session.execute(text(query2))
        count = result2.scalar()
        print(f'Found {count} appointments with recurring data')
    except Exception as e:
        print(f'Error checking recurring appointments: {e}')