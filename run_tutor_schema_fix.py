#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to run the database schema fix migration
This script applies the database migration to fix the mismatch between
the database schema and SQLAlchemy models for the tutors and dependants tables.
"""

import os
import sys
import subprocess
from pathlib import Path

def get_db_connection_string():
    """Get database connection string from environment variables"""
    db_host = os.getenv('DB_HOST', 'localhost')
    db_port = os.getenv('DB_PORT', '5432')
    db_name = os.getenv('DB_NAME', 'tutoraide')
    db_user = os.getenv('DB_USER', 'postgres')
    db_password = os.getenv('DB_PASSWORD', '')
    
    if not db_password:
        print("Warning: DB_PASSWORD environment variable not set")
        db_password = input("Enter database password: ")
    
    return f"postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}"

def run_migration():
    """Run the database schema fix migration for tutors and dependants tables"""
    script_dir = Path(__file__).parent
    migration_file = script_dir / "app" / "migrations" / "fix_tutor_table_schema.sql"
    
    if not migration_file.exists():
        print(f"Error: Migration file not found at {migration_file}")
        return False
    
    # Get database connection details
    db_host = os.getenv('DB_HOST', 'localhost')
    db_port = os.getenv('DB_PORT', '5432')
    db_name = os.getenv('DB_NAME', 'tutoraide')
    db_user = os.getenv('DB_USER', 'postgres')
    db_password = os.getenv('DB_PASSWORD', '')
    
    if not db_password:
        print("Warning: DB_PASSWORD environment variable not set")
        db_password = input("Enter database password: ")
    
    # Set PGPASSWORD environment variable
    env = os.environ.copy()
    env['PGPASSWORD'] = db_password
    
    print("Running database schema fix migration...")
    print(f"Database: {db_host}:{db_port}/{db_name}")
    print(f"User: {db_user}")
    print(f"Migration file: {migration_file}")
    print("This will fix both tutors and dependants table schemas.")
    print()
    
    try:
        # Run the migration using psql
        result = subprocess.run([
            'psql',
            '-h', db_host,
            '-p', db_port,
            '-U', db_user,
            '-d', db_name,
            '-f', str(migration_file)
        ], env=env, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✓ Migration completed successfully!")
            print("\nOutput:")
            print(result.stdout)
            return True
        else:
            print("✗ Migration failed!")
            print("\nError output:")
            print(result.stderr)
            return False
            
    except FileNotFoundError:
        print("Error: psql command not found. Please ensure PostgreSQL client is installed.")
        return False
    except Exception as e:
        print(f"Error running migration: {e}")
        return False

def main():
    """Main function"""
    print("Database Schema Fix Migration")
    print("=" * 40)
    print("Fixes both tutors and dependants table schemas")
    print()
    
    # Check if we're in the right directory
    if not Path("app").exists():
        print("Error: This script must be run from the project root directory")
        print("Current directory:", os.getcwd())
        return 1
    
    # Run the migration
    if run_migration():
        print("\n" + "=" * 40)
        print("Migration completed successfully!")
        print("The tutors and dependants table schemas have been updated to match the SQLAlchemy models.")
        print("You can now restart your application.")
        return 0
    else:
        print("\n" + "=" * 40)
        print("Migration failed!")
        print("Please check the error messages above and try again.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
