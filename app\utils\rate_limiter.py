# app/utils/rate_limiter.py
from functools import wraps
from flask import request, jsonify, current_app
from datetime import datetime, timedelta
from collections import defaultdict
from typing import Dict, Tuple
import json

class EnrollmentRateLimiter:
    """Rate limiter specifically for TECFÉE enrollment to prevent abuse."""
    
    def __init__(self):
        # In-memory storage (replace with <PERSON><PERSON> in production)
        self.attempts: Dict[str, list] = defaultdict(list)
        self.blocked_ips: Dict[str, datetime] = {}
        
    def get_client_ip(self) -> str:
        """Get client IP address, handling proxies."""
        if request.environ.get('HTTP_X_FORWARDED_FOR'):
            # Behind proxy
            return request.environ['HTTP_X_FORWARDED_FOR'].split(',')[0].strip()
        elif request.environ.get('HTTP_X_REAL_IP'):
            return request.environ['HTTP_X_REAL_IP']
        else:
            return request.environ.get('REMOTE_ADDR', 'unknown')
    
    def is_rate_limited(self, identifier: str, limit: int = 5, window_minutes: int = 60) -> Tuple[bool, str]:
        """
        Check if identifier (IP or email) is rate limited.
        Returns: (is_limited, reason)
        """
        now = datetime.utcnow()
        window_start = now - timedelta(minutes=window_minutes)
        
        # Clean old attempts
        self.attempts[identifier] = [
            attempt for attempt in self.attempts[identifier]
            if attempt > window_start
        ]
        
        # Check if blocked
        if identifier in self.blocked_ips:
            if self.blocked_ips[identifier] > now:
                remaining = (self.blocked_ips[identifier] - now).total_seconds() / 60
                return True, f"Trop de tentatives. Réessayez dans {int(remaining)} minutes."
            else:
                del self.blocked_ips[identifier]
        
        # Check current attempts
        attempt_count = len(self.attempts[identifier])
        if attempt_count >= limit:
            # Block for progressive duration
            block_duration = min(60 * (2 ** (attempt_count - limit)), 1440)  # Max 24 hours
            self.blocked_ips[identifier] = now + timedelta(minutes=block_duration)
            return True, f"Trop de tentatives. Réessayez dans {block_duration} minutes."
        
        return False, ""
    
    def record_attempt(self, identifier: str):
        """Record an enrollment attempt."""
        self.attempts[identifier].append(datetime.utcnow())
    
    def check_enrollment_abuse(self, email: str, ip: str) -> Tuple[bool, str]:
        """
        Comprehensive abuse check for enrollment.
        Returns: (is_blocked, reason)
        """
        # Check IP rate limit (5 attempts per hour)
        ip_limited, ip_reason = self.is_rate_limited(ip, limit=5, window_minutes=60)
        if ip_limited:
            return True, ip_reason
        
        # Check email rate limit (3 attempts per day)
        email_limited, email_reason = self.is_rate_limited(
            f"email:{email}", 
            limit=3, 
            window_minutes=1440
        )
        if email_limited:
            return True, "Cette adresse email a déjà tenté plusieurs inscriptions aujourd'hui."
        
        # Check IP velocity (too many different emails from same IP)
        ip_emails_key = f"ip_emails:{ip}"
        if ip_emails_key not in self.attempts:
            self.attempts[ip_emails_key] = []
        
        # Clean old email attempts from this IP
        now = datetime.utcnow()
        day_ago = now - timedelta(days=1)
        self.attempts[ip_emails_key] = [
            (email_time, email_addr) for email_time, email_addr in self.attempts[ip_emails_key]
            if email_time > day_ago
        ]
        
        # Count unique emails from this IP in last 24 hours
        unique_emails = set(email_addr for _, email_addr in self.attempts[ip_emails_key])
        if len(unique_emails) >= 10:
            self.blocked_ips[ip] = now + timedelta(hours=24)
            return True, "Activité suspecte détectée. Veuillez contacter le support."
        
        return False, ""
    
    def record_enrollment_attempt(self, email: str, ip: str, success: bool = False):
        """Record an enrollment attempt with context."""
        self.record_attempt(ip)
        self.record_attempt(f"email:{email}")
        
        # Track email-IP associations
        ip_emails_key = f"ip_emails:{ip}"
        if ip_emails_key not in self.attempts:
            self.attempts[ip_emails_key] = []
        self.attempts[ip_emails_key].append((datetime.utcnow(), email))
        
        # Log to database (implement as needed)
        current_app.logger.info(
            f"Enrollment attempt: email={email}, ip={ip}, success={success}"
        )


# Global rate limiter instance
enrollment_limiter = EnrollmentRateLimiter()


def rate_limit_enrollment(f):
    """Decorator to rate limit enrollment endpoints."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        ip = enrollment_limiter.get_client_ip()
        
        # For form submissions, check the email
        if request.method == 'POST':
            email = request.form.get('email', '').strip().lower()
            if email:
                is_blocked, reason = enrollment_limiter.check_enrollment_abuse(email, ip)
                if is_blocked:
                    if request.is_json:
                        return jsonify({'error': reason}), 429
                    else:
                        from flask import flash, redirect, url_for
                        flash(reason, 'error')
                        return redirect(url_for('public.tecfee_enrollment_single_step'))
        
        return f(*args, **kwargs)
    
    return decorated_function