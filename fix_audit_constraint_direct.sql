-- Direct Fix for Appointment Audit Constraint
-- This script directly addresses the constraint mismatch

-- ========================================
-- STEP 1: IDENTIFY THE PROBLEM
-- ========================================

-- Show current constraints
SELECT 'CURRENT CHECK CONSTRAINTS ON appointment_audit:' as info;
SELECT 
    tc.constraint_name,
    cc.check_clause
FROM information_schema.table_constraints tc
JOIN information_schema.check_constraints cc ON tc.constraint_name = cc.constraint_name
WHERE tc.table_name = 'appointment_audit' 
AND tc.constraint_type = 'CHECK';

-- ========================================
-- STEP 2: FIX THE CONSTRAINT
-- ========================================

-- Drop existing check constraints on action column
DO $$ 
DECLARE
    constraint_rec RECORD;
BEGIN
    -- Find and drop all check constraints that mention 'action'
    FOR constraint_rec IN 
        SELECT tc.constraint_name
        FROM information_schema.table_constraints tc
        JOIN information_schema.check_constraints cc ON tc.constraint_name = cc.constraint_name
        WHERE tc.table_name = 'appointment_audit' 
        AND tc.constraint_type = 'CHECK'
        AND cc.check_clause ILIKE '%action%'
    LOOP
        EXECUTE format('ALTER TABLE appointment_audit DROP CONSTRAINT %I', constraint_rec.constraint_name);
        RAISE NOTICE 'Dropped constraint: %', constraint_rec.constraint_name;
    END LOOP;
    
    -- If no constraints were found, try common constraint names
    BEGIN
        ALTER TABLE appointment_audit DROP CONSTRAINT IF EXISTS appointment_audit_action_check;
        RAISE NOTICE 'Dropped appointment_audit_action_check (if it existed)';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'appointment_audit_action_check did not exist';
    END;
    
    BEGIN
        ALTER TABLE appointment_audit DROP CONSTRAINT IF EXISTS appointment_audit_action_valid;
        RAISE NOTICE 'Dropped appointment_audit_action_valid (if it existed)';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'appointment_audit_action_valid did not exist';
    END;
END $$;

-- Add the correct constraint
ALTER TABLE appointment_audit 
ADD CONSTRAINT appointment_audit_action_check 
CHECK (action IN ('create', 'update', 'delete', 'cancel', 'complete', 'reschedule'));

RAISE NOTICE 'Added new constraint allowing: create, update, delete, cancel, complete, reschedule';

-- ========================================
-- STEP 3: VERIFY THE FIX
-- ========================================

-- Show new constraints
SELECT 'NEW CHECK CONSTRAINTS:' as info;
SELECT 
    tc.constraint_name,
    cc.check_clause
FROM information_schema.table_constraints tc
JOIN information_schema.check_constraints cc ON tc.constraint_name = cc.constraint_name
WHERE tc.table_name = 'appointment_audit' 
AND tc.constraint_type = 'CHECK';

-- Test the constraint
DO $$
BEGIN
    -- Test valid action
    BEGIN
        INSERT INTO appointment_audit (appointment_id, action, timestamp) 
        VALUES (999999, 'create', NOW());
        DELETE FROM appointment_audit WHERE appointment_id = 999999;
        RAISE NOTICE 'SUCCESS: action=create is now allowed';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'ERROR: action=create failed: %', SQLERRM;
    END;
    
    -- Test invalid action
    BEGIN
        INSERT INTO appointment_audit (appointment_id, action, timestamp) 
        VALUES (999998, 'invalid_action', NOW());
        DELETE FROM appointment_audit WHERE appointment_id = 999998;
        RAISE NOTICE 'ERROR: invalid_action should have been rejected but was allowed';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE 'SUCCESS: invalid_action correctly rejected';
    END;
END $$;

-- ========================================
-- STEP 4: ENSURE MISSING COLUMNS EXIST
-- ========================================

-- Add any missing columns that the AppointmentAudit model expects
DO $$
BEGIN
    -- Check and add missing columns
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointment_audit' AND column_name = 'user_id') THEN
        ALTER TABLE appointment_audit ADD COLUMN user_id INTEGER REFERENCES users(user_id);
        RAISE NOTICE 'Added user_id column';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointment_audit' AND column_name = 'user_role') THEN
        ALTER TABLE appointment_audit ADD COLUMN user_role VARCHAR(50);
        RAISE NOTICE 'Added user_role column';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointment_audit' AND column_name = 'user_email') THEN
        ALTER TABLE appointment_audit ADD COLUMN user_email VARCHAR(255);
        RAISE NOTICE 'Added user_email column';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointment_audit' AND column_name = 'timestamp') THEN
        ALTER TABLE appointment_audit ADD COLUMN timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Added timestamp column';
    END IF;
    
    -- Add other expected columns
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointment_audit' AND column_name = 'ip_address') THEN
        ALTER TABLE appointment_audit ADD COLUMN ip_address INET;
        RAISE NOTICE 'Added ip_address column';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointment_audit' AND column_name = 'user_agent') THEN
        ALTER TABLE appointment_audit ADD COLUMN user_agent TEXT;
        RAISE NOTICE 'Added user_agent column';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointment_audit' AND column_name = 'notes') THEN
        ALTER TABLE appointment_audit ADD COLUMN notes TEXT;
        RAISE NOTICE 'Added notes column';
    END IF;
    
    -- Add audit tracking columns
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointment_audit' AND column_name = 'old_status') THEN
        ALTER TABLE appointment_audit ADD COLUMN old_status VARCHAR(50);
        RAISE NOTICE 'Added old_status column';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointment_audit' AND column_name = 'new_status') THEN
        ALTER TABLE appointment_audit ADD COLUMN new_status VARCHAR(50);
        RAISE NOTICE 'Added new_status column';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointment_audit' AND column_name = 'old_tutor_id') THEN
        ALTER TABLE appointment_audit ADD COLUMN old_tutor_id INTEGER;
        RAISE NOTICE 'Added old_tutor_id column';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointment_audit' AND column_name = 'new_tutor_id') THEN
        ALTER TABLE appointment_audit ADD COLUMN new_tutor_id INTEGER;
        RAISE NOTICE 'Added new_tutor_id column';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointment_audit' AND column_name = 'old_client_id') THEN
        ALTER TABLE appointment_audit ADD COLUMN old_client_id INTEGER;
        RAISE NOTICE 'Added old_client_id column';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointment_audit' AND column_name = 'new_client_id') THEN
        ALTER TABLE appointment_audit ADD COLUMN new_client_id INTEGER;
        RAISE NOTICE 'Added new_client_id column';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointment_audit' AND column_name = 'old_start_time') THEN
        ALTER TABLE appointment_audit ADD COLUMN old_start_time TIMESTAMP;
        RAISE NOTICE 'Added old_start_time column';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointment_audit' AND column_name = 'new_start_time') THEN
        ALTER TABLE appointment_audit ADD COLUMN new_start_time TIMESTAMP;
        RAISE NOTICE 'Added new_start_time column';
    END IF;
END $$;

SELECT 'APPOINTMENT AUDIT CONSTRAINT FIX COMPLETED!' as status,
       'Audit logging should now work with action=create' as message;
