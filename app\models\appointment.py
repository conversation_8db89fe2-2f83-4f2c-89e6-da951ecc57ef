# app/models/appointment.py
from datetime import datetime, timedelta, date, time
from app.extensions import db

class Appointment(db.Model):
    __tablename__ = 'appointments'

    # Use descriptive primary key name to match database schema
    appointment_id = db.Column(db.Integer, primary_key=True)
    tutor_id = db.Column(db.<PERSON><PERSON><PERSON>, db.<PERSON>('tutors.tutor_id'), nullable=False)
    client_id = db.Column(db.Integer, db.ForeignKey('clients.client_id'), nullable=False)
    dependant_id = db.Column(db.Integer, db.ForeignKey('dependants.dependant_id'), nullable=True)  # For appointments with dependants
    tutor_service_id = db.Column(db.Integer, db.<PERSON>ey('tutor_services.tutor_service_id'), nullable=False)
    start_time = db.Column(db.DateTime, nullable=False)  # Always required
    end_time = db.Column(db.DateTime, nullable=False)    # Always required
    status = db.Column(db.String(50), nullable=False, default='scheduled')
    location = db.Column(db.String(200), nullable=True)
    notes = db.Column(db.Text, nullable=True)
    transport_fee = db.Column(db.Numeric(10, 2), nullable=True)
    transport_fee_for_tutor = db.Column(db.Boolean, default=True)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscriptions.subscription_id'), nullable=True)
    is_subscription_based = db.Column(db.Boolean, default=False)
    
    # Foreign key relationship to recurring schedules
    recurring_schedule_id = db.Column(db.Integer, db.ForeignKey('appointment_recurring_schedules.schedule_id'), nullable=True)
    
    # Audit tracking fields
    created_by = db.Column(db.Integer, db.ForeignKey('users.user_id'), nullable=True)
    modified_by = db.Column(db.Integer, db.ForeignKey('users.user_id'), nullable=True)
    deleted_by = db.Column(db.Integer, db.ForeignKey('users.user_id'), nullable=True)
    deleted_at = db.Column(db.DateTime, nullable=True)
    insert_date = db.Column(db.DateTime, default=datetime.utcnow)
    modification_date = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships with overlaps parameter to fix SQLAlchemy warnings
    client = db.relationship('Client', foreign_keys=[client_id], lazy='joined', overlaps="appointments")
    tutor = db.relationship('Tutor', foreign_keys=[tutor_id], lazy='joined', overlaps="appointments")
    tutor_service = db.relationship('TutorService', foreign_keys=[tutor_service_id], lazy='joined', overlaps="appointments")
    dependant = db.relationship('Dependant', foreign_keys=[dependant_id], lazy='joined', overlaps="appointments")
    subscription = db.relationship('Subscription', foreign_keys=[subscription_id], lazy='select', overlaps="appointments,subscription_usage")

    invoice_items = db.relationship('InvoiceItem', lazy='dynamic')
    subscription_usage = db.relationship('SubscriptionUsage', backref='source_appointment', uselist=False)

    def __repr__(self):
        return f'<Appointment {self.appointment_id} tutor={self.tutor_id} client={self.client_id}>'

    @property
    def appointment_subject(self):
        """Get the subject of the appointment (dependant if present, otherwise client)."""
        # COALESCE logic: prefer dependant over client
        if self.dependant_id and self.dependant:
            return self.dependant
        elif self.client:
            return self.client
        return None

    @property
    def appointment_subject_name(self):
        """Get the name of the appointment subject (dependant name if present, otherwise client name)."""
        subject = self.appointment_subject
        if subject:
            return f"{subject.first_name} {subject.last_name}"
        else:
            # Fallback: try to get names directly if relationships aren't loaded
            if self.dependant_id:
                # Try to get dependant name
                from app.models.dependant import Dependant
                dependant = Dependant.query.get(self.dependant_id)
                if dependant:
                    return f"{dependant.first_name} {dependant.last_name}"
            
            if self.client_id:
                # Try to get client name
                from app.models.client import Client
                client = Client.query.get(self.client_id)
                if client:
                    return f"{client.first_name} {client.last_name}"
            
            return "Unknown"

    @property
    def is_for_dependant(self):
        """Check if this appointment is for a dependant."""
        return self.dependant_id is not None

    @property
    def calculated_duration_minutes(self):
        """Calculate duration from start_time and end_time."""
        if self.start_time and self.end_time:
            delta = self.end_time - self.start_time
            return int(delta.total_seconds() / 60)
        return 0

    def get_duration_minutes(self):
        """Get duration in minutes, preferring stored value over calculated."""
        # Use stored duration_minutes if available, otherwise calculate
        if hasattr(self, 'duration_minutes') and self.duration_minutes:
            return self.duration_minutes
        return self.calculated_duration_minutes

    @property
    def is_completed(self):
        return self.status == 'completed'

    @property
    def is_cancelled(self):
        return self.status == 'cancelled'

    @property
    def is_no_show(self):
        return self.status == 'no-show'

    @property
    def is_scheduled(self):
        return self.status == 'scheduled'

    @property
    def is_generated_from_recurring(self):
        """Check if this appointment was generated from a recurring schedule."""
        return self.recurring_schedule_id is not None