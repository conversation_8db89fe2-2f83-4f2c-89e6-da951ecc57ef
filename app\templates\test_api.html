<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test Page</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e9; color: #2e7d32; }
        button { padding: 8px 16px; margin: 5px; }
        input { padding: 8px; margin: 5px; width: 200px; }
    </style>
</head>
<body>
    <h1>🧪 API Test Page</h1>
    <p>This page tests the client/dependant search API endpoints.</p>

    <div class="test-section">
        <h3>🔍 Test Client/Dependant Search</h3>
        <input type="text" id="searchQuery" placeholder="Enter search term (e.g., 'da')" value="da">
        <button onclick="testSearch()">Test Search</button>
        <div id="searchResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>📋 Test Individual Endpoints</h3>
        <button onclick="testEndpoint('/api/clients-and-dependants/search?q=da')">Test Clients & Dependants</button>
        <button onclick="testEndpoint('/api/dependants/search?q=da')">Test Dependants Only</button>
        <button onclick="testEndpoint('/api/clients/search?q=da')">Test Clients Only</button>
        <button onclick="testEndpoint('/api/tutors')">Test Tutors</button>
        <div id="endpointResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>🔧 Debug Information</h3>
        <button onclick="showDebugInfo()">Show Debug Info</button>
        <div id="debugInfo" class="result"></div>
    </div>

    <script>
        function testSearch() {
            const query = document.getElementById('searchQuery').value;
            const resultDiv = document.getElementById('searchResult');
            
            if (query.length < 2) {
                resultDiv.innerHTML = '<div class="error">Query must be at least 2 characters</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div>🔄 Searching...</div>';
            
            fetch(`/api/clients-and-dependants/search?q=${encodeURIComponent(query)}`)
                .then(response => {
                    console.log('Response status:', response.status);
                    console.log('Response headers:', response.headers);
                    
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    
                    return response.json();
                })
                .then(data => {
                    console.log('Search results:', data);
                    
                    if (Array.isArray(data) && data.length > 0) {
                        let html = '<div class="success">✅ Success! Found ' + data.length + ' results:</div>';
                        data.forEach(item => {
                            html += `<div>• ${item.name} (${item.type}) - ${item.email}</div>`;
                        });
                        resultDiv.innerHTML = html;
                    } else if (Array.isArray(data) && data.length === 0) {
                        resultDiv.innerHTML = '<div class="success">✅ Success! No results found for "' + query + '"</div>';
                    } else {
                        resultDiv.innerHTML = '<div class="error">❌ Unexpected response format: ' + JSON.stringify(data) + '</div>';
                    }
                })
                .catch(error => {
                    console.error('Search error:', error);
                    resultDiv.innerHTML = '<div class="error">❌ Error: ' + error.message + '</div>';
                });
        }
        
        function testEndpoint(url) {
            const resultDiv = document.getElementById('endpointResult');
            resultDiv.innerHTML = '<div>🔄 Testing ' + url + '...</div>';
            
            fetch(url)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    resultDiv.innerHTML = '<div class="success">✅ ' + url + ' - Success! Response: ' + JSON.stringify(data, null, 2) + '</div>';
                })
                .catch(error => {
                    resultDiv.innerHTML = '<div class="error">❌ ' + url + ' - Error: ' + error.message + '</div>';
                });
        }
        
        function showDebugInfo() {
            const debugDiv = document.getElementById('debugInfo');
            
            const info = {
                'Current URL': window.location.href,
                'User Agent': navigator.userAgent,
                'Cookies': document.cookie,
                'Local Storage': Object.keys(localStorage).length + ' items',
                'Session Storage': Object.keys(sessionStorage).length + ' items'
            };
            
            let html = '<div class="success">🔧 Debug Information:</div>';
            for (const [key, value] of Object.entries(info)) {
                html += `<div><strong>${key}:</strong> ${value}</div>`;
            }
            
            debugDiv.innerHTML = html;
        }
        
        // Auto-test on page load
        window.addEventListener('load', function() {
            console.log('🧪 API Test Page loaded');
            showDebugInfo();
        });
    </script>
</body>
</html>
