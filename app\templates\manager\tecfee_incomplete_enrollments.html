{% extends "base.html" %}

{% block title %}Inscriptions TECFÉE Incomplètes - TutorAide{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col">
        <h2 class="mb-3">Inscriptions TECFÉE Incomplètes</h2>
        <p class="text-muted">G<PERSON>rez les inscriptions en attente et abandonnées</p>
    </div>
    <div class="col-auto">
        <a href="{{ url_for('manager.tecfee_dashboard') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Retour au tableau TECFÉE
        </a>
    </div>
</div>

{% if enrollments_data %}
    <div class="card">
        <div class="card-header">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{{ enrollments_data|length }} inscription(s) incomplète(s)</h5>
                <div>
                    <span class="badge bg-warning">En attente</span>
                    <span class="badge bg-danger">Expirée</span>
                    <span class="badge bg-info">Email envoyé</span>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Client</th>
                            <th>Email</th>
                            <th>Téléphone</th>
                            <th>Date de création</th>
                            <th>Temps écoulé</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for data in enrollments_data %}
                            <tr class="{{ 'table-danger' if data.is_expired else '' }}">
                                <td>
                                    <strong>{{ data.client.first_name }} {{ data.client.last_name }}</strong>
                                    <br>
                                    <small class="text-muted">ID: {{ data.enrollment.id }}</small>
                                </td>
                                <td>{{ data.user.email }}</td>
                                <td>{{ data.client.phone or '-' }}</td>
                                <td>
                                    {{ data.enrollment.created_at.strftime('%Y-%m-%d %H:%M') }}
                                </td>
                                <td>
                                    {% if data.is_expired %}
                                        <span class="text-danger">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            {{ data.hours_elapsed }}h (Expirée)
                                        </span>
                                    {% else %}
                                        <span class="text-warning">
                                            <i class="fas fa-clock"></i>
                                            {{ data.hours_elapsed }}h
                                            <br>
                                            <small>{{ data.time_remaining|round(1) }}h restantes</small>
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'danger' if data.is_expired else 'warning' }}">
                                        {{ 'Expirée' if data.is_expired else 'En attente' }}
                                    </span>
                                    {% if data.has_recovery_token %}
                                        <span class="badge bg-info">
                                            <i class="fas fa-envelope"></i> Email envoyé
                                        </span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        {% if not data.is_expired %}
                                            <form method="POST" action="{{ url_for('manager.send_enrollment_reminder', id=data.enrollment.id) }}" class="d-inline">
                                                <button type="submit" class="btn btn-info btn-sm" 
                                                        title="Envoyer un email de rappel">
                                                    <i class="fas fa-envelope"></i>
                                                </button>
                                            </form>
                                        {% endif %}
                                        
                                        <form method="POST" action="{{ url_for('manager.cancel_incomplete_enrollment', id=data.enrollment.id) }}" 
                                              class="d-inline"
                                              onsubmit="return confirm('Êtes-vous sûr de vouloir annuler cette inscription?');">
                                            <button type="submit" class="btn btn-danger btn-sm" 
                                                    title="Annuler l'inscription">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="row mt-4">
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <h3 class="text-warning">{{ enrollments_data|selectattr('is_expired', 'false')|list|length }}</h3>
                    <p class="mb-0">En attente (< 24h)</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <h3 class="text-danger">{{ enrollments_data|selectattr('is_expired', 'true')|list|length }}</h3>
                    <p class="mb-0">Expirées (> 24h)</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <h3 class="text-info">{{ enrollments_data|selectattr('has_recovery_token', 'true')|list|length }}</h3>
                    <p class="mb-0">Emails envoyés</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Help Text -->
    <div class="alert alert-info mt-4">
        <h5><i class="fas fa-info-circle"></i> Gestion des inscriptions incomplètes</h5>
        <ul class="mb-0">
            <li><strong>En attente:</strong> L'inscription a été commencée mais le paiement n'a pas été complété</li>
            <li><strong>Expirée:</strong> Plus de 24 heures se sont écoulées depuis le début de l'inscription</li>
            <li><strong>Email de rappel:</strong> Envoie un lien de récupération valide 48 heures</li>
            <li><strong>Nettoyage automatique:</strong> Les inscriptions expirées sont supprimées automatiquement après 24 heures</li>
        </ul>
    </div>

{% else %}
    <div class="alert alert-success">
        <i class="fas fa-check-circle"></i> Aucune inscription incomplète trouvée.
    </div>
{% endif %}

<style>
.table-danger {
    background-color: #f8d7da !important;
}

.btn-group-sm .btn {
    margin: 0 2px;
}
</style>
{% endblock %}