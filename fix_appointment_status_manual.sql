-- Manual fix for appointment status issues
-- Run these commands in your PostgreSQL database

-- Step 1: Expand the status column to accommodate longer status values
ALTER TABLE appointments ALTER COLUMN status TYPE VARCHAR(50);

-- Step 2: Update any existing truncated status values
UPDATE appointments SET status = 'awaiting_confirmation' WHERE status = 'awaiting_confirmatio';

-- Step 3: Drop any existing status check constraint that might be blocking the update
ALTER TABLE appointments DROP CONSTRAINT IF EXISTS appointments_status_check;

-- Step 4: Create a new check constraint that includes all valid status values
ALTER TABLE appointments ADD CONSTRAINT appointments_status_check 
CHECK (status IN (
    'scheduled', 
    'completed', 
    'cancelled', 
    'no-show', 
    'awaiting_confirmation',
    'confirmed',
    'rescheduled'
));

-- Step 5: Verify the changes
SELECT DISTINCT status, LENGTH(status) as status_length FROM appointments ORDER BY status;

-- Step 6: Check the constraint was created properly
SELECT conname, consrc 
FROM pg_constraint 
WHERE conrelid = 'appointments'::regclass 
AND contype = 'c' 
AND conname = 'appointments_status_check';

SELECT 'Appointment status fix completed successfully!' as result;
