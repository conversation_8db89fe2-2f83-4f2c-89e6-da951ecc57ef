# app/forms/prospect_forms.py
from flask_wtf import FlaskForm
from wtforms import StringField, SelectField, TextAreaField, SubmitField, HiddenField, PasswordField
from wtforms.validators import DataRequired, Email, Length, Optional, EqualTo

class TecfeeClientRegistrationForm(FlaskForm):
    """Form for direct client registration for TECFÉE program."""

    # Personal Information
    first_name = StringField('Prénom', validators=[
        DataRequired(message='Le prénom est requis'),
        Length(min=2, max=100, message='Le prénom doit contenir entre 2 et 100 caractères')
    ])

    last_name = StringField('Nom de famille', validators=[
        DataRequired(message='Le nom de famille est requis'),
        Length(min=2, max=100, message='Le nom de famille doit contenir entre 2 et 100 caractères')
    ])

    email = StringField('Adresse courriel', validators=[
        DataRequired(message='L\'adresse courriel est requise'),
        Email(message='Veuillez entrer une adresse courriel valide')
    ])

    password = PasswordField('Mot de passe', validators=[
        DataRequired(message='Le mot de passe est requis'),
        Length(min=8, message='Le mot de passe doit contenir au moins 8 caractères')
    ])

    confirm_password = PasswordField('Confirmer le mot de passe', validators=[
        DataRequired(message='La confirmation du mot de passe est requise'),
        EqualTo('password', message='Les mots de passe doivent correspondre')
    ])

    phone = StringField('Numéro de téléphone', validators=[
        DataRequired(message='Le numéro de téléphone est requis'),
        Length(max=20, message='Le numéro de téléphone ne peut pas dépasser 20 caractères')
    ])

    # Structured address fields
    civic_number = StringField('Numéro civique', validators=[
        DataRequired(message='Le numéro civique est requis'),
        Length(max=20, message='Le numéro civique ne peut pas dépasser 20 caractères')
    ])

    street = StringField('Rue', validators=[
        DataRequired(message='La rue est requise'),
        Length(max=255, message='La rue ne peut pas dépasser 255 caractères')
    ])

    city = StringField('Ville', validators=[
        DataRequired(message='La ville est requise'),
        Length(max=100, message='La ville ne peut pas dépasser 100 caractères')
    ])

    postal_code = StringField('Code postal', validators=[
        DataRequired(message='Le code postal est requis'),
        Length(max=10, message='Le code postal ne peut pas dépasser 10 caractères')
    ])

    province = SelectField('Province', choices=[
        ('Quebec', 'Québec'),
        ('Ontario', 'Ontario'),
        ('British Columbia', 'Colombie-Britannique'),
        ('Alberta', 'Alberta'),
        ('Manitoba', 'Manitoba'),
        ('Saskatchewan', 'Saskatchewan'),
        ('Nova Scotia', 'Nouvelle-Écosse'),
        ('New Brunswick', 'Nouveau-Brunswick'),
        ('Newfoundland and Labrador', 'Terre-Neuve-et-Labrador'),
        ('Prince Edward Island', 'Île-du-Prince-Édouard'),
        ('Northwest Territories', 'Territoires du Nord-Ouest'),
        ('Nunavut', 'Nunavut'),
        ('Yukon', 'Yukon')
    ], default='Quebec', validators=[DataRequired(message='La province est requise')])

    country = SelectField('Pays', choices=[
        ('Canada', 'Canada'),
        ('United States', 'États-Unis'),
        ('France', 'France'),
        ('Other', 'Autre')
    ], default='Canada', validators=[DataRequired(message='Le pays est requis')])

    # Program Information
    interested_program = HiddenField(default='TECFEE')

    pricing_preference = SelectField('Option de tarification', choices=[
        ('per_session', 'Par module (44,99$ par module)'),
        ('full_package', 'Forfait complet (399,00$ pour les 12 modules)')
    ], validators=[DataRequired(message='Veuillez sélectionner une option de tarification')])

    source = HiddenField(default='website')
    preferred_language = HiddenField(default='fr')

    # Contact Preferences
    preferred_contact_method = SelectField('Méthode de contact préférée', choices=[
        ('email', 'Courriel'),
        ('phone', 'Téléphone')
    ], default='email', validators=[Optional()])

    notes = TextAreaField('Notes ou commentaires', validators=[
        Optional(),
        Length(max=500, message='Les notes ne peuvent pas dépasser 500 caractères')
    ])

    submit = SubmitField('S\'inscrire au programme TECFÉE')



