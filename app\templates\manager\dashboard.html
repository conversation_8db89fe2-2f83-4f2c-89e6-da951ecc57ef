<!-- app/templates/manager/dashboard.html -->
{% extends "base.html" %}

{% block title %}{{ t('manager.dashboard.title') }} - {{ t('manager.title_full', 'Tutoring Appointment System') }}{% endblock %}

{% block content %}
<div class="page-header fade-in-up">
    <h1>{{ t('manager.dashboard.title') }}</h1>
    <p>{{ t('manager.dashboard.subtitle') }}</p>
</div>

<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-md-4 mb-3">
        <div class="stat-card fade-in-up">
            <div class="stat-number">{{ clients_count }}</div>
            <div class="stat-label">{{ t('manager.dashboard.stats.total_clients') }}</div>
            <div class="mt-3">
                <a href="{{ url_for('manager.clients_list') }}" class="btn btn-sm btn-primary">{{ t('manager.navigation.view_all') }}</a>
            </div>
        </div>
    </div>

    <div class="col-md-4 mb-3">
        <div class="stat-card fade-in-up">
            <div class="stat-number">{{ tutors_count }}</div>
            <div class="stat-label">{{ t('manager.dashboard.stats.active_tutors') }}</div>
            <div class="mt-3">
                <a href="{{ url_for('manager.tutors_list') }}" class="btn btn-sm btn-primary">{{ t('manager.navigation.view_all') }}</a>
            </div>
        </div>
    </div>

    <div class="col-md-4 mb-3">
        <div class="stat-card fade-in-up">
            <i class="fas fa-calendar-alt" style="font-size: 2.5rem; color: var(--primary-red); margin-bottom: 16px;"></i>
            <div class="stat-label">{{ t('manager.dashboard.stats.appointment_schedule') }}</div>
            <div class="mt-3">
                <a href="{{ url_for('manager.schedule') }}" class="btn btn-sm btn-primary">{{ t('buttons.view_details') }}</a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Upcoming Appointments -->
    <div class="col-md-6 mb-4">
        <div class="card h-100 fade-in-up">
            <div class="card-header">
                <h5 class="mb-0">{{ t('manager.dashboard.sections.upcoming_appointments') }}</h5>
            </div>
            <div class="card-body">
                {% if upcoming_appointments %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{{ t('manager.appointments.date_time') }}</th>
                                    <th>{{ t('manager.appointments.client') }}</th>
                                    <th>{{ t('manager.appointments.tutor') }}</th>
                                    <th>{{ t('manager.appointments.actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for appointment in upcoming_appointments %}
                                    <tr>
                                        <td>{{ appointment.start_time.strftime('%Y-%m-%d %H:%M') }}</td>
                                        <td>
                                            {% set client = appointment.client %}
                                            {{ client.first_name }} {{ client.last_name }}
                                        </td>
                                        <td>
                                            {% set tutor = appointment.tutor %}
                                            {{ tutor.first_name }} {{ tutor.last_name }}
                                        </td>
                                        <td>
                                            <a href="{{ url_for('manager.edit_appointment', id=appointment.id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ url_for('manager.schedule') }}" class="btn btn-outline-primary">{{ t('manager.navigation.view_all_appointments') }}</a>
                    </div>
                {% else %}
                    <p class="text-muted text-center my-4">{{ t('manager.dashboard.messages.no_upcoming_appointments') }}</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Unpaid Invoices -->
    <div class="col-md-6 mb-4">
        <div class="card h-100 fade-in-up">
            <div class="card-header">
                <h5 class="mb-0">{{ t('manager.dashboard.sections.unpaid_invoices') }}</h5>
            </div>
            <div class="card-body">
                {% if unpaid_invoices %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{{ t('manager.invoices.table.invoice_number') }}</th>
                                    <th>{{ t('manager.invoices.table.client') }}</th>
                                    <th>{{ t('manager.invoices.table.amount') }}</th>
                                    <th>{{ t('manager.invoices.table.due_date') }}</th>
                                    <th>{{ t('manager.invoices.table.actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invoice in unpaid_invoices %}
                                    <tr class="{{ 'table-danger' if invoice.is_overdue else '' }}">
                                        <td>{{ invoice.id }}</td>
                                        <td>
                                            {% set client = invoice.client %}
                                            {{ client.first_name }} {{ client.last_name }}
                                        </td>
                                        <td>${{ "%.2f"|format(invoice.total_amount) }}</td>
                                        <td>{{ invoice.due_date.strftime('%Y-%m-%d') }}</td>
                                        <td>
                                            <a href="{{ url_for('manager.view_invoice', id=invoice.id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ url_for('manager.invoices_list') }}" class="btn btn-outline-primary">{{ t('manager.navigation.view_all_invoices') }}</a>
                    </div>
                {% else %}
                    <p class="text-muted text-center my-4">{{ t('manager.dashboard.messages.no_unpaid_invoices') }}</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Past Appointments Needing Update -->
    <div class="col-md-6 mb-4">
        <div class="card h-100 fade-in-up" style="border-left: 4px solid var(--primary-red-dark);">
            <div class="card-header">
                <h5 class="mb-0" style="color: var(--primary-red-dark);">{{ t('manager.dashboard.sections.past_appointments_needing_update') }}</h5>
            </div>
            <div class="card-body">
                {% if past_appointments_needing_update %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{{ t('manager.appointments.date_time') }}</th>
                                    <th>{{ t('manager.appointments.client') }}</th>
                                    <th>{{ t('manager.appointments.tutor') }}</th>
                                    <th>{{ t('manager.appointments.actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for appointment in past_appointments_needing_update %}
                                    <tr>
                                        <td>{{ appointment.start_time.strftime('%Y-%m-%d %H:%M') }}</td>
                                        <td>
                                            {% set client = appointment.client %}
                                            {{ client.first_name }} {{ client.last_name }}
                                        </td>
                                        <td>
                                            {% set tutor = appointment.tutor %}
                                            {{ tutor.first_name }} {{ tutor.last_name }}
                                        </td>
                                        <td>
                                            <a href="{{ url_for('manager.edit_appointment', id=appointment.id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i> {{ t('manager.appointments.update') }}
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center my-4">{{ t('manager.dashboard.messages.no_past_appointments_need_updating') }}</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Pending Time-Off Requests -->
    <div class="col-md-6 mb-4">
        <div class="card h-100 fade-in-up" style="border-left: 4px solid var(--warning-red);">
            <div class="card-header">
                <h5 class="mb-0" style="color: var(--primary-red);">{{ t('manager.dashboard.sections.pending_time_off_requests') }}</h5>
            </div>
            <div class="card-body">
                {% if pending_time_off_requests %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>{{ t('manager.appointments.tutor') }}</th>
                                    <th>{{ t('invoices.date', 'Dates') }}</th>
                                    <th>{{ t('appointments.duration') }}</th>
                                    <th>{{ t('manager.appointments.actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for request in pending_time_off_requests %}
                                    <tr>
                                        <td>{{ request.tutor.first_name }} {{ request.tutor.last_name }}</td>
                                        <td>{{ request.start_date.strftime('%Y-%m-%d') }} to {{ request.end_date.strftime('%Y-%m-%d') }}</td>
                                        <td>{{ request.duration_days }} {{ t('manager.appointments.duration_days') if request.duration_days == 1 else t('manager.appointments.duration_days_plural') }}</td>
                                        <td>
                                            <a href="{{ url_for('manager.view_time_off_request', id=request.id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i> {{ t('manager.appointments.review') }}
                                            </a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="text-center mt-3">
                        <a href="{{ url_for('manager.time_off_requests') }}" class="btn btn-outline-primary">{{ t('manager.navigation.view_all_requests') }}</a>
                    </div>
                {% else %}
                    <p class="text-muted text-center my-4">{{ t('manager.dashboard.messages.no_pending_time_off_requests') }}</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card fade-in-up">
            <div class="card-header">
                <h5 class="mb-0">{{ t('manager.dashboard.sections.quick_actions') }}</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('manager.new_appointment') }}" class="btn btn-lg btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center align-items-center p-3">
                            <i class="fas fa-calendar-plus mb-2 fa-2x"></i>
                            <span>{{ t('manager.dashboard.quick_actions.new_appointment') }}</span>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('manager.new_client') }}" class="btn btn-lg btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center align-items-center p-3">
                            <i class="fas fa-user-plus mb-2 fa-2x"></i>
                            <span>{{ t('manager.dashboard.quick_actions.new_client') }}</span>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('manager.new_tutor') }}" class="btn btn-lg btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center align-items-center p-3">
                            <i class="fas fa-chalkboard-teacher mb-2 fa-2x"></i>
                            <span>{{ t('manager.dashboard.quick_actions.new_tutor') }}</span>
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('manager.generate_invoices') }}" class="btn btn-lg btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center align-items-center p-3">
                            <i class="fas fa-file-invoice-dollar mb-2 fa-2x"></i>
                            <span>{{ t('manager.dashboard.quick_actions.generate_invoices') }}</span>
                        </a>
                    </div>
                </div>

                <!-- Payment & Reports Actions -->
                <div class="row text-center mt-3">
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('manager.weekly_payment_report') }}" class="btn btn-lg btn-outline-success w-100 h-100 d-flex flex-column justify-content-center align-items-center p-3">
                            <i class="fas fa-calendar-check mb-2 fa-2x"></i>
                            <span>{{ t('manager.dashboard.quick_actions.weekly_payment_report') }}</span>
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('manager.new_subscription_plan') }}" class="btn btn-lg btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center align-items-center p-3">
                            <i class="fas fa-tag mb-2 fa-2x"></i>
                            <span>{{ t('manager.dashboard.quick_actions.create_subscription_plan') }}</span>
                        </a>
                    </div>
                    <div class="col-md-4 mb-3">
                        <a href="{{ url_for('manager.new_subscription') }}" class="btn btn-lg btn-outline-primary w-100 h-100 d-flex flex-column justify-content-center align-items-center p-3">
                            <i class="fas fa-sync mb-2 fa-2x"></i>
                            <span>{{ t('manager.dashboard.quick_actions.assign_subscription_to_client') }}</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}