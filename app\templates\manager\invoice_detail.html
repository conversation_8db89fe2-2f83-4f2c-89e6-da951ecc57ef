<!-- app/templates/manager/invoice_detail.html -->
{% extends "base.html" %}

{% block title %}Invoice #{{ invoice.id }} - Tutoring Appointment System{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">Invoice #{{ invoice.id }}</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('manager.invoices_list') }}" class="btn btn-outline-primary me-2">
            <i class="fas fa-arrow-left"></i> Back to Invoices
        </a>

        <div class="btn-group">
            <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-cog"></i> Actions
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
                <li>
                    <button type="button" class="dropdown-item" data-bs-toggle="modal" data-bs-target="#emailInvoiceModal">
                        <i class="fas fa-envelope me-2"></i> Email Invoice
                    </button>
                </li>
                <li>
                    <a href="javascript:window.print();" class="dropdown-item">
                        <i class="fas fa-print me-2"></i> Print Invoice
                    </a>
                </li>
                <li><hr class="dropdown-divider"></li>
                <li>
                    <button type="button" class="dropdown-item text-danger" data-bs-toggle="modal" data-bs-target="#deleteInvoiceModal">
                        <i class="fas fa-trash me-2"></i> Delete Invoice
                    </button>
                </li>
            </ul>
        </div>
    </div>
</div>

<div class="card shadow">
    <div class="card-body">
        <div class="row mb-4">
            <div class="col-sm-6">
                <h6 class="mb-3">From:</h6>
                <div><strong>TutorAide Inc.</strong></div>
                <div>2958 Guy-Hoffmann</div>
                <div>Saint-Laurent, QC, H4R 2R1</div>
                <div>Courriel: <EMAIL></div>
                <div>Téléphone: (*************</div>
            </div>

            <div class="col-sm-6">
                <h6 class="mb-3">To:</h6>
                <div><strong>{{ invoice.client.first_name }} {{ invoice.client.last_name }}</strong></div>
                <div>{{ invoice.client.address|nl2br }}</div>
                <div>Email: {{ invoice.client.email or (invoice.client.user.email if invoice.client.user else '') }}</div>
                <div>Phone: {{ invoice.client.phone }}</div>
            </div>
        </div>

        <!-- Clients with access to this invoice -->
        <div class="row mb-4">
            <div class="col-12">
                <h6 class="mb-2">Clients with Access:</h6>
                <div class="d-flex flex-wrap">
                    {% for client in accessible_clients %}
                        <span class="badge bg-info me-2 mb-2 p-2">
                            {{ client.first_name }} {{ client.last_name }}
                            {% if client.id == invoice.client_id %}
                                <i class="fas fa-star ms-1" title="Primary billing client"></i>
                            {% endif %}
                            {% if invoice.paid_by_client_id == client.id %}
                                <i class="fas fa-credit-card ms-1" title="Paid by this client"></i>
                            {% endif %}
                        </span>
                    {% endfor %}
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-sm-6">
                <div><strong>Invoice Date:</strong> {{ invoice.invoice_date.strftime('%Y-%m-%d') }}</div>
                <div><strong>Due Date:</strong> {{ invoice.due_date.strftime('%Y-%m-%d') }}</div>
            </div>

            <div class="col-sm-6 text-end">
                <div class="mb-2">
                    <span class="badge bg-{{ 'success' if invoice.status == 'paid' else 'danger' if invoice.is_overdue else 'warning' }} p-2">
                        {{ 'Overdue' if invoice.is_overdue else invoice.status | capitalize }}
                    </span>
                </div>
                {% if invoice.status == 'paid' %}
                    {% if paying_client %}
                        <div><small>Paid by: {{ paying_client.first_name }} {{ paying_client.last_name }} on {{ invoice.paid_date.strftime('%Y-%m-%d') }}</small></div>
                    {% endif %}
                    {% if invoice.stripe_payment_intent_id %}
                        <div><small>Payment ID: {{ invoice.stripe_payment_intent_id }}</small></div>
                    {% endif %}
                {% endif %}
            </div>
        </div>

        <div class="table-responsive-sm">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th class="text-center">#</th>
                        <th>Service</th>
                        <th>Client</th>
                        <th class="text-center">Date</th>
                        <th class="text-end">Amount</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in invoice.items %}
                        <tr>
                            <td class="text-center">{{ loop.index }}</td>
                            <td>{{ item.description }}</td>
                            <td>
                                {% set appointment = item.appointment %}
                                {% if appointment and appointment.client %}
                                    {{ appointment.client.first_name }} {{ appointment.client.last_name }}
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                {% if appointment %}
                                    {{ appointment.start_time.strftime('%Y-%m-%d') }}
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td class="text-end">${{ "%.2f"|format(item.amount) }}</td>
                        </tr>
                    {% endfor %}
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="4" class="text-end"><strong>Total:</strong></td>
                        <td class="text-end"><strong>${{ "%.2f"|format(invoice.total_amount) }}</strong></td>
                    </tr>
                </tfoot>
            </table>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <div class="mb-4">
                    <h6>Notes:</h6>
                    <p class="text-muted">{{ invoice.notes|default('Thank you for your business!', true) }}</p>
                </div>
            </div>

            <div class="col-lg-4">
                <div class="bg-light p-3 rounded">
                    <h6 class="mb-3">Payment Summary</h6>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Subtotal:</span>
                        <span>${{ "%.2f"|format(invoice.total_amount) }}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Tax:</span>
                        <span>$0.00</span>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between fw-bold">
                        <span>Total:</span>
                        <span>${{ "%.2f"|format(invoice.total_amount) }}</span>
                    </div>

                    {% if invoice.status == 'paid' %}
                        <div class="text-center mt-3">
                            <span class="badge bg-success p-2">
                                Paid{% if invoice.paid_date %} on {{ invoice.paid_date|format_date }}{% endif %}
                            </span>
                        </div>
                    {% elif invoice.is_overdue %}
                        <div class="text-center mt-3">
                            <span class="badge bg-danger p-2">Overdue</span>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    <div class="card-footer">
        <!-- Status Update Form -->
        <form action="{{ url_for('manager.update_invoice_status', id=invoice.id) }}" method="POST" class="row g-3">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
            <div class="col-md-4">
                <label for="status" class="form-label">Update Status</label>
                <select name="status" id="status" class="form-select">
                    <option value="pending" {% if invoice.status == 'pending' %}selected{% endif %}>Pending</option>
                    <option value="paid" {% if invoice.status == 'paid' %}selected{% endif %}>Paid</option>
                    <option value="cancelled" {% if invoice.status == 'cancelled' %}selected{% endif %}>Cancelled</option>
                </select>
            </div>
            <div class="col-md-8 d-flex align-items-end">
                <button type="submit" class="btn btn-primary">Update Status</button>
            </div>
        </form>
    </div>
</div>

<!-- Email Invoice Modal -->
<div class="modal fade" id="emailInvoiceModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Email Invoice</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('manager.email_invoice', id=invoice.id) }}" method="POST">
                <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <!-- Dropdown to select from client emails -->
                        <select name="email" id="email" class="form-select">
                            {% for client in accessible_clients %}
                                <option value="{{ client.email or (client.user.email if client.user else '') }}">
                                    {{ client.first_name }} {{ client.last_name }} &lt;{{ client.email or (client.user.email if client.user else '') }}&gt;
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="message" class="form-label">Message (Optional)</label>
                        <textarea class="form-control" id="message" name="message" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Send Email</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Invoice Modal -->
<div class="modal fade" id="deleteInvoiceModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Invoice</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete Invoice #{{ invoice.id }}?</p>
                <p class="text-danger">This action cannot be undone. All invoice items will also be deleted.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="{{ url_for('manager.delete_invoice', id=invoice.id) }}" method="POST">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}