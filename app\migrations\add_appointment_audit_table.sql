-- Migration: Add appointment audit table
-- This table tracks all changes to appointments including who made the change and when

CREATE TABLE IF NOT EXISTS appointment_audit (
    audit_id SERIAL PRIMARY KEY,
    appointment_id INTEGER NOT NULL,
    action VARCHAR(20) NOT NULL CHECK (action IN ('create', 'update', 'delete', 'cancel')),
    user_id INTEGER REFERENCES users(user_id) ON DELETE SET NULL,
    user_role VARCHAR(50),
    user_email VARCHAR(255),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Old and new values for tracking changes
    old_values JSONB,
    new_values JSONB,
    
    -- Specific fields for quick querying
    old_status VARCHAR(50),
    new_status VARCHAR(50),
    old_tutor_id INTEGER,
    new_tutor_id INTEGER,
    old_client_id INTEGER,
    new_client_id INTEGER,
    old_start_time TIMESTAMP,
    new_start_time TIMESTAMP,
    
    -- Additional context
    ip_address INET,
    user_agent TEXT,
    notes TEXT
);

-- Create indexes for better query performance
CREATE INDEX idx_appointment_audit_appointment_id ON appointment_audit(appointment_id);
CREATE INDEX idx_appointment_audit_user_id ON appointment_audit(user_id);
CREATE INDEX idx_appointment_audit_timestamp ON appointment_audit(timestamp);
CREATE INDEX idx_appointment_audit_action ON appointment_audit(action);

-- Add created_by to appointments table if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointments' 
                   AND column_name = 'created_by') THEN
        ALTER TABLE appointments ADD COLUMN created_by INTEGER REFERENCES users(user_id) ON DELETE SET NULL;
    END IF;
END $$;

-- Add modified_by to appointments table
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointments' 
                   AND column_name = 'modified_by') THEN
        ALTER TABLE appointments ADD COLUMN modified_by INTEGER REFERENCES users(id) ON DELETE SET NULL;
    END IF;
END $$;

-- Add deleted_by to appointments table (for soft deletes)
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointments' 
                   AND column_name = 'deleted_by') THEN
        ALTER TABLE appointments ADD COLUMN deleted_by INTEGER REFERENCES users(id) ON DELETE SET NULL;
    END IF;
END $$;

-- Add deleted_at to appointments table (for soft deletes)
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointments' 
                   AND column_name = 'deleted_at') THEN
        ALTER TABLE appointments ADD COLUMN deleted_at TIMESTAMP;
    END IF;
END $$;

-- Create a function to automatically create audit records
CREATE OR REPLACE FUNCTION audit_appointment_changes()
RETURNS TRIGGER AS $$
DECLARE
    audit_action VARCHAR(20);
    old_record JSONB;
    new_record JSONB;
BEGIN
    -- Determine the action
    IF TG_OP = 'INSERT' THEN
        audit_action := 'create';
        old_record := NULL;
        new_record := to_jsonb(NEW);
    ELSIF TG_OP = 'UPDATE' THEN
        audit_action := 'update';
        old_record := to_jsonb(OLD);
        new_record := to_jsonb(NEW);
        
        -- Check if this is actually a cancellation
        IF OLD.status != 'cancelled' AND NEW.status = 'cancelled' THEN
            audit_action := 'cancel';
        END IF;
    ELSIF TG_OP = 'DELETE' THEN
        audit_action := 'delete';
        old_record := to_jsonb(OLD);
        new_record := NULL;
    END IF;
    
    -- Insert audit record
    INSERT INTO appointment_audit (
        appointment_id,
        action,
        user_id,
        old_values,
        new_values,
        old_status,
        new_status,
        old_tutor_id,
        new_tutor_id,
        old_client_id,
        new_client_id,
        old_start_time,
        new_start_time
    ) VALUES (
        COALESCE(NEW.id, OLD.id),
        audit_action,
        COALESCE(NEW.modified_by, NEW.created_by, OLD.modified_by, OLD.created_by),
        old_record,
        new_record,
        OLD.status,
        NEW.status,
        OLD.tutor_id,
        NEW.tutor_id,
        OLD.client_id,
        NEW.client_id,
        OLD.start_time,
        NEW.start_time
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for appointment changes
DROP TRIGGER IF EXISTS appointment_audit_trigger ON appointments;
CREATE TRIGGER appointment_audit_trigger
AFTER INSERT OR UPDATE OR DELETE ON appointments
FOR EACH ROW
EXECUTE FUNCTION audit_appointment_changes();

-- Grant appropriate permissions
GRANT SELECT ON appointment_audit TO PUBLIC;
GRANT INSERT ON appointment_audit TO PUBLIC;