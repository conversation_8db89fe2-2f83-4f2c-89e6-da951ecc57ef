<!-- app/templates/client/invoice_detail.html -->
{% extends "base.html" %}

{% block title %}{{ t('invoices.title') }}{{ invoice.id }} - TutorAide Inc.{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">{{ t('invoices.title') }}{{ invoice.id }}</h2>
    </div>
    <div class="col-md-4 text-end">
        {% if invoice.status == 'pending' %}
            <a href="{{ url_for('client.pay_invoice', id=invoice.id) }}" class="btn btn-success">
                <i class="fas fa-credit-card"></i> {{ t('invoices.pay_invoice') }}
            </a>
        {% endif %}
        <a href="javascript:window.print();" class="btn btn-outline-secondary ms-2">
            <i class="fas fa-print"></i> {{ t('invoices.print') }}
        </a>
    </div>
</div>

<div class="card shadow">
    <div class="card-body">
        <div class="row mb-4">
            <div class="col-sm-6">
                <h6 class="mb-3">{{ t('invoices.from') }}</h6>
                <div><strong>TutorAide Inc.</strong></div>
                <div>2958 Guy-Hoffmann</div>
                <div>Saint-Laurent, QC, H4R 2R1</div>
                <div>Email/Courriel: <EMAIL></div>
                <div>Phone/Téléphone: ************</div>
            </div>

            <div class="col-sm-6">
                <h6 class="mb-3">{{ t('invoices.to') }}</h6>
                <div><strong>{{ invoice.client.first_name }} {{ invoice.client.last_name }}</strong></div>
                <div>{{ invoice.client.address|nl2br }}</div>
                <div>Email: {{ invoice.client.email or (invoice.client.user.email if invoice.client.user else '') }}</div>
                <div>Phone: {{ invoice.client.phone }}</div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-sm-6">
                <div><strong>{{ t('invoices.invoice_date') }}</strong> {{ invoice.invoice_date.strftime('%Y-%m-%d') }}</div>
                <div><strong>{{ t('invoices.due_date') }}:</strong> {{ invoice.due_date.strftime('%Y-%m-%d') }}</div>
            </div>

            <div class="col-sm-6 text-end">
                <div class="mb-2">
                    <span class="badge bg-{{ 'success' if invoice.status == 'paid' else 'danger' if invoice.is_overdue else 'warning' }}">
                        {{ t('invoices.overdue') if invoice.is_overdue else t('invoices.' + invoice.status) }}
                    </span>
                </div>
                {% if invoice.status == 'paid' %}
                    {% if paying_client %}
                        <div>
                            <small>
                                {{ t('invoices.paid_by') }}:
                                {% if paying_client.id == client.id %}
                                    {{ t('invoices.you') }}
                                {% else %}
                                    {{ paying_client.first_name }} {{ paying_client.last_name }}
                                {% endif %}
                                {{ t('invoices.on') }} {{ invoice.paid_date.strftime('%Y-%m-%d') }}
                            </small>
                        </div>
                    {% endif %}
                    {% if invoice.stripe_payment_intent_id %}
                        <div><small>{{ t('invoices.payment_id') }}: {{ invoice.stripe_payment_intent_id }}</small></div>
                    {% endif %}
                {% endif %}
            </div>
        </div>

        <div class="table-responsive-sm">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th class="text-center">#</th>
                        <th>{{ t('invoices.service') }}</th>
                        <th>{{ t('invoices.client') }}</th>
                        <th class="text-center">{{ t('invoices.date') }}</th>
                        <th class="text-end">{{ t('invoices.amount') }}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in invoice.items %}
                        <tr>
                            <td class="text-center">{{ loop.index }}</td>
                            <td>{{ item.description }}</td>
                            <td>
                                {% if "Transport fee" not in item.description %}
                                    {% set appointment = item.appointment %}
                                    {% if appointment.client %}
                                        {{ appointment.client.first_name }} {{ appointment.client.last_name }}
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                {% set appointment = item.appointment %}
                                {% if appointment and appointment.start_time %}
                                    {{ appointment.start_time.strftime('%Y-%m-%d') }}
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td class="text-end">${{ "%.2f"|format(item.amount) }}</td>
                        </tr>
                    {% endfor %}
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="4" class="text-end"><strong>{{ t('invoices.total') }}</strong></td>
                        <td class="text-end"><strong>${{ "%.2f"|format(invoice.total_amount) }}</strong></td>
                    </tr>
                </tfoot>
            </table>
        </div>

        <div class="row">
            <div class="col-lg-8">
                <div class="mb-4">
                    <h6>{{ t('invoices.notes') }}</h6>
                    <p class="text-muted">{{ invoice.notes|default(t('invoices.default_thank_you'), true) }}</p>
                </div>

                {% if invoice.status == 'pending' %}
                    <div class="alert alert-info">
                        <h6 class="alert-heading">{{ t('invoices.payment_information') }}</h6>
                        <p>{{ t('invoices.payment_instructions') }}</p>
                        <p class="mb-0">{{ t('invoices.payment_questions') }}</p>
                    </div>
                {% endif %}
            </div>

            <div class="col-lg-4">
                <div class="bg-light p-3 rounded">
                    <h6 class="mb-3">{{ t('invoices.payment_summary') }}</h6>
                    <div class="d-flex justify-content-between mb-2">
                        <span>{{ t('invoices.subtotal') }}</span>
                        <span>${{ "%.2f"|format(invoice.total_amount) }}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>{{ t('invoices.tax') }}</span>
                        <span>$0.00</span>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between fw-bold">
                        <span>{{ t('invoices.total') }}</span>
                        <span>${{ "%.2f"|format(invoice.total_amount) }}</span>
                    </div>

                    {% if invoice.status == 'paid' %}
                        <div class="text-center mt-3">
                            <span class="badge bg-success p-2">{{ t('invoices.paid_on') }} {{ invoice.paid_date.strftime('%Y-%m-%d') }}</span>
                        </div>
                    {% elif invoice.is_overdue %}
                        <div class="text-center mt-3">
                            <span class="badge bg-danger p-2">{{ t('invoices.overdue') }}</span>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Stripe Payment Modal template for client.pay_invoice route -->
{% if current_user.role == 'client' and invoice.status == 'pending' %}
    <div class="modal fade" id="paymentModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ t('invoices.payment') }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Stripe Elements Placeholder -->
                    <div id="payment-element"></div>
                    <div id="payment-message" class="mt-2"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ t('invoices.cancel') }}</button>
                    <button type="button" id="submit-payment" class="btn btn-primary">{{ t('invoices.pay_invoice') }} ${{ "%.2f"|format(invoice.total_amount) }}</button>
                </div>
            </div>
        </div>
    </div>
{% endif %}
{% endblock %}

{% block scripts %}
{% if current_user.role == 'client' and invoice.status == 'pending' %}
<script src="https://js.stripe.com/v3/"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const paymentButton = document.querySelector('a[href="{{ url_for('client.pay_invoice', id=invoice.id) }}"]');

        if (paymentButton) {
            paymentButton.addEventListener('click', function(e) {
                e.preventDefault();

                // Show payment modal
                fetch("{{ url_for('client.pay_invoice', id=invoice.id) }}", {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': "{{ csrf_token() }}"
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.client_secret) {
                        const stripe = Stripe("{{ config['STRIPE_PUBLISHABLE_KEY'] }}");
                        const elements = stripe.elements({
                            clientSecret: data.client_secret
                        });

                        const paymentElement = elements.create('payment');
                        paymentElement.mount('#payment-element');

                        const paymentModal = new bootstrap.Modal(document.getElementById('paymentModal'));
                        paymentModal.show();

                        document.getElementById('submit-payment').addEventListener('click', async function() {
                            // Show loading state
                            this.disabled = true;
                            this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> {{ t("invoices.processing") }}';

                            // Confirm payment
                            const { error } = await stripe.confirmPayment({
                                elements,
                                confirmParams: {
                                    return_url: "{{ url_for('client.payment_success', invoice_id=invoice.id, _external=True) }}"
                                }
                            });

                            if (error) {
                                // Show error message
                                document.getElementById('payment-message').innerHTML = `
                                    <div class="alert alert-danger">
                                        ${error.message}
                                    </div>
                                `;

                                // Reset button
                                this.disabled = false;
                                this.innerHTML = '{{ t("invoices.pay_amount") }} ${{ "%.2f"|format(invoice.total_amount) }}';
                            }
                        });
                    } else {
                        alert('{{ t("invoices.payment_error") }}');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('{{ t("invoices.payment_error") }}');
                });
            });
        }
    });
</script>
{% endif %}
{% endblock %}
