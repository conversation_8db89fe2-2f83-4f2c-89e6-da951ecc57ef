<!-- app/templates/client/profile.html -->
{% extends "base.html" %}

{% block title %}{{ t('profile.title') }} - TutorAide Inc.{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">{{ t('profile.title') }}</h2>
    </div>
</div>

<div class="row">
    <!-- Profile Information -->
    <div class="col-md-8">
        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">{{ t('profile.personal_info') }}</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('client.profile') }}">
                    {{ form.csrf_token }}
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="first_name" class="form-label">{{ t('profile.first_name') }}</label>
                            {{ form.first_name(class="form-control" + (" is-invalid" if form.first_name.errors else "")) }}
                            {% if form.first_name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.first_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="last_name" class="form-label">{{ t('profile.last_name') }}</label>
                            {{ form.last_name(class="form-control" + (" is-invalid" if form.last_name.errors else "")) }}
                            {% if form.last_name.errors %}
                                <div class="invalid-feedback">
                                    {% for error in form.last_name.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">{{ t('profile.email') }}</label>
                        <input type="email" class="form-control" id="email" value="{{ client.user.email }}" disabled>
                        <div class="form-text">{{ t('profile.email_change_help') }}</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="phone" class="form-label">{{ t('profile.phone') }}</label>
                        {{ form.phone(class="form-control" + (" is-invalid" if form.phone.errors else "")) }}
                        {% if form.phone.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.phone.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="address" class="form-label">{{ t('profile.address') }}</label>
                        {{ form.address(class="form-control" + (" is-invalid" if form.address.errors else ""), rows=3) }}
                        {% if form.address.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.address.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="preferred_language" class="form-label">{{ t('profile.preferred_language') }}</label>
                        {{ form.preferred_language(class="form-select" + (" is-invalid" if form.preferred_language.errors else "")) }}
                        {% if form.preferred_language.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.preferred_language.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <hr class="my-4">
                    <h5 class="mb-3">{{ t('profile.change_password') }}</h5>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">{{ t('profile.new_password') }}</label>
                        {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else "")) }}
                        {% if form.password.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.password.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-text">{{ t('profile.password_help') }}</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">{{ t('profile.confirm_password') }}</label>
                        {{ form.confirm_password(class="form-control" + (" is-invalid" if form.confirm_password.errors else "")) }}
                        {% if form.confirm_password.errors %}
                            <div class="invalid-feedback">
                                {% for error in form.confirm_password.errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <button type="submit" class="btn btn-primary">
                            {{ t('buttons.save_changes') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Sidebar -->
    <div class="col-md-4">
        <!-- Privacy Settings -->
        <div class="card shadow mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">{{ t('profile.privacy_settings') }}</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('client.update_privacy') }}">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    
                    <div class="form-check form-switch mb-3">
                        <input class="form-check-input" type="checkbox" id="optionalConsent" name="optional_consent" 
                               {% if client_consent and client_consent.optional_consent %}checked{% endif %}>
                        <label class="form-check-label" for="optionalConsent">
                            {{ t('consent.optional_checkbox') }}
                        </label>
                        <div class="form-text">{{ t('consent.optional_help') }}</div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-info">
                            {{ t('buttons.update_privacy') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Account Status -->
        <div class="card shadow">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0">{{ t('profile.account_status') }}</h5>
            </div>
            <div class="card-body">
                <p class="mb-2">
                    <strong>{{ t('profile.account_type') }}:</strong> 
                    {% if client.client_type == 'individual' %}
                        {{ t('clients.type.individual') }}
                    {% elif client.client_type == 'institutional' %}
                        {{ t('clients.type.institutional') }}
                    {% endif %}
                </p>
                
                <p class="mb-2">
                    <strong>{{ t('profile.member_since') }}:</strong> 
                    {{ client.insert_date.strftime('%b %d, %Y') }}
                </p>
                
                <p class="mb-2">
                    <strong>{{ t('profile.last_login') }}:</strong> 
                    {% if client.user.last_login %}
                        {{ client.user.last_login.strftime('%b %d, %Y %H:%M') }}
                    {% else %}
                        {{ t('profile.never') }}
                    {% endif %}
                </p>
                
                {% if client.is_suspended %}
                    <div class="alert alert-danger mt-3">
                        <h6 class="alert-heading">{{ t('profile.account_suspended') }}</h6>
                        <p>{{ t('profile.suspension_reason') }}: {{ client.suspension_reason }}</p>
                        <p class="mb-0">{{ t('profile.suspended_at') }}: {{ client.suspended_at.strftime('%b %d, %Y') }}</p>
                    </div>
                {% else %}
                    <div class="alert alert-success mt-3">
                        <p class="mb-0">{{ t('profile.account_active') }}</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
