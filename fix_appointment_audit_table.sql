-- Fix Appointment Audit Table Schema
-- This script updates the appointment_audit table to match the AppointmentAudit model

-- ========================================
-- APPOINTMENT_AUDIT TABLE FIXES
-- ========================================

-- Add missing columns to appointment_audit table
DO $$ 
BEGIN
    -- Add user_id column (rename from changed_by if it exists)
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'appointment_audit' 
               AND column_name = 'changed_by') 
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns 
                       WHERE table_name = 'appointment_audit' 
                       AND column_name = 'user_id') THEN
        ALTER TABLE appointment_audit RENAME COLUMN changed_by TO user_id;
        RAISE NOTICE 'Renamed changed_by to user_id in appointment_audit table';
    ELSIF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                      WHERE table_name = 'appointment_audit' 
                      AND column_name = 'user_id') THEN
        ALTER TABLE appointment_audit ADD COLUMN user_id INTEGER REFERENCES users(user_id) ON DELETE SET NULL;
        RAISE NOTICE 'Added user_id column to appointment_audit table';
    END IF;
    
    -- Add user_role column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointment_audit' 
                   AND column_name = 'user_role') THEN
        ALTER TABLE appointment_audit ADD COLUMN user_role VARCHAR(50);
        RAISE NOTICE 'Added user_role column to appointment_audit table';
    END IF;
    
    -- Add user_email column
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointment_audit' 
                   AND column_name = 'user_email') THEN
        ALTER TABLE appointment_audit ADD COLUMN user_email VARCHAR(255);
        RAISE NOTICE 'Added user_email column to appointment_audit table';
    END IF;
    
    -- Add timestamp column (rename from insert_date if it exists)
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'appointment_audit' 
               AND column_name = 'insert_date') 
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns 
                       WHERE table_name = 'appointment_audit' 
                       AND column_name = 'timestamp') THEN
        ALTER TABLE appointment_audit RENAME COLUMN insert_date TO timestamp;
        RAISE NOTICE 'Renamed insert_date to timestamp in appointment_audit table';
    ELSIF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                      WHERE table_name = 'appointment_audit' 
                      AND column_name = 'timestamp') THEN
        ALTER TABLE appointment_audit ADD COLUMN timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
        RAISE NOTICE 'Added timestamp column to appointment_audit table';
    END IF;
    
    -- Add specific audit fields for quick querying
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointment_audit' 
                   AND column_name = 'old_status') THEN
        ALTER TABLE appointment_audit ADD COLUMN old_status VARCHAR(50);
        RAISE NOTICE 'Added old_status column to appointment_audit table';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointment_audit' 
                   AND column_name = 'new_status') THEN
        ALTER TABLE appointment_audit ADD COLUMN new_status VARCHAR(50);
        RAISE NOTICE 'Added new_status column to appointment_audit table';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointment_audit' 
                   AND column_name = 'old_tutor_id') THEN
        ALTER TABLE appointment_audit ADD COLUMN old_tutor_id INTEGER;
        RAISE NOTICE 'Added old_tutor_id column to appointment_audit table';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointment_audit' 
                   AND column_name = 'new_tutor_id') THEN
        ALTER TABLE appointment_audit ADD COLUMN new_tutor_id INTEGER;
        RAISE NOTICE 'Added new_tutor_id column to appointment_audit table';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointment_audit' 
                   AND column_name = 'old_client_id') THEN
        ALTER TABLE appointment_audit ADD COLUMN old_client_id INTEGER;
        RAISE NOTICE 'Added old_client_id column to appointment_audit table';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointment_audit' 
                   AND column_name = 'new_client_id') THEN
        ALTER TABLE appointment_audit ADD COLUMN new_client_id INTEGER;
        RAISE NOTICE 'Added new_client_id column to appointment_audit table';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointment_audit' 
                   AND column_name = 'old_start_time') THEN
        ALTER TABLE appointment_audit ADD COLUMN old_start_time TIMESTAMP;
        RAISE NOTICE 'Added old_start_time column to appointment_audit table';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointment_audit' 
                   AND column_name = 'new_start_time') THEN
        ALTER TABLE appointment_audit ADD COLUMN new_start_time TIMESTAMP;
        RAISE NOTICE 'Added new_start_time column to appointment_audit table';
    END IF;
    
    -- Ensure ip_address and user_agent columns exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointment_audit' 
                   AND column_name = 'ip_address') THEN
        ALTER TABLE appointment_audit ADD COLUMN ip_address INET;
        RAISE NOTICE 'Added ip_address column to appointment_audit table';
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointment_audit' 
                   AND column_name = 'user_agent') THEN
        ALTER TABLE appointment_audit ADD COLUMN user_agent TEXT;
        RAISE NOTICE 'Added user_agent column to appointment_audit table';
    END IF;
    
    -- Add notes column for audit notes
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'appointment_audit' 
                   AND column_name = 'notes') THEN
        ALTER TABLE appointment_audit ADD COLUMN notes TEXT;
        RAISE NOTICE 'Added notes column to appointment_audit table';
    END IF;
    
END $$;

-- ========================================
-- VERIFICATION QUERIES
-- ========================================

-- Check appointment_audit table structure
SELECT 'APPOINTMENT_AUDIT TABLE COLUMNS:' as info;
SELECT 
    column_name,
    data_type,
    character_maximum_length,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'appointment_audit' 
ORDER BY ordinal_position;

-- Final success message
SELECT 'APPOINTMENT_AUDIT TABLE FIX COMPLETED!' as status,
       'The appointment_audit table now matches the AppointmentAudit model.' as message,
       'You can now restart your application and create appointments.' as next_step;
