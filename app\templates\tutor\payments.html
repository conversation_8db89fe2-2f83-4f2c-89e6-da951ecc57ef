<!-- app/templates/tutor/payments.html -->
{% extends "base.html" %}

{% block title %}{{ t('tutor.payments.page_title') }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">{{ t('tutor.payments.title') }}</h2>
    </div>
</div>

<!-- Filter Form -->
<div class="card shadow mb-4">
    <div class="card-body">
        <form method="GET" action="{{ url_for('tutor.payments') }}" class="row g-3 filter-form">
            <div class="col-md-10">
                <label for="status" class="form-label">{{ t('appointments.status') }}</label>
                <select class="form-select" id="status" name="status">
                    <option value="all" {% if current_status == 'all' %}selected{% endif %}>{{ t('tutor.payments.all_payments') }}</option>
                    <option value="pending" {% if current_status == 'pending' %}selected{% endif %}>{{ t('tutor.payments.pending') }}</option>
                    <option value="paid" {% if current_status == 'paid' %}selected{% endif %}>{{ t('tutor.payments.paid') }}</option>
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-primary w-100">{{ t('tutor.payments.filter') }}</button>
            </div>
        </form>
    </div>
</div>

<!-- Payment Summary Card -->
<div class="card shadow mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">{{ t('tutor.payments.payment_summary') }}</h5>
    </div>
    <div class="card-body">
        <div class="row text-center">
            <div class="col-md-4 mb-3">
                <div class="card bg-light">
                    <div class="card-body">
                        <h5 class="card-title">{{ t('tutor.payments.service_earnings') }}</h5>
                        <p class="display-6">${{ "%.2f"|format(service_total) }}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card bg-light">
                    <div class="card-body">
                        <h5 class="card-title">{{ t('tutor.payments.transport_fees') }}</h5>
                        <p class="display-6">${{ "%.2f"|format(transport_total) }}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <h5 class="card-title">{{ t('tutor.payments.total_earnings') }}</h5>
                        <p class="display-6">${{ "%.2f"|format(grand_total) }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payments Table -->
<div class="card shadow">
    <div class="card-header bg-light">
        <h5 class="mb-0">{{ t('tutor.payments.payment_history') }}</h5>
    </div>
    <div class="card-body">
        {% if payments %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>{{ t('appointments.date') }}</th>
                            <th>{{ t('tutor.payments.appointment') }}</th>
                            <th>{{ t('appointments.client') }}</th>
                            <th>{{ t('tutor.payments.service_amount') }}</th>
                            <th>{{ t('tutor.payments.transport_fee') }}</th>
                            <th>{{ t('appointments.total') }}</th>
                            <th>{{ t('appointments.status') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for payment in payments %}
                            <tr>
                                <td>
                                    {% if payment.payment_date %}
                                        {{ payment.payment_date.strftime('%Y-%m-%d') }}
                                    {% else %}
                                        {{ payment.insert_date.strftime('%Y-%m-%d') }}
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('tutor.view_appointment', id=payment.appointment_id) }}">
                                        {{ payment.appointment.start_time.strftime('%Y-%m-%d %H:%M') }}
                                    </a>
                                </td>
                                <td>{{ payment.appointment.client.first_name }} {{ payment.appointment.client.last_name }}</td>
                                <td>${{ "%.2f"|format(payment.service_amount) }}</td>
                                <td>${{ "%.2f"|format(payment.transport_amount) }}</td>
                                <td><strong>${{ "%.2f"|format(payment.total_amount) }}</strong></td>
                                <td>
                                    {% if payment.status == 'paid' %}
                                        <span class="badge bg-success">{{ t('tutor.payments.paid') }}</span>
                                    {% elif payment.status == 'ready' %}
                                        <span class="badge bg-warning">{{ t('tutor.payments.pending') }}</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ payment.status }}</span>
                                    {% endif %}
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center p-5">
                <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                <h5>{{ t('tutor.payments.no_payments_found') }}</h5>
                <p class="text-muted">{{ t('tutor.payments.no_payments_message') }}</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- Payment Process Information -->
<div class="card shadow mt-4">
    <div class="card-header bg-light">
        <h5 class="mb-0">{{ t('tutor.payments.payment_process_info') }}</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6>{{ t('tutor.payments.how_payments_work') }}</h6>
                <ol>
                    <li>{{ t('tutor.payments.payments_processed_for_completed') }}</li>
                    <li>{{ t('tutor.payments.mark_completed_eligible') }}</li>
                    <li>{{ t('tutor.payments.management_reviews') }}</li>
                    <li>{{ t('tutor.payments.payments_processed_schedule') }}</li>
                    <li>{{ t('tutor.payments.transport_fees_included') }}</li>
                </ol>
            </div>
            <div class="col-md-6">
                <h6>{{ t('tutor.payments.payment_status_definitions') }}</h6>
                <ul>
                    <li><strong>{{ t('tutor.payments.pending') }}</strong>: {{ t('tutor.payments.pending_definition') }}</li>
                    <li><strong>{{ t('tutor.payments.paid') }}</strong>: {{ t('tutor.payments.paid_definition') }}</li>
                </ul>
                <p class="mt-3">{{ t('tutor.payments.payment_questions') }}</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}