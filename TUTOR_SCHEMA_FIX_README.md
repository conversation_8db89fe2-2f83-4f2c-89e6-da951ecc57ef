# Tutor Table Schema Fix

## Problem Description

The application is experiencing a database error:

```
sqlalchemy.exc.ProgrammingError: (psycopg2.errors.UndefinedColumn) column tutors.street_address does not exist
```

This error occurs because there's a mismatch between the database schema and the SQLAlchemy model for the `tutors` table.

### Root Cause

1. **Database Schema** (`app/schema.sql`): The `tutors` table has columns:
   - `civic_number` (VARCHAR(20))
   - `street` (VARCHAR(200))
   - `postal_code` (VARCHAR(10))
   - `date_of_birth` (DATE)
   - `bank_transit_number` (VARCHAR(10))
   - `bank_institution_number` (VARCHAR(10))
   - `bank_account_number_encrypted` (TEXT)

2. **SQLAlchemy Model** (`app/models/tutor.py`): The `Tutor` class expects:
   - `street_address` (TEXT)
   - `zip_code` (VARCHAR(20))
   - `birthdate` (DATE)
   - `bank_transit_number_encrypted` (VARCHAR(255))
   - `bank_institution_number_encrypted` (VARCHAR(255))
   - `bank_account_number_encrypted` (VARCHAR(255))

3. **Missing Migration**: The migration file `app/docs/database/tutor_profile_enhancement.sql` referenced in `app/docs/tutor_profile_setup.md` is missing.

## Solution

We've created a migration script that:

1. **Adds missing columns** that the SQLAlchemy model expects
2. **Migrates existing data** from old columns to new columns
3. **Updates column types** to match the model
4. **Creates indexes** for better performance
5. **Verifies** the migration was successful

### Migration Details

The migration script (`app/migrations/fix_tutor_table_schema.sql`) performs these operations:

#### Column Additions and Data Migration:
- **`street_address`**: Combines `civic_number` and `street` into a single field
- **`zip_code`**: Copies data from `postal_code`
- **`birthdate`**: Copies data from `date_of_birth`
- **`bank_transit_number_encrypted`**: Adds new encrypted column (data not migrated for security)
- **`bank_institution_number_encrypted`**: Adds new encrypted column (data not migrated for security)
- **`bank_account_number_encrypted`**: Renames existing column or adds if missing

#### Column Type Updates:
- Updates `city`, `province`, and `country` columns to VARCHAR(100)

#### Index Creation:
- Adds indexes on `user_id`, `is_active`, `city`, and `province` for better performance

## How to Apply the Fix

### Option 1: Using Python Script (Recommended)

1. **Set environment variables** for your database connection:
   ```bash
   export DB_HOST=your_host
   export DB_PORT=5432
   export DB_NAME=your_database
   export DB_USER=your_username
   export DB_PASSWORD=your_password
   ```

2. **Run the migration script**:
   ```bash
   python run_tutor_schema_fix.py
   ```

### Option 2: Using Batch Script (Windows)

1. **Set environment variables** or the script will prompt for them
2. **Run the batch script**:
   ```cmd
   run_tutor_schema_fix.bat
   ```

### Option 3: Manual Database Execution

1. **Connect to your PostgreSQL database** using psql, DBeaver, or another client
2. **Execute the migration file**:
   ```sql
   \i app/migrations/fix_tutor_table_schema.sql
   ```

## Verification

After running the migration, you can verify it worked by:

1. **Checking the table structure**:
   ```sql
   \d tutors
   ```

2. **Verifying required columns exist**:
   ```sql
   SELECT column_name, data_type, is_nullable 
   FROM information_schema.columns 
   WHERE table_name = 'tutors' 
   AND column_name IN ('street_address', 'zip_code', 'birthdate', 
                       'bank_transit_number_encrypted', 
                       'bank_institution_number_encrypted', 
                       'bank_account_number_encrypted')
   ORDER BY column_name;
   ```

3. **Testing the application**: Restart your application and try accessing the `/manager/` route that was failing.

## Post-Migration Notes

### Banking Information
- The old unencrypted banking columns (`bank_transit_number`, `bank_institution_number`) are not migrated to the new encrypted columns for security reasons
- Users will need to re-enter their banking information through the application interface
- The application will properly encrypt this information when saved

### Data Integrity
- All existing address data is preserved by combining `civic_number` and `street` into `street_address`
- Postal codes are copied to the new `zip_code` column
- Birth dates are copied to the new `birthdate` column

### Old Columns
- The migration does not drop the old columns (`civic_number`, `street`, `postal_code`, `date_of_birth`) to ensure data safety
- You can manually drop these columns later if desired, after verifying the migration worked correctly

## Troubleshooting

### Common Issues

1. **psql command not found**: Install PostgreSQL client tools
2. **Permission denied**: Ensure your database user has ALTER TABLE privileges
3. **Connection refused**: Check your database connection parameters

### If Migration Fails

1. **Check the error message** in the output
2. **Verify database connection** parameters
3. **Ensure the database user has sufficient privileges**
4. **Check if the migration was partially applied** and re-run if safe

### Rolling Back

If you need to roll back the migration:

1. **Drop the new columns**:
   ```sql
   ALTER TABLE tutors DROP COLUMN IF EXISTS street_address;
   ALTER TABLE tutors DROP COLUMN IF EXISTS zip_code;
   ALTER TABLE tutors DROP COLUMN IF EXISTS birthdate;
   ALTER TABLE tutors DROP COLUMN IF EXISTS bank_transit_number_encrypted;
   ALTER TABLE tutors DROP COLUMN IF EXISTS bank_institution_number_encrypted;
   ```

2. **Rename back if needed**:
   ```sql
   ALTER TABLE tutors RENAME COLUMN bank_account_number_encrypted TO bank_account_number;
   ```

## Files Created/Modified

- `app/migrations/fix_tutor_table_schema.sql` - The migration script
- `run_tutor_schema_fix.py` - Python script to run the migration
- `run_tutor_schema_fix.bat` - Windows batch script to run the migration
- `TUTOR_SCHEMA_FIX_README.md` - This documentation file
