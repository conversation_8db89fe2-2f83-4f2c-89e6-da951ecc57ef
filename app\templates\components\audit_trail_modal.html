<!-- app/templates/components/audit_trail_modal.html -->
<!-- Audit Trail Modal Component -->
<div class="modal fade" id="auditTrailModal" tabindex="-1" aria-labelledby="auditTrailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-scrollable">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header">
                <h5 class="modal-title" id="auditTrailModalLabel">
                    <i class="fas fa-history me-2"></i>
                    Appointment Audit Trail
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>

            <!-- Modal Body -->
            <div class="modal-body">
                <!-- Loading State -->
                <div id="auditLoadingState" class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading audit trail...</span>
                    </div>
                    <p class="mt-2 text-muted">Loading audit trail...</p>
                </div>

                <!-- Enhanced Error State -->
                <div id="auditErrorState" class="alert alert-danger d-none" role="alert">
                    <div class="d-flex align-items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                        </div>
                        <div class="flex-grow-1">
                            <strong class="error-title">Error loading audit trail</strong>
                            <p class="error-message mb-3 mt-2">Unable to load the audit trail for this appointment. Please try again later.</p>
                            
                            <!-- Error Actions -->
                            <div class="error-actions">
                                <button type="button" class="btn btn-sm btn-outline-danger retry-button me-2">
                                    <i class="fas fa-redo me-1"></i>
                                    Try Again
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" data-bs-dismiss="modal">
                                    <i class="fas fa-times me-1"></i>
                                    Close
                                </button>
                            </div>
                            
                            <!-- Error Details (Collapsible) -->
                            <div class="mt-3">
                                <button class="btn btn-sm btn-link p-0 text-decoration-none error-details-toggle" 
                                        type="button" 
                                        data-bs-toggle="collapse" 
                                        data-bs-target="#errorDetails" 
                                        aria-expanded="false" 
                                        aria-controls="errorDetails">
                                    <i class="fas fa-chevron-down me-1"></i>
                                    <small>Show technical details</small>
                                </button>
                                <div class="collapse mt-2" id="errorDetails">
                                    <div class="card card-body bg-light border-0">
                                        <small class="text-muted error-technical-details">
                                            If this problem persists, please contact your system administrator with the following information:
                                            <br>
                                            <code class="error-code">Error details will be shown here</code>
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Network Status Indicator -->
                <div id="networkStatusIndicator" class="alert alert-warning d-none" role="alert">
                    <div class="d-flex align-items-center">
                        <div class="spinner-border spinner-border-sm me-2" role="status">
                            <span class="visually-hidden">Checking connection...</span>
                        </div>
                        <small>Checking network connection...</small>
                    </div>
                </div>

                <!-- Appointment Summary -->
                <div id="auditAppointmentSummary" class="appointment-summary mb-4 d-none">
                    <div class="card border-0 bg-light">
                        <div class="card-body py-3">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <h6 class="mb-1" id="appointmentSummaryTitle">Appointment #12345</h6>
                                    <p class="mb-0 text-muted" id="appointmentSummaryDetails">
                                        <span id="appointmentClient">John Doe</span> with 
                                        <span id="appointmentTutor">Jane Smith</span>
                                    </p>
                                </div>
                                <div class="col-md-4 text-md-end">
                                    <span class="badge bg-primary" id="appointmentStatus">Scheduled</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Audit Timeline -->
                <div id="auditTimeline" class="audit-timeline d-none">
                    <!-- Timeline entries will be dynamically inserted here -->
                    <!-- Virtual scrolling container will be created here if needed -->
                </div>
                
                <!-- Performance Indicator -->
                <div id="performanceIndicator" class="performance-indicator">
                    <span id="performanceText">Loading...</span>
                </div>

                <!-- Empty State -->
                <div id="auditEmptyState" class="text-center py-4 d-none">
                    <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                    <h6 class="text-muted">No audit entries found</h6>
                    <p class="text-muted mb-0">This appointment has no recorded audit trail.</p>
                </div>

                <!-- Enhanced Pagination -->
                <div id="auditPagination" class="d-flex justify-content-between align-items-center mt-4 d-none">
                    <div class="pagination-info">
                        <small class="text-muted">
                            Showing <span id="paginationStart">1</span>-<span id="paginationEnd">10</span> 
                            of <span id="paginationTotal">25</span> entries
                        </small>
                        <div class="cache-indicator d-none" id="cacheIndicator">
                            <small class="badge bg-success">Cached</small>
                        </div>
                    </div>
                    <nav aria-label="Audit trail pagination">
                        <ul class="pagination pagination-sm mb-0">
                            <li class="page-item" id="paginationFirst">
                                <a class="page-link" href="#" aria-label="First" title="First page">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item" id="paginationPrev">
                                <a class="page-link" href="#" aria-label="Previous" title="Previous page">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            <!-- Dynamic page numbers will be inserted here -->
                            <li class="page-item active" id="paginationCurrent">
                                <a class="page-link" href="#">1</a>
                            </li>
                            <li class="page-item" id="paginationNext">
                                <a class="page-link" href="#" aria-label="Next" title="Next page">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item" id="paginationLast">
                                <a class="page-link" href="#" aria-label="Last" title="Last page">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                        </ul>
                    </nav>
                </div>
                
                <!-- Virtual Scrolling Loading Indicator -->
                <div id="virtualScrollLoading" class="lazy-loading-indicator d-none">
                    <div class="spinner-border spinner-border-sm" role="status">
                        <span class="visually-hidden">Loading more entries...</span>
                    </div>
                    <span class="ms-2">Loading more audit entries...</span>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Audit Entry Template (Hidden, used for cloning) -->
<template id="auditEntryTemplate">
    <div class="audit-entry mb-3" data-entry-id="" data-action="">
        <div class="audit-entry-card card border-start border-3" data-action="">
            <div class="card-body py-3">
                <div class="row align-items-start">
                    <!-- Action Icon -->
                    <div class="col-auto">
                        <div class="audit-action-icon" data-action="">
                            <i class="fas fa-plus-circle text-success audit-icon" data-action="create" title="Created"></i>
                            <i class="fas fa-edit text-primary audit-icon d-none" data-action="update" title="Updated"></i>
                            <i class="fas fa-trash-alt text-danger audit-icon d-none" data-action="delete" title="Deleted"></i>
                            <i class="fas fa-eye text-info audit-icon d-none" data-action="read" title="Viewed"></i>
                        </div>
                    </div>

                    <!-- Entry Content -->
                    <div class="col">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <div class="flex-grow-1">
                                <h6 class="mb-1 audit-action-title">Appointment Action</h6>
                                
                                <!-- User and Timestamp Information -->
                                <div class="audit-meta text-muted mb-2">
                                    <div class="d-flex flex-wrap align-items-center gap-3">
                                        <!-- User Information -->
                                        <div class="user-info d-flex align-items-center">
                                            <i class="fas fa-user me-1"></i>
                                            <span class="audit-user fw-medium">User Name</span>
                                            <span class="badge badge-role ms-2 d-none">Role</span>
                                        </div>
                                        
                                        <!-- Timestamp -->
                                        <div class="timestamp-info d-flex align-items-center">
                                            <i class="fas fa-clock me-1"></i>
                                            <span class="audit-timestamp" data-utc="">Timestamp</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Quick Summary -->
                                <div class="audit-summary">
                                    <p class="mb-0 text-muted">Action summary</p>
                                </div>
                            </div>

                            <!-- Details Toggle Button -->
                            <button class="btn btn-sm btn-outline-secondary audit-details-toggle ms-3" 
                                    type="button" 
                                    data-bs-toggle="collapse" 
                                    data-bs-target=""
                                    aria-expanded="false"
                                    aria-controls="">
                                <i class="fas fa-chevron-down"></i>
                                <span class="ms-1 d-none d-sm-inline">Details</span>
                            </button>
                        </div>

                        <!-- Collapsible Details Section -->
                        <div class="collapse audit-details" id="">
                            <div class="card card-body bg-light border-0 mt-2">
                                <div class="audit-changes">
                                    <!-- Changes will be dynamically inserted here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<!-- Enhanced Change Item Template (Hidden, used for cloning) -->
<template id="changeItemTemplate">
    <div class="change-item mb-3">
        <div class="row">
            <div class="col-md-3">
                <strong class="change-field">Field Name</strong>
            </div>
            <div class="col-md-9">
                <div class="change-comparison">
                    <!-- Before Value -->
                    <div class="old-value mb-2">
                        <div class="d-flex align-items-center">
                            <small class="text-muted me-2">Before:</small>
                            <span class="badge bg-light text-dark border">Old Value</span>
                        </div>
                    </div>
                    
                    <!-- Arrow Indicator -->
                    <div class="change-arrow mb-2">
                        <i class="fas fa-arrow-down text-muted"></i>
                    </div>
                    
                    <!-- After Value -->
                    <div class="new-value">
                        <div class="d-flex align-items-center">
                            <small class="text-muted me-2">After:</small>
                            <span class="badge bg-primary">New Value</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<!-- Creation Details Template -->
<template id="creationDetailsTemplate">
    <div class="creation-details">
        <h6 class="mb-3 text-success">
            <i class="fas fa-plus-circle me-2"></i>
            Initial Values
        </h6>
        <div class="creation-values">
            <!-- Initial values will be inserted here -->
        </div>
    </div>
</template>

<!-- Deletion Details Template -->
<template id="deletionDetailsTemplate">
    <div class="deletion-details">
        <h6 class="mb-3 text-danger">
            <i class="fas fa-trash-alt me-2"></i>
            Deleted Information
        </h6>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            This appointment was permanently removed from the system.
        </div>
        <div class="deleted-values mt-3 d-none">
            <small class="text-muted">Final values before deletion:</small>
            <div class="deletion-values-list">
                <!-- Deleted values will be inserted here -->
            </div>
        </div>
    </div>
</template>