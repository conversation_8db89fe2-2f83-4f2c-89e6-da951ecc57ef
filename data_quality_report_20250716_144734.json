{"timestamp": "2025-07-16T18:47:34.150700", "report_type": "comprehensive_data_quality", "sections": {"validation_results": {"timestamp": "2025-07-16T18:47:34.150714", "summary": {"total_checks": 13, "passed_checks": 0, "failed_checks": 13, "warnings": 0, "critical_issues": 13}, "results": {"users": {"table": "users", "issues": [{"type": "validation_error", "severity": "critical", "message": "Error validating users: Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}], "warnings": [], "total_issues": 1, "total_warnings": 0}, "clients": {"table": "clients", "issues": [{"type": "validation_error", "severity": "critical", "message": "Error validating clients: Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}], "warnings": [], "total_issues": 1, "total_warnings": 0}, "tutors": {"table": "tutors", "issues": [{"type": "validation_error", "severity": "critical", "message": "Error validating tutors: Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}], "warnings": [], "total_issues": 1, "total_warnings": 0}, "dependants": {"table": "dependants", "issues": [{"type": "validation_error", "severity": "critical", "message": "Error validating dependants: Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}], "warnings": [], "total_issues": 1, "total_warnings": 0}, "services": {"table": "services", "issues": [{"type": "validation_error", "severity": "critical", "message": "Error validating services: Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}], "warnings": [], "total_issues": 1, "total_warnings": 0}, "appointments": {"table": "appointments", "issues": [{"type": "validation_error", "severity": "critical", "message": "Error validating appointments: Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}], "warnings": [], "total_issues": 1, "total_warnings": 0}, "recurring_schedules": {"table": "appointment_recurring_schedules", "issues": [{"type": "validation_error", "severity": "critical", "message": "Error validating recurring schedules: Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}], "warnings": [], "total_issues": 1, "total_warnings": 0}, "subscriptions": {"table": "subscriptions", "issues": [{"type": "validation_error", "severity": "critical", "message": "Error validating subscriptions: Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}], "warnings": [], "total_issues": 1, "total_warnings": 0}, "invoices": {"table": "invoices", "issues": [{"type": "validation_error", "severity": "critical", "message": "Error validating invoices: Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}], "warnings": [], "total_issues": 1, "total_warnings": 0}, "programs": {"table": "programs", "issues": [{"type": "validation_error", "severity": "critical", "message": "Error validating programs: Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}], "warnings": [], "total_issues": 1, "total_warnings": 0}, "foreign_keys": {"table": "foreign_keys", "issues": [{"type": "validation_error", "severity": "critical", "message": "Error validating foreign keys: Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}], "warnings": [], "total_issues": 1, "total_warnings": 0}, "business_rules": {"table": "business_rules", "issues": [{"type": "validation_error", "severity": "critical", "message": "Error validating business rules: Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}], "warnings": [], "total_issues": 1, "total_warnings": 0}, "data_consistency": {"table": "data_consistency", "issues": [{"type": "validation_error", "severity": "critical", "message": "Error validating data consistency: Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}], "warnings": [], "total_issues": 1, "total_warnings": 0}}, "recommendations": [{"priority": "high", "category": "data_integrity", "message": "Found 13 critical data integrity issues that require immediate attention."}, {"priority": "high", "category": "referential_integrity", "message": "Foreign key integrity issues detected. Run cleanup procedures to fix orphaned records."}, {"priority": "medium", "category": "appointments", "message": "Appointment data issues detected. Review scheduling logic and data entry procedures."}]}, "cleanup_recommendations": {"timestamp": "2025-07-16T18:47:34.150914", "dry_run": true, "summary": {"total_operations": 10, "successful_operations": 0, "failed_operations": 10, "records_affected": 0}, "operations": {"orphaned_dependants": {"operation": "cleanup_orphaned_dependants", "dry_run": true, "records_affected": 0, "success": false, "details": [], "error": "Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}, "orphaned_appointments": {"operation": "cleanup_orphaned_appointments", "dry_run": true, "records_affected": 0, "success": false, "details": [], "error": "Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}, "orphaned_tutor_services": {"operation": "cleanup_orphaned_tutor_services", "dry_run": true, "records_affected": 0, "success": false, "details": [], "error": "Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}, "orphaned_subscription_usage": {"operation": "cleanup_orphaned_subscription_usage", "dry_run": true, "records_affected": 0, "success": false, "details": [], "error": "Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}, "orphaned_invoice_items": {"operation": "cleanup_orphaned_invoice_items", "dry_run": true, "records_affected": 0, "success": false, "details": [], "error": "Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}, "invalid_recurring_schedules": {"operation": "cleanup_invalid_recurring_schedules", "dry_run": true, "records_affected": 0, "success": false, "details": [], "error": "Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}, "expired_subscriptions": {"operation": "cleanup_expired_subscriptions", "dry_run": true, "records_affected": 0, "success": false, "details": [], "error": "Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}, "old_scheduled_appointments": {"operation": "cleanup_old_scheduled_appointments", "dry_run": true, "records_affected": 0, "success": false, "details": [], "error": "Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}, "duplicate_relationships": {"operation": "cleanup_duplicate_relationships", "dry_run": true, "records_affected": 0, "success": false, "details": [], "error": "Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}, "invalid_data_values": {"operation": "cleanup_invalid_data_values", "dry_run": true, "records_affected": 0, "success": false, "details": [], "error": "Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}}, "errors": []}, "manual_review_items": {"timestamp": "2025-07-16T18:47:34.151053", "categories": {"data_quality_concerns": [{"type": "error", "priority": "high", "description": "Error finding data quality concerns: Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}], "business_rule_violations": [{"type": "error", "priority": "high", "description": "Error finding business rule violations: Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}], "potential_duplicates": [{"type": "error", "priority": "high", "description": "Error finding potential duplicates: Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}], "unusual_patterns": [{"type": "error", "priority": "high", "description": "Error finding unusual patterns: Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}], "missing_information": [{"type": "error", "priority": "high", "description": "Error finding missing information: Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}]}, "total_items": 0, "high_priority_count": 0, "medium_priority_count": 0, "low_priority_count": 0}, "data_statistics": {"timestamp": "2025-07-16T18:47:34.151158", "table_counts": {}, "growth_metrics": {}, "utilization_metrics": {}, "quality_metrics": {}, "error": "Statistics generation failed: Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}, "trend_analysis": {"timestamp": "2025-07-16T18:47:34.151475", "appointment_trends": [], "client_growth": [], "tutor_utilization": [], "revenue_trends": [], "error": "Trend analysis failed: Working outside of application context.\n\nThis typically means that you attempted to use functionality that needed\nthe current application. To solve this, set up an application context\nwith app.app_context(). See the documentation for more information."}}, "summary": {"overall_health_score": 0, "critical_issues": 13, "high_priority_items": 0, "medium_priority_items": 0, "low_priority_items": 0, "manual_review_required": 0}, "recommendations": [{"priority": "high", "category": "data_quality", "title": "Improve Overall Data Quality", "description": "Data health score is 0%. Focus on resolving critical issues first.", "actions": ["Run automated cleanup procedures", "Address all critical validation issues", "Implement data validation at input level"]}, {"priority": "critical", "category": "data_integrity", "title": "Resolve Critical Data Issues", "description": "Found 13 critical data integrity issues.", "actions": ["Review and fix foreign key violations", "Resolve orphaned records", "Validate data consistency"]}]}