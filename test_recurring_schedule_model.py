#!/usr/bin/env python3
"""
Test script to verify the AppointmentRecurringSchedule model works correctly
"""
from datetime import datetime, date, time
from app import create_app
from app.extensions import db
from app.models.appointment_recurring_schedule import AppointmentRecurringSchedule

def test_model():
    app = create_app()
    with app.app_context():
        try:
            # Test that we can import and create an instance
            print("Testing AppointmentRecurringSchedule model...")
            
            # Test basic model creation (without saving to DB)
            schedule = AppointmentRecurringSchedule(
                tutor_id=1,
                client_id=1,
                tutor_service_id=1,
                start_time=time(10, 0),  # 10:00 AM
                duration_minutes=60,
                frequency='weekly',
                day_of_week=1,  # Tuesday
                pattern_start_date=date(2025, 1, 20),
                pattern_end_date=date(2025, 6, 20),
                default_status='scheduled'
            )
            
            print("✓ Model instance created successfully")
            
            # Test properties
            print(f"✓ Frequency: {schedule.frequency}")
            print(f"✓ Duration: {schedule.duration_minutes} minutes")
            print(f"✓ Start time: {schedule.start_time}")
            print(f"✓ Is active: {schedule.is_active}")
            
            # Test next occurrence calculation
            next_occurrence = schedule.get_next_occurrence(date(2025, 1, 15))
            print(f"✓ Next occurrence from 2025-01-15: {next_occurrence}")
            
            # Test with a date after the start date
            next_occurrence2 = schedule.get_next_occurrence(date(2025, 1, 22))
            print(f"✓ Next occurrence from 2025-01-22: {next_occurrence2}")
            
            # Test table exists in database
            with db.engine.connect() as conn:
                result = conn.execute(db.text("""
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_name = 'appointment_recurring_schedules'
                """))
                table_exists = result.fetchone()
                print(f"✓ Table exists in database: {table_exists is not None}")
                
                # Test foreign key column exists
                result = conn.execute(db.text("""
                    SELECT column_name 
                    FROM information_schema.columns 
                    WHERE table_name = 'appointments' 
                    AND column_name = 'recurring_schedule_id'
                """))
                fk_exists = result.fetchone()
                print(f"✓ Foreign key column exists: {fk_exists is not None}")
            
            print("\n✓ All tests passed! AppointmentRecurringSchedule model is working correctly.")
            
        except Exception as e:
            print(f"✗ Test failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    return True

if __name__ == "__main__":
    test_model()