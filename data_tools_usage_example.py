#!/usr/bin/env python3
"""
Data Validation and Cleanup Tools - Usage Example
Demonstrates how to use the comprehensive data quality tools in production
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

from datetime import datetime
import json

def example_validation_workflow():
    """Example of running data validation workflow."""
    print("=" * 70)
    print("DATA VALIDATION WORKFLOW EXAMPLE")
    print("=" * 70)
    
    try:
        from app.services.data_validation_service import DataValidationService
        
        print("Step 1: Running comprehensive data validation...")
        validation_report = DataValidationService.validate_all_data()
        
        print(f"✓ Validation completed at {validation_report['timestamp']}")
        print(f"✓ Total checks performed: {validation_report['summary']['total_checks']}")
        print(f"✓ Checks passed: {validation_report['summary']['passed_checks']}")
        print(f"✓ Checks failed: {validation_report['summary']['failed_checks']}")
        print(f"✓ Critical issues found: {validation_report['summary']['critical_issues']}")
        print(f"✓ Warnings found: {validation_report['summary']['warnings']}")
        
        # Show recommendations
        if validation_report.get('recommendations'):
            print("\nRecommendations:")
            for i, rec in enumerate(validation_report['recommendations'][:3], 1):
                print(f"  {i}. {rec.get('message', 'No message')}")
        
        return validation_report
        
    except Exception as e:
        print(f"❌ Validation workflow failed: {e}")
        return None

def example_cleanup_workflow():
    """Example of running data cleanup workflow."""
    print("\n" + "=" * 70)
    print("DATA CLEANUP WORKFLOW EXAMPLE")
    print("=" * 70)
    
    try:
        from app.services.data_cleanup_service import DataCleanupService
        
        print("Step 1: Running cleanup analysis (dry run)...")
        cleanup_report = DataCleanupService.cleanup_all_data(dry_run=True)
        
        print(f"✓ Cleanup analysis completed at {cleanup_report['timestamp']}")
        print(f"✓ Total operations analyzed: {cleanup_report['summary']['total_operations']}")
        print(f"✓ Records that would be affected: {cleanup_report['summary']['records_affected']}")
        
        # Show top cleanup operations
        print("\nTop cleanup operations:")
        for op_name, op_result in list(cleanup_report['operations'].items())[:5]:
            if op_result['records_affected'] > 0:
                print(f"  • {op_name}: {op_result['records_affected']} records")
        
        # Example of running specific cleanup
        print("\nStep 2: Running specific cleanup for expired subscriptions...")
        specific_cleanup = DataCleanupService.cleanup_specific_issues(
            ['expired_subscriptions', 'invalid_data_values'], 
            dry_run=True
        )
        
        print(f"✓ Specific cleanup analyzed: {specific_cleanup['summary']['records_affected']} records")
        
        return cleanup_report
        
    except Exception as e:
        print(f"❌ Cleanup workflow failed: {e}")
        return None

def example_reporting_workflow():
    """Example of running comprehensive reporting workflow."""
    print("\n" + "=" * 70)
    print("DATA REPORTING WORKFLOW EXAMPLE")
    print("=" * 70)
    
    try:
        from app.services.data_reporting_service import DataReportingService
        
        print("Step 1: Generating comprehensive data quality report...")
        comprehensive_report = DataReportingService.generate_comprehensive_report()
        
        print(f"✓ Comprehensive report generated at {comprehensive_report['timestamp']}")
        print(f"✓ Overall health score: {comprehensive_report['summary']['overall_health_score']}")
        print(f"✓ Critical issues: {comprehensive_report['summary']['critical_issues']}")
        print(f"✓ Items requiring manual review: {comprehensive_report['summary']['manual_review_required']}")
        
        print("\nStep 2: Generating summary report...")
        summary_report = DataReportingService.generate_summary_report()
        
        print(f"✓ Summary report generated")
        print(f"✓ Overall status: {summary_report['health_check']['overall_status']}")
        print(f"✓ Health score: {summary_report['key_metrics'].get('health_score', 'N/A')}")
        
        # Show next actions
        if summary_report.get('next_actions'):
            print("\nRecommended next actions:")
            for i, action in enumerate(summary_report['next_actions'][:3], 1):
                print(f"  {i}. {action}")
        
        print("\nStep 3: Exporting report to JSON...")
        filename = DataReportingService.export_report_to_json(
            comprehensive_report, 
            f"data_quality_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        )
        print(f"✓ Report exported to: {filename}")
        
        return comprehensive_report
        
    except Exception as e:
        print(f"❌ Reporting workflow failed: {e}")
        return None

def example_integrated_workflow():
    """Example of integrated workflow using all services together."""
    print("\n" + "=" * 70)
    print("INTEGRATED WORKFLOW EXAMPLE")
    print("=" * 70)
    
    try:
        from app.services.data_validation_service import DataValidationService
        from app.services.data_cleanup_service import DataCleanupService
        from app.services.data_reporting_service import DataReportingService
        
        print("Running integrated data quality workflow...")
        
        # Step 1: Validate
        print("\n1. Validation Phase:")
        validation_results = DataValidationService.validate_all_data()
        critical_issues = validation_results['summary']['critical_issues']
        print(f"   Found {critical_issues} critical issues")
        
        # Step 2: Cleanup (if needed)
        print("\n2. Cleanup Phase:")
        if critical_issues > 0:
            cleanup_results = DataCleanupService.cleanup_all_data(dry_run=True)
            print(f"   {cleanup_results['summary']['records_affected']} records can be cleaned automatically")
        else:
            print("   No critical issues found, skipping cleanup")
        
        # Step 3: Report
        print("\n3. Reporting Phase:")
        comprehensive_report = DataReportingService.generate_comprehensive_report()
        health_score = comprehensive_report['summary']['overall_health_score']
        print(f"   Overall data health score: {health_score}%")
        
        # Step 4: Recommendations
        print("\n4. Recommendations:")
        recommendations = comprehensive_report.get('recommendations', [])
        if recommendations:
            for i, rec in enumerate(recommendations[:3], 1):
                print(f"   {i}. [{rec.get('priority', 'medium').upper()}] {rec.get('title', 'No title')}")
        else:
            print("   No specific recommendations at this time")
        
        print(f"\n✅ Integrated workflow completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Integrated workflow failed: {e}")
        return False

def show_production_usage_tips():
    """Show tips for using these tools in production."""
    print("\n" + "=" * 70)
    print("PRODUCTION USAGE TIPS")
    print("=" * 70)
    
    tips = [
        "1. Schedule regular validation checks (daily or weekly)",
        "2. Always run cleanup in dry-run mode first to review changes",
        "3. Export reports to JSON for historical tracking",
        "4. Set up alerts for critical data integrity issues",
        "5. Review manual review items regularly with data stewards",
        "6. Use specific cleanup for targeted issue resolution",
        "7. Monitor data health scores over time for trends",
        "8. Integrate validation into your CI/CD pipeline",
        "9. Create custom validation rules for business-specific needs",
        "10. Keep backups before running any cleanup operations"
    ]
    
    for tip in tips:
        print(f"   {tip}")
    
    print("\n" + "=" * 70)
    print("EXAMPLE CRON JOBS")
    print("=" * 70)
    
    cron_examples = [
        "# Daily validation check at 2 AM",
        "0 2 * * * /usr/bin/python /path/to/validation_script.py",
        "",
        "# Weekly comprehensive report on Sundays at 6 AM", 
        "0 6 * * 0 /usr/bin/python /path/to/weekly_report.py",
        "",
        "# Monthly cleanup (dry run) on first day of month",
        "0 3 1 * * /usr/bin/python /path/to/monthly_cleanup.py"
    ]
    
    for example in cron_examples:
        print(f"   {example}")

def main():
    """Main function to run all examples."""
    print("DATA VALIDATION AND CLEANUP TOOLS - USAGE EXAMPLES")
    print("=" * 70)
    print("This script demonstrates how to use the comprehensive data quality tools")
    print("in a production environment.")
    print()
    
    # Run example workflows
    validation_report = example_validation_workflow()
    cleanup_report = example_cleanup_workflow()
    reporting_report = example_reporting_workflow()
    integrated_success = example_integrated_workflow()
    
    # Show production tips
    show_production_usage_tips()
    
    # Final summary
    print("\n" + "=" * 70)
    print("EXAMPLE EXECUTION SUMMARY")
    print("=" * 70)
    
    results = [
        ("Validation Workflow", validation_report is not None),
        ("Cleanup Workflow", cleanup_report is not None),
        ("Reporting Workflow", reporting_report is not None),
        ("Integrated Workflow", integrated_success)
    ]
    
    for workflow_name, success in results:
        status = "✅ SUCCESS" if success else "❌ FAILED"
        print(f"{workflow_name}: {status}")
    
    all_success = all(success for _, success in results)
    
    if all_success:
        print(f"\n🎉 ALL EXAMPLE WORKFLOWS COMPLETED SUCCESSFULLY!")
        print("\nThe data validation and cleanup tools are ready for production use.")
        print("Refer to the examples above for implementation guidance.")
    else:
        print(f"\n⚠️ Some workflows failed. Check the error messages above.")
    
    return all_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)