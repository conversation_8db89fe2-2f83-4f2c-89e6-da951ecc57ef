#!/usr/bin/env python3
"""
Simple test script to verify database recreation
"""

import os
import sys

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

try:
    from app import create_app
    from app.extensions import db
    
    print("Creating Flask app...")
    app = create_app()
    
    print("Setting up app context...")
    with app.app_context():
        print("App context established")
        
        # Check current database URL
        print(f"Database URL: {app.config.get('SQLALCHEMY_DATABASE_URI', 'Not set')}")
        
        # Try to connect to database
        try:
            print("Testing database connection...")
            result = db.engine.execute(db.text("SELECT 1"))
            print("✓ Database connection successful")
        except Exception as e:
            print(f"✗ Database connection failed: {e}")
            
        # Drop all tables
        print("Dropping all existing tables...")
        try:
            db.drop_all()
            print("✓ All tables dropped successfully")
        except Exception as e:
            print(f"✗ Error dropping tables: {e}")
            
        # Create all tables from models
        print("Creating all tables from models...")
        try:
            db.create_all()
            print("✓ All tables created successfully")
        except Exception as e:
            print(f"✗ Error creating tables: {e}")
            
        # Verify tables exist
        print("Verifying tables were created...")
        try:
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            print(f"✓ Found {len(tables)} tables: {', '.join(tables)}")
            
            # Check some key tables for correct primary keys
            key_tables = ['users', 'clients', 'tutors', 'services', 'appointments']
            for table in key_tables:
                if table in tables:
                    pk_constraint = inspector.get_pk_constraint(table)
                    pk_columns = pk_constraint.get('constrained_columns', [])
                    print(f"  - {table}: primary key = {pk_columns}")
                else:
                    print(f"  - {table}: NOT FOUND")
                    
        except Exception as e:
            print(f"✗ Error verifying tables: {e}")
            
        print("Database recreation test completed!")
        
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure you're in the correct directory and have all dependencies installed")
except Exception as e:
    print(f"Unexpected error: {e}")
    import traceback
    traceback.print_exc()