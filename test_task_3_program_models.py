#!/usr/bin/env python3
"""
Test script to verify Task 3: Program-Related Models have correct primary key naming.

This test verifies that all program-related models use descriptive primary key names
and that all foreign key references are correct.
"""

import sys
import os
sys.path.insert(0, os.path.abspath('.'))

def test_program_models():
    """Test that all program-related models have correct primary key naming."""
    try:
        from app import create_app
        from app.extensions import db
        from app.models.program import (
            Program, ProgramModule, Enrollment, ModuleProgress, 
            ModuleSession, ProgramPricing, GroupSession, GroupSessionParticipant
        )
        
        app = create_app()
        
        with app.app_context():
            print("Testing Program-Related Models Primary Key Naming...")
            
            # Test 1: Verify primary key column names
            print("\n1. Testing Primary Key Names:")
            
            # Program model
            program_pk = Program.__table__.primary_key.columns.keys()[0]
            print(f"   Program primary key: {program_pk}")
            assert program_pk == 'program_id', f"Expected 'program_id', got '{program_pk}'"
            
            # ProgramModule model
            module_pk = ProgramModule.__table__.primary_key.columns.keys()[0]
            print(f"   ProgramModule primary key: {module_pk}")
            assert module_pk == 'module_id', f"Expected 'module_id', got '{module_pk}'"
            
            # Enrollment model
            enrollment_pk = Enrollment.__table__.primary_key.columns.keys()[0]
            print(f"   Enrollment primary key: {enrollment_pk}")
            assert enrollment_pk == 'enrollment_id', f"Expected 'enrollment_id', got '{enrollment_pk}'"
            
            # ModuleProgress model
            progress_pk = ModuleProgress.__table__.primary_key.columns.keys()[0]
            print(f"   ModuleProgress primary key: {progress_pk}")
            assert progress_pk == 'progress_id', f"Expected 'progress_id', got '{progress_pk}'"
            
            # ModuleSession model
            session_pk = ModuleSession.__table__.primary_key.columns.keys()[0]
            print(f"   ModuleSession primary key: {session_pk}")
            assert session_pk == 'session_id', f"Expected 'session_id', got '{session_pk}'"
            
            # ProgramPricing model
            pricing_pk = ProgramPricing.__table__.primary_key.columns.keys()[0]
            print(f"   ProgramPricing primary key: {pricing_pk}")
            assert pricing_pk == 'pricing_id', f"Expected 'pricing_id', got '{pricing_pk}'"
            
            # GroupSession model
            group_session_pk = GroupSession.__table__.primary_key.columns.keys()[0]
            print(f"   GroupSession primary key: {group_session_pk}")
            assert group_session_pk == 'group_session_id', f"Expected 'group_session_id', got '{group_session_pk}'"
            
            # GroupSessionParticipant model
            participant_pk = GroupSessionParticipant.__table__.primary_key.columns.keys()[0]
            print(f"   GroupSessionParticipant primary key: {participant_pk}")
            assert participant_pk == 'participant_id', f"Expected 'participant_id', got '{participant_pk}'"
            
            print("   ✓ All primary keys use descriptive naming convention")
            
            # Test 2: Verify foreign key references
            print("\n2. Testing Foreign Key References:")
            
            # ProgramModule foreign keys
            program_module_fks = {fk.column.name: fk.target_fullname for fk in ProgramModule.__table__.foreign_keys}
            print(f"   ProgramModule foreign keys: {program_module_fks}")
            assert 'program_id' in program_module_fks
            assert program_module_fks['program_id'] == 'programs.program_id'
            
            # Enrollment foreign keys
            enrollment_fks = {fk.column.name: fk.target_fullname for fk in Enrollment.__table__.foreign_keys}
            print(f"   Enrollment foreign keys: {enrollment_fks}")
            assert 'client_id' in enrollment_fks
            assert 'program_id' in enrollment_fks
            assert enrollment_fks['client_id'] == 'clients.client_id'
            assert enrollment_fks['program_id'] == 'programs.program_id'
            
            # ModuleProgress foreign keys
            progress_fks = {fk.column.name: fk.target_fullname for fk in ModuleProgress.__table__.foreign_keys}
            print(f"   ModuleProgress foreign keys: {progress_fks}")
            assert 'enrollment_id' in progress_fks
            assert 'module_id' in progress_fks
            assert progress_fks['enrollment_id'] == 'enrollments.enrollment_id'
            assert progress_fks['module_id'] == 'program_modules.module_id'
            
            # ModuleSession foreign keys
            session_fks = {fk.column.name: fk.target_fullname for fk in ModuleSession.__table__.foreign_keys}
            print(f"   ModuleSession foreign keys: {session_fks}")
            assert 'module_progress_id' in session_fks
            assert 'appointment_id' in session_fks
            assert session_fks['module_progress_id'] == 'module_progress.progress_id'
            assert session_fks['appointment_id'] == 'appointments.appointment_id'
            
            # GroupSession foreign keys
            group_session_fks = {fk.column.name: fk.target_fullname for fk in GroupSession.__table__.foreign_keys}
            print(f"   GroupSession foreign keys: {group_session_fks}")
            assert 'program_id' in group_session_fks
            assert 'module_id' in group_session_fks
            assert 'tutor_id' in group_session_fks
            assert group_session_fks['program_id'] == 'programs.program_id'
            assert group_session_fks['module_id'] == 'program_modules.module_id'
            assert group_session_fks['tutor_id'] == 'tutors.tutor_id'
            
            # GroupSessionParticipant foreign keys
            participant_fks = {fk.column.name: fk.target_fullname for fk in GroupSessionParticipant.__table__.foreign_keys}
            print(f"   GroupSessionParticipant foreign keys: {participant_fks}")
            assert 'group_session_id' in participant_fks
            assert 'enrollment_id' in participant_fks
            assert participant_fks['group_session_id'] == 'group_sessions.group_session_id'
            assert participant_fks['enrollment_id'] == 'enrollments.enrollment_id'
            
            print("   ✓ All foreign key references are correct")
            
            # Test 3: Verify model relationships work
            print("\n3. Testing Model Relationships:")
            
            # Test that models can be imported and relationships are defined
            program = Program()
            assert hasattr(program, 'modules'), "Program should have 'modules' relationship"
            assert hasattr(program, 'enrollments'), "Program should have 'enrollments' relationship"
            
            module = ProgramModule()
            assert hasattr(module, 'module_progress'), "ProgramModule should have 'module_progress' relationship"
            
            enrollment = Enrollment()
            assert hasattr(enrollment, 'module_progress'), "Enrollment should have 'module_progress' relationship"
            
            progress = ModuleProgress()
            assert hasattr(progress, 'module_sessions'), "ModuleProgress should have 'module_sessions' relationship"
            
            group_session = GroupSession()
            assert hasattr(group_session, 'participants'), "GroupSession should have 'participants' relationship"
            
            print("   ✓ All model relationships are properly defined")
            
            print("\n✅ Task 3 - Program-Related Models: ALL TESTS PASSED")
            print("   - All models use descriptive primary key names")
            print("   - All foreign key references are correct")
            print("   - All model relationships are properly configured")
            
            return True
            
    except Exception as e:
        print(f"\n❌ Task 3 - Program-Related Models: TEST FAILED")
        print(f"Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_program_models()
    sys.exit(0 if success else 1)