# Stripe Payouts Installation Guide

## Prerequisites

1. **Stripe Account**: Must have Canadian payouts enabled
2. **Environment Variables**: Stripe API keys and encryption key configured
3. **Database Access**: Ability to run SQL migrations

## Installation Steps

### 1. Run Database Migration

Execute the SQL migration to add the new column:

```bash
# Navigate to your database and run:
psql -d your_database -f app/migrations/add_stripe_payout_id.sql
```

Or manually execute:
```sql
ALTER TABLE tutor_payments
ADD COLUMN stripe_payout_id VARCHAR(255) NULL;

CREATE INDEX idx_tutor_payments_stripe_payout_id ON tutor_payments(stripe_payout_id);

COMMENT ON COLUMN tutor_payments.stripe_payout_id IS 'Stripe payout ID for tracking direct bank transfers';
```

### 2. Verify Environment Configuration

Ensure these environment variables are set:

```bash
# Required for Stripe integration
STRIPE_SECRET_KEY=sk_live_... # or sk_test_... for testing
STRIPE_PUBLISHABLE_KEY=pk_live_... # or pk_test_... for testing

# Required for bank account encryption
ENCRYPTION_KEY=5H_gMR5wm4WwSvqj4VWNhOyIT9zef9n6Tk89scFQmOE=
```

**Note**: For production, generate your own unique encryption key:
```bash
python docs/generate_encryption_key.py
```

### 3. Install Dependencies

The implementation uses existing dependencies. Verify these are installed:

```bash
pip install stripe
pip install cryptography
```

### 4. Restart Application

Restart your Flask application to load the new code:

```bash
# If using systemd
sudo systemctl restart your-app-service

# If running directly
python run.py
```

## Verification

### 1. Check Database Schema

Verify the new column exists:
```sql
\d tutor_payments
```

Should show `stripe_payout_id` column.

### 2. Test Manager Interface

1. Login as manager
2. Navigate to Tutor Payments
3. Verify "Process with Stripe direct bank payouts" option appears
4. Check that paid payments show Stripe payout IDs

### 3. Test Payment Processing

**Important**: Start with test mode!

1. Ensure Stripe is in test mode (`sk_test_...`)
2. Process a small test payment
3. Check Stripe Dashboard for payout record
4. Verify payment status updates correctly

## Troubleshooting

### Common Issues

#### 1. Missing Stripe Payout ID Column
**Error**: Column 'stripe_payout_id' doesn't exist
**Solution**: Run the database migration

#### 2. Stripe API Key Issues
**Error**: Invalid API key
**Solution**: Verify `STRIPE_SECRET_KEY` environment variable

#### 3. Encryption Key Issues
**Error**: Encryption key not configured
**Solution**: Set `ENCRYPTION_KEY` environment variable

**Error**: Fernet key must be 32 url-safe base64-encoded bytes
**Solution**:
- Use the provided key: `ENCRYPTION_KEY=5H_gMR5wm4WwSvqj4VWNhOyIT9zef9n6Tk89scFQmOE=`
- Or generate your own: `python docs/generate_encryption_key.py`

#### 4. Bank Account Token Errors
**Error**: Failed to create bank account token
**Solution**:
- Verify tutor has complete bank information
- Check Canadian routing number format
- Ensure Stripe account supports Canadian payouts

### Testing Checklist

- [ ] Database migration completed successfully
- [ ] Environment variables configured
- [ ] Application restarted
- [ ] Manager can access payment processing interface
- [ ] Stripe payout option appears in modal
- [ ] Test payment processes successfully (test mode)
- [ ] Stripe Dashboard shows payout record
- [ ] Payment status updates to 'paid'
- [ ] Stripe payout ID is stored and displayed

## Rollback Plan

If issues occur, you can disable Stripe payouts:

### 1. Temporary Disable
In the manager interface, uncheck "Process with Stripe direct bank payouts" to process payments without Stripe.

### 2. Code Rollback
Revert the `TutorPaymentService.process_payment()` method to call without Stripe:
```python
# In manager.py, change:
results = TutorPaymentService.process_multiple_payments(payment_ids, use_stripe=False)
```

### 3. Database Rollback (if needed)
```sql
-- Remove the column if necessary
ALTER TABLE tutor_payments DROP COLUMN stripe_payout_id;
```

## Support

For issues:
1. Check application logs for detailed error messages
2. Verify Stripe Dashboard for payout status
3. Test with small amounts in Stripe test mode first
4. Ensure all tutors have complete banking information

## Security Notes

- Never store bank account tokens
- All bank information remains encrypted
- Payout IDs are safe to log and display
- Test thoroughly before processing real payments
