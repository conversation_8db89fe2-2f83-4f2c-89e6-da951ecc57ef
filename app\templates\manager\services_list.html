<!-- app/templates/manager/services_list.html -->
{% extends "base.html" %}

{% block title %}{{ t('manager.services.title_full') }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">{{ t('manager.services.title') }}</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('manager.new_service') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add Service
        </a>
    </div>
</div>

<!-- Filter Form -->
<div class="card shadow mb-4">
    <div class="card-body">
        <form method="GET" action="{{ url_for('manager.services_list') }}" class="row g-3 filter-form">
            <div class="col-md-10">
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" class="form-control" name="search" value="{{ form.search.data or '' }}" placeholder="{{ t('manager.services.filters.search_placeholder') }}">
                </div>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary w-100">{{ t('manager.services.filters.filter', 'Filter') }}</button>
            </div>
        </form>
    </div>
</div>

<!-- Services Table -->
<div class="card shadow">
    <div class="card-body">
        {% if services %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>{{ t('manager.services.table.name') }}</th>
                            <th>{{ t('manager.services.table.duration') }}</th>
                            <th>{{ t('manager.services.table.default_price') }}</th>
                            <th>{{ t('manager.services.table.status') }}</th>
                            <th>{{ t('manager.services.table.actions') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for service in services %}
                            <tr>
                                <td>{{ service.name }}</td>
                                <td>{{ service.duration_minutes }} minutes</td>
                                <td>${{ "%.2f"|format(service.default_price) }}</td>
                                <td>
                                    <span class="badge bg-{{ 'success' if service.is_active else 'danger' }}">
                                        {{ t('manager.services.status.active') if service.is_active else t('manager.services.status.inactive') }}
                                    </span>
                                </td>
                                <td>
                                    <a href="{{ url_for('manager.edit_service', id=service.id) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit"></i> {{ t('manager.services.actions.edit') }}
                                    </a>
                                    <a href="{{ url_for('manager.tutors_by_service', id=service.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-users"></i> {{ t('manager.services.actions.tutors') }}
                                    </a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center p-5">
                <i class="fas fa-book fa-3x text-muted mb-3"></i>
                <h5>{{ t('manager.services.messages.no_services_found') }}</h5>
                <p class="text-muted">{{ t('manager.services.messages.no_services_message') }}</p>
                <a href="{{ url_for('manager.new_service') }}" class="btn btn-primary mt-3">
                    <i class="fas fa-plus"></i> {{ t('manager.services.add_service') }}
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}