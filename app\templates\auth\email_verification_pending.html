{% extends "base.html" %}

{% block title %}{{ t('auth.email_verification.title') }}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Verification Pending Header -->
            <div class="text-center mb-5">
                <div class="mb-4">
                    <i class="fas fa-envelope fa-5x text-warning"></i>
                </div>
                <h1 class="display-5 fw-bold text-warning mb-3">{{ t('auth.email_verification.header') }}</h1>
                <p class="lead">{{ t('auth.email_verification.subtitle') }}</p>
            </div>

            <!-- Instructions Card -->
            <div class="card border-warning mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle"></i>
                        {{ t('auth.email_verification.next_steps') }}
                    </h5>
                </div>
                <div class="card-body">
                    <ol class="mb-0">
                        <li class="mb-2">
                            <strong>{{ t('auth.email_verification.step1_title') }}</strong><br>
                            <small class="text-muted">{{ t('auth.email_verification.step1_desc') }}</small>
                        </li>
                        <li class="mb-2">
                            <strong>{{ t('auth.email_verification.step2_title') }}</strong><br>
                            <small class="text-muted">{{ t('auth.email_verification.step2_desc') }}</small>
                        </li>
                        <li class="mb-2">
                            <strong>{{ t('auth.email_verification.step3_title') }}</strong><br>
                            <small class="text-muted">{{ t('auth.email_verification.step3_desc') }}</small>
                        </li>
                    </ol>
                </div>
            </div>

            <!-- Security Information -->
            <div class="card border-info mb-4">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-shield-alt"></i>
                        {{ t('auth.email_verification.security_title') }}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>{{ t('auth.email_verification.security_header') }}</h6>
                            <ul class="list-unstyled small">
                                <li><i class="fas fa-check text-success"></i> {{ t('auth.email_verification.security_fraud') }}</li>
                                <li><i class="fas fa-check text-success"></i> {{ t('auth.email_verification.security_identity') }}</li>
                                <li><i class="fas fa-check text-success"></i> {{ t('auth.email_verification.security_payment') }}</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>{{ t('auth.email_verification.communication_header') }}</h6>
                            <ul class="list-unstyled small">
                                <li><i class="fas fa-check text-success"></i> {{ t('auth.email_verification.communication_receipts') }}</li>
                                <li><i class="fas fa-check text-success"></i> {{ t('auth.email_verification.communication_notifications') }}</li>
                                <li><i class="fas fa-check text-success"></i> {{ t('auth.email_verification.communication_support') }}</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Troubleshooting -->
            <div class="card border-secondary">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="fas fa-question-circle"></i>
                        {{ t('auth.email_verification.trouble_title') }}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-light">
                        <ul class="mb-0">
                            <li>{{ t('auth.email_verification.trouble_spam') }}</li>
                            <li>{{ t('auth.email_verification.trouble_correct') }}</li>
                            <li>{{ t('auth.email_verification.trouble_wait') }}</li>
                            <li>{{ t('auth.email_verification.trouble_contact') }}</li>
                        </ul>
                    </div>
                    
                    <div class="text-center mt-3">
                        <a href="mailto:<EMAIL>" class="btn btn-outline-primary">
                            <i class="fas fa-envelope"></i> {{ t('auth.email_verification.contact_support') }}
                        </a>
                    </div>
                </div>
            </div>

            <!-- Back to Home -->
            <div class="text-center mt-4">
                <a href="{{ url_for('public.tecfee_enrollment_single_step') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> {{ t('auth.email_verification.back_to_tecfee') }}
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
