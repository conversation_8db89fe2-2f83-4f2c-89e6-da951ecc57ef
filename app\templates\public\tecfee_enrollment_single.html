<!DOCTYPE html>
<html lang="{{ session.get('language', 'en') }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ t('public.tecfee.enrollment_single.page_title') }}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-red: #e57373;
            --primary-red-dark: #d32f2f;
            --primary-red-light: #ffcdd2;
            --text-dark: #212529;
            --text-gray: #6c757d;
            --bg-light: #f8f9fa;
            --bg-white: #ffffff;
            --border-color: #dee2e6;
            --shadow-sm: 0 2px 4px rgba(0,0,0,0.05);
            --shadow-md: 0 4px 6px rgba(0,0,0,0.07);
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background-color: var(--bg-light);
            color: var(--text-dark);
        }
        
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Playfair Display', serif;
            color: var(--text-dark);
        }
        
        /* Header */
        .enrollment-header {
            background: var(--bg-white);
            border-bottom: 1px solid var(--border-color);
            padding: 2rem 0;
            margin-bottom: 3rem;
        }
        
        .enrollment-header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }
        
        .enrollment-header p {
            color: var(--text-gray);
            font-size: 1.1rem;
            margin: 0;
        }
        
        /* Progress Steps */
        .progress-steps {
            display: flex;
            justify-content: center;
            margin-bottom: 3rem;
            padding: 0;
            list-style: none;
        }
        
        .progress-steps li {
            position: relative;
            flex: 1;
            text-align: center;
        }
        
        .progress-steps li:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 20px;
            left: 50%;
            width: 100%;
            height: 2px;
            background: var(--border-color);
            z-index: -1;
        }
        
        .progress-steps li.active::after {
            background: var(--primary-red);
        }
        
        .progress-step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--bg-light);
            border: 2px solid var(--border-color);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-bottom: 0.5rem;
            position: relative;
            z-index: 1;
        }
        
        .progress-steps li.active .progress-step {
            background: var(--primary-red);
            border-color: var(--primary-red);
            color: white;
        }
        
        .progress-steps li.completed .progress-step {
            background: var(--primary-red);
            border-color: var(--primary-red);
            color: white;
        }
        
        /* Cards */
        .enrollment-card {
            background: var(--bg-white);
            border-radius: 12px;
            box-shadow: var(--shadow-sm);
            margin-bottom: 2rem;
            overflow: hidden;
            transition: box-shadow 0.3s ease;
        }
        
        .enrollment-card:hover {
            box-shadow: var(--shadow-md);
        }
        
        .card-header {
            background: var(--bg-light);
            border-bottom: 1px solid var(--border-color);
            padding: 1.5rem;
        }
        
        .card-header h4 {
            margin: 0;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
        }
        
        .card-header h4 i {
            margin-right: 0.75rem;
            color: var(--primary-red);
        }
        
        /* Form Elements */
        .form-control, .form-select {
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 0.75rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: var(--primary-red);
            box-shadow: 0 0 0 0.2rem rgba(229, 115, 115, 0.25);
        }
        
        .form-label {
            font-weight: 500;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
        }
        
        .form-text {
            color: var(--text-gray);
            font-size: 0.875rem;
        }
        
        /* Session Selection */
        .session-module {
            margin-bottom: 2rem;
        }
        
        .module-header {
            background: var(--bg-light);
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
            position: relative;
        }
        
        .module-header h5 {
            margin: 0;
            font-size: 1.25rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-weight: 700;
        }
        
        .module-info-icon {
            color: var(--primary-red);
            cursor: pointer;
            font-size: 1rem;
            transition: transform 0.3s ease;
        }
        
        .module-info-icon:hover {
            transform: scale(1.1);
        }
        
        .module-description {
            margin-top: 0.75rem;
            padding-top: 0.75rem;
            border-top: 1px solid var(--border-color);
            font-size: 0.9rem;
            color: var(--text-gray);
            display: none;
        }
        
        .module-description.show {
            display: block;
        }
        
        .module-duration {
            font-weight: 500;
            color: var(--primary-red);
            margin-top: 0.5rem;
        }
        
        .session-option {
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 0.75rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .session-option:hover {
            border-color: var(--primary-red);
            background: var(--primary-red-light);
        }
        
        .session-option input[type="checkbox"]:checked + label,
        .session-option input[type="radio"]:checked + label {
            border-color: var(--primary-red);
            background: var(--primary-red-light);
        }
        
        .session-option label {
            cursor: pointer;
            margin: 0;
            width: 100%;
        }
        
        .session-date {
            font-weight: 600;
            color: var(--text-dark);
        }
        
        .session-time {
            color: var(--text-gray);
            font-size: 0.875rem;
        }
        
        .session-spots {
            float: right;
            font-size: 0.875rem;
        }
        
        .spots-available {
            color: #28a745;
        }
        
        .spots-limited {
            color: #ffc107;
        }
        
        .spots-full {
            color: #dc3545;
        }
        
        /* Pricing Options */
        .pricing-option {
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .pricing-option:hover {
            border-color: var(--primary-red);
        }
        
        .pricing-option.selected {
            border-color: var(--primary-red);
            background: var(--primary-red-light);
        }
        
        .pricing-option input[type="radio"] {
            margin-right: 0.5rem;
        }
        
        .pricing-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .pricing-price {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-red);
        }
        
        .pricing-description {
            color: var(--text-gray);
            margin-top: 0.5rem;
        }
        
        .savings-badge {
            background: var(--primary-red);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            margin-left: 0.5rem;
        }
        
        /* Total Display */
        .total-display {
            background: var(--bg-light);
            border: 2px solid var(--primary-red);
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            margin-top: 2rem;
        }
        
        .total-label {
            font-size: 1.1rem;
            color: var(--text-gray);
            margin-bottom: 0.5rem;
        }
        
        .total-amount {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-red);
        }
        
        /* Buttons */
        .btn-primary {
            background: var(--primary-red);
            border-color: var(--primary-red);
            color: white;
            padding: 0.75rem 2rem;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: var(--primary-red-dark);
            border-color: var(--primary-red-dark);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(229, 115, 115, 0.3);
        }
        
        .btn-secondary {
            background: var(--bg-light);
            border-color: var(--border-color);
            color: var(--text-dark);
            padding: 0.75rem 2rem;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .btn-secondary:hover {
            background: var(--border-color);
            border-color: var(--border-color);
        }
        
        /* Alerts */
        .alert {
            border-radius: 8px;
            border: none;
            padding: 1rem 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        .alert-info {
            background: #e3f2fd;
            color: #1565c0;
        }
        
        .alert-warning {
            background: #fff8e1;
            color: #f57c00;
        }
        
        .alert-danger {
            background: var(--primary-red-light);
            color: var(--primary-red-dark);
        }
        
        /* Loading */
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 2rem;
        }
        
        .loading-spinner i {
            font-size: 3rem;
            color: var(--primary-red);
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Program Features */
        .program-features {
            background: var(--bg-white);
            border-radius: 12px;
            padding: 2rem;
            margin: 3rem 0;
            box-shadow: var(--shadow-sm);
        }
        
        .program-features h3 {
            text-align: center;
            margin-bottom: 2rem;
            color: var(--text-dark);
        }
        
        .feature-item {
            text-align: center;
            padding: 1.5rem;
        }
        
        .feature-icon {
            font-size: 3rem;
            color: var(--primary-red);
            margin-bottom: 1rem;
        }
        
        .feature-title {
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 0.5rem;
            color: var(--text-dark);
        }
        
        .feature-description {
            color: var(--text-gray);
            font-size: 0.9rem;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .enrollment-header h1 {
                font-size: 2rem;
            }
            
            .progress-steps {
                display: none;
            }
            
            .pricing-price {
                font-size: 1.5rem;
            }
            
            .total-amount {
                font-size: 2rem;
            }
            
            .feature-item {
                margin-bottom: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="enrollment-header">
        <div class="container">
            <div class="text-center">
                <h1>{{ t('public.tecfee.enrollment_single.title') }}</h1>
                <p>{{ t('public.tecfee.enrollment_single.subtitle') }}</p>
            </div>
        </div>
    </div>

    <!-- Progress Steps -->
    <div class="container">
        <ul class="progress-steps">
            <li class="active">
                <div class="progress-step">1</div>
                <div>Informations</div>
            </li>
            <li>
                <div class="progress-step">2</div>
                <div>Sessions</div>
            </li>
            <li>
                <div class="progress-step">3</div>
                <div>Paiement</div>
            </li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="container mb-5">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <form id="enrollment-form" method="POST" action="{{ url_for('public.tecfee_enrollment_single_step') }}">
            <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
            <!-- Personal Information -->
            <div class="enrollment-card">
                <div class="card-header">
                    <h4><i class="fas fa-user"></i> Informations personnelles</h4>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Adresse email *</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                            <div class="form-text">Nous utiliserons cet email pour votre compte</div>
                            <div id="email-check-message" class="mt-2"></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">Téléphone *</label>
                            <input type="tel" class="form-control" id="phone" name="phone" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="first_name" class="form-label">Prénom *</label>
                            <input type="text" class="form-control" id="first_name" name="first_name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="last_name" class="form-label">Nom de famille *</label>
                            <input type="text" class="form-control" id="last_name" name="last_name" required>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Session Selection -->
            <div class="enrollment-card">
                <div class="card-header">
                    <h4><i class="fas fa-calendar-alt"></i> Sélection des sessions</h4>
                </div>
                <div class="card-body p-4">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> Sélectionnez les sessions auxquelles vous souhaitez participer. 
                        Vous pouvez choisir le forfait complet (12 modules) ou payer par session.
                    </div>

                    <!-- Sessions by Module -->
                    {% for module_order, module_data in sessions_by_module|dictsort %}
                        <div class="session-module">
                            <div class="module-header">
                                <h5>
                                    <span>Module {{ module_order }}: {{ module_data.module.name }}</span>
                                    <i class="fas fa-info-circle module-info-icon" data-module="{{ module_order }}"></i>
                                </h5>
                                <div class="module-description" id="module-desc-{{ module_order }}">
                                    {% if module_order == 1 %}
                                        Manipulation des structures syntaxiques et identification des classes de mots
                                        <div class="module-duration"><i class="far fa-clock"></i> Durée: 1h</div>
                                    {% elif module_order == 2 %}
                                        Utilisation appropriée des connecteurs logiques et textuels
                                        <div class="module-duration"><i class="far fa-clock"></i> Durée: 1h</div>
                                    {% elif module_order == 3 %}
                                        Identification et correction des pléonasmes, impropriétés et anglicismes
                                        <div class="module-duration"><i class="far fa-clock"></i> Durée: 1h</div>
                                    {% elif module_order == 4 %}
                                        Règles d'accord du participe passé
                                        <div class="module-duration"><i class="far fa-clock"></i> Durée: 1h</div>
                                    {% elif module_order == 5 %}
                                        Distinction des homophones et compréhension de la logique du code linguistique
                                        <div class="module-duration"><i class="far fa-clock"></i> Durée: 1h</div>
                                    {% elif module_order == 6 %}
                                        Formation des mots par affixation et expressions figées
                                        <div class="module-duration"><i class="far fa-clock"></i> Durée: 1h</div>
                                    {% elif module_order == 7 %}
                                        Analyse syntaxique et construction de phrases
                                        <div class="module-duration"><i class="far fa-clock"></i> Durée: 1h</div>
                                    {% elif module_order == 8 %}
                                        Règles de ponctuation et usage approprié des signes de ponctuation
                                        <div class="module-duration"><i class="far fa-clock"></i> Durée: 1h</div>
                                    {% elif module_order == 9 %}
                                        Identification des constituants de phrase et maîtrise de la conjugaison
                                        <div class="module-duration"><i class="far fa-clock"></i> Durée: 1h</div>
                                    {% elif module_order == 10 %}
                                        Morphologie et accords grammaticaux
                                        <div class="module-duration"><i class="far fa-clock"></i> Durée: 1h</div>
                                    {% elif module_order == 11 %}
                                        Règles d'accord complexes et cas particuliers
                                        <div class="module-duration"><i class="far fa-clock"></i> Durée: 1h</div>
                                    {% elif module_order == 12 %}
                                        Examen blanc complet dans les conditions réelles du TECFÉE (1h30 + 30 minutes pour la partie rédaction)
                                        <div class="module-duration"><i class="far fa-clock"></i> Durée: 2h</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="session-options">
                                {% for session in module_data.sessions %}
                                    <div class="session-option">
                                        <input type="checkbox" 
                                               id="session_{{ session.id }}" 
                                               name="sessions" 
                                               value="{{ session.id }}"
                                               class="form-check-input session-checkbox"
                                               data-module="{{ module_order }}">
                                        <label for="session_{{ session.id }}" class="w-100 p-2">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <span class="session-date">{{ session.session_date.strftime('%d %B %Y') }}</span>
                                                    <span class="session-time ms-3">
                                                        {{ session.start_time.strftime('%H:%M') }} - {{ session.end_time.strftime('%H:%M') }}
                                                    </span>
                                                </div>
                                                <div class="session-spots">
                                                    {% set spots_left = session.max_participants - session.current_participants_count %}
                                                    {% if spots_left > 5 %}
                                                        <span class="spots-available">
                                                            <i class="fas fa-users"></i> {{ spots_left }} places
                                                        </span>
                                                    {% elif spots_left > 0 %}
                                                        <span class="spots-limited">
                                                            <i class="fas fa-users"></i> {{ spots_left }} places
                                                        </span>
                                                    {% else %}
                                                        <span class="spots-full">
                                                            <i class="fas fa-times-circle"></i> Complet
                                                        </span>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                {% endfor %}
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Pricing Options -->
            <div class="enrollment-card">
                <div class="card-header">
                    <h4><i class="fas fa-dollar-sign"></i> Options de paiement</h4>
                </div>
                <div class="card-body p-4">
                    <div class="pricing-option" data-pricing="per_session">
                        <label class="w-100">
                            <input type="radio" name="pricing_type" value="per_session" checked>
                            <span class="pricing-title">Paiement par session</span>
                            <div class="pricing-price">44,99$ <small>par session</small></div>
                            <div class="pricing-description">
                                Payez uniquement pour les sessions auxquelles vous participez
                            </div>
                        </label>
                    </div>

                    <div class="pricing-option" data-pricing="full_package">
                        <label class="w-100">
                            <input type="radio" name="pricing_type" value="full_package">
                            <span class="pricing-title">
                                Forfait complet
                                <span class="savings-badge">Économisez 140,88$</span>
                            </span>
                            <div class="pricing-price">399,00$ <small>pour 12 modules</small></div>
                            <div class="pricing-description">
                                Accès à tous les modules du programme TECFÉE
                            </div>
                        </label>
                    </div>

                    <!-- Total Display -->
                    <div class="total-display">
                        <div class="total-label">Total à payer</div>
                        <div class="total-amount" id="total-amount">0,00$</div>
                        <div class="text-muted" id="session-count">0 session(s) sélectionnée(s)</div>
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="text-center mt-4">
                <button type="submit" class="btn btn-primary btn-lg" id="submit-btn">
                    <i class="fas fa-lock me-2"></i> Procéder au paiement sécurisé
                </button>
            </div>

            <!-- Loading Spinner -->
            <div class="loading-spinner" id="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
                <p class="mt-3">Redirection vers le paiement sécurisé...</p>
            </div>
        </form>
        
        <!-- Program Features -->
        <div class="program-features">
            <h3>Caractéristiques du Programme</h3>
            <div class="row">
                <div class="col-md-4">
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="feature-title">Sessions de Groupe</div>
                        <div class="feature-description">
                            4 à 10 étudiants par session pour un apprentissage interactif
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-video"></i>
                        </div>
                        <div class="feature-title">En Ligne et en Direct</div>
                        <div class="feature-description">
                            Sessions interactives en temps réel avec votre tuteur
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-chalkboard-teacher"></i>
                        </div>
                        <div class="feature-title">Tuteur Spécialisé</div>
                        <div class="feature-description">
                            Expertise spécifique au test TECFÉE
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="{{ url_for('static', filename='js/csrf.js') }}"></script>
    <script>
        $(document).ready(function() {
            // Email validation and checking
            let emailTimeout;
            $('#email').on('input', function() {
                clearTimeout(emailTimeout);
                const email = $(this).val();
                const messageDiv = $('#email-check-message');
                
                if (email && email.includes('@')) {
                    emailTimeout = setTimeout(function() {
                        $.ajax({
                            url: '{{ url_for("public.tecfee_check_existing") }}',
                            method: 'POST',
                            contentType: 'application/json',
                            data: JSON.stringify({email: email}),
                            success: function(response) {
                                if (response.invalid) {
                                    // Invalid email format or disposable
                                    messageDiv.html(
                                        '<div class="alert alert-danger p-2">' + 
                                        '<i class="fas fa-times-circle"></i> ' + 
                                        response.error + 
                                        '</div>'
                                    );
                                    $('#email').addClass('is-invalid');
                                } else if (response.suggestion) {
                                    // Typo suggestion
                                    messageDiv.html(
                                        '<div class="alert alert-info p-2">' + 
                                        '<i class="fas fa-info-circle"></i> ' + 
                                        response.message + 
                                        ' <a href="#" class="alert-link" onclick="$(\'#email\').val(\'' + 
                                        response.suggestion + '\'); return false;">Cliquez pour corriger</a>' +
                                        '</div>'
                                    );
                                } else if (response.exists) {
                                    // Email already exists
                                    messageDiv.html(
                                        '<div class="alert alert-warning p-2">' + 
                                        '<i class="fas fa-exclamation-triangle"></i> ' + 
                                        response.message + 
                                        '</div>'
                                    );
                                } else {
                                    // Email is valid and available
                                    messageDiv.html(
                                        '<div class="alert alert-success p-2">' + 
                                        '<i class="fas fa-check-circle"></i> Email valide' + 
                                        '</div>'
                                    );
                                    $('#email').removeClass('is-invalid');
                                }
                            }
                        });
                    }, 500);
                }
            });

            // Pricing option selection
            $('.pricing-option').click(function() {
                $('.pricing-option').removeClass('selected');
                $(this).addClass('selected');
                $(this).find('input[type="radio"]').prop('checked', true);
                updatePricing();
            });

            // Session selection
            $('.session-checkbox').change(function() {
                updatePricing();
            });

            // Update pricing calculation
            function updatePricing() {
                const pricingType = $('input[name="pricing_type"]:checked').val();
                const selectedSessions = $('.session-checkbox:checked').length;
                let total = 0;

                if (pricingType === 'full_package') {
                    // For full package, all sessions should be selected
                    if (selectedSessions < 12) {
                        $('#session-count').text('Sélectionnez toutes les sessions (12 modules)');
                        $('#session-count').addClass('text-danger');
                    } else {
                        $('#session-count').text('12 modules - Forfait complet');
                        $('#session-count').removeClass('text-danger');
                    }
                    total = 399.00;
                } else {
                    $('#session-count').text(selectedSessions + ' session(s) sélectionnée(s)');
                    $('#session-count').removeClass('text-danger');
                    total = selectedSessions * 44.99;
                }

                $('#total-amount').text(total.toFixed(2).replace('.', ',') + '$');
            }

            // Form submission validation
            $('#enrollment-form').submit(function(e) {
                const selectedSessions = $('.session-checkbox:checked').length;
                const pricingType = $('input[name="pricing_type"]:checked').val();

                if (selectedSessions === 0) {
                    e.preventDefault();
                    alert('Veuillez sélectionner au moins une session.');
                    return false;
                }

                if (pricingType === 'full_package' && selectedSessions < 12) {
                    e.preventDefault();
                    alert('Pour le forfait complet, vous devez sélectionner une session dans chaque module (12 au total).');
                    return false;
                }

                // Show loading spinner
                $('#submit-btn').prop('disabled', true);
                $('#loading-spinner').show();
            });

            // Initialize pricing
            updatePricing();
            
            // Module info toggle
            $('.module-info-icon').click(function(e) {
                e.stopPropagation();
                const moduleId = $(this).data('module');
                const description = $('#module-desc-' + moduleId);
                
                // Toggle the description
                description.toggleClass('show');
                
                // Rotate the icon
                if (description.hasClass('show')) {
                    $(this).removeClass('fa-info-circle').addClass('fa-times-circle');
                } else {
                    $(this).removeClass('fa-times-circle').addClass('fa-info-circle');
                }
            });
        });
    </script>
</body>
</html>