{% extends 'base.html' %}

{% block title %}{{ t('manager.time_off.title') }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1>{{ t('manager.time_off.title') }}</h1>
            <p class="lead">{{ t('manager.time_off.subtitle', 'Manage tutor time-off requests.') }}</p>
        </div>
        <div class="col-md-4 text-end">
            <div class="btn-group">
                <a href="{{ url_for('manager.time_off_requests', status='all') }}" class="btn btn-outline-primary {{ 'active' if current_status == 'all' }}">All</a>
                <a href="{{ url_for('manager.time_off_requests', status='pending') }}" class="btn btn-outline-warning {{ 'active' if current_status == 'pending' }}">Pending</a>
                <a href="{{ url_for('manager.time_off_requests', status='approved') }}" class="btn btn-outline-success {{ 'active' if current_status == 'approved' }}">Approved</a>
                <a href="{{ url_for('manager.time_off_requests', status='rejected') }}" class="btn btn-outline-danger {{ 'active' if current_status == 'rejected' }}">Rejected</a>
            </div>
        </div>
    </div>

    <div class="card shadow">
        <div class="card-body">
            {% if requests %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Tutor</th>
                                <th>Dates</th>
                                <th>Duration</th>
                                <th>Reason</th>
                                <th>Status</th>
                                <th>Requested On</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for request in requests %}
                                <tr>
                                    <td>{{ request.tutor.first_name }} {{ request.tutor.last_name }}</td>
                                    <td>{{ request.start_date.strftime('%Y-%m-%d') }} to {{ request.end_date.strftime('%Y-%m-%d') }}</td>
                                    <td>{{ request.duration_days }} day{% if request.duration_days != 1 %}s{% endif %}</td>
                                    <td>{{ request.reason }}</td>
                                    <td>
                                        {% if request.is_pending %}
                                            <span class="badge bg-warning">Pending</span>
                                        {% elif request.is_approved %}
                                            <span class="badge bg-success">Approved</span>
                                        {% elif request.is_rejected %}
                                            <span class="badge bg-danger">Rejected</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ request.insert_date.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        <a href="{{ url_for('manager.view_time_off_request', id=request.id) }}" class="btn btn-sm btn-outline-primary">View</a>
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info">
                    No time-off requests found.
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
