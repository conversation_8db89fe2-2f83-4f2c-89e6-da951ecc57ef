# app/services/scheduling_service.py
from datetime import datetime, timedelta, date, time
from app.extensions import db
from app.models.appointment import Appointment
from app.models.appointment_recurring_schedule import AppointmentRecurringSchedule
from app.services.recurring_appointment_service import RecurringAppointmentService
from sqlalchemy import and_, or_, func
from flask import current_app
import logging

class SchedulingService:
    """Service for managing appointment scheduling workflows and operations."""

    @staticmethod
    def create_recurring_schedule_with_initial_appointments(schedule_data, generate_weeks=4):
        """
        Create a recurring schedule and generate initial appointments.
        
        Args:
            schedule_data (dict): Dictionary containing schedule parameters
            generate_weeks (int): Number of weeks ahead to generate initial appointments
            
        Returns:
            tuple: (schedule, generated_appointments, error_message)
        """
        try:
            # Create the recurring schedule
            schedule, error = RecurringAppointmentService.create_recurring_schedule(schedule_data)
            if error:
                return None, [], error
            
            # Generate initial appointments
            end_date = datetime.now().date() + timedelta(weeks=generate_weeks)
            appointments, gen_error = RecurringAppointmentService.generate_appointments_for_schedule(
                schedule.schedule_id, end_date
            )
            
            if gen_error:
                current_app.logger.warning(f"Schedule created but appointment generation failed: {gen_error}")
                return schedule, [], gen_error
            
            db.session.commit()
            
            current_app.logger.info(
                f"Created recurring schedule {schedule.schedule_id} with {len(appointments)} initial appointments"
            )
            
            return schedule, appointments, None
            
        except Exception as e:
            db.session.rollback()
            error_msg = f"Error creating recurring schedule with appointments: {str(e)}"
            current_app.logger.error(error_msg)
            return None, [], error_msg

    @staticmethod
    def get_tutor_availability_conflicts(tutor_id, start_time, end_time, exclude_appointment_id=None):
        """
        Get detailed information about tutor availability conflicts.
        
        Args:
            tutor_id (int): ID of the tutor
            start_time (datetime): Start time to check
            end_time (datetime): End time to check
            exclude_appointment_id (int): Appointment ID to exclude from conflict check
            
        Returns:
            list: List of conflicting appointments
        """
        try:
            conflicts_query = Appointment.query.filter(
                Appointment.tutor_id == tutor_id,
                Appointment.status != 'cancelled',
                or_(
                    # Case 1: New appointment starts during existing appointment
                    and_(
                        Appointment.start_time <= start_time,
                        Appointment.end_time > start_time
                    ),
                    # Case 2: New appointment ends during existing appointment
                    and_(
                        Appointment.start_time < end_time,
                        Appointment.end_time >= end_time
                    ),
                    # Case 3: New appointment completely overlaps existing appointment
                    and_(
                        Appointment.start_time >= start_time,
                        Appointment.end_time <= end_time
                    )
                )
            )
            
            if exclude_appointment_id:
                conflicts_query = conflicts_query.filter(Appointment.appointment_id != exclude_appointment_id)
            
            return conflicts_query.all()
            
        except Exception as e:
            current_app.logger.error(f"Error checking tutor availability: {str(e)}")
            return []

    @staticmethod
    def find_available_time_slots(tutor_id, date, duration_minutes, start_hour=8, end_hour=18):
        """
        Find available time slots for a tutor on a specific date.
        
        Args:
            tutor_id (int): ID of the tutor
            date (date): Date to check availability
            duration_minutes (int): Duration of the appointment in minutes
            start_hour (int): Earliest hour to consider (default: 8 AM)
            end_hour (int): Latest hour to consider (default: 6 PM)
            
        Returns:
            list: List of available time slots as (start_time, end_time) tuples
        """
        try:
            # Get existing appointments for the tutor on this date
            existing_appointments = Appointment.query.filter(
                Appointment.tutor_id == tutor_id,
                func.date(Appointment.start_time) == date,
                Appointment.status != 'cancelled'
            ).order_by(Appointment.start_time).all()
            
            # Create time slots to check (every 30 minutes)
            available_slots = []
            current_time = datetime.combine(date, time(start_hour, 0))
            end_time = datetime.combine(date, time(end_hour, 0))
            
            while current_time + timedelta(minutes=duration_minutes) <= end_time:
                slot_end = current_time + timedelta(minutes=duration_minutes)
                
                # Check if this slot conflicts with any existing appointment
                has_conflict = False
                for appointment in existing_appointments:
                    if (current_time < appointment.end_time and slot_end > appointment.start_time):
                        has_conflict = True
                        break
                
                if not has_conflict:
                    available_slots.append((current_time, slot_end))
                
                # Move to next 30-minute slot
                current_time += timedelta(minutes=30)
            
            return available_slots
            
        except Exception as e:
            current_app.logger.error(f"Error finding available time slots: {str(e)}")
            return []

    @staticmethod
    def reschedule_appointment(appointment_id, new_start_time, new_end_time, reason=None):
        """
        Reschedule an existing appointment to a new time.
        
        Args:
            appointment_id (int): ID of the appointment to reschedule
            new_start_time (datetime): New start time
            new_end_time (datetime): New end time
            reason (str): Optional reason for rescheduling
            
        Returns:
            tuple: (success, error_message)
        """
        try:
            appointment = Appointment.query.get(appointment_id)
            if not appointment:
                return False, "Appointment not found"
            
            # Check availability at new time
            conflicts = SchedulingService.get_tutor_availability_conflicts(
                appointment.tutor_id, new_start_time, new_end_time, appointment_id
            )
            
            if conflicts:
                conflict_times = [f"{c.start_time.strftime('%H:%M')}-{c.end_time.strftime('%H:%M')}" 
                                for c in conflicts]
                return False, f"Tutor has conflicts at: {', '.join(conflict_times)}"
            
            # Store old values for audit
            old_start = appointment.start_time
            old_end = appointment.end_time
            
            # Update appointment
            appointment.start_time = new_start_time
            appointment.end_time = new_end_time
            appointment.status = 'rescheduled'
            appointment.modification_date = datetime.utcnow()
            
            if reason:
                appointment.notes = f"{appointment.notes or ''}\nRescheduled: {reason}".strip()
            
            db.session.commit()
            
            current_app.logger.info(
                f"Rescheduled appointment {appointment_id} from "
                f"{old_start.strftime('%Y-%m-%d %H:%M')} to {new_start_time.strftime('%Y-%m-%d %H:%M')}"
            )
            
            return True, None
            
        except Exception as e:
            db.session.rollback()
            error_msg = f"Error rescheduling appointment {appointment_id}: {str(e)}"
            current_app.logger.error(error_msg)
            return False, error_msg

    @staticmethod
    def bulk_reschedule_recurring_appointments(schedule_id, new_start_time, effective_date=None):
        """
        Reschedule all future appointments from a recurring schedule.
        
        Args:
            schedule_id (int): ID of the recurring schedule
            new_start_time (time): New start time for appointments
            effective_date (date): Date from which to apply changes (default: today)
            
        Returns:
            tuple: (rescheduled_count, error_message)
        """
        try:
            schedule = AppointmentRecurringSchedule.query.get(schedule_id)
            if not schedule:
                return 0, "Recurring schedule not found"
            
            if effective_date is None:
                effective_date = datetime.now().date()
            
            # Update the schedule template
            old_time = schedule.start_time
            schedule.start_time = new_start_time
            schedule.modification_date = datetime.utcnow()
            
            # Get future appointments to reschedule
            future_appointments = schedule.generated_appointments.filter(
                func.date(Appointment.start_time) >= effective_date,
                Appointment.status.in_(['scheduled', 'confirmed'])
            ).all()
            
            rescheduled_count = 0
            for appointment in future_appointments:
                # Calculate new times
                appointment_date = appointment.start_time.date()
                new_start_datetime = datetime.combine(appointment_date, new_start_time)
                new_end_datetime = new_start_datetime + timedelta(minutes=schedule.duration_minutes)
                
                # Check for conflicts
                conflicts = SchedulingService.get_tutor_availability_conflicts(
                    appointment.tutor_id, new_start_datetime, new_end_datetime, appointment.appointment_id
                )
                
                if not conflicts:
                    appointment.start_time = new_start_datetime
                    appointment.end_time = new_end_datetime
                    appointment.modification_date = datetime.utcnow()
                    rescheduled_count += 1
                else:
                    current_app.logger.warning(
                        f"Could not reschedule appointment {appointment.appointment_id} due to conflicts"
                    )
            
            db.session.commit()
            
            current_app.logger.info(
                f"Bulk rescheduled {rescheduled_count} appointments for schedule {schedule_id} "
                f"from {old_time.strftime('%H:%M')} to {new_start_time.strftime('%H:%M')}"
            )
            
            return rescheduled_count, None
            
        except Exception as e:
            db.session.rollback()
            error_msg = f"Error bulk rescheduling appointments for schedule {schedule_id}: {str(e)}"
            current_app.logger.error(error_msg)
            return 0, error_msg

    @staticmethod
    def get_scheduling_statistics(start_date=None, end_date=None):
        """
        Get scheduling statistics for a date range.
        
        Args:
            start_date (date): Start date for statistics (default: 30 days ago)
            end_date (date): End date for statistics (default: today)
            
        Returns:
            dict: Scheduling statistics
        """
        try:
            if start_date is None:
                start_date = datetime.now().date() - timedelta(days=30)
            if end_date is None:
                end_date = datetime.now().date()
            
            # Base query for the date range
            base_query = Appointment.query.filter(
                func.date(Appointment.start_time) >= start_date,
                func.date(Appointment.start_time) <= end_date
            )
            
            # Get statistics
            total_appointments = base_query.count()
            recurring_appointments = base_query.filter(
                Appointment.recurring_schedule_id.isnot(None)
            ).count()
            
            status_counts = {}
            for status in ['scheduled', 'completed', 'cancelled', 'no-show', 'rescheduled']:
                status_counts[status] = base_query.filter(Appointment.status == status).count()
            
            # Active recurring schedules
            active_schedules = AppointmentRecurringSchedule.query.filter_by(is_active=True).count()
            
            return {
                'date_range': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat()
                },
                'total_appointments': total_appointments,
                'recurring_appointments': recurring_appointments,
                'one_time_appointments': total_appointments - recurring_appointments,
                'status_breakdown': status_counts,
                'active_recurring_schedules': active_schedules,
                'recurring_percentage': round((recurring_appointments / total_appointments * 100), 2) if total_appointments > 0 else 0
            }
            
        except Exception as e:
            current_app.logger.error(f"Error getting scheduling statistics: {str(e)}")
            return {}

    @staticmethod
    def validate_appointment_timing(start_time, end_time, tutor_id=None):
        """
        Validate appointment timing constraints.
        
        Args:
            start_time (datetime): Start time of the appointment
            end_time (datetime): End time of the appointment
            tutor_id (int): Optional tutor ID for availability check
            
        Returns:
            tuple: (is_valid, error_messages)
        """
        errors = []
        
        # Basic time validation
        if start_time >= end_time:
            errors.append("Start time must be before end time")
        
        # Duration validation
        duration = (end_time - start_time).total_seconds() / 60
        if duration < 15:
            errors.append("Appointment must be at least 15 minutes long")
        elif duration > 480:  # 8 hours
            errors.append("Appointment cannot be longer than 8 hours")
        
        # Business hours validation (8 AM to 8 PM)
        if start_time.hour < 8 or end_time.hour > 20:
            errors.append("Appointments must be between 8:00 AM and 8:00 PM")
        
        # Past date validation
        if start_time < datetime.now():
            errors.append("Cannot schedule appointments in the past")
        
        # Weekend validation (optional - can be configured)
        if start_time.weekday() == 6:  # Sunday
            errors.append("Appointments cannot be scheduled on Sundays")
        
        # Tutor availability check
        if tutor_id:
            conflicts = SchedulingService.get_tutor_availability_conflicts(tutor_id, start_time, end_time)
            if conflicts:
                conflict_times = [f"{c.start_time.strftime('%H:%M')}-{c.end_time.strftime('%H:%M')}" 
                                for c in conflicts]
                errors.append(f"Tutor has conflicts at: {', '.join(conflict_times)}")
        
        return len(errors) == 0, errors

    @staticmethod
    def get_upcoming_recurring_generations():
        """
        Get information about upcoming recurring appointment generations.
        
        Returns:
            list: List of schedules that need appointment generation
        """
        try:
            # Find schedules that haven't generated appointments recently
            cutoff_date = datetime.now().date() + timedelta(days=7)  # Need appointments for next week
            
            schedules_needing_generation = []
            active_schedules = AppointmentRecurringSchedule.query.filter_by(is_active=True).all()
            
            for schedule in active_schedules:
                # Check if we need to generate more appointments
                last_generated = schedule.last_generated_date or schedule.pattern_start_date
                
                if last_generated < cutoff_date:
                    next_occurrence = schedule.get_next_occurrence(last_generated)
                    if next_occurrence and next_occurrence <= cutoff_date:
                        schedules_needing_generation.append({
                            'schedule_id': schedule.schedule_id,
                            'tutor_name': f"{schedule.tutor.first_name} {schedule.tutor.last_name}" if schedule.tutor else "Unknown",
                            'client_name': schedule.appointment_subject_name,
                            'frequency': schedule.frequency,
                            'last_generated_date': last_generated.isoformat(),
                            'next_occurrence': next_occurrence.isoformat()
                        })
            
            return schedules_needing_generation
            
        except Exception as e:
            current_app.logger.error(f"Error getting upcoming recurring generations: {str(e)}")
            return []