<!-- app/templates/manager/appointment_detail.html -->
{% extends "base.html" %}

{% block title %}Appointment Details - Tutoring Appointment System{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/audit_trail_modal.css') }}">
{% endblock %}

{% block content %}
<div class="page-header fade-in-up">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1>Appointment Details</h1>
            <p>View and manage appointment information</p>
        </div>
        <div class="action-buttons">
            <a href="{{ url_for('manager.schedule') }}" class="btn btn-outline-primary">
                <i class="fas fa-calendar-alt"></i> Schedule
            </a>
            <button type="button" class="btn btn-outline-secondary" data-audit-appointment-id="{{ appointment.id }}" title="View Audit Trail">
                <i class="fas fa-history"></i> Audit Trail
            </button>
            <a href="{{ url_for('manager.edit_appointment', id=appointment.id) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> Edit
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <!-- Appointment Details Card -->
        <div class="card mb-4 fade-in-up">
            <div class="card-header">
                <h5 class="mb-0">Appointment Information</h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <h6 class="mb-2">Date & Time</h6>
                        <p>
                            {% if appointment.start_time and appointment.end_time %}
                                {{ appointment.start_time.strftime('%A, %B %d, %Y') }}<br>
                                {{ appointment.start_time.strftime('%I:%M %p') }} - {{ appointment.end_time.strftime('%I:%M %p') }}
                                {% if appointment.is_recurring %}
                                    <br><small class="text-muted">(Recurring appointment)</small>
                                {% endif %}
                            {% else %}
                                <em>Time not set</em>
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="mb-2">Status</h6>
                        <p>
                            {% set status_color = 'primary' %}
                            {% if appointment.status == 'completed' %}
                                {% set status_color = 'success' %}
                            {% elif appointment.status == 'cancelled' %}
                                {% set status_color = 'danger' %}
                            {% elif appointment.status == 'awaiting_confirmation' or appointment.status == 'awaiting_confirm' %}
                                {% set status_color = 'info' %}
                            {% elif appointment.status == 'no-show' %}
                                {% set status_color = 'warning' %}
                            {% endif %}
                            <span class="badge bg-{{ status_color }} p-2">
                                {% if appointment.status == 'awaiting_confirmation' or appointment.status == 'awaiting_confirm' %}
                                    Awaiting Confirmation
                                {% else %}
                                    {{ appointment.status | capitalize }}
                                {% endif %}
                            </span>
                        </p>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <h6 class="mb-2">
                            {% if appointment.dependant_id %}
                                Student (Dependant)
                            {% else %}
                                Client
                            {% endif %}
                        </h6>
                        <p>
                            {% if appointment.dependant_id and appointment.dependant %}
                                <a href="{{ url_for('manager.view_dependant', id=appointment.dependant.id) }}">
                                    {{ appointment.dependant.first_name }} {{ appointment.dependant.last_name }}
                                </a>
                                <br>
                                {% if appointment.client %}
                                <small class="text-muted">
                                    Client: <a href="{{ url_for('manager.view_client', id=appointment.client.id) }}">
                                        {{ appointment.client.first_name }} {{ appointment.client.last_name }}
                                    </a>
                                </small>
                                {% endif %}
                            {% elif appointment.client %}
                                <a href="{{ url_for('manager.view_client', id=appointment.client.id) }}">
                                    {{ appointment.client.first_name }} {{ appointment.client.last_name }}
                                </a>
                            {% else %}
                                <em>No client information available</em>
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="mb-2">Tutor</h6>
                        <p>
                            {% if appointment.tutor %}
                            <a href="{{ url_for('manager.view_tutor', id=appointment.tutor.id) }}">
                                {{ appointment.tutor.first_name }} {{ appointment.tutor.last_name }}
                            </a>
                            {% else %}
                            <em>No tutor information available</em>
                            {% endif %}
                        </p>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <h6 class="mb-2">Service</h6>
                        <p>
                            {% if appointment.tutor_service and appointment.tutor_service.service %}
                                {{ appointment.tutor_service.service.name }}
                            {% else %}
                                N/A
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="mb-2">Duration</h6>
                        <p>
                            {% if appointment.calculated_duration_minutes %}
                                {{ appointment.calculated_duration_minutes }} minutes
                            {% else %}
                                <em>Duration not calculated</em>
                            {% endif %}
                        </p>
                    </div>
                </div>

                {% if appointment.notes %}
                <div class="row mb-3">
                    <div class="col-12">
                        <h6 class="mb-2">Notes</h6>
                        <p>{{ appointment.notes }}</p>
                    </div>
                </div>
                {% endif %}

                <!-- Attendance Confirmation Section -->
                <div class="row mt-4">
                    <div class="col-12">


                        {% if appointment.status == 'scheduled' %}
                            <div class="alert alert-primary">
                                <i class="fas fa-calendar-check"></i> <strong>Ready for Attendance</strong><br>
                                <small>Use the buttons below to update the appointment status.</small>
                            </div>
                            <div class="d-grid gap-2 d-md-block">
                                <form method="POST" action="{{ url_for('manager.take_attendance', id=appointment.id) }}" class="d-inline">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                    <button type="submit" class="btn btn-warning btn-lg me-2 mb-2">
                                        <i class="fas fa-clock"></i> Mark Awaiting Confirmation
                                    </button>
                                </form>
                                <form method="POST" action="{{ url_for('manager.quick_status_change', id=appointment.id) }}" class="d-inline">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                    <input type="hidden" name="status" value="cancelled">
                                    <button type="submit" class="btn btn-danger btn-lg mb-2">
                                        <i class="fas fa-times-circle"></i> Cancel
                                    </button>
                                </form>
                            </div>

                        {% elif appointment.status == 'awaiting_confirmation' or appointment.status == 'awaiting_confirm' %}
                            <div class="alert alert-info border-left-info">
                                <i class="fas fa-clock"></i> <strong>Attendance Confirmation Required</strong><br>
                                <small>This appointment is awaiting attendance confirmation. Please confirm the attendance status below.</small>
                            </div>
                            <div class="d-grid gap-2 d-md-block">
                                <form method="POST" action="{{ url_for('manager.quick_status_change', id=appointment.id) }}" class="d-inline">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                    <input type="hidden" name="status" value="completed">
                                    <button type="submit" class="btn btn-success btn-lg me-2 mb-2">
                                        <i class="fas fa-check-circle"></i> Mark Completed
                                    </button>
                                </form>
                                <form method="POST" action="{{ url_for('manager.quick_status_change', id=appointment.id) }}" class="d-inline">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                    <input type="hidden" name="status" value="cancelled">
                                    <button type="submit" class="btn btn-danger btn-lg mb-2">
                                        <i class="fas fa-times-circle"></i> Cancel
                                    </button>
                                </form>
                            </div>

                        {% elif appointment.status == 'completed' %}
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle"></i> <strong>Appointment Completed</strong><br>
                                <small>This appointment has been marked as completed.</small>
                            </div>
                            <div class="d-grid gap-2 d-md-block">
                                <form method="POST" action="{{ url_for('manager.quick_status_change', id=appointment.id) }}" class="d-inline">
                                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                                    <input type="hidden" name="status" value="cancelled">
                                    <button type="submit" class="btn btn-outline-danger btn-lg mb-2">
                                        <i class="fas fa-times-circle"></i> Cancel
                                    </button>
                                </form>
                            </div>

                        {% elif appointment.status == 'cancelled' %}
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-circle"></i> <strong>Appointment Cancelled</strong><br>
                                <small>This appointment was cancelled with notice.</small>
                            </div>

                        {% elif appointment.status == 'no-show' %}
                            <div class="alert alert-danger">
                                <i class="fas fa-times-circle"></i> <strong>No-Show</strong><br>
                                <small>This appointment was marked as a no-show (cancelled without notice).</small>
                            </div>

                        {% else %}
                            <div class="alert alert-secondary">
                                <i class="fas fa-question-circle"></i> <strong>Unknown Status</strong><br>
                                <small>Status: {{ appointment.status }}</small>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Student/Client Details Card -->
        <div class="card shadow mb-4">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    {% if appointment.dependant_id %}
                        Student Information
                    {% else %}
                        Client Information
                    {% endif %}
                </h5>
            </div>
            <div class="card-body">
                {% if appointment.dependant_id and appointment.dependant %}
                    <!-- Dependant Information -->
                    <h6>{{ appointment.dependant.first_name }} {{ appointment.dependant.last_name }}</h6>

                    {% if appointment.dependant.date_of_birth %}
                        <div class="mb-3">
                            <h6 class="text-muted mb-2">Age</h6>
                            <p>{{ appointment.dependant.age }} years</p>
                        </div>
                    {% endif %}

                    {% if appointment.dependant.school_grade %}
                        <div class="mb-3">
                            <h6 class="text-muted mb-2">Grade</h6>
                            <p>{{ appointment.dependant.school_grade }}</p>
                        </div>
                    {% endif %}

                    {% if appointment.dependant.notes %}
                        <div class="mb-3">
                            <h6 class="text-muted mb-2">Notes</h6>
                            <p>{{ appointment.dependant.notes }}</p>
                        </div>
                    {% endif %}

                    <a href="{{ url_for('manager.view_dependant', id=appointment.dependant.id) }}" class="btn btn-outline-info btn-sm me-2">
                        <i class="fas fa-user"></i> View Student Profile
                    </a>

                    <hr class="my-3">
                    <h6 class="text-muted">Responsible Client</h6>
                    <p>{{ appointment.client.first_name }} {{ appointment.client.last_name }}</p>
                    <a href="{{ url_for('manager.view_client', id=appointment.client.id) }}" class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-user"></i> View Client Profile
                    </a>
                {% else %}
                    <!-- Client Information -->
                    <h6>{{ appointment.client.first_name }} {{ appointment.client.last_name }}</h6>

                    {% if appointment.client.client_type == 'individual' and appointment.client.individual_clients %}
                        {% if appointment.client.individual_clients.date_of_birth %}
                            <div class="mb-3">
                                <h6 class="text-muted mb-2">Age</h6>
                                <p>{{ appointment.client.individual_clients.age }} years</p>
                            </div>
                        {% endif %}

                        {% if appointment.client.individual_clients.notes %}
                            <div class="mb-3">
                                <h6 class="text-muted mb-2">Notes</h6>
                                <p>{{ appointment.client.individual_clients.notes }}</p>
                            </div>
                        {% endif %}
                    {% endif %}

                    <a href="{{ url_for('manager.view_client', id=appointment.client.id) }}" class="btn btn-outline-info btn-sm">
                        <i class="fas fa-user"></i> View Client Profile
                    </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get all forms with status change actions
    const statusForms = document.querySelectorAll('form[action*="quick_status_change"], form[action*="take_attendance"], form[action*="confirm_attendance"]');
    
    statusForms.forEach(form => {
        form.addEventListener('submit', function(e) {
            // Get the submit button
            const submitButton = form.querySelector('button[type="submit"]');
            
            if (submitButton) {
                // Check if already submitting
                if (submitButton.disabled) {
                    e.preventDefault();
                    return false;
                }
                
                // Disable the button
                submitButton.disabled = true;
                
                // Store original button content
                const originalContent = submitButton.innerHTML;
                
                // Add loading spinner
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
                
                // Re-enable after 5 seconds as a failsafe (in case of error)
                setTimeout(() => {
                    submitButton.disabled = false;
                    submitButton.innerHTML = originalContent;
                }, 5000);
            }
        });
    });
    
    // Also prevent double-clicking on any status change button
    const allStatusButtons = document.querySelectorAll('.btn-success, .btn-danger, .btn-warning, .btn-info');
    allStatusButtons.forEach(button => {
        if (button.type === 'submit') {
            button.addEventListener('click', function(e) {
                // If button is already disabled, prevent the click
                if (this.disabled) {
                    e.preventDefault();
                    e.stopPropagation();
                }
            });
        }
    });
});
</script>
<script src="{{ url_for('static', filename='js/audit_trail_modal.js') }}"></script>
{% endblock %}

<!-- Include Audit Trail Modal -->
{% include 'components/audit_trail_modal.html' %}
