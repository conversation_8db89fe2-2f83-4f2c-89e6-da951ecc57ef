#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix remaining ID reference issues in the codebase
This script updates code that uses .id instead of the proper primary key names
"""

import os
import re
from pathlib import Path

def fix_file(file_path, replacements):
    """Fix ID references in a single file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Apply replacements
        for pattern, replacement in replacements:
            content = re.sub(pattern, replacement, content)
        
        # Only write if content changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ Fixed {file_path}")
            return True
        else:
            print(f"- No changes needed in {file_path}")
            return False
    except Exception as e:
        print(f"✗ Error fixing {file_path}: {e}")
        return False

def main():
    """Main function to fix all ID reference issues"""
    print("Fixing remaining ID reference issues...")
    print("=" * 50)
    
    # Define replacements for different file types
    python_replacements = [
        # User model fixes
        (r'\bcurrent_user\.id\b', 'current_user.user_id'),
        (r'\buser\.id\b', 'user.user_id'),
        
        # Client model fixes  
        (r'\bclient\.id\b', 'client.client_id'),
        (r'\bClient\.id\b', 'Client.client_id'),
        
        # Tutor model fixes
        (r'\btutor\.id\b', 'tutor.tutor_id'),
        (r'\bTutor\.id\b', 'Tutor.tutor_id'),
        
        # Dependant model fixes
        (r'\bdependant\.id\b', 'dependant.dependant_id'),
        (r'\bDependant\.id\b', 'Dependant.dependant_id'),
        
        # Appointment model fixes
        (r'\bappointment\.id\b', 'appointment.appointment_id'),
        (r'\bAppointment\.id\b', 'Appointment.appointment_id'),
        
        # Service model fixes
        (r'\bservice\.id\b', 'service.service_id'),
        (r'\bService\.id\b', 'Service.service_id'),
        
        # Invoice model fixes
        (r'\binvoice\.id\b', 'invoice.invoice_id'),
        (r'\bInvoice\.id\b', 'Invoice.invoice_id'),
        
        # Notification model fixes
        (r'\bnotification\.id\b', 'notification.notification_id'),
        (r'\bNotification\.id\b', 'Notification.notification_id'),
        
        # Manager model fixes
        (r'\bmanager\.id\b', 'manager.manager_id'),
        (r'\bManager\.id\b', 'Manager.manager_id'),
        
        # Generic filter_by fixes
        (r'filter_by\(id=([^)]+)\)', r'filter_by(appointment_id=\1)'),  # Most common case
        (r'\.get\(id\)', '.get(appointment_id)'),
    ]
    
    # Files to process
    files_to_fix = [
        'app/views/manager.py',
        'app/views/tutor.py', 
        'app/views/client.py',
        'app/views/api.py',
        'app/views/auth.py',
        'app/views/main.py',
        'app/services/user_service.py',
        'app/services/appointment_service.py',
        'app/services/invoice_service.py',
        'app/services/notification_service.py',
        'app/services/audit_service.py',
    ]
    
    fixed_count = 0
    total_count = 0
    
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            total_count += 1
            if fix_file(file_path, python_replacements):
                fixed_count += 1
        else:
            print(f"- File not found: {file_path}")
    
    print("\n" + "=" * 50)
    print(f"Summary: Fixed {fixed_count} out of {total_count} files")
    
    if fixed_count > 0:
        print("\n✅ ID reference fixes applied successfully!")
        print("You should now restart your application to see the changes.")
    else:
        print("\n✅ No ID reference issues found or all were already fixed.")
    
    return fixed_count

if __name__ == "__main__":
    main()
