@echo off
R<PERSON> Batch script to run the database schema fix migration on Windows
REM This script applies the database migration to fix the mismatch between
REM the database schema and SQLAlchemy models for the tutors and dependants tables.

echo Database Schema Fix Migration
echo ========================================
echo Fixes both tutors and dependants table schemas
echo.

REM Check if we're in the right directory
if not exist "app" (
    echo Error: This script must be run from the project root directory
    echo Current directory: %CD%
    pause
    exit /b 1
)

REM Check if migration file exists
if not exist "app\migrations\fix_tutor_table_schema.sql" (
    echo Error: Migration file not found at app\migrations\fix_tutor_table_schema.sql
    pause
    exit /b 1
)

REM Get database connection details from environment variables or prompt
if "%DB_HOST%"=="" set DB_HOST=localhost
if "%DB_PORT%"=="" set DB_PORT=5432
if "%DB_NAME%"=="" set DB_NAME=tutoraide
if "%DB_USER%"=="" set DB_USER=postgres

if "%DB_PASSWORD%"=="" (
    echo Warning: DB_PASSWORD environment variable not set
    set /p DB_PASSWORD=Enter database password: 
)

REM Set PGPASSWORD environment variable
set PGPASSWORD=%DB_PASSWORD%

echo Running database schema fix migration...
echo Database: %DB_HOST%:%DB_PORT%/%DB_NAME%
echo User: %DB_USER%
echo Migration file: app\migrations\fix_tutor_table_schema.sql
echo This will fix both tutors and dependants table schemas.
echo.

REM Run the migration using psql
psql -h %DB_HOST% -p %DB_PORT% -U %DB_USER% -d %DB_NAME% -f "app\migrations\fix_tutor_table_schema.sql"

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo ✓ Migration completed successfully!
    echo The tutors and dependants table schemas have been updated to match the SQLAlchemy models.
    echo You can now restart your application.
) else (
    echo.
    echo ========================================
    echo ✗ Migration failed!
    echo Please check the error messages above and try again.
)

echo.
pause
