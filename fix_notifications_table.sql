-- Fix Notifications Table Schema to Match SQLAlchemy Model
-- This script fixes the mismatch between the database schema and the Notification model

-- ========================================
-- NOTIFICATIONS TABLE FIXES
-- ========================================

-- Add category column (rename from notification_type if it exists)
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'notifications' 
               AND column_name = 'notification_type') 
       AND NOT EXISTS (SELECT 1 FROM information_schema.columns 
                       WHERE table_name = 'notifications' 
                       AND column_name = 'category') THEN
        ALTER TABLE notifications RENAME COLUMN notification_type TO category;
        RAISE NOTICE 'Renamed notification_type to category in notifications table';
    ELSIF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                      WHERE table_name = 'notifications' 
                      AND column_name = 'category') THEN
        ALTER TABLE notifications ADD COLUMN category VARCHAR(50) NOT NULL DEFAULT 'system';
        RAISE NOTICE 'Added category column to notifications table';
    END IF;
END $$;

-- Add related_id column (generic related object ID)
ALTER TABLE notifications ADD COLUMN IF NOT EXISTS related_id INTEGER;

-- Migrate data from related_appointment_id to related_id if needed
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'notifications' 
               AND column_name = 'related_appointment_id') THEN
        -- Copy appointment IDs to related_id where related_id is null
        UPDATE notifications 
        SET related_id = related_appointment_id 
        WHERE related_appointment_id IS NOT NULL AND related_id IS NULL;
        RAISE NOTICE 'Migrated related_appointment_id data to related_id';
    END IF;
END $$;

-- Remove title column if it exists (not in SQLAlchemy model)
-- We'll keep it for now but note it for manual cleanup
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'notifications' 
               AND column_name = 'title') THEN
        RAISE NOTICE 'Note: title column exists but is not in SQLAlchemy model - consider manual cleanup';
    END IF;
END $$;

-- Remove read_at column if it exists (not in SQLAlchemy model)
-- We'll keep it for now but note it for manual cleanup
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'notifications' 
               AND column_name = 'read_at') THEN
        RAISE NOTICE 'Note: read_at column exists but is not in SQLAlchemy model - consider manual cleanup';
    END IF;
END $$;

-- Remove priority column if it exists (not in SQLAlchemy model)
-- We'll keep it for now but note it for manual cleanup
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns 
               WHERE table_name = 'notifications' 
               AND column_name = 'priority') THEN
        RAISE NOTICE 'Note: priority column exists but is not in SQLAlchemy model - consider manual cleanup';
    END IF;
END $$;

-- Update any check constraints on the category column
DO $$
BEGIN
    -- Drop old constraint if it exists
    IF EXISTS (SELECT 1 FROM information_schema.table_constraints 
               WHERE constraint_name = 'notifications_notification_type_check') THEN
        ALTER TABLE notifications DROP CONSTRAINT notifications_notification_type_check;
        RAISE NOTICE 'Dropped old notification_type check constraint';
    END IF;
    
    -- Add new constraint for category
    IF NOT EXISTS (SELECT 1 FROM information_schema.table_constraints 
                   WHERE constraint_name = 'notifications_category_check') THEN
        ALTER TABLE notifications ADD CONSTRAINT notifications_category_check 
        CHECK (category IN ('appointment', 'time_off', 'system', 'payment', 'reminder'));
        RAISE NOTICE 'Added category check constraint';
    END IF;
EXCEPTION
    WHEN others THEN
        RAISE NOTICE 'Could not update check constraints - this is usually fine';
END $$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_notifications_category ON notifications(category);
CREATE INDEX IF NOT EXISTS idx_notifications_related_id ON notifications(related_id);

-- ========================================
-- VERIFICATION QUERIES
-- ========================================

-- Check notifications table structure
SELECT 'NOTIFICATIONS TABLE COLUMNS:' as info;
SELECT 
    column_name,
    data_type,
    character_maximum_length,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'notifications' 
AND column_name IN ('category', 'related_id', 'notification_type', 'title', 'read_at', 'priority')
ORDER BY column_name;

-- Final success message
SELECT 'NOTIFICATIONS TABLE FIX COMPLETED!' as status,
       'The notifications table schema has been updated to match the SQLAlchemy model.' as message,
       'You can now restart your application.' as next_step;
