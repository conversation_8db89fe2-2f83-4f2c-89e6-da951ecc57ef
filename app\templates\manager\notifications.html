{% extends 'base.html' %}

{% block title %}Notifications{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1>Notifications</h1>
            <p class="lead">View and manage your notifications.</p>
        </div>
        <div class="col-md-4 text-end">
            <form method="POST" action="{{ url_for('manager.mark_notifications_read') }}">
                <button type="submit" class="btn btn-outline-primary">
                    <i class="fas fa-check-double"></i> Mark All as Read
                </button>
            </form>
        </div>
    </div>

    <div class="card shadow">
        <div class="card-body">
            {% if notifications %}
                <div class="list-group">
                    {% for notification in notifications %}
                        <div class="list-group-item list-group-item-action {% if not notification.is_read %}list-group-item-primary{% endif %}">
                            <div class="d-flex w-100 justify-content-between">
                                <h5 class="mb-1">
                                    {% if notification.category == 'time_off' %}
                                        <i class="fas fa-calendar-alt text-primary me-2"></i>
                                    {% elif notification.category == 'appointment' %}
                                        <i class="fas fa-clock text-warning me-2"></i>
                                    {% else %}
                                        <i class="fas fa-bell text-info me-2"></i>
                                    {% endif %}
                                    {{ notification.category|capitalize }} Notification
                                </h5>
                                <small>{{ notification.insert_date.strftime('%Y-%m-%d %H:%M') }}</small>
                            </div>
                            <p class="mb-1">{{ notification.message }}</p>
                            <div class="d-flex justify-content-between align-items-center mt-2">
                                <small>
                                    {% if notification.is_read %}
                                        <span class="text-muted"><i class="fas fa-check"></i> Read</span>
                                    {% else %}
                                        <span class="text-primary"><i class="fas fa-circle"></i> Unread</span>
                                    {% endif %}
                                </small>
                                <div>
                                    {% if notification.category == 'time_off' and notification.related_id %}
                                        <a href="{{ url_for('manager.view_time_off_request', id=notification.related_id) }}" class="btn btn-sm btn-outline-primary">View Request</a>
                                    {% elif notification.category == 'appointment' and notification.related_id %}
                                        <a href="{{ url_for('manager.view_appointment', id=notification.related_id) }}" class="btn btn-sm btn-outline-primary">View Appointment</a>
                                    {% endif %}
                                    
                                    {% if not notification.is_read %}
                                        <form method="POST" action="{{ url_for('manager.mark_notifications_read') }}" class="d-inline">
                                            <input type="hidden" name="notification_id" value="{{ notification.id }}">
                                            <button type="submit" class="btn btn-sm btn-outline-secondary">Mark as Read</button>
                                        </form>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="alert alert-info">
                    You have no notifications.
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
