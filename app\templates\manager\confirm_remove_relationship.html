{% extends "base.html" %}

{% block title %}Remove Relationship - TutorAide Inc.{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">Remove Relationship</h2>
        <p class="text-muted">
            Confirm removal of the relationship between clients
        </p>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('manager.view_client', id=client.id) }}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left"></i> Back to Client
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card border-danger shadow">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0">
                    <i class="fas fa-exclamation-triangle"></i> Confirm Removal
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Warning:</strong> This action cannot be undone.
                </div>
                
                <p class="mb-4">
                    You are about to remove the following relationship:
                </p>
                
                <div class="card bg-light">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-5 text-center">
                                <h6 class="text-primary">{{ client.first_name }} {{ client.last_name }}</h6>
                                <small class="text-muted">{{ client.client_type|title }}</small>
                            </div>
                            <div class="col-md-2 text-center">
                                <i class="fas fa-arrow-right text-muted"></i>
                                <br>
                                <small class="text-muted">{{ relationship.relationship_type|title }}</small>
                                {% if relationship.is_primary %}
                                    <br><span class="badge bg-success">Primary</span>
                                {% endif %}
                            </div>
                            <div class="col-md-5 text-center">
                                <h6 class="text-info">{{ relationship.related_client.first_name }} {{ relationship.related_client.last_name }}</h6>
                                <small class="text-muted">{{ relationship.related_client.client_type|title }}</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <p class="mt-4 mb-4">
                    Are you sure you want to remove this relationship? This will permanently delete the connection 
                    between <strong>{{ client.first_name }} {{ client.last_name }}</strong> and 
                    <strong>{{ relationship.related_client.first_name }} {{ relationship.related_client.last_name }}</strong>.
                </p>
                
                <div class="d-flex justify-content-between">
                    <a href="{{ url_for('manager.view_client', id=client.id) }}" class="btn btn-secondary">
                        <i class="fas fa-times"></i> Cancel
                    </a>
                    <form method="POST" class="d-inline">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash"></i> Yes, Remove Relationship
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
