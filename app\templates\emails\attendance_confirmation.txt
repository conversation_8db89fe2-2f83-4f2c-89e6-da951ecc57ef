Attendance Confirmation Required

Hello {{ tutor.first_name }},

Please confirm the attendance status for your recent tutoring session:

Client: {{ appointment.client.first_name }} {{ appointment.client.last_name }}
Date: {{ appointment.start_time.strftime('%A, %B %d, %Y') }}
Time: {{ appointment.start_time.strftime('%I:%M %p') }} - {{ appointment.end_time.strftime('%I:%M %p') }}
Service: {{ appointment.tutor_service.service.name }}

Please confirm the status of this session by visiting one of these links:

Session Attended:
{{ url_for('tutor.confirm_attendance_email', id=appointment.id, token=token, confirmation='attended', _external=True) }}

Cancelled (With Notice):
{{ url_for('tutor.confirm_attendance_email', id=appointment.id, token=token, confirmation='cancelled_with_notice', _external=True) }}

Cancelled (No Notice):
{{ url_for('tutor.confirm_attendance_email', id=appointment.id, token=token, confirmation='cancelled_without_notice', _external=True) }}

Alternatively, you can log in to your account and update the session status there.

Thank you for your prompt attention to this matter.

Best regards,
Tutoring Appointment System

---
This is an automated message. Please do not reply to this email.
