# app/views/public.py
from flask import Blueprint, render_template, redirect, url_for, flash, request, session, current_app, jsonify
from flask_login import login_required, current_user, login_user
from datetime import datetime, date, timedelta
from app.extensions import db
from app.models.client import Client, IndividualClient
from app.models.program import Program, ProgramPricing, Enrollment, GroupSession, GroupSessionParticipant
from app.models.user import User
from app.services.group_session_service import GroupSessionService
from app.services.user_service import UserService
from app.services.invoice_service import InvoiceService
from app.utils.email import send_email
from app.utils.email_validator import validate_enrollment_email, EmailValidator
from app.utils.rate_limiter import rate_limit_enrollment, enrollment_limiter
import stripe
import secrets
import string

public_bp = Blueprint('public', __name__)

@public_bp.route('/')
def index():
    """Redirect to main website."""
    return redirect('https://www.tutoraide.ca')

# ============= NEW SINGLE-STEP ENROLLMENT ROUTES =============

def generate_temp_password(length=12):
    """Generate a temporary password for new users."""
    characters = string.ascii_letters + string.digits + "!@#$%^&*"
    return ''.join(secrets.choice(characters) for i in range(length))

def calculate_tecfee_total(pricing_type, session_count):
    """Calculate the total cost based on pricing type and sessions."""
    if pricing_type == 'full_package':
        return 399.00  # Fixed price for full package
    else:
        return session_count * 44.99  # Per session pricing

def send_new_account_email(user, temp_password):
    """Send welcome email with temporary password to new user."""
    subject = "Bienvenue chez TutorAide - Inscription TECFÉE"
    
    html_body = f"""
    <h2>Bienvenue chez TutorAide!</h2>
    
    <p>Votre compte a été créé avec succès lors de votre inscription au programme TECFÉE.</p>
    
    <p><strong>Informations de connexion:</strong></p>
    <ul>
        <li>Email: {user.email}</li>
        <li>Mot de passe temporaire: {temp_password}</li>
    </ul>
    
    <p>Pour votre sécurité, veuillez changer votre mot de passe lors de votre première connexion.</p>
    
    <p>Vous pouvez vous connecter à votre compte ici: 
    <a href="{url_for('auth.login', _external=True)}">Connexion</a></p>
    
    <p>Si vous avez des questions, n'hésitez pas à nous contacter.</p>
    
    <p>Cordialement,<br>
    L'équipe TutorAide</p>
    """
    
    text_body = f"""
    Bienvenue chez TutorAide!
    
    Votre compte a été créé avec succès lors de votre inscription au programme TECFÉE.
    
    Informations de connexion:
    - Email: {user.email}
    - Mot de passe temporaire: {temp_password}
    
    Pour votre sécurité, veuillez changer votre mot de passe lors de votre première connexion.
    
    Connexion: {url_for('auth.login', _external=True)}
    
    Cordialement,
    L'équipe TutorAide
    """
    
    send_email(
        subject=subject,
        recipients=[user.email],
        text_body=text_body,
        html_body=html_body
    )

@public_bp.route('/tecfee-enrollment-new', methods=['GET', 'POST'])
@rate_limit_enrollment
def tecfee_enrollment_single_step():
    """Single-step TECFÉE enrollment - all in one page."""
    # Check if user already has an active enrollment
    if current_user.is_authenticated and current_user.role == 'client':
        client = Client.query.filter_by(user_id=current_user.user_id).first()
        if client:
            tecfee_program = GroupSessionService.get_tecfee_program()
            existing_enrollment = Enrollment.query.filter_by(
                client_id=client.client_id,
                program_id=tecfee_program.program_id,
                status='active'
            ).first()
            
            if existing_enrollment:
                flash('Vous êtes déjà inscrit au programme TECFÉE.', 'info')
                return redirect(url_for('client.dashboard'))
    
    # Get TECFÉE program
    tecfee_program = GroupSessionService.get_tecfee_program()
    if not tecfee_program:
        flash('Programme TECFÉE non trouvé.', 'error')
        return redirect(url_for('public.index'))
    
    # Get available sessions grouped by module
    available_sessions = GroupSession.query.filter_by(
        program_id=tecfee_program.program_id,
        status='scheduled'
    ).filter(
        GroupSession.session_date >= date.today()
    ).order_by(GroupSession.module_id, GroupSession.session_date).all()
    
    # Group sessions by module
    sessions_by_module = {}
    for group_session in available_sessions:
        if group_session.module:
            module_order = group_session.module.module_order
            if module_order not in sessions_by_module:
                sessions_by_module[module_order] = {
                    'module': group_session.module,
                    'sessions': []
                }
            sessions_by_module[module_order]['sessions'].append(group_session)
    
    if request.method == 'POST':
        # Validate all form data
        email = request.form.get('email', '').strip()
        first_name = request.form.get('first_name', '').strip()
        last_name = request.form.get('last_name', '').strip()
        phone = request.form.get('phone', '').strip()
        selected_sessions = request.form.getlist('sessions')
        pricing_type = request.form.get('pricing_type', 'per_session')
        
        # Basic validation
        errors = []
        if not email:
            errors.append('Email requis')
        else:
            # Validate email format and check for fake/disposable
            email_valid, email_error = validate_enrollment_email(email)
            if not email_valid:
                errors.append(email_error)
            else:
                # Check for typo suggestions
                suggestion = EmailValidator.suggest_correction(email)
                if suggestion and suggestion != email:
                    errors.append(f'Vouliez-vous dire {suggestion}?')
        
        if not first_name:
            errors.append('Prénom requis')
        if not last_name:
            errors.append('Nom requis')
        if not phone:
            errors.append('Téléphone requis')
        if not selected_sessions:
            errors.append('Veuillez sélectionner au moins une session')
        
        if errors:
            # Track failed attempt
            ip = enrollment_limiter.get_client_ip()
            if email:
                enrollment_limiter.record_enrollment_attempt(email, ip, success=False)
            flash(' • '.join(errors), 'error')
            return render_template('public/tecfee_enrollment_single.html',
                                 program=tecfee_program,
                                 sessions_by_module=sessions_by_module)
        
        # Check for existing user/enrollment
        existing_user = User.query.filter_by(email=email).first()
        if existing_user:
            client = Client.query.filter_by(user_id=existing_user.user_id).first()
            if client:
                # Check for existing enrollment
                existing_enrollment = Enrollment.query.filter_by(
                    client_id=client.client_id,
                    program_id=tecfee_program.program_id
                ).filter(
                    Enrollment.status.in_(['active', 'pending'])
                ).first()
                
                if existing_enrollment:
                    if existing_enrollment.status == 'active':
                        flash('Un compte avec cet email est déjà inscrit au programme TECFÉE.', 'warning')
                        return redirect(url_for('auth.login'))
                    else:
                        # Offer to continue existing enrollment
                        flash('Vous avez une inscription incomplète. Veuillez vous connecter pour continuer.', 'info')
                        return redirect(url_for('auth.login'))
        
        # Store data in session for payment processing
        session['tecfee_enrollment_data'] = {
            'email': email,
            'first_name': first_name,
            'last_name': last_name,
            'phone': phone,
            'selected_sessions': selected_sessions,
            'pricing_type': pricing_type,
            'timestamp': datetime.utcnow().isoformat()
        }
        
        # Calculate total
        total = calculate_tecfee_total(pricing_type, len(selected_sessions))
        
        try:
            # Initialize Stripe
            stripe.api_key = current_app.config.get('STRIPE_SECRET_KEY')
            
            # Create Stripe checkout session
            checkout_session = stripe.checkout.Session.create(
                payment_method_types=['card'],
                line_items=[{
                    'price_data': {
                        'currency': 'cad',
                        'product_data': {
                            'name': f'Programme TECFÉE - {"Forfait complet" if pricing_type == "full_package" else "Par session"}',
                            'description': f'{len(selected_sessions)} sessions sélectionnées'
                        },
                        'unit_amount': int(total * 100),  # Stripe uses cents
                    },
                    'quantity': 1,
                }],
                mode='payment',
                success_url=url_for('public.tecfee_enrollment_success', 
                                  session_id='{CHECKOUT_SESSION_ID}',
                                  _external=True),
                cancel_url=url_for('public.tecfee_enrollment_single_step', 
                                 _external=True),
                metadata={
                    'enrollment_type': 'tecfee',
                    'pricing_type': pricing_type,
                    'session_count': str(len(selected_sessions))
                }
            )
            
            # Store checkout session ID
            session['tecfee_checkout_session_id'] = checkout_session.id
            
            return redirect(checkout_session.url, code=303)
            
        except stripe.error.StripeError as e:
            flash(f'Erreur de paiement: {str(e)}', 'error')
            return render_template('public/tecfee_enrollment_single.html',
                                 program=tecfee_program,
                                 sessions_by_module=sessions_by_module)
    
    return render_template('public/tecfee_enrollment_single.html',
                         program=tecfee_program,
                         sessions_by_module=sessions_by_module)

@public_bp.route('/tecfee-enrollment-success')
def tecfee_enrollment_success():
    """Handle successful payment and create all records."""
    # Get session ID from Stripe
    stripe_session_id = request.args.get('session_id')
    if not stripe_session_id:
        flash('Session de paiement invalide.', 'error')
        return redirect(url_for('public.tecfee_enrollment_single_step'))
    
    # Get enrollment data from session
    enrollment_data = session.get('tecfee_enrollment_data')
    if not enrollment_data:
        flash('Session expirée. Veuillez recommencer.', 'error')
        return redirect(url_for('public.tecfee_enrollment_single_step'))
    
    # Check if data is too old (more than 1 hour)
    timestamp = datetime.fromisoformat(enrollment_data['timestamp'])
    if datetime.utcnow() - timestamp > timedelta(hours=1):
        session.pop('tecfee_enrollment_data', None)
        flash('Session expirée. Veuillez recommencer.', 'error')
        return redirect(url_for('public.tecfee_enrollment_single_step'))
    
    try:
        # Initialize Stripe
        stripe.api_key = current_app.config.get('STRIPE_SECRET_KEY')
        
        # Retrieve the session from Stripe
        stripe_checkout_session = stripe.checkout.Session.retrieve(stripe_session_id)
        
        # Verify payment was successful
        if stripe_checkout_session.payment_status != 'paid':
            flash('Le paiement n\'a pas été complété.', 'error')
            return redirect(url_for('public.tecfee_enrollment_single_step'))
        
        # Get payment intent
        payment_intent = stripe_checkout_session.payment_intent
        
        # Start database transaction
        db.session.begin_nested()
        
        # 1. Check if user exists
        user = User.query.filter_by(email=enrollment_data['email']).first()
        temp_password = None
        
        if not user:
            # Create new user
            temp_password = generate_temp_password()
            user = User(
                email=enrollment_data['email'],
                username=enrollment_data['email'],
                role='client',
                is_active=True,
                email_verified=True  # Trust Stripe payment as email verification
            )
            user.set_password(temp_password)
            db.session.add(user)
            db.session.flush()
        
        # 2. Get or create client
        client = Client.query.filter_by(user_id=user.user_id).first()
        if not client:
            client = IndividualClient(
                user_id=user.user_id,
                first_name=enrollment_data['first_name'],
                last_name=enrollment_data['last_name'],
                phone=enrollment_data['phone'],
                preferred_language='fr'  # Default to French for TECFÉE
            )
            db.session.add(client)
            db.session.flush()
        
        # 3. Get TECFÉE program
        tecfee_program = GroupSessionService.get_tecfee_program()
        
        # 4. Create enrollment
        enrollment = Enrollment(
            client_id=client.client_id,
            program_id=tecfee_program.program_id,
            pricing_type=enrollment_data['pricing_type'],
            enrollment_date=datetime.utcnow(),
            status='active',  # Active immediately since paid
            payment_status='paid',
            stripe_payment_intent=payment_intent,
            stripe_checkout_session_id=stripe_session_id,
            total_sessions=len(enrollment_data['selected_sessions']),
            total_amount=stripe_checkout_session.amount_total / 100  # Convert from cents
        )
        db.session.add(enrollment)
        db.session.flush()
        
        # 5. Add to selected sessions
        for session_id in enrollment_data['selected_sessions']:
            participant = GroupSessionParticipant(
                group_session_id=int(session_id),
                enrollment_id=enrollment.enrollment_id,
                attendance_status='registered',
                is_paid=True  # Mark as paid
            )
            db.session.add(participant)
        
        # 6. Generate invoice
        invoice = InvoiceService.generate_invoice_for_tecfee_enrollment(
            client_id=client.client_id,
            pricing_type=enrollment_data['pricing_type'],
            amount=stripe_checkout_session.amount_total / 100
        )
        
        if invoice:
            # Mark invoice as paid
            invoice.status = 'paid'
            invoice.payment_date = datetime.now()
            invoice.payment_method = 'stripe'
            invoice.stripe_payment_intent = payment_intent
        
        # Commit everything
        db.session.commit()
        
        # Track successful enrollment
        ip = enrollment_limiter.get_client_ip()
        enrollment_limiter.record_enrollment_attempt(
            enrollment_data['email'], 
            ip, 
            success=True
        )
        
        # Send welcome email if new user
        if temp_password:
            send_new_account_email(user, temp_password)
        
        # Clear session data
        session.pop('tecfee_enrollment_data', None)
        session.pop('tecfee_checkout_session_id', None)
        
        # Log user in if new
        if not current_user.is_authenticated:
            login_user(user)
        
        # Show success page
        return render_template('public/tecfee_enrollment_complete.html',
                             enrollment=enrollment,
                             client=client,
                             new_account=bool(temp_password))
        
    except stripe.error.StripeError as e:
        db.session.rollback()
        current_app.logger.error(f"Stripe error during enrollment: {e}")
        flash('Erreur lors de la vérification du paiement.', 'error')
        return redirect(url_for('public.tecfee_enrollment_single_step'))
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Enrollment creation failed: {e}")
        flash('Une erreur est survenue. Votre paiement sera remboursé.', 'error')
        # TODO: Trigger Stripe refund
        return redirect(url_for('public.tecfee_enrollment_single_step'))

@public_bp.route('/tecfee-check-existing', methods=['POST'])
@rate_limit_enrollment
def tecfee_check_existing():
    """AJAX endpoint to check if email already has enrollment."""
    email = request.json.get('email', '').strip().lower()
    
    if not email:
        return jsonify({'exists': False})
    
    # Validate email first
    email_valid, email_error = validate_enrollment_email(email)
    if not email_valid:
        return jsonify({
            'exists': False,
            'invalid': True,
            'error': email_error
        })
    
    # Check for typo suggestions
    suggestion = EmailValidator.suggest_correction(email)
    if suggestion and suggestion != email:
        return jsonify({
            'exists': False,
            'suggestion': suggestion,
            'message': f'Vouliez-vous dire {suggestion}?'
        })
    
    user = User.query.filter_by(email=email).first()
    if not user:
        return jsonify({'exists': False})
    
    client = Client.query.filter_by(user_id=user.user_id).first()
    if not client:
        return jsonify({'exists': False})
    
    tecfee_program = GroupSessionService.get_tecfee_program()
    enrollment = Enrollment.query.filter_by(
        client_id=client.client_id,
        program_id=tecfee_program.program_id
    ).filter(
        Enrollment.status.in_(['active', 'pending'])
    ).first()
    
    if enrollment:
        return jsonify({
            'exists': True,
            'status': enrollment.status,
            'message': 'Un compte avec cet email existe déjà.' if enrollment.status == 'active' 
                      else 'Vous avez une inscription incomplète.'
        })
    
    return jsonify({'exists': False})

@public_bp.route('/tecfee-continue-enrollment/<token>')
def continue_tecfee_enrollment(token):
    """Continue an incomplete TECFÉE enrollment using recovery token."""
    # Find enrollment by recovery token
    enrollment = Enrollment.query.filter_by(
        recovery_token=token,
        status='pending'
    ).first()
    
    if not enrollment:
        flash('Lien invalide ou expiré.', 'error')
        return redirect(url_for('public.tecfee_enrollment_single_step'))
    
    # Check if token is expired
    if enrollment.recovery_token_expires < datetime.utcnow():
        flash('Ce lien a expiré. Veuillez recommencer votre inscription.', 'error')
        return redirect(url_for('public.tecfee_enrollment_single_step'))
    
    # Get client and user info
    client = Client.query.get(enrollment.client_id)
    user = User.query.get(client.user_id)
    
    # Pre-fill the session data
    session['tecfee_enrollment_data'] = {
        'email': user.email,
        'first_name': client.first_name,
        'last_name': client.last_name,
        'phone': client.phone,
        'pricing_type': enrollment.pricing_type,
        'enrollment_id': enrollment.enrollment_id,
        'recovery': True,
        'timestamp': datetime.utcnow().isoformat()
    }
    
    flash('Continuez votre inscription là où vous l\'avez laissée.', 'info')
    return redirect(url_for('public.tecfee_enrollment_single_step'))

@public_bp.route('/about')
def about():
    """Redirect to main website services page."""
    return redirect('https://www.tutoraide.ca/services.html')

@public_bp.route('/contact')
def contact():
    """Redirect to main website contact page."""
    return redirect('https://www.tutoraide.ca/nous-joindre.html')


