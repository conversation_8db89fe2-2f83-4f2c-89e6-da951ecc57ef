/**
 * Audit Trail Modal JavaScript
 * Handles modal functionality, data loading, and user interactions
 */

class AuditTrailModal {
    constructor() {
        this.modal = null;
        this.currentAppointmentId = null;
        this.currentPage = 1;
        this.totalPages = 1;
        this.perPage = 20;  // Increased default page size
        this.isLoading = false;
        this.useVirtualScrolling = false;
        this.virtualScrollContainer = null;
        this.virtualScrollData = [];
        this.virtualScrollViewport = { start: 0, end: 0 };
        this.itemHeight = 120; // Estimated height per audit entry
        this.visibleItems = 10; // Number of items visible at once
        this.performanceMetrics = {};
        this.cacheEnabled = true;
        
        this.init();
    }

    init() {
        // Initialize modal element
        this.modal = document.getElementById('auditTrailModal');
        if (!this.modal) {
            console.error('Audit Trail Modal element not found');
            return;
        }

        // Bind event listeners
        this.bindEvents();
        
        // Initialize Bootstrap modal
        this.bootstrapModal = new bootstrap.Modal(this.modal, {
            backdrop: 'static',
            keyboard: true,
            focus: true
        });
        
        // Initialize network monitoring
        this.initializeNetworkMonitoring();
    }

    bindEvents() {
        // Modal events
        this.modal.addEventListener('show.bs.modal', (e) => this.onModalShow(e));
        this.modal.addEventListener('hidden.bs.modal', (e) => this.onModalHidden(e));
        
        // Keyboard navigation
        this.modal.addEventListener('keydown', (e) => this.handleKeyboardNavigation(e));
        
        // Enhanced pagination events
        const prevBtn = document.getElementById('paginationPrev');
        const nextBtn = document.getElementById('paginationNext');
        const firstBtn = document.getElementById('paginationFirst');
        const lastBtn = document.getElementById('paginationLast');
        
        if (prevBtn) {
            prevBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.previousPage();
            });
        }
        
        if (nextBtn) {
            nextBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.nextPage();
            });
        }
        
        if (firstBtn) {
            firstBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.firstPage();
            });
        }
        
        if (lastBtn) {
            lastBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.lastPage();
            });
        }

        // Global event listener for audit trail buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-audit-appointment-id]') || 
                e.target.closest('[data-audit-appointment-id]')) {
                const button = e.target.matches('[data-audit-appointment-id]') 
                    ? e.target 
                    : e.target.closest('[data-audit-appointment-id]');
                
                const appointmentId = button.getAttribute('data-audit-appointment-id');
                if (appointmentId) {
                    this.showAuditTrail(appointmentId);
                }
            }
        });
    }

    /**
     * Show audit trail for a specific appointment
     * @param {string|number} appointmentId - The appointment ID
     */
    async showAuditTrail(appointmentId) {
        if (this.isLoading) return;
        
        this.currentAppointmentId = appointmentId;
        this.currentPage = 1;
        this.useVirtualScrolling = false; // Reset virtual scrolling
        this.virtualScrollData = [];
        this.performanceMetrics = {};
        
        // Show modal
        this.bootstrapModal.show();
        
        // Load audit data with optimizations
        await this.loadAuditData();
    }

    /**
     * Load audit data from the server with comprehensive error handling
     */
    async loadAuditData(retryCount = 0) {
        if (!this.currentAppointmentId) return;
        
        this.isLoading = true;
        this.showLoadingState();
        
        const maxRetries = 3;
        const retryDelay = 1000 * Math.pow(2, retryCount); // Exponential backoff
        
        try {
            // Add timeout to prevent hanging requests
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout
            
            const response = await fetch(`/api/appointment/${this.currentAppointmentId}/audit?page=${this.currentPage}&per_page=${this.perPage}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                credentials: 'same-origin',
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                // Handle different HTTP error codes with specific messages
                let errorMessage = 'Failed to load audit trail';
                let errorDetails = '';
                let canRetry = false;
                
                switch (response.status) {
                    case 401:
                        errorMessage = 'Authentication Required';
                        errorDetails = 'Please log in again to access audit trails.';
                        break;
                    case 403:
                        errorMessage = 'Access Denied';
                        errorDetails = 'You do not have permission to view audit trails. Only managers can access this information.';
                        break;
                    case 404:
                        errorMessage = 'Appointment Not Found';
                        errorDetails = 'The requested appointment could not be found or may have been deleted.';
                        break;
                    case 429:
                        errorMessage = 'Too Many Requests';
                        errorDetails = 'Please wait a moment before trying again.';
                        canRetry = true;
                        break;
                    case 500:
                    case 502:
                    case 503:
                    case 504:
                        errorMessage = 'Server Error';
                        errorDetails = 'A temporary server error occurred. Please try again in a few moments.';
                        canRetry = true;
                        break;
                    default:
                        errorMessage = 'Network Error';
                        errorDetails = `Server responded with status ${response.status}. Please try again.`;
                        canRetry = response.status >= 500;
                }
                
                // Try to get error details from response body
                try {
                    const errorData = await response.json();
                    if (errorData.message) {
                        errorDetails = errorData.message;
                    }
                } catch (e) {
                    // Ignore JSON parsing errors for error responses
                }
                
                throw new Error(JSON.stringify({
                    message: errorMessage,
                    details: errorDetails,
                    status: response.status,
                    canRetry: canRetry
                }));
            }

            const data = await response.json();
            
            // Validate response data structure
            if (!data || typeof data !== 'object') {
                throw new Error(JSON.stringify({
                    message: 'Invalid Response',
                    details: 'The server returned invalid data. Please try again.',
                    canRetry: true
                }));
            }
            
            // Check for server-side errors in the response
            if (data.error) {
                throw new Error(JSON.stringify({
                    message: data.error,
                    details: data.message || 'An error occurred while loading the audit trail.',
                    canRetry: false
                }));
            }
            
            this.renderAuditData(data);
            
        } catch (error) {
            console.error('Error loading audit data:', error);
            
            let errorInfo;
            try {
                errorInfo = JSON.parse(error.message);
            } catch (e) {
                // Handle network errors, timeout errors, etc.
                if (error.name === 'AbortError') {
                    errorInfo = {
                        message: 'Request Timeout',
                        details: 'The request took too long to complete. Please check your connection and try again.',
                        canRetry: true
                    };
                } else if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
                    errorInfo = {
                        message: 'Network Error',
                        details: 'Unable to connect to the server. Please check your internet connection and try again.',
                        canRetry: true
                    };
                } else {
                    errorInfo = {
                        message: 'Unexpected Error',
                        details: 'An unexpected error occurred while loading the audit trail.',
                        canRetry: true
                    };
                }
            }
            
            // Implement retry logic for retryable errors
            if (errorInfo.canRetry && retryCount < maxRetries) {
                console.log(`Retrying request (attempt ${retryCount + 1}/${maxRetries}) after ${retryDelay}ms`);
                setTimeout(() => {
                    this.loadAuditData(retryCount + 1);
                }, retryDelay);
                return;
            }
            
            this.showErrorState(errorInfo);
        } finally {
            this.isLoading = false;
        }
    }

    /**
     * Render audit data in the modal with comprehensive error handling
     * @param {Object} data - Audit data from server
     */
    renderAuditData(data) {
        try {
            // Hide loading state
            this.hideLoadingState();
            
            // Validate data structure
            if (!this.validateAuditData(data)) {
                this.showCorruptedDataFallback(data);
                return;
            }
            
            if (!data.audit_entries || data.audit_entries.length === 0) {
                this.showEmptyState();
                return;
            }

            // Show appointment summary with error handling
            try {
                this.renderAppointmentSummary(data.appointment);
            } catch (error) {
                console.warn('Error rendering appointment summary:', error);
                this.logError('appointment_summary_render', error, { appointmentId: this.currentAppointmentId });
                // Continue without appointment summary
            }
            
            // Render audit entries with fallback handling
            try {
                // Check if virtual scrolling should be used
                if (data.virtual_scrolling && data.virtual_scrolling.recommended && data.total > 100) {
                    this.initializeVirtualScrolling(data.total);
                    this.virtualScrollData = data.audit_entries;
                    this.renderVirtualScrollItems();
                } else {
                    // Standard rendering for smaller datasets
                    this.renderAuditEntries(data.audit_entries);
                }
            } catch (error) {
                console.error('Error rendering audit entries:', error);
                this.logError('audit_entries_render', error, { 
                    appointmentId: this.currentAppointmentId,
                    entriesCount: data.audit_entries ? data.audit_entries.length : 0
                });
                this.showPartialDataFallback(data.audit_entries);
                return;
            }
            
            // Update pagination with error handling
            try {
                this.updatePagination(data.pagination);
            } catch (error) {
                console.warn('Error updating pagination:', error);
                this.logError('pagination_update', error, { appointmentId: this.currentAppointmentId });
                // Hide pagination on error
                document.getElementById('auditPagination').classList.add('d-none');
            }
            
            // Show performance metrics if available
            try {
                this.updatePerformanceIndicator(data.performance);
            } catch (error) {
                console.warn('Error updating performance indicator:', error);
                // Non-critical, continue without performance metrics
            }
            
            // Show timeline
            this.showTimeline();
            
        } catch (error) {
            console.error('Critical error in renderAuditData:', error);
            this.logError('render_audit_data_critical', error, { 
                appointmentId: this.currentAppointmentId,
                dataStructure: typeof data
            });
            this.showErrorState({
                message: 'Data Processing Error',
                details: 'An error occurred while processing the audit data. The data may be corrupted or incomplete.',
                canRetry: true
            });
        }
    }

    /**
     * Validate audit data structure
     * @param {Object} data - Data to validate
     * @returns {boolean} - Whether data is valid
     */
    validateAuditData(data) {
        if (!data || typeof data !== 'object') {
            return false;
        }
        
        // Check for required properties
        const requiredProps = ['audit_entries'];
        for (const prop of requiredProps) {
            if (!(prop in data)) {
                console.warn(`Missing required property: ${prop}`);
                return false;
            }
        }
        
        // Validate audit_entries is an array
        if (!Array.isArray(data.audit_entries)) {
            console.warn('audit_entries is not an array');
            return false;
        }
        
        return true;
    }

    /**
     * Show fallback display for corrupted data
     * @param {Object} data - Potentially corrupted data
     */
    showCorruptedDataFallback(data) {
        const timeline = document.getElementById('auditTimeline');
        timeline.innerHTML = `
            <div class="alert alert-warning" role="alert">
                <div class="d-flex align-items-start">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="alert-heading">Data Structure Issue</h6>
                        <p class="mb-2">The audit data appears to be corrupted or incomplete. Some information may not display correctly.</p>
                        <hr>
                        <div class="mb-0">
                            <button type="button" class="btn btn-sm btn-outline-warning me-2" onclick="auditTrailModal.retryLoadAuditData()">
                                <i class="fas fa-redo me-1"></i>
                                Reload Data
                            </button>
                            <button type="button" class="btn btn-sm btn-link" data-bs-toggle="collapse" data-bs-target="#rawDataView">
                                View Raw Data
                            </button>
                        </div>
                        <div class="collapse mt-3" id="rawDataView">
                            <div class="card card-body bg-light">
                                <small class="text-muted">Raw server response:</small>
                                <pre class="mt-2 mb-0"><code>${JSON.stringify(data, null, 2)}</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        this.showTimeline();
        this.logError('corrupted_data_fallback', new Error('Corrupted audit data'), { 
            appointmentId: this.currentAppointmentId,
            dataReceived: data
        });
    }

    /**
     * Show partial data fallback when some entries fail to render
     * @param {Array} entries - Audit entries array
     */
    showPartialDataFallback(entries) {
        const timeline = document.getElementById('auditTimeline');
        timeline.innerHTML = '';
        
        // Add warning message
        const warningDiv = document.createElement('div');
        warningDiv.className = 'alert alert-warning mb-3';
        warningDiv.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <div>
                    <strong>Partial Data Display</strong>
                    <p class="mb-0 mt-1">Some audit entries could not be displayed due to data issues. Showing available entries below.</p>
                </div>
            </div>
        `;
        timeline.appendChild(warningDiv);
        
        // Try to render individual entries with error handling
        let successCount = 0;
        let errorCount = 0;
        
        if (Array.isArray(entries)) {
            entries.forEach((entry, index) => {
                try {
                    const entryElement = this.createAuditEntryWithFallback(entry, index);
                    if (entryElement) {
                        timeline.appendChild(entryElement);
                        successCount++;
                    } else {
                        errorCount++;
                    }
                } catch (error) {
                    console.warn(`Error rendering entry ${index}:`, error);
                    errorCount++;
                    
                    // Create minimal fallback entry
                    const fallbackEntry = this.createFallbackAuditEntry(entry, index, error);
                    timeline.appendChild(fallbackEntry);
                }
            });
        }
        
        // Add summary of rendering results
        if (errorCount > 0) {
            const summaryDiv = document.createElement('div');
            summaryDiv.className = 'alert alert-info mt-3';
            summaryDiv.innerHTML = `
                <small>
                    <i class="fas fa-info-circle me-1"></i>
                    Successfully displayed ${successCount} entries. ${errorCount} entries had display issues.
                </small>
            `;
            timeline.appendChild(summaryDiv);
        }
        
        this.showTimeline();
    }

    /**
     * Create audit entry with comprehensive error handling
     * @param {Object} entry - Audit entry data
     * @param {number} index - Entry index
     * @returns {HTMLElement|null} - Created entry element or null if failed
     */
    createAuditEntryWithFallback(entry, index) {
        try {
            // Validate entry structure
            if (!entry || typeof entry !== 'object') {
                throw new Error('Invalid entry structure');
            }
            
            // Check for required fields
            const requiredFields = ['id', 'action'];
            for (const field of requiredFields) {
                if (!(field in entry)) {
                    throw new Error(`Missing required field: ${field}`);
                }
            }
            
            const template = document.getElementById('auditEntryTemplate');
            if (!template) {
                throw new Error('Audit entry template not found');
            }
            
            return this.createAuditEntry(entry, template);
            
        } catch (error) {
            console.warn(`Failed to create audit entry ${index}:`, error);
            this.logError('audit_entry_creation', error, { 
                appointmentId: this.currentAppointmentId,
                entryIndex: index,
                entryId: entry ? entry.id : 'unknown'
            });
            return null;
        }
    }

    /**
     * Create fallback audit entry for corrupted data
     * @param {Object} entry - Potentially corrupted entry data
     * @param {number} index - Entry index
     * @param {Error} error - The error that occurred
     * @returns {HTMLElement} - Fallback entry element
     */
    createFallbackAuditEntry(entry, index, error) {
        const fallbackDiv = document.createElement('div');
        fallbackDiv.className = 'audit-entry mb-3';
        
        const entryId = entry && entry.id ? entry.id : `unknown-${index}`;
        const action = entry && entry.action ? entry.action : 'unknown';
        const timestamp = entry && entry.timestamp_est ? entry.timestamp_est : 'Unknown time';
        
        fallbackDiv.innerHTML = `
            <div class="card border-warning">
                <div class="card-body">
                    <div class="d-flex align-items-start">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h6 class="card-title mb-1">
                                Audit Entry #${entryId} 
                                <small class="text-muted">(${action})</small>
                            </h6>
                            <p class="card-text text-muted mb-2">${timestamp}</p>
                            <div class="alert alert-warning alert-sm mb-0">
                                <small>
                                    <strong>Display Error:</strong> This entry could not be displayed properly due to data corruption.
                                    <br>
                                    <em>Error: ${error.message}</em>
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        return fallbackDiv;
    }

    /**
     * Log errors for debugging purposes
     * @param {string} errorType - Type of error
     * @param {Error} error - The error object
     * @param {Object} context - Additional context information
     */
    logError(errorType, error, context = {}) {
        const errorLog = {
            type: errorType,
            message: error.message,
            stack: error.stack,
            timestamp: new Date().toISOString(),
            context: context,
            userAgent: navigator.userAgent,
            url: window.location.href
        };
        
        // Log to console for development
        console.error('Audit Trail Error:', errorLog);
        
        // In production, you might want to send this to a logging service
        // this.sendErrorToLoggingService(errorLog);
        
        // Store in session storage for debugging (limit to last 10 errors)
        try {
            const storedErrors = JSON.parse(sessionStorage.getItem('auditTrailErrors') || '[]');
            storedErrors.unshift(errorLog);
            storedErrors.splice(10); // Keep only last 10 errors
            sessionStorage.setItem('auditTrailErrors', JSON.stringify(storedErrors));
        } catch (e) {
            // Ignore storage errors
        }
    }

    /**
     * Check network connectivity
     * @returns {Promise<boolean>} - Whether network is available
     */
    async checkNetworkConnectivity() {
        if (!navigator.onLine) {
            return false;
        }
        
        try {
            // Try to fetch a small resource to verify actual connectivity
            const response = await fetch('/api/health-check', {
                method: 'HEAD',
                cache: 'no-cache',
                signal: AbortSignal.timeout(5000) // 5 second timeout
            });
            return response.ok;
        } catch (error) {
            console.warn('Network connectivity check failed:', error);
            return false;
        }
    }

    /**
     * Show network status indicator
     * @param {string} status - Status message
     * @param {boolean} isError - Whether this is an error status
     */
    showNetworkStatus(status, isError = false) {
        const indicator = document.getElementById('networkStatusIndicator');
        const spinner = indicator.querySelector('.spinner-border');
        const message = indicator.querySelector('small');
        
        if (isError) {
            indicator.className = 'alert alert-danger';
            spinner.style.display = 'none';
        } else {
            indicator.className = 'alert alert-warning';
            spinner.style.display = 'inline-block';
        }
        
        message.textContent = status;
        indicator.classList.remove('d-none');
        
        // Auto-hide after 5 seconds for non-error messages
        if (!isError) {
            setTimeout(() => {
                indicator.classList.add('d-none');
            }, 5000);
        }
    }

    /**
     * Hide network status indicator
     */
    hideNetworkStatus() {
        document.getElementById('networkStatusIndicator').classList.add('d-none');
    }

    /**
     * Handle network status changes
     */
    handleNetworkStatusChange() {
        if (navigator.onLine) {
            this.hideNetworkStatus();
            // If we were showing an error due to network issues, try to reload
            if (this.currentAppointmentId && !this.isLoading) {
                const errorState = document.getElementById('auditErrorState');
                if (!errorState.classList.contains('d-none')) {
                    console.log('Network restored, attempting to reload audit data');
                    this.retryLoadAuditData();
                }
            }
        } else {
            this.showNetworkStatus('No internet connection detected', true);
        }
    }

    /**
     * Initialize network monitoring
     */
    initializeNetworkMonitoring() {
        // Listen for online/offline events
        window.addEventListener('online', () => this.handleNetworkStatusChange());
        window.addEventListener('offline', () => this.handleNetworkStatusChange());
        
        // Periodic connectivity check (every 30 seconds when modal is open)
        this.connectivityCheckInterval = setInterval(async () => {
            if (this.modal && !this.modal.classList.contains('d-none')) {
                const isConnected = await this.checkNetworkConnectivity();
                if (!isConnected && navigator.onLine) {
                    this.showNetworkStatus('Connection issues detected', true);
                }
            }
        }, 30000);
    }

    /**
     * Clean up network monitoring
     */
    cleanupNetworkMonitoring() {
        if (this.connectivityCheckInterval) {
            clearInterval(this.connectivityCheckInterval);
            this.connectivityCheckInterval = null;
        }
    }

    /**
     * Enhanced pagination with error handling
     */
    async navigateToPage(targetPage) {
        if (this.isLoading || targetPage < 1 || targetPage > this.totalPages) {
            return;
        }
        
        const previousPage = this.currentPage;
        this.currentPage = targetPage;
        
        try {
            await this.loadAuditData();
        } catch (error) {
            // Restore previous page on error
            this.currentPage = previousPage;
            console.error('Error navigating to page:', error);
            this.showErrorState({
                message: 'Navigation Error',
                details: 'Failed to load the requested page. Please try again.',
                canRetry: true
            });
        }
    }

    /**
     * Enhanced first page navigation
     */
    async firstPage() {
        await this.navigateToPage(1);
    }

    /**
     * Enhanced last page navigation
     */
    async lastPage() {
        await this.navigateToPage(this.totalPages);
    }

    /**
     * Retry loading audit data with user feedback
     */
    async retryLoadAuditData() {
        if (this.isLoading) return;
        
        this.hideErrorState();
        this.hideNetworkStatus();
        
        // Show retry feedback
        this.showNetworkStatus('Retrying...', false);
        
        try {
            await this.loadAuditData();
            this.hideNetworkStatus();
        } catch (error) {
            this.showNetworkStatus('Retry failed', true);
            console.error('Retry failed:', error);
        }
    }

    /**
     * Show error state with enhanced user feedback
     * @param {Object} errorInfo - Error information object
     */
    showErrorState(errorInfo) {
        this.hideLoadingState();
        this.hideTimeline();
        
        const errorState = document.getElementById('auditErrorState');
        const errorTitle = errorState.querySelector('.error-title');
        const errorMessage = errorState.querySelector('.error-message');
        const errorCode = errorState.querySelector('.error-code');
        const retryButton = errorState.querySelector('.retry-button');
        
        // Set error title and message
        errorTitle.textContent = errorInfo.message || 'Error loading audit trail';
        errorMessage.textContent = errorInfo.details || 'An unexpected error occurred. Please try again.';
        
        // Set technical details
        if (errorCode) {
            const technicalDetails = [
                `Error Code: ${errorInfo.error_code || 'UNKNOWN'}`,
                `Timestamp: ${errorInfo.timestamp || new Date().toISOString()}`,
                `Appointment ID: ${this.currentAppointmentId}`,
                `User Agent: ${navigator.userAgent.substring(0, 100)}...`
            ].join('\n');
            errorCode.textContent = technicalDetails;
        }
        
        // Configure retry button
        if (retryButton) {
            if (errorInfo.canRetry !== false && errorInfo.retry_recommended !== false) {
                retryButton.style.display = 'inline-block';
                retryButton.onclick = () => this.retryLoadAuditData();
            } else {
                retryButton.style.display = 'none';
            }
        }
        
        // Log error for debugging
        this.logError('error_state_shown', new Error(errorInfo.message), {
            errorInfo: errorInfo,
            appointmentId: this.currentAppointmentId
        });
        
        errorState.classList.remove('d-none');
    }

    /**
     * Hide error state
     */
    hideErrorState() {
        document.getElementById('auditErrorState').classList.add('d-none');
    }

    /**
     * Show loading state with enhanced feedback
     */
    showLoadingState() {
        this.hideErrorState();
        this.hideTimeline();
        document.getElementById('auditLoadingState').classList.remove('d-none');
    }

    /**
     * Hide loading state
     */
    hideLoadingState() {
        document.getElementById('auditLoadingState').classList.add('d-none');
    }

    /**
     * Show timeline
     */
    showTimeline() {
        this.hideLoadingState();
        this.hideErrorState();
        document.getElementById('auditTimeline').classList.remove('d-none');
        document.getElementById('auditAppointmentSummary').classList.remove('d-none');
    }

    /**
     * Show empty state when no audit entries exist
     */
    showEmptyState() {
        this.hideLoadingState();
        this.hideErrorState();
        document.getElementById('auditEmptyState').classList.remove('d-none');
        document.getElementById('auditAppointmentSummary').classList.remove('d-none');
    }

    /**
     * Enhanced error recovery with user guidance
     * @param {Error} error - The error that occurred
     * @param {string} context - Context where the error occurred
     */
    handleRecoverableError(error, context) {
        console.warn(`Recoverable error in ${context}:`, error);
        
        // Show user-friendly notification
        this.showUserNotification({
            type: 'warning',
            title: 'Minor Issue Detected',
            message: 'Some data may not display correctly, but the audit trail is still functional.',
            duration: 5000
        });
        
        // Log for debugging
        this.logError('recoverable_error', error, { context: context });
    }

    /**
     * Show user notification
     * @param {Object} notification - Notification configuration
     */
    showUserNotification(notification) {
        // Create notification element if it doesn't exist
        let notificationContainer = document.getElementById('auditNotificationContainer');
        if (!notificationContainer) {
            notificationContainer = document.createElement('div');
            notificationContainer.id = 'auditNotificationContainer';
            notificationContainer.className = 'position-fixed top-0 end-0 p-3';
            notificationContainer.style.zIndex = '9999';
            document.body.appendChild(notificationContainer);
        }
        
        // Create notification
        const notificationId = 'notification-' + Date.now();
        const notificationElement = document.createElement('div');
        notificationElement.id = notificationId;
        notificationElement.className = `toast align-items-center text-white bg-${notification.type || 'info'} border-0`;
        notificationElement.setAttribute('role', 'alert');
        notificationElement.setAttribute('aria-live', 'assertive');
        notificationElement.setAttribute('aria-atomic', 'true');
        
        notificationElement.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <strong>${notification.title || 'Notification'}</strong>
                    <br>
                    ${notification.message || ''}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        `;
        
        notificationContainer.appendChild(notificationElement);
        
        // Initialize and show toast
        const toast = new bootstrap.Toast(notificationElement, {
            delay: notification.duration || 3000
        });
        toast.show();
        
        // Remove element after it's hidden
        notificationElement.addEventListener('hidden.bs.toast', () => {
            notificationElement.remove();
        });
    }

    /**
     * Enhanced error boundary for critical operations
     * @param {Function} operation - The operation to execute
     * @param {string} operationName - Name of the operation for logging
     * @param {Object} fallbackOptions - Fallback options if operation fails
     */
    async executeWithErrorBoundary(operation, operationName, fallbackOptions = {}) {
        try {
            return await operation();
        } catch (error) {
            console.error(`Error in ${operationName}:`, error);
            this.logError('error_boundary', error, { 
                operation: operationName,
                appointmentId: this.currentAppointmentId
            });
            
            if (fallbackOptions.showUserError !== false) {
                this.showUserNotification({
                    type: 'danger',
                    title: 'Operation Failed',
                    message: fallbackOptions.userMessage || `Failed to ${operationName}. Please try again.`,
                    duration: 7000
                });
            }
            
            if (fallbackOptions.fallbackFunction) {
                try {
                    return await fallbackOptions.fallbackFunction();
                } catch (fallbackError) {
                    console.error(`Fallback also failed for ${operationName}:`, fallbackError);
                    this.logError('fallback_error', fallbackError, { 
                        operation: operationName,
                        originalError: error.message
                    });
                }
            }
            
            throw error;
        }
    }

    /**
     * Render appointment summary with error handling
     * @param {Object} appointment - Appointment data
     */
    renderAppointmentSummary(appointment) {
        try {
            if (!appointment) {
                console.warn('No appointment data provided for summary');
                return;
            }

            const summaryElement = document.getElementById('auditAppointmentSummary');
            const titleElement = document.getElementById('appointmentSummaryTitle');
            const clientElement = document.getElementById('appointmentClient');
            const tutorElement = document.getElementById('appointmentTutor');
            const statusElement = document.getElementById('appointmentStatus');

            if (!summaryElement || !titleElement || !clientElement || !tutorElement || !statusElement) {
                throw new Error('Required appointment summary elements not found');
            }

            // Set appointment title
            titleElement.textContent = `Appointment #${appointment.id || 'Unknown'}`;
            
            // Set client and tutor names with fallbacks
            clientElement.textContent = appointment.client_name || 'Unknown Client';
            tutorElement.textContent = appointment.tutor_name || 'Unknown Tutor';
            
            // Set status with appropriate styling
            const status = appointment.status || 'unknown';
            statusElement.textContent = status.replace('_', ' ').toUpperCase();
            
            // Apply status-specific styling
            statusElement.className = 'badge';
            switch (status.toLowerCase()) {
                case 'scheduled':
                    statusElement.classList.add('bg-primary');
                    break;
                case 'completed':
                    statusElement.classList.add('bg-success');
                    break;
                case 'cancelled':
                    statusElement.classList.add('bg-danger');
                    break;
                case 'no_show':
                    statusElement.classList.add('bg-warning');
                    break;
                default:
                    statusElement.classList.add('bg-secondary');
            }

            summaryElement.classList.remove('d-none');
            
        } catch (error) {
            console.error('Error rendering appointment summary:', error);
            this.logError('appointment_summary_render', error, { 
                appointmentId: this.currentAppointmentId,
                appointmentData: appointment
            });
            
            // Show minimal summary on error
            this.showMinimalAppointmentSummary();
        }
    }

    /**
     * Show minimal appointment summary when full rendering fails
     */
    showMinimalAppointmentSummary() {
        try {
            const summaryElement = document.getElementById('auditAppointmentSummary');
            const titleElement = document.getElementById('appointmentSummaryTitle');
            
            if (summaryElement && titleElement) {
                titleElement.textContent = `Appointment #${this.currentAppointmentId}`;
                summaryElement.classList.remove('d-none');
            }
        } catch (error) {
            console.error('Error showing minimal appointment summary:', error);
        }
    }

    /**
     * Render audit entries with comprehensive error handling
     * @param {Array} entries - Array of audit entries
     */
    renderAuditEntries(entries) {
        const timeline = document.getElementById('auditTimeline');
        
        try {
            if (!timeline) {
                throw new Error('Audit timeline element not found');
            }

            timeline.innerHTML = '';

            if (!Array.isArray(entries)) {
                throw new Error('Entries must be an array');
            }

            if (entries.length === 0) {
                this.showEmptyState();
                return;
            }

            let successCount = 0;
            let errorCount = 0;

            entries.forEach((entry, index) => {
                try {
                    const entryElement = this.createAuditEntry(entry);
                    if (entryElement) {
                        timeline.appendChild(entryElement);
                        successCount++;
                    } else {
                        errorCount++;
                        console.warn(`Failed to create entry element for entry ${index}`);
                    }
                } catch (entryError) {
                    errorCount++;
                    console.error(`Error creating audit entry ${index}:`, entryError);
                    
                    // Create fallback entry
                    const fallbackEntry = this.createFallbackAuditEntry(entry, index, entryError);
                    timeline.appendChild(fallbackEntry);
                }
            });

            // Show summary if there were errors
            if (errorCount > 0) {
                this.showRenderingSummary(successCount, errorCount);
            }

            this.showTimeline();

        } catch (error) {
            console.error('Critical error rendering audit entries:', error);
            this.logError('audit_entries_render_critical', error, {
                appointmentId: this.currentAppointmentId,
                entriesCount: entries ? entries.length : 0
            });
            
            this.showPartialDataFallback(entries);
        }
    }

    /**
     * Show rendering summary when some entries fail
     * @param {number} successCount - Number of successfully rendered entries
     * @param {number} errorCount - Number of failed entries
     */
    showRenderingSummary(successCount, errorCount) {
        const timeline = document.getElementById('auditTimeline');
        const summaryDiv = document.createElement('div');
        summaryDiv.className = 'alert alert-warning mb-3';
        summaryDiv.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <div>
                    <strong>Partial Display</strong>
                    <p class="mb-0 mt-1">Successfully displayed ${successCount} entries. ${errorCount} entries had display issues.</p>
                </div>
            </div>
        `;
        timeline.insertBefore(summaryDiv, timeline.firstChild);
    }

    /**
     * Create audit entry with enhanced error handling
     * @param {Object} entry - Audit entry data
     * @returns {HTMLElement|null} - Created entry element
     */
    createAuditEntry(entry) {
        try {
            if (!entry || typeof entry !== 'object') {
                throw new Error('Invalid entry data');
            }

            const template = document.getElementById('auditEntryTemplate');
            if (!template) {
                throw new Error('Audit entry template not found');
            }

            const entryElement = template.content.cloneNode(true);
            const entryDiv = entryElement.querySelector('.audit-entry');
            
            if (!entryDiv) {
                throw new Error('Entry div not found in template');
            }

            // Set entry attributes
            entryDiv.setAttribute('data-entry-id', entry.id || '');
            entryDiv.setAttribute('data-action', entry.action || '');

            // Set action icon and styling
            this.setActionIcon(entryElement, entry);
            
            // Set entry content
            this.setEntryContent(entryElement, entry);
            
            // Set up details toggle
            this.setupDetailsToggle(entryElement, entry);

            return entryDiv;

        } catch (error) {
            console.error('Error creating audit entry:', error);
            this.logError('audit_entry_creation', error, {
                entryId: entry ? entry.id : 'unknown',
                appointmentId: this.currentAppointmentId
            });
            return null;
        }
    }

    /**
     * Set action icon and styling for audit entry
     * @param {DocumentFragment} entryElement - Entry element
     * @param {Object} entry - Entry data
     */
    setActionIcon(entryElement, entry) {
        try {
            const actionIcons = entryElement.querySelectorAll('.audit-icon');
            const actionContainer = entryElement.querySelector('.audit-action-icon');
            const card = entryElement.querySelector('.audit-entry-card');

            // Hide all icons first
            actionIcons.forEach(icon => icon.classList.add('d-none'));

            // Show appropriate icon
            const action = entry.action || 'unknown';
            const targetIcon = entryElement.querySelector(`[data-action="${action}"]`);
            
            if (targetIcon) {
                targetIcon.classList.remove('d-none');
            } else {
                // Fallback icon
                const fallbackIcon = entryElement.querySelector('.audit-icon');
                if (fallbackIcon) {
                    fallbackIcon.classList.remove('d-none');
                    fallbackIcon.className = 'fas fa-question-circle text-secondary audit-icon';
                }
            }

            // Set card border color based on action
            if (card) {
                const colorMap = {
                    'create': 'border-success',
                    'update': 'border-primary', 
                    'delete': 'border-danger',
                    'cancel': 'border-warning'
                };
                
                const borderClass = colorMap[action] || 'border-secondary';
                card.classList.add(borderClass);
            }

        } catch (error) {
            console.warn('Error setting action icon:', error);
        }
    }

    /**
     * Set entry content with error handling
     * @param {DocumentFragment} entryElement - Entry element
     * @param {Object} entry - Entry data
     */
    setEntryContent(entryElement, entry) {
        try {
            // Set action title
            const titleElement = entryElement.querySelector('.audit-action-title');
            if (titleElement) {
                titleElement.textContent = entry.action_description || 'Unknown Action';
            }

            // Set user information
            const userElement = entryElement.querySelector('.audit-user');
            if (userElement) {
                userElement.textContent = entry.user_name || 'Unknown User';
            }

            // Set user role badge
            const roleElement = entryElement.querySelector('.badge-role');
            if (roleElement && entry.user_role) {
                roleElement.textContent = entry.user_role;
                roleElement.classList.remove('d-none');
                
                // Style role badge
                const roleClass = entry.user_role.toLowerCase() === 'manager' ? 'bg-primary' : 'bg-secondary';
                roleElement.classList.add(roleClass);
            }

            // Set timestamp
            const timestampElement = entryElement.querySelector('.audit-timestamp');
            if (timestampElement) {
                timestampElement.textContent = entry.timestamp_est || 'Unknown Time';
                if (entry.timestamp_utc) {
                    timestampElement.setAttribute('data-utc', entry.timestamp_utc);
                    timestampElement.setAttribute('title', entry.timestamp_est_long || entry.timestamp_est);
                }
            }

            // Set summary
            const summaryElement = entryElement.querySelector('.audit-summary p');
            if (summaryElement) {
                summaryElement.textContent = entry.changes_summary || 'No summary available';
            }

        } catch (error) {
            console.warn('Error setting entry content:', error);
        }
    }

    /**
     * Setup details toggle functionality
     * @param {DocumentFragment} entryElement - Entry element
     * @param {Object} entry - Entry data
     */
    setupDetailsToggle(entryElement, entry) {
        try {
            const toggleButton = entryElement.querySelector('.audit-details-toggle');
            const detailsContainer = entryElement.querySelector('.audit-details');
            
            if (!toggleButton || !detailsContainer) {
                return;
            }

            const detailsId = `audit-details-${entry.id || Date.now()}`;
            detailsContainer.id = detailsId;
            toggleButton.setAttribute('data-bs-target', `#${detailsId}`);
            toggleButton.setAttribute('aria-controls', detailsId);

            // Populate details content
            if (entry.changes_detail && entry.changes_detail.length > 0) {
                this.populateChangeDetails(detailsContainer, entry.changes_detail);
            } else {
                const changesContainer = detailsContainer.querySelector('.audit-changes');
                if (changesContainer) {
                    changesContainer.innerHTML = '<p class="text-muted mb-0">No detailed changes available</p>';
                }
            }

        } catch (error) {
            console.warn('Error setting up details toggle:', error);
        }
    }

    /**
     * Populate change details in the details container
     * @param {HTMLElement} container - Details container
     * @param {Array} changes - Array of changes
     */
    populateChangeDetails(container, changes) {
        try {
            const changesContainer = container.querySelector('.audit-changes');
            if (!changesContainer) {
                return;
            }

            changesContainer.innerHTML = '';

            changes.forEach(change => {
                const changeElement = this.createChangeElement(change);
                if (changeElement) {
                    changesContainer.appendChild(changeElement);
                }
            });

        } catch (error) {
            console.warn('Error populating change details:', error);
        }
    }

    /**
     * Create change element from change data
     * @param {Object} change - Change data
     * @returns {HTMLElement|null} - Change element
     */
    createChangeElement(change) {
        try {
            const template = document.getElementById('changeItemTemplate');
            if (!template) {
                return null;
            }

            const changeElement = template.content.cloneNode(true);
            
            // Set field name
            const fieldElement = changeElement.querySelector('.change-field');
            if (fieldElement) {
                fieldElement.textContent = change.field_display || change.field || 'Unknown Field';
            }

            // Set old value
            const oldValueElement = changeElement.querySelector('.old-value .badge');
            if (oldValueElement && change.old_value_display) {
                oldValueElement.textContent = change.old_value_display;
            } else if (oldValueElement) {
                oldValueElement.parentElement.style.display = 'none';
            }

            // Set new value
            const newValueElement = changeElement.querySelector('.new-value .badge');
            if (newValueElement && change.new_value_display) {
                newValueElement.textContent = change.new_value_display;
            } else if (newValueElement) {
                newValueElement.parentElement.style.display = 'none';
            }

            // Hide arrow if only one value
            const arrowElement = changeElement.querySelector('.change-arrow');
            if (arrowElement && (!change.old_value_display || !change.new_value_display)) {
                arrowElement.style.display = 'none';
            }

            return changeElement;

        } catch (error) {
            console.warn('Error creating change element:', error);
            return null;
        }
    }

    /**
     * Update pagination with error handling
     * @param {Object} pagination - Pagination data
     */
    updatePagination(pagination) {
        try {
            const paginationContainer = document.getElementById('auditPagination');
            if (!paginationContainer) {
                return;
            }

            if (!pagination || pagination.total_pages <= 1) {
                paginationContainer.classList.add('d-none');
                return;
            }

            // Update pagination info
            this.updatePaginationInfo(pagination);
            
            // Update pagination controls
            this.updatePaginationControls(pagination);
            
            paginationContainer.classList.remove('d-none');

        } catch (error) {
            console.error('Error updating pagination:', error);
            this.logError('pagination_update', error, {
                appointmentId: this.currentAppointmentId,
                paginationData: pagination
            });
        }
    }

    /**
     * Update pagination information display
     * @param {Object} pagination - Pagination data
     */
    updatePaginationInfo(pagination) {
        try {
            const startElement = document.getElementById('paginationStart');
            const endElement = document.getElementById('paginationEnd');
            const totalElement = document.getElementById('paginationTotal');

            if (startElement && endElement && totalElement) {
                const start = ((pagination.current_page - 1) * pagination.per_page) + 1;
                const end = Math.min(pagination.current_page * pagination.per_page, pagination.total_entries);
                
                startElement.textContent = start;
                endElement.textContent = end;
                totalElement.textContent = pagination.total_entries;
            }
        } catch (error) {
            console.warn('Error updating pagination info:', error);
        }
    }

    /**
     * Update pagination controls
     * @param {Object} pagination - Pagination data
     */
    updatePaginationControls(pagination) {
        try {
            const firstBtn = document.getElementById('paginationFirst');
            const prevBtn = document.getElementById('paginationPrev');
            const nextBtn = document.getElementById('paginationNext');
            const lastBtn = document.getElementById('paginationLast');
            const currentBtn = document.getElementById('paginationCurrent');

            // Update current page display
            if (currentBtn) {
                const link = currentBtn.querySelector('a');
                if (link) {
                    link.textContent = pagination.current_page;
                }
            }

            // Enable/disable navigation buttons
            if (firstBtn) {
                firstBtn.classList.toggle('disabled', !pagination.has_previous);
            }
            if (prevBtn) {
                prevBtn.classList.toggle('disabled', !pagination.has_previous);
            }
            if (nextBtn) {
                nextBtn.classList.toggle('disabled', !pagination.has_next);
            }
            if (lastBtn) {
                lastBtn.classList.toggle('disabled', !pagination.has_next);
            }

            // Store pagination data for navigation
            this.totalPages = pagination.total_pages;

        } catch (error) {
            console.warn('Error updating pagination controls:', error);
        }
    }

    /**
     * Get CSRF token for requests
     * @returns {string} - CSRF token
     */
    getCSRFToken() {
        try {
            const token = document.querySelector('meta[name="csrf-token"]');
            return token ? token.getAttribute('content') : '';
        } catch (error) {
            console.warn('Error getting CSRF token:', error);
            return '';
        }
    }

    /**
     * Handle modal show event
     * @param {Event} event - Modal show event
     */
    onModalShow(event) {
        try {
            // Reset error states
            this.hideErrorState();
            this.hideNetworkStatus();
            
            // Initialize performance tracking
            this.performanceMetrics.modalShowTime = Date.now();
            
        } catch (error) {
            console.error('Error handling modal show:', error);
        }
    }

    /**
     * Handle modal hidden event
     * @param {Event} event - Modal hidden event
     */
    onModalHidden(event) {
        try {
            // Clean up
            this.currentAppointmentId = null;
            this.currentPage = 1;
            this.virtualScrollData = [];
            
            // Clean up network monitoring
            this.cleanupNetworkMonitoring();
            
            // Reset UI state
            this.hideErrorState();
            this.hideNetworkStatus();
            document.getElementById('auditTimeline').classList.add('d-none');
            document.getElementById('auditAppointmentSummary').classList.add('d-none');
            document.getElementById('auditEmptyState').classList.add('d-none');
            
        } catch (error) {
            console.error('Error handling modal hidden:', error);
        }
    }

    /**
     * Handle keyboard navigation
     * @param {KeyboardEvent} event - Keyboard event
     */
    handleKeyboardNavigation(event) {
        try {
            if (event.key === 'Escape') {
                this.bootstrapModal.hide();
            } else if (event.key === 'F5' || (event.ctrlKey && event.key === 'r')) {
                event.preventDefault();
                this.retryLoadAuditData();
            }
        } catch (error) {
            console.warn('Error handling keyboard navigation:', error);
        }
    }

    /**
     * Previous page navigation
     */
    async previousPage() {
        if (this.currentPage > 1) {
            await this.navigateToPage(this.currentPage - 1);
        }
    }

    /**
     * Next page navigation
     */
    async nextPage() {
        if (this.currentPage < this.totalPages) {
            await this.navigateToPage(this.currentPage + 1);
        }
    }
}

// Initialize the audit trail modal when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    try {
        window.auditTrailModal = new AuditTrailModal();
        console.log('Audit Trail Modal initialized successfully');
    } catch (error) {
        console.error('Failed to initialize Audit Trail Modal:', error);
    }
});mentStatus');

        if (appointment) {
            titleElement.textContent = `Appointment #${appointment.id}`;
            clientElement.textContent = appointment.client_name || 'Unknown Client';
            tutorElement.textContent = appointment.tutor_name || 'Unknown Tutor';
            statusElement.textContent = this.formatStatus(appointment.status);
            statusElement.className = `badge ${this.getStatusBadgeClass(appointment.status)}`;
        }

        summaryElement.classList.remove('d-none');
    }

    /**
     * Render audit entries in timeline
     * @param {Array} entries - Array of audit entries
     */
    renderAuditEntries(entries) {
        const timeline = document.getElementById('auditTimeline');
        const template = document.getElementById('auditEntryTemplate');
        
        // Clear existing entries
        timeline.innerHTML = '';
        
        entries.forEach((entry, index) => {
            const entryElement = this.createAuditEntry(entry, template);
            timeline.appendChild(entryElement);
        });
    }

    /**
     * Create a single audit entry element
     * @param {Object} entry - Audit entry data
     * @param {HTMLElement} template - Template element
     * @returns {HTMLElement} - Created entry element
     */
    createAuditEntry(entry, template) {
        const clone = template.content.cloneNode(true);
        const entryDiv = clone.querySelector('.audit-entry');
        const card = clone.querySelector('.audit-entry-card');
        
        // Set data attributes
        entryDiv.setAttribute('data-entry-id', entry.id);
        entryDiv.setAttribute('data-action', entry.action);
        card.setAttribute('data-action', entry.action);
        
        // Set action icon
        this.setActionIcon(clone, entry.action);
        
        // Set basic content
        clone.querySelector('.audit-action-title').textContent = this.getActionTitle(entry.action);
        
        // Set user information with role badge
        const userElement = clone.querySelector('.audit-user');
        const roleBadge = clone.querySelector('.badge-role');
        
        userElement.textContent = entry.user_name || 'System';
        
        if (entry.user_role) {
            roleBadge.textContent = entry.user_role.toUpperCase();
            roleBadge.classList.remove('d-none');
        } else {
            roleBadge.classList.add('d-none');
        }
        
        // Set timestamp with EST formatting
        const timestampElement = clone.querySelector('.audit-timestamp');
        timestampElement.textContent = this.formatTimestampEST(entry.timestamp_est);
        if (entry.timestamp_utc) {
            timestampElement.setAttribute('data-utc', entry.timestamp_utc);
        }
        
        // Set summary
        const summaryElement = clone.querySelector('.audit-summary p');
        summaryElement.textContent = entry.summary || this.getDefaultSummary(entry.action);
        
        // Set up collapsible details
        const detailsId = `auditDetails_${entry.id}`;
        const toggleButton = clone.querySelector('.audit-details-toggle');
        const detailsDiv = clone.querySelector('.audit-details');
        
        toggleButton.setAttribute('data-bs-target', `#${detailsId}`);
        toggleButton.setAttribute('aria-controls', detailsId);
        detailsDiv.setAttribute('id', detailsId);
        
        // Render changes based on action type
        const hasChanges = this.renderAuditDetails(clone.querySelector('.audit-changes'), entry);
        
        if (!hasChanges) {
            // Hide details button if no changes
            toggleButton.style.display = 'none';
        }
        
        return entryDiv;
    }

    /**
     * Set the appropriate action icon
     * @param {DocumentFragment} clone - Cloned template
     * @param {string} action - Action type
     */
    setActionIcon(clone, action) {
        const icons = clone.querySelectorAll('.audit-icon');
        icons.forEach(icon => {
            if (icon.getAttribute('data-action') === action) {
                icon.classList.remove('d-none');
            } else {
                icon.classList.add('d-none');
            }
        });
    }

    /**
     * Render audit details based on action type
     * @param {HTMLElement} container - Changes container
     * @param {Object} entry - Audit entry data
     * @returns {boolean} - Whether changes were rendered
     */
    renderAuditDetails(container, entry) {
        container.innerHTML = '';
        
        switch (entry.action) {
            case 'create':
                return this.renderCreationDetails(container, entry);
            case 'update':
                return this.renderUpdateDetails(container, entry);
            case 'delete':
                return this.renderDeletionDetails(container, entry);
            default:
                return false;
        }
    }

    /**
     * Render creation details
     * @param {HTMLElement} container - Container element
     * @param {Object} entry - Audit entry data
     * @returns {boolean} - Whether details were rendered
     */
    renderCreationDetails(container, entry) {
        const template = document.getElementById('creationDetailsTemplate');
        if (!template || !entry.initial_values) return false;

        const clone = template.content.cloneNode(true);
        const valuesContainer = clone.querySelector('.creation-values');

        Object.entries(entry.initial_values).forEach(([field, value]) => {
            const valueDiv = document.createElement('div');
            valueDiv.className = 'change-item mb-2';
            valueDiv.innerHTML = `
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <strong class="change-field">${this.formatFieldName(field)}</strong>
                    </div>
                    <div class="col-md-9">
                        <span class="badge bg-success">${value || 'Not set'}</span>
                    </div>
                </div>
            `;
            valuesContainer.appendChild(valueDiv);
        });

        container.appendChild(clone);
        return true;
    }

    /**
     * Render update details with enhanced before/after comparison
     * @param {HTMLElement} container - Container element
     * @param {Object} entry - Audit entry data
     * @returns {boolean} - Whether details were rendered
     */
    renderUpdateDetails(container, entry) {
        if (!entry.field_changes || entry.field_changes.length === 0) {
            return this.renderChanges(container, entry.changes_detail || []);
        }

        const updateDiv = document.createElement('div');
        updateDiv.className = 'update-details';
        
        // Determine if this is a cancellation
        const isCancellation = entry.action === 'cancel';
        const headerClass = isCancellation ? 'text-warning' : 'text-primary';
        const headerIcon = isCancellation ? 'fas fa-times-circle' : 'fas fa-edit';
        const headerText = isCancellation ? 'Cancellation Details' : 'Changes Made';
        
        updateDiv.innerHTML = `
            <h6 class="mb-3 ${headerClass}">
                <i class="${headerIcon} me-2"></i>
                ${headerText}
            </h6>
        `;

        // Sort changes by importance (if available)
        const sortedChanges = entry.field_changes.sort((a, b) => {
            return (a.importance || 3) - (b.importance || 3);
        });

        sortedChanges.forEach(change => {
            const changeDiv = document.createElement('div');
            changeDiv.className = 'change-item mb-3';
            
            // Add field category data attribute for CSS styling
            if (change.field_category) {
                changeDiv.setAttribute('data-field-category', change.field_category);
            }
            
            // Add change indicator based on field type
            const changeIndicator = this.getChangeIndicator(change);
            
            changeDiv.innerHTML = `
                <div class="row">
                    <div class="col-md-3">
                        ${changeIndicator}
                        <strong class="change-field">${change.field_display_name || this.formatFieldName(change.field)}</strong>
                        ${change.description ? `<div class="text-muted small mt-1">${change.description}</div>` : ''}
                    </div>
                    <div class="col-md-9">
                        <div class="change-comparison">
                            <div class="old-value">
                                <div class="d-flex align-items-center">
                                    <small class="text-muted me-2">Before:</small>
                                    <span class="badge ${this.getFieldBadgeClass(change.field)}">${change.old_value || 'Not set'}</span>
                                </div>
                            </div>
                            <div class="change-arrow">
                                <i class="fas fa-arrow-down"></i>
                            </div>
                            <div class="new-value">
                                <div class="d-flex align-items-center">
                                    <small class="text-muted me-2">After:</small>
                                    <span class="badge ${this.getFieldBadgeClass(change.field)}">${change.new_value || 'Not set'}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            updateDiv.appendChild(changeDiv);
        });

        // Add context information if available
        if (entry.context_info && Object.keys(entry.context_info).length > 0) {
            this.addContextInfo(updateDiv, entry.context_info);
        }

        container.appendChild(updateDiv);
        return true;
    }

    /**
     * Render deletion details
     * @param {HTMLElement} container - Container element
     * @param {Object} entry - Audit entry data
     * @returns {boolean} - Whether details were rendered
     */
    renderDeletionDetails(container, entry) {
        const template = document.getElementById('deletionDetailsTemplate');
        if (!template) return false;

        const clone = template.content.cloneNode(true);
        
        if (entry.deleted_values && Object.keys(entry.deleted_values).length > 0) {
            const deletedValuesDiv = clone.querySelector('.deleted-values');
            const valuesList = clone.querySelector('.deletion-values-list');
            
            Object.entries(entry.deleted_values).forEach(([field, value]) => {
                const valueDiv = document.createElement('div');
                valueDiv.className = 'change-item mb-1';
                valueDiv.innerHTML = `
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <small class="change-field">${this.formatFieldName(field)}</small>
                        </div>
                        <div class="col-md-9">
                            <small class="text-muted">${value || 'Not set'}</small>
                        </div>
                    </div>
                `;
                valuesList.appendChild(valueDiv);
            });
            
            deletedValuesDiv.classList.remove('d-none');
        }

        container.appendChild(clone);
        return true;
    }

    /**
     * Add context information to details
     * @param {HTMLElement} container - Container element
     * @param {Object} contextInfo - Context information
     */
    addContextInfo(container, contextInfo) {
        const contextDiv = document.createElement('div');
        contextDiv.className = 'context-info mt-3 pt-3 border-top';
        contextDiv.innerHTML = `
            <h6 class="mb-2 text-muted">
                <i class="fas fa-info-circle me-2"></i>
                Additional Context
            </h6>
        `;

        Object.entries(contextInfo).forEach(([key, value]) => {
            const contextItem = document.createElement('div');
            contextItem.className = 'context-item mb-1';
            contextItem.innerHTML = `
                <small>
                    <strong>${this.formatFieldName(key)}:</strong> ${value}
                </small>
            `;
            contextDiv.appendChild(contextItem);
        });

        container.appendChild(contextDiv);
    }

    /**
     * Render field changes (legacy method for backward compatibility)
     * @param {HTMLElement} container - Changes container
     * @param {Array} changes - Changes array
     */
    renderChanges(container, changes) {
        if (!changes || changes.length === 0) return false;

        const changeTemplate = document.getElementById('changeItemTemplate');
        if (!changeTemplate) return false;

        container.innerHTML = '';
        
        changes.forEach(change => {
            const changeClone = changeTemplate.content.cloneNode(true);
            
            changeClone.querySelector('.change-field').textContent = change.field_display || this.formatFieldName(change.field);
            changeClone.querySelector('.old-value .badge').textContent = change.old_value_display || change.old_value || 'None';
            changeClone.querySelector('.new-value .badge').textContent = change.new_value_display || change.new_value || 'None';
            
            container.appendChild(changeClone);
        });

        return true;
    }

    /**
     * Format timestamp for EST display
     * @param {string} timestamp - Timestamp string
     * @returns {string} Formatted timestamp
     */
    formatTimestampEST(timestamp) {
        if (!timestamp) return 'Unknown time';
        
        // Remove EST suffix if already present to avoid duplication
        return timestamp.replace(/\s+EST$/, '');
    }

    /**
     * Update pagination controls
     * @param {Object} pagination - Pagination data
     */
    updatePagination(pagination) {
        if (!pagination || pagination.total <= this.perPage) {
            document.getElementById('auditPagination').classList.add('d-none');
            return;
        }

        this.totalPages = pagination.pages;
        this.currentPage = pagination.page;

        // Update pagination info
        document.getElementById('paginationStart').textContent = ((pagination.page - 1) * this.perPage) + 1;
        document.getElementById('paginationEnd').textContent = Math.min(pagination.page * this.perPage, pagination.total);
        document.getElementById('paginationTotal').textContent = pagination.total;

        // Update pagination buttons
        const prevBtn = document.getElementById('paginationPrev');
        const nextBtn = document.getElementById('paginationNext');
        const currentBtn = document.getElementById('paginationCurrent');

        prevBtn.classList.toggle('disabled', pagination.page <= 1);
        nextBtn.classList.toggle('disabled', pagination.page >= pagination.pages);
        currentBtn.querySelector('.page-link').textContent = pagination.page;

        document.getElementById('auditPagination').classList.remove('d-none');
    }

    /**
     * Navigate to previous page
     */
    async previousPage() {
        if (this.currentPage > 1 && !this.isLoading) {
            this.currentPage--;
            await this.loadAuditData();
        }
    }

    /**
     * Navigate to next page
     */
    async nextPage() {
        if (this.currentPage < this.totalPages && !this.isLoading) {
            this.currentPage++;
            await this.loadAuditData();
        }
    }

    /**
     * Show loading state
     */
    showLoadingState() {
        document.getElementById('auditLoadingState').classList.remove('d-none');
        document.getElementById('auditErrorState').classList.add('d-none');
        document.getElementById('auditAppointmentSummary').classList.add('d-none');
        document.getElementById('auditTimeline').classList.add('d-none');
        document.getElementById('auditEmptyState').classList.add('d-none');
        document.getElementById('auditPagination').classList.add('d-none');
    }

    /**
     * Hide loading state
     */
    hideLoadingState() {
        document.getElementById('auditLoadingState').classList.add('d-none');
    }

    /**
     * Show error state with enhanced error information and retry functionality
     * @param {Object} errorInfo - Error information object
     */
    showErrorState(errorInfo = null) {
        const errorStateElement = document.getElementById('auditErrorState');
        
        if (errorInfo) {
            // Update error message and details
            const errorTitle = errorStateElement.querySelector('.error-title') || errorStateElement.querySelector('strong');
            const errorMessage = errorStateElement.querySelector('.error-message') || errorStateElement.querySelector('p');
            const retryButton = errorStateElement.querySelector('.retry-button');
            
            if (errorTitle) {
                errorTitle.textContent = errorInfo.message || 'Error loading audit trail';
            }
            
            if (errorMessage) {
                errorMessage.textContent = errorInfo.details || 'Unable to load the audit trail for this appointment. Please try again later.';
            }
            
            // Show/hide retry button based on whether error is retryable
            if (retryButton) {
                if (errorInfo.canRetry) {
                    retryButton.classList.remove('d-none');
                    retryButton.onclick = () => this.retryLoadAuditData();
                } else {
                    retryButton.classList.add('d-none');
                }
            }
            
            // Add specific error class for styling
            errorStateElement.className = 'alert alert-danger';
            if (errorInfo.status === 403) {
                errorStateElement.className = 'alert alert-warning';
            } else if (errorInfo.status === 404) {
                errorStateElement.className = 'alert alert-info';
            }
        }
        
        errorStateElement.classList.remove('d-none');
        document.getElementById('auditLoadingState').classList.add('d-none');
        document.getElementById('auditAppointmentSummary').classList.add('d-none');
        document.getElementById('auditTimeline').classList.add('d-none');
        document.getElementById('auditEmptyState').classList.add('d-none');
        document.getElementById('auditPagination').classList.add('d-none');
    }

    /**
     * Retry loading audit data
     */
    async retryLoadAuditData() {
        await this.loadAuditData(0); // Reset retry count
    }

    /**
     * Show empty state
     */
    showEmptyState() {
        document.getElementById('auditEmptyState').classList.remove('d-none');
        document.getElementById('auditLoadingState').classList.add('d-none');
        document.getElementById('auditErrorState').classList.add('d-none');
        document.getElementById('auditTimeline').classList.add('d-none');
        document.getElementById('auditPagination').classList.add('d-none');
    }

    /**
     * Show timeline
     */
    showTimeline() {
        document.getElementById('auditTimeline').classList.remove('d-none');
        document.getElementById('auditLoadingState').classList.add('d-none');
        document.getElementById('auditErrorState').classList.add('d-none');
        document.getElementById('auditEmptyState').classList.add('d-none');
    }

    /**
     * Handle modal show event
     * @param {Event} e - Event object
     */
    onModalShow(e) {
        // Set focus to modal for accessibility
        setTimeout(() => {
            this.modal.focus();
        }, 150);
    }

    /**
     * Handle modal hidden event
     * @param {Event} e - Event object
     */
    onModalHidden(e) {
        // Reset modal state
        this.currentAppointmentId = null;
        this.currentPage = 1;
        this.showLoadingState();
        
        // Hide network status indicator
        this.hideNetworkStatus();
        
        // Clear any error states
        document.getElementById('auditErrorState').classList.add('d-none');
        document.getElementById('networkStatusIndicator').classList.add('d-none');
    }

    /**
     * Handle keyboard navigation
     * @param {KeyboardEvent} e - Keyboard event
     */
    handleKeyboardNavigation(e) {
        switch (e.key) {
            case 'Escape':
                if (!this.isLoading) {
                    this.bootstrapModal.hide();
                }
                break;
            case 'ArrowLeft':
                if (e.ctrlKey || e.metaKey) {
                    e.preventDefault();
                    this.previousPage();
                }
                break;
            case 'ArrowRight':
                if (e.ctrlKey || e.metaKey) {
                    e.preventDefault();
                    this.nextPage();
                }
                break;
        }
    }

    /**
     * Get CSRF token from meta tag
     * @returns {string} CSRF token
     */
    getCSRFToken() {
        const token = document.querySelector('meta[name="csrf-token"]');
        return token ? token.getAttribute('content') : '';
    }

    /**
     * Get action title for display
     * @param {string} action - Action type
     * @returns {string} Formatted action title
     */
    getActionTitle(action) {
        const titles = {
            'create': 'Appointment Created',
            'update': 'Appointment Updated',
            'delete': 'Appointment Deleted',
            'read': 'Appointment Viewed'
        };
        return titles[action] || 'Unknown Action';
    }

    /**
     * Get default summary for action
     * @param {string} action - Action type
     * @returns {string} Default summary
     */
    getDefaultSummary(action) {
        const summaries = {
            'create': 'Initial appointment creation',
            'update': 'Appointment details modified',
            'delete': 'Appointment removed from system',
            'read': 'Appointment details accessed'
        };
        return summaries[action] || 'Action performed on appointment';
    }

    /**
     * Format field name for display
     * @param {string} field - Field name
     * @returns {string} Formatted field name
     */
    formatFieldName(field) {
        return field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }

    /**
     * Format status for display
     * @param {string} status - Status value
     * @returns {string} Formatted status
     */
    formatStatus(status) {
        if (!status) return 'Unknown';
        return status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }

    /**
     * Get Bootstrap badge class for status
     * @param {string} status - Status value
     * @returns {string} Badge class
     */
    getStatusBadgeClass(status) {
        const classes = {
            'scheduled': 'bg-primary',
            'completed': 'bg-success',
            'cancelled': 'bg-danger',
            'awaiting_confirmation': 'bg-warning',
            'awaiting_confirm': 'bg-warning',
            'no-show': 'bg-secondary'
        };
        return classes[status] || 'bg-secondary';
    }

    /**
     * Get change indicator HTML for a field change
     * @param {Object} change - Change object
     * @returns {string} HTML for change indicator
     */
    getChangeIndicator(change) {
        if (!change.visual_indicator) return '';
        
        const indicator = change.visual_indicator;
        const field = change.field;
        
        // Determine indicator type based on field
        let indicatorClass = 'change-indicator';
        let indicatorText = '';
        
        if (field === 'status') {
            indicatorClass += ' status-change';
            indicatorText = 'Status';
        } else if (field.includes('time')) {
            indicatorClass += ' time-change';
            indicatorText = 'Time';
        } else if (field.includes('tutor') || field.includes('client') || field.includes('dependant')) {
            indicatorClass += ' person-change';
            indicatorText = 'Person';
        } else if (field.includes('fee') || field.includes('rate')) {
            indicatorClass += ' financial-change';
            indicatorText = 'Financial';
        }
        
        if (indicatorText) {
            return `<div class="${indicatorClass}">
                        <i class="${indicator.icon || 'fas fa-edit'}"></i>
                        <span>${indicatorText}</span>
                    </div>`;
        }
        
        return '';
    }

    /**
     * Get field-specific badge class
     * @param {string} field - Field name
     * @returns {string} Badge class
     */
    getFieldBadgeClass(field) {
        const fieldClasses = {
            'status': 'field-status',
            'start_time': 'field-time',
            'end_time': 'field-time',
            'tutor_id': 'field-person',
            'client_id': 'field-person',
            'dependant_id': 'field-person',
            'transport_fee': 'field-money',
            'duration_minutes': 'field-time'
        };
        
        return fieldClasses[field] || '';
    }

    /**
     * Update performance indicator
     * @param {Object} performance - Performance metrics
     */
    updatePerformanceIndicator(performance) {
        const indicator = document.getElementById('performanceIndicator');
        const text = document.getElementById('performanceText');
        
        if (!performance || !indicator || !text) return;
        
        let message = '';
        let className = 'performance-indicator';
        
        if (performance.cache_hit) {
            message = `Loaded from cache (${performance.total_time_ms}ms)`;
            className += ' cache-hit';
        } else {
            message = `Query: ${performance.query_time_ms || performance.total_time_ms}ms`;
            if (performance.total_time_ms > 1000) {
                className += ' slow-query';
                message += ' (slow)';
            } else if (performance.total_time_ms < 100) {
                className += ' fast-query';
                message += ' (fast)';
            }
        }
        
        if (performance.entries_processed) {
            message += ` • ${performance.entries_processed} entries`;
        }
        
        text.textContent = message;
        indicator.className = className;
        
        // Auto-hide after 3 seconds
        setTimeout(() => {
            indicator.style.opacity = '0.5';
        }, 3000);
    }

    // ===== VIRTUAL SCROLLING METHODS =====
    
    /**
     * Initialize virtual scrolling for large audit histories
     * @param {number} totalEntries - Total number of audit entries
     */
    initializeVirtualScrolling(totalEntries) {
        if (totalEntries <= 50) {
            this.useVirtualScrolling = false;
            return;
        }
        
        this.useVirtualScrolling = true;
        this.virtualScrollData = [];
        
        // Create virtual scroll container
        const timeline = document.getElementById('auditTimeline');
        timeline.innerHTML = `
            <div class="virtual-scroll-container" style="height: 600px; overflow-y: auto;">
                <div class="virtual-scroll-spacer-top" style="height: 0px;"></div>
                <div class="virtual-scroll-content"></div>
                <div class="virtual-scroll-spacer-bottom" style="height: 0px;"></div>
            </div>
        `;
        
        this.virtualScrollContainer = timeline.querySelector('.virtual-scroll-container');
        this.virtualScrollContent = timeline.querySelector('.virtual-scroll-content');
        this.virtualScrollSpacerTop = timeline.querySelector('.virtual-scroll-spacer-top');
        this.virtualScrollSpacerBottom = timeline.querySelector('.virtual-scroll-spacer-bottom');
        
        // Bind scroll event
        this.virtualScrollContainer.addEventListener('scroll', (e) => this.handleVirtualScroll(e));
        
        // Calculate visible range
        this.updateVirtualScrollViewport();
    }
    
    /**
     * Handle virtual scroll events
     * @param {Event} e - Scroll event
     */
    async handleVirtualScroll(e) {
        if (!this.useVirtualScrolling || this.isLoading) return;
        
        const container = e.target;
        const scrollTop = container.scrollTop;
        const containerHeight = container.clientHeight;
        
        // Calculate visible range
        const startIndex = Math.floor(scrollTop / this.itemHeight);
        const endIndex = Math.min(
            startIndex + Math.ceil(containerHeight / this.itemHeight) + 2, // +2 for buffer
            this.virtualScrollData.length
        );
        
        // Check if we need to load more data
        const loadThreshold = Math.max(endIndex - 10, 0);
        
        if (loadThreshold >= this.virtualScrollData.length && this.virtualScrollData.length < this.totalEntries) {
            await this.loadMoreVirtualScrollData();
        }
        
        // Update viewport
        this.virtualScrollViewport = { start: startIndex, end: endIndex };
        this.renderVirtualScrollItems();
    }
    
    /**
     * Load more data for virtual scrolling
     */
    async loadMoreVirtualScrollData() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        
        try {
            const nextPage = Math.floor(this.virtualScrollData.length / this.perPage) + 1;
            const response = await fetch(
                `/api/appointment/${this.currentAppointmentId}/audit?page=${nextPage}&per_page=${this.perPage}&optimized=true&cache=${this.cacheEnabled}`,
                {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': this.getCSRFToken()
                    },
                    credentials: 'same-origin'
                }
            );

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            
            // Add new entries to virtual scroll data
            if (data.audit_entries && data.audit_entries.length > 0) {
                this.virtualScrollData.push(...data.audit_entries);
                this.updateVirtualScrollSizes();
            }
            
        } catch (error) {
            console.error('Error loading more virtual scroll data:', error);
        } finally {
            this.isLoading = false;
        }
    }
    
    /**
     * Update virtual scroll container sizes
     */
    updateVirtualScrollSizes() {
        if (!this.useVirtualScrolling) return;
        
        const totalHeight = this.virtualScrollData.length * this.itemHeight;
        const topSpacerHeight = this.virtualScrollViewport.start * this.itemHeight;
        const bottomSpacerHeight = Math.max(0, totalHeight - (this.virtualScrollViewport.end * this.itemHeight));
        
        this.virtualScrollSpacerTop.style.height = `${topSpacerHeight}px`;
        this.virtualScrollSpacerBottom.style.height = `${bottomSpacerHeight}px`;
    }
    
    /**
     * Render visible virtual scroll items
     */
    renderVirtualScrollItems() {
        if (!this.useVirtualScrolling) return;
        
        const template = document.getElementById('auditEntryTemplate');
        if (!template) return;
        
        // Clear current content
        this.virtualScrollContent.innerHTML = '';
        
        // Render only visible items
        const visibleEntries = this.virtualScrollData.slice(
            this.virtualScrollViewport.start,
            this.virtualScrollViewport.end
        );
        
        visibleEntries.forEach((entry) => {
            const entryElement = this.createAuditEntry(entry, template);
            this.virtualScrollContent.appendChild(entryElement);
        });
        
        // Update spacer sizes
        this.updateVirtualScrollSizes();
    }
    
    /**
     * Update virtual scroll viewport
     */
    updateVirtualScrollViewport() {
        if (!this.useVirtualScrolling || !this.virtualScrollContainer) return;
        
        const containerHeight = this.virtualScrollContainer.clientHeight;
        const scrollTop = this.virtualScrollContainer.scrollTop;
        
        const startIndex = Math.floor(scrollTop / this.itemHeight);
        const endIndex = Math.min(
            startIndex + Math.ceil(containerHeight / this.itemHeight) + 2,
            this.virtualScrollData.length
        );
        
        this.virtualScrollViewport = { start: startIndex, end: endIndex };
    }

    // ===== ENHANCED PAGINATION METHODS =====
    
    /**
     * Navigate to a specific page
     * @param {number} pageNumber - Page number to navigate to
     */
    async goToPage(pageNumber) {
        if (pageNumber < 1 || pageNumber > this.totalPages || this.isLoading) return;
        
        this.currentPage = pageNumber;
        await this.loadAuditData();
    }
    
    /**
     * Navigate to first page
     */
    async firstPage() {
        await this.goToPage(1);
    }
    
    /**
     * Navigate to last page
     */
    async lastPage() {
        await this.goToPage(this.totalPages);
    }
    
    /**
     * Update pagination with enhanced controls
     * @param {Object} pagination - Pagination data
     */
    updatePagination(pagination) {
        const paginationContainer = document.getElementById('auditPagination');
        
        if (!pagination || pagination.total_entries <= this.perPage) {
            paginationContainer.classList.add('d-none');
            return;
        }

        this.totalPages = pagination.total_pages;
        this.currentPage = pagination.current_page;
        this.totalEntries = pagination.total_entries;

        // Update pagination info
        const start = ((pagination.current_page - 1) * this.perPage) + 1;
        const end = Math.min(pagination.current_page * this.perPage, pagination.total_entries);
        
        document.getElementById('paginationStart').textContent = start;
        document.getElementById('paginationEnd').textContent = end;
        document.getElementById('paginationTotal').textContent = pagination.total_entries;

        // Update pagination buttons
        const prevBtn = document.getElementById('paginationPrev');
        const nextBtn = document.getElementById('paginationNext');
        const currentBtn = document.getElementById('paginationCurrent');

        prevBtn.classList.toggle('disabled', !pagination.has_previous);
        nextBtn.classList.toggle('disabled', !pagination.has_next);
        
        if (currentBtn) {
            currentBtn.querySelector('.page-link').textContent = pagination.current_page;
        }

        // Add enhanced pagination controls if many pages
        if (pagination.total_pages > 5) {
            this.renderEnhancedPaginationControls(pagination);
        }

        paginationContainer.classList.remove('d-none');
    }
    
    /**
     * Render enhanced pagination controls for large datasets
     * @param {Object} pagination - Pagination data
     */
    renderEnhancedPaginationControls(pagination) {
        const paginationList = document.querySelector('#auditPagination .pagination');
        if (!paginationList) return;
        
        // Clear existing pagination items except prev/next
        const existingItems = paginationList.querySelectorAll('.page-item:not(#paginationPrev):not(#paginationNext)');
        existingItems.forEach(item => item.remove());
        
        // Insert enhanced controls before next button
        const nextBtn = document.getElementById('paginationNext');
        
        // Add first page button
        if (pagination.current_page > 3) {
            const firstBtn = this.createPaginationButton(1, '1');
            paginationList.insertBefore(firstBtn, nextBtn);
            
            if (pagination.current_page > 4) {
                const ellipsis = this.createPaginationEllipsis();
                paginationList.insertBefore(ellipsis, nextBtn);
            }
        }
        
        // Add surrounding page buttons
        const startPage = Math.max(1, pagination.current_page - 2);
        const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);
        
        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = this.createPaginationButton(i, i.toString(), i === pagination.current_page);
            paginationList.insertBefore(pageBtn, nextBtn);
        }
        
        // Add last page button
        if (pagination.current_page < pagination.total_pages - 2) {
            if (pagination.current_page < pagination.total_pages - 3) {
                const ellipsis = this.createPaginationEllipsis();
                paginationList.insertBefore(ellipsis, nextBtn);
            }
            
            const lastBtn = this.createPaginationButton(pagination.total_pages, pagination.total_pages.toString());
            paginationList.insertBefore(lastBtn, nextBtn);
        }
    }
    
    /**
     * Create a pagination button
     * @param {number} pageNumber - Page number
     * @param {string} text - Button text
     * @param {boolean} active - Whether button is active
     * @returns {HTMLElement} Pagination button element
     */
    createPaginationButton(pageNumber, text, active = false) {
        const li = document.createElement('li');
        li.className = `page-item ${active ? 'active' : ''}`;
        
        const a = document.createElement('a');
        a.className = 'page-link';
        a.href = '#';
        a.textContent = text;
        a.addEventListener('click', (e) => {
            e.preventDefault();
            if (!active) {
                this.goToPage(pageNumber);
            }
        });
        
        li.appendChild(a);
        return li;
    }
    
    /**
     * Create pagination ellipsis
     * @returns {HTMLElement} Ellipsis element
     */
    createPaginationEllipsis() {
        const li = document.createElement('li');
        li.className = 'page-item disabled';
        
        const span = document.createElement('span');
        span.className = 'page-link';
        span.textContent = '...';
        
        li.appendChild(span);
        return li;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize if the modal exists
    if (document.getElementById('auditTrailModal')) {
        window.auditTrailModal = new AuditTrailModal();
    }
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuditTrailModal;
}: 'field-time',
            'end_time': 'field-time',
            'tutor_id': 'field-person',
            'client_id': 'field-person',
            'dependant_id': 'field-person',
            'transport_fee': 'field-money',
            'duration_minutes': 'field-time'
        };
        
        return fieldClasses[field] || '';
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize if the modal exists on the page
    if (document.getElementById('auditTrailModal')) {
        window.auditTrailModal = new AuditTrailModal();
    }
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuditTrailModal;
} 
   // ===== VIRTUAL SCROLLING METHODS =====
    
    /**
     * Initialize virtual scrolling for large audit histories
     * @param {number} totalEntries - Total number of audit entries
     */
    initializeVirtualScrolling(totalEntries) {
        if (totalEntries <= 50) {
            this.useVirtualScrolling = false;
            return;
        }
        
        this.useVirtualScrolling = true;
        this.virtualScrollData = [];
        
        // Create virtual scroll container
        const timeline = document.getElementById('auditTimeline');
        timeline.innerHTML = `
            <div class="virtual-scroll-container" style="height: 600px; overflow-y: auto;">
                <div class="virtual-scroll-spacer-top" style="height: 0px;"></div>
                <div class="virtual-scroll-content"></div>
                <div class="virtual-scroll-spacer-bottom" style="height: 0px;"></div>
            </div>
        `;
        
        this.virtualScrollContainer = timeline.querySelector('.virtual-scroll-container');
        this.virtualScrollContent = timeline.querySelector('.virtual-scroll-content');
        this.virtualScrollSpacerTop = timeline.querySelector('.virtual-scroll-spacer-top');
        this.virtualScrollSpacerBottom = timeline.querySelector('.virtual-scroll-spacer-bottom');
        
        // Bind scroll event
        this.virtualScrollContainer.addEventListener('scroll', (e) => this.handleVirtualScroll(e));
        
        // Calculate visible range
        this.updateVirtualScrollViewport();
    }
    
    /**
     * Handle virtual scroll events
     * @param {Event} e - Scroll event
     */
    async handleVirtualScroll(e) {
        if (!this.useVirtualScrolling || this.isLoading) return;
        
        const container = e.target;
        const scrollTop = container.scrollTop;
        const containerHeight = container.clientHeight;
        
        // Calculate which items should be visible
        const startIndex = Math.floor(scrollTop / this.itemHeight);
        const endIndex = Math.min(
            startIndex + Math.ceil(containerHeight / this.itemHeight) + 2, // +2 for buffer
            this.virtualScrollData.length
        );
        
        // Check if we need to load more data
        const totalItems = this.performanceMetrics.total_entries || 0;
        const loadThreshold = Math.max(endIndex - 10, 0);
        
        if (loadThreshold >= this.virtualScrollData.length && this.virtualScrollData.length < totalItems) {
            await this.loadMoreVirtualScrollData();
        }
        
        // Update viewport
        this.virtualScrollViewport = { start: startIndex, end: endIndex };
        this.renderVirtualScrollItems();
    }
    
    /**
     * Load more data for virtual scrolling
     */
    async loadMoreVirtualScrollData() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        
        try {
            const nextPage = Math.floor(this.virtualScrollData.length / this.perPage) + 1;
            const response = await fetch(
                `/api/appointment/${this.currentAppointmentId}/audit?page=${nextPage}&per_page=${this.perPage}&optimized=true&cache=${this.cacheEnabled}`,
                {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': this.getCSRFToken()
                    },
                    credentials: 'same-origin'
                }
            );
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            // Add new entries to virtual scroll data
            if (data.audit_entries && data.audit_entries.length > 0) {
                this.virtualScrollData.push(...data.audit_entries);
                this.updateVirtualScrollSizes();
            }
            
            // Store performance metrics
            if (data.performance) {
                this.performanceMetrics = { ...this.performanceMetrics, ...data.performance };
            }
            
        } catch (error) {
            console.error('Error loading more virtual scroll data:', error);
        } finally {
            this.isLoading = false;
        }
    }
    
    /**
     * Update virtual scroll container sizes
     */
    updateVirtualScrollSizes() {
        if (!this.useVirtualScrolling) return;
        
        const totalHeight = this.virtualScrollData.length * this.itemHeight;
        const topSpacerHeight = this.virtualScrollViewport.start * this.itemHeight;
        const bottomSpacerHeight = Math.max(0, totalHeight - (this.virtualScrollViewport.end * this.itemHeight));
        
        this.virtualScrollSpacerTop.style.height = `${topSpacerHeight}px`;
        this.virtualScrollSpacerBottom.style.height = `${bottomSpacerHeight}px`;
    }
    
    /**
     * Render visible virtual scroll items
     */
    renderVirtualScrollItems() {
        if (!this.useVirtualScrolling) return;
        
        const content = this.virtualScrollContent;
        content.innerHTML = '';
        
        const template = document.getElementById('auditEntryTemplate');
        
        for (let i = this.virtualScrollViewport.start; i < this.virtualScrollViewport.end; i++) {
            if (i >= this.virtualScrollData.length) break;
            
            const entry = this.virtualScrollData[i];
            const entryElement = this.createAuditEntry(entry, template);
            content.appendChild(entryElement);
        }
        
        this.updateVirtualScrollSizes();
    }
    
    /**
     * Update virtual scroll viewport calculations
     */
    updateVirtualScrollViewport() {
        if (!this.useVirtualScrolling || !this.virtualScrollContainer) return;
        
        const containerHeight = this.virtualScrollContainer.clientHeight;
        this.visibleItems = Math.ceil(containerHeight / this.itemHeight);
    }
    
    // ===== PERFORMANCE OPTIMIZATION METHODS =====
    
    /**
     * Load audit data with performance optimizations
     */
    async loadAuditDataOptimized() {
        if (!this.currentAppointmentId) return;
        
        this.isLoading = true;
        this.showLoadingState();
        
        const startTime = performance.now();
        
        try {
            // First, get performance metrics to determine best loading strategy
            const metricsResponse = await fetch(
                `/api/appointment/${this.currentAppointmentId}/audit/performance`,
                {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': this.getCSRFToken()
                    },
                    credentials: 'same-origin'
                }
            );
            
            let useVirtualScrolling = false;
            if (metricsResponse.ok) {
                const metrics = await metricsResponse.json();
                this.performanceMetrics = metrics;
                
                // Use virtual scrolling for large datasets
                useVirtualScrolling = metrics.total_entries > 100;
                
                // Adjust page size based on performance
                if (metrics.performance.paginated_query_ms > 500) {
                    this.perPage = Math.max(10, Math.floor(this.perPage / 2));
                } else if (metrics.performance.paginated_query_ms < 100) {
                    this.perPage = Math.min(50, this.perPage * 2);
                }
            }
            
            // Load audit data with optimizations
            const auditResponse = await fetch(
                `/api/appointment/${this.currentAppointmentId}/audit?page=${this.currentPage}&per_page=${this.perPage}&optimized=true&cache=${this.cacheEnabled}`,
                {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': this.getCSRFToken()
                    },
                    credentials: 'same-origin'
                }
            );
            
            if (!auditResponse.ok) {
                throw new Error(`HTTP error! status: ${auditResponse.status}`);
            }
            
            const data = await auditResponse.json();
            
            // Store performance metrics
            if (data.performance) {
                this.performanceMetrics = { ...this.performanceMetrics, ...data.performance };
            }
            
            const loadTime = performance.now() - startTime;
            console.log(`Audit data loaded in ${loadTime.toFixed(2)}ms`, this.performanceMetrics);
            
            // Initialize virtual scrolling if needed
            if (useVirtualScrolling && data.pagination && data.pagination.total_entries > 100) {
                this.initializeVirtualScrolling(data.pagination.total_entries);
                this.virtualScrollData = data.audit_entries || [];
                this.renderVirtualScrollItems();
            } else {
                this.renderAuditData(data);
            }
            
            // Show performance info in console for debugging
            this.logPerformanceMetrics();
            
        } catch (error) {
            console.error('Error loading optimized audit data:', error);
            this.showErrorState();
        } finally {
            this.isLoading = false;
        }
    }
    
    /**
     * Log performance metrics to console
     */
    logPerformanceMetrics() {
        if (!this.performanceMetrics || !console.group) return;
        
        console.group('🔍 Audit Trail Performance Metrics');
        
        if (this.performanceMetrics.performance) {
            const perf = this.performanceMetrics.performance;
            console.log(`📊 Query Performance:`);
            console.log(`   • Total Time: ${perf.total_time_ms}ms`);
            console.log(`   • Query Time: ${perf.query_time_ms}ms`);
            console.log(`   • Cache Hit: ${perf.cache_hit ? '✅' : '❌'}`);
            console.log(`   • Entries Processed: ${perf.entries_processed}`);
        }
        
        if (this.performanceMetrics.total_entries) {
            console.log(`📈 Data Statistics:`);
            console.log(`   • Total Entries: ${this.performanceMetrics.total_entries}`);
            console.log(`   • Virtual Scrolling: ${this.useVirtualScrolling ? '✅' : '❌'}`);
            console.log(`   • Page Size: ${this.perPage}`);
        }
        
        if (this.performanceMetrics.recommendations) {
            console.log(`💡 Recommendations:`);
            this.performanceMetrics.recommendations.forEach(rec => {
                console.log(`   • ${rec}`);
            });
        }
        
        console.groupEnd();
    }
    
    /**
     * Clear cache for current appointment
     */
    async clearAuditCache() {
        try {
            const response = await fetch('/api/audit/cache/clear', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                credentials: 'same-origin',
                body: JSON.stringify({
                    appointment_id: this.currentAppointmentId
                })
            });
            
            if (response.ok) {
                console.log('✅ Audit cache cleared successfully');
                // Reload data to see fresh results
                await this.loadAuditData();
            } else {
                console.error('❌ Failed to clear audit cache');
            }
            
        } catch (error) {
            console.error('Error clearing audit cache:', error);
        }
    }
    
    /**
     * Toggle cache usage
     */
    toggleCache() {
        this.cacheEnabled = !this.cacheEnabled;
        console.log(`🔄 Cache ${this.cacheEnabled ? 'enabled' : 'disabled'}`);
        
        // Add visual indicator
        const modalTitle = document.getElementById('auditTrailModalLabel');
        if (modalTitle) {
            const cacheIndicator = modalTitle.querySelector('.cache-indicator');
            if (cacheIndicator) {
                cacheIndicator.remove();
            }
            
            if (!this.cacheEnabled) {
                const indicator = document.createElement('span');
                indicator.className = 'cache-indicator badge bg-warning ms-2';
                indicator.textContent = 'No Cache';
                indicator.title = 'Caching is disabled';
                modalTitle.appendChild(indicator);
            }
        }
    }
    
    /**
     * Enhanced load audit data method that chooses the best strategy
     */
    async loadAuditData() {
        // Use optimized loading if available
        if (typeof this.loadAuditDataOptimized === 'function') {
            await this.loadAuditDataOptimized();
        } else {
            // Fallback to original method
            await this.loadAuditDataOriginal();
        }
    }
    
    /**
     * Original load audit data method (renamed for fallback)
     */
    async loadAuditDataOriginal() {
        if (!this.currentAppointmentId) return;
        
        this.isLoading = true;
        this.showLoadingState();
        
        try {
            const response = await fetch(`/api/appointment/${this.currentAppointmentId}/audit?page=${this.currentPage}&per_page=${this.perPage}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCSRFToken()
                },
                credentials: 'same-origin'
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            this.renderAuditData(data);
            
        } catch (error) {
            console.error('Error loading audit data:', error);
            this.showErrorState();
        } finally {
            this.isLoading = false;
        }
    }
    
    // ===== ENHANCED PAGINATION METHODS =====
    
    /**
     * Enhanced pagination with lazy loading
     */
    async navigateToPage(pageNumber) {
        if (pageNumber < 1 || pageNumber > this.totalPages || this.isLoading) {
            return;
        }
        
        // Show loading indicator for the specific page
        this.showPageLoadingIndicator(pageNumber);
        
        this.currentPage = pageNumber;
        
        if (this.useVirtualScrolling) {
            // For virtual scrolling, just scroll to the right position
            const targetScrollTop = (pageNumber - 1) * this.perPage * this.itemHeight;
            this.virtualScrollContainer.scrollTop = targetScrollTop;
        } else {
            // For regular pagination, load the page
            await this.loadAuditData();
        }
        
        this.hidePageLoadingIndicator();
    }
    
    /**
     * Show loading indicator for specific page
     */
    showPageLoadingIndicator(pageNumber) {
        const currentBtn = document.getElementById('paginationCurrent');
        if (currentBtn) {
            const pageLink = currentBtn.querySelector('.page-link');
            pageLink.innerHTML = `<span class="spinner-border spinner-border-sm" role="status"></span>`;
        }
    }
    
    /**
     * Hide page loading indicator
     */
    hidePageLoadingIndicator() {
        const currentBtn = document.getElementById('paginationCurrent');
        if (currentBtn) {
            const pageLink = currentBtn.querySelector('.page-link');
            pageLink.textContent = this.currentPage;
        }
    }
    
    /**
     * Enhanced previous page with preloading
     */
    async previousPage() {
        if (this.currentPage > 1 && !this.isLoading) {
            await this.navigateToPage(this.currentPage - 1);
            
            // Preload previous page if it exists
            if (this.currentPage > 1) {
                this.preloadPage(this.currentPage - 1);
            }
        }
    }
    
    /**
     * Enhanced next page with preloading
     */
    async nextPage() {
        if (this.currentPage < this.totalPages && !this.isLoading) {
            await this.navigateToPage(this.currentPage + 1);
            
            // Preload next page if it exists
            if (this.currentPage < this.totalPages) {
                this.preloadPage(this.currentPage + 1);
            }
        }
    }
    
    /**
     * Preload a page in the background
     */
    async preloadPage(pageNumber) {
        if (pageNumber < 1 || pageNumber > this.totalPages) return;
        
        try {
            // Preload in background without showing loading state
            const response = await fetch(
                `/api/appointment/${this.currentAppointmentId}/audit?page=${pageNumber}&per_page=${this.perPage}&optimized=true&cache=true`,
                {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': this.getCSRFToken()
                    },
                    credentials: 'same-origin'
                }
            );
            
            if (response.ok) {
                console.log(`📦 Preloaded page ${pageNumber}`);
            }
            
        } catch (error) {
            // Silently fail preloading
            console.debug(`Failed to preload page ${pageNumber}:`, error);
        }
    }
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AuditTrailModal;
}          
  // Update total pages for navigation
            this.totalPages = pagination.total_pages;

        } catch (error) {
            console.warn('Error updating pagination controls:', error);
        }
    }

    /**
     * Get CSRF token for requests
     * @returns {string} - CSRF token
     */
    getCSRFToken() {
        try {
            const token = document.querySelector('meta[name="csrf-token"]');
            return token ? token.getAttribute('content') : '';
        } catch (error) {
            console.warn('Could not get CSRF token:', error);
            return '';
        }
    }

    /**
     * Modal event handlers
     */
    onModalShow(event) {
        try {
            // Reset error states when modal opens
            this.hideErrorState();
            this.hideNetworkStatus();
            
            // Initialize performance tracking
            this.performanceMetrics.modalOpenTime = Date.now();
            
        } catch (error) {
            console.error('Error in modal show handler:', error);
        }
    }

    onModalHidden(event) {
        try {
            // Clean up when modal closes
            this.currentAppointmentId = null;
            this.currentPage = 1;
            this.isLoading = false;
            
            // Clear timeline
            const timeline = document.getElementById('auditTimeline');
            if (timeline) {
                timeline.innerHTML = '';
                timeline.classList.add('d-none');
            }
            
            // Hide all states
            this.hideLoadingState();
            this.hideErrorState();
            this.hideNetworkStatus();
            document.getElementById('auditEmptyState').classList.add('d-none');
            document.getElementById('auditAppointmentSummary').classList.add('d-none');
            document.getElementById('auditPagination').classList.add('d-none');
            
            // Clean up network monitoring
            this.cleanupNetworkMonitoring();
            
        } catch (error) {
            console.error('Error in modal hidden handler:', error);
        }
    }

    /**
     * Handle keyboard navigation
     * @param {KeyboardEvent} event - Keyboard event
     */
    handleKeyboardNavigation(event) {
        try {
            if (this.isLoading) return;
            
            switch (event.key) {
                case 'Escape':
                    // Let Bootstrap handle modal close
                    break;
                case 'ArrowLeft':
                    if (event.ctrlKey) {
                        event.preventDefault();
                        this.previousPage();
                    }
                    break;
                case 'ArrowRight':
                    if (event.ctrlKey) {
                        event.preventDefault();
                        this.nextPage();
                    }
                    break;
                case 'Home':
                    if (event.ctrlKey) {
                        event.preventDefault();
                        this.firstPage();
                    }
                    break;
                case 'End':
                    if (event.ctrlKey) {
                        event.preventDefault();
                        this.lastPage();
                    }
                    break;
                case 'r':
                case 'R':
                    if (event.ctrlKey) {
                        event.preventDefault();
                        this.retryLoadAuditData();
                    }
                    break;
            }
        } catch (error) {
            console.error('Error in keyboard navigation:', error);
        }
    }

    /**
     * Enhanced pagination methods
     */
    async previousPage() {
        if (this.currentPage > 1) {
            await this.navigateToPage(this.currentPage - 1);
        }
    }

    async nextPage() {
        if (this.currentPage < this.totalPages) {
            await this.navigateToPage(this.currentPage + 1);
        }
    }

    /**
     * Update performance indicator
     * @param {Object} performance - Performance data
     */
    updatePerformanceIndicator(performance) {
        try {
            const indicator = document.getElementById('performanceIndicator');
            const textElement = document.getElementById('performanceText');
            
            if (!indicator || !textElement) {
                return;
            }
            
            if (performance && performance.load_time) {
                textElement.textContent = `Loaded in ${performance.load_time}ms`;
                indicator.classList.remove('d-none');
                
                // Auto-hide after 3 seconds
                setTimeout(() => {
                    indicator.classList.add('d-none');
                }, 3000);
            } else {
                indicator.classList.add('d-none');
            }
            
        } catch (error) {
            console.warn('Error updating performance indicator:', error);
        }
    }
}

// Initialize the audit trail modal when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    try {
        window.auditTrailModal = new AuditTrailModal();
        console.log('Audit Trail Modal initialized successfully');
    } catch (error) {
        console.error('Failed to initialize Audit Trail Modal:', error);
        
        // Show user notification about initialization failure
        const notification = document.createElement('div');
        notification.className = 'alert alert-danger position-fixed top-0 start-50 translate-middle-x mt-3';
        notification.style.zIndex = '9999';
        notification.innerHTML = `
            <strong>System Error:</strong> Audit trail functionality is not available.
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(notification);
        
        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 10000);
    }
});