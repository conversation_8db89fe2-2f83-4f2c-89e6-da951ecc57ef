#!/usr/bin/env python3
"""
Comprehensive System Health Check Script

This script verifies database connectivity, schema integrity, and overall system health
to ensure the appointment management system is ready for operation.

Requirements addressed: 7.1, 7.2, 7.3, 7.4, 7.5
"""

import os
import sys
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Tuple

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

try:
    from app import create_app
    from app.extensions import db
    from app.models.appointment import Appointment
    from app.models.appointment_recurring_schedule import AppointmentRecurringSchedule
    from app.utils.db_connection import DatabaseConnection
    from sqlalchemy import text, inspect
    from sqlalchemy.exc import SQLAlchemyError
except ImportError as e:
    print(f"❌ Import Error: {e}")
    print("Make sure you're running this from the project root directory")
    sys.exit(1)


class SystemHealthChecker:
    """Comprehensive system health verification"""
    
    def __init__(self):
        self.app = None
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'UNKNOWN',
            'checks': {},
            'summary': {},
            'recommendations': []
        }
    
    def run_all_checks(self) -> Dict[str, Any]:
        """Run all health checks and return comprehensive results"""
        print("🔍 Starting Comprehensive System Health Check...")
        print("=" * 60)
        
        try:
            # Initialize Flask app
            self._initialize_app()
            
            # Run all health checks
            self._check_database_connectivity()
            self._check_schema_integrity()
            self._check_data_integrity()
            self._check_environment_configuration()
            self._check_application_startup()
            
            # Generate overall assessment
            self._generate_overall_assessment()
            
            # Display results
            self._display_results()
            
            return self.results
            
        except Exception as e:
            self.results['overall_status'] = 'CRITICAL_ERROR'
            self.results['error'] = str(e)
            print(f"❌ Critical error during health check: {e}")
            return self.results
    
    def _initialize_app(self):
        """Initialize Flask application context"""
        try:
            self.app = create_app()
            self.app.app_context().push()
            self.results['checks']['app_initialization'] = {
                'status': 'PASS',
                'message': 'Flask application initialized successfully'
            }
        except Exception as e:
            self.results['checks']['app_initialization'] = {
                'status': 'FAIL',
                'message': f'Failed to initialize Flask app: {e}'
            }
            raise
    
    def _check_database_connectivity(self):
        """Verify database connection and basic operations"""
        print("🔗 Checking Database Connectivity...")
        
        try:
            # Test basic connection
            db_conn = DatabaseConnection()
            connection_status = db_conn.test_connection()
            
            if not connection_status['success']:
                self.results['checks']['database_connectivity'] = {
                    'status': 'FAIL',
                    'message': f"Database connection failed: {connection_status['error']}"
                }
                return
            
            # Test database operations
            with db.engine.connect() as conn:
                # Test basic query
                result = conn.execute(text("SELECT 1 as test"))
                test_value = result.fetchone()[0]
                
                if test_value != 1:
                    raise Exception("Basic query test failed")
                
                # Test table access
                conn.execute(text("SELECT COUNT(*) FROM appointments"))
                
            self.results['checks']['database_connectivity'] = {
                'status': 'PASS',
                'message': 'Database connectivity verified successfully',
                'details': connection_status
            }
            print("  ✅ Database connectivity: PASS")
            
        except Exception as e:
            self.results['checks']['database_connectivity'] = {
                'status': 'FAIL',
                'message': f'Database connectivity check failed: {e}'
            }
            print(f"  ❌ Database connectivity: FAIL - {e}")
    
    def _check_schema_integrity(self):
        """Verify database schema matches expected structure"""
        print("📋 Checking Schema Integrity...")
        
        try:
            inspector = inspect(db.engine)
            
            # Check required tables exist
            required_tables = [
                'appointments',
                'appointment_recurring_schedules',
                'users',
                'clients',
                'tutors'
            ]
            
            existing_tables = inspector.get_table_names()
            missing_tables = [table for table in required_tables if table not in existing_tables]
            
            if missing_tables:
                self.results['checks']['schema_integrity'] = {
                    'status': 'FAIL',
                    'message': f'Missing required tables: {missing_tables}'
                }
                print(f"  ❌ Schema integrity: FAIL - Missing tables: {missing_tables}")
                return
            
            # Check critical columns
            schema_checks = []
            
            # Check appointments table structure
            appointments_columns = {col['name']: col for col in inspector.get_columns('appointments')}
            
            # Verify appointment status column can handle long values
            if 'status' in appointments_columns:
                status_col = appointments_columns['status']
                if hasattr(status_col['type'], 'length') and status_col['type'].length >= 50:
                    schema_checks.append("✅ Appointment status column supports long values")
                else:
                    schema_checks.append("⚠️ Appointment status column may be too short")
            
            # Check for recurring schedule relationship
            if 'recurring_schedule_id' in appointments_columns:
                schema_checks.append("✅ Recurring schedule relationship exists")
            else:
                schema_checks.append("⚠️ Recurring schedule relationship missing")
            
            self.results['checks']['schema_integrity'] = {
                'status': 'PASS',
                'message': 'Schema integrity verified',
                'details': {
                    'existing_tables': existing_tables,
                    'schema_checks': schema_checks
                }
            }
            print("  ✅ Schema integrity: PASS")
            
        except Exception as e:
            self.results['checks']['schema_integrity'] = {
                'status': 'FAIL',
                'message': f'Schema integrity check failed: {e}'
            }
            print(f"  ❌ Schema integrity: FAIL - {e}")
    
    def _check_data_integrity(self):
        """Verify data consistency and relationships"""
        print("🔍 Checking Data Integrity...")
        
        try:
            integrity_issues = []
            
            # Check for orphaned appointments
            orphaned_appointments = db.session.execute(text("""
                SELECT COUNT(*) as count FROM appointments a
                LEFT JOIN clients c ON a.client_id = c.client_id
                WHERE c.client_id IS NULL AND a.client_id IS NOT NULL
            """)).fetchone()[0]
            
            if orphaned_appointments > 0:
                integrity_issues.append(f"Found {orphaned_appointments} appointments with invalid client references")
            
            # Check for appointments with unknown names
            unknown_appointments = db.session.execute(text("""
                SELECT COUNT(*) as count FROM appointments
                WHERE client_name = 'Unknown' OR client_name IS NULL
            """)).fetchone()[0]
            
            if unknown_appointments > 0:
                integrity_issues.append(f"Found {unknown_appointments} appointments with unknown client names")
            
            # Check recurring schedule integrity
            try:
                recurring_count = db.session.execute(text("""
                    SELECT COUNT(*) as count FROM appointment_recurring_schedules
                """)).fetchone()[0]
                
                if recurring_count > 0:
                    integrity_issues.append(f"✅ Found {recurring_count} recurring schedules")
            except Exception:
                integrity_issues.append("⚠️ Could not verify recurring schedules table")
            
            status = 'PASS' if len([issue for issue in integrity_issues if not issue.startswith('✅')]) == 0 else 'WARN'
            
            self.results['checks']['data_integrity'] = {
                'status': status,
                'message': 'Data integrity check completed',
                'details': {
                    'issues_found': integrity_issues,
                    'orphaned_appointments': orphaned_appointments,
                    'unknown_appointments': unknown_appointments
                }
            }
            
            if status == 'PASS':
                print("  ✅ Data integrity: PASS")
            else:
                print("  ⚠️ Data integrity: WARNINGS FOUND")
                for issue in integrity_issues:
                    if not issue.startswith('✅'):
                        print(f"    - {issue}")
            
        except Exception as e:
            self.results['checks']['data_integrity'] = {
                'status': 'FAIL',
                'message': f'Data integrity check failed: {e}'
            }
            print(f"  ❌ Data integrity: FAIL - {e}")
    
    def _check_environment_configuration(self):
        """Verify environment configuration is complete"""
        print("⚙️ Checking Environment Configuration...")
        
        try:
            config_issues = []
            
            # Check required environment variables
            required_vars = [
                'DATABASE_URL',
                'SECRET_KEY'
            ]
            
            for var in required_vars:
                if not os.getenv(var):
                    config_issues.append(f"Missing environment variable: {var}")
            
            # Check database URL format
            db_url = os.getenv('DATABASE_URL')
            if db_url and not db_url.startswith(('postgresql://', 'sqlite://')):
                config_issues.append("DATABASE_URL format may be incorrect")
            
            status = 'PASS' if len(config_issues) == 0 else 'FAIL'
            
            self.results['checks']['environment_configuration'] = {
                'status': status,
                'message': 'Environment configuration check completed',
                'details': {
                    'issues_found': config_issues,
                    'database_url_configured': bool(db_url)
                }
            }
            
            if status == 'PASS':
                print("  ✅ Environment configuration: PASS")
            else:
                print("  ❌ Environment configuration: FAIL")
                for issue in config_issues:
                    print(f"    - {issue}")
            
        except Exception as e:
            self.results['checks']['environment_configuration'] = {
                'status': 'FAIL',
                'message': f'Environment configuration check failed: {e}'
            }
            print(f"  ❌ Environment configuration: FAIL - {e}")
    
    def _check_application_startup(self):
        """Verify application can start without errors"""
        print("🚀 Checking Application Startup...")
        
        try:
            # Test model imports and basic operations
            appointment_count = Appointment.query.count()
            
            # Test recurring schedule model if available
            try:
                recurring_count = AppointmentRecurringSchedule.query.count()
                recurring_available = True
            except Exception:
                recurring_count = 0
                recurring_available = False
            
            self.results['checks']['application_startup'] = {
                'status': 'PASS',
                'message': 'Application startup verified',
                'details': {
                    'appointment_count': appointment_count,
                    'recurring_schedules_available': recurring_available,
                    'recurring_count': recurring_count
                }
            }
            print("  ✅ Application startup: PASS")
            
        except Exception as e:
            self.results['checks']['application_startup'] = {
                'status': 'FAIL',
                'message': f'Application startup check failed: {e}'
            }
            print(f"  ❌ Application startup: FAIL - {e}")
    
    def _generate_overall_assessment(self):
        """Generate overall system health assessment"""
        checks = self.results['checks']
        
        failed_checks = [name for name, check in checks.items() if check['status'] == 'FAIL']
        warning_checks = [name for name, check in checks.items() if check['status'] == 'WARN']
        passed_checks = [name for name, check in checks.items() if check['status'] == 'PASS']
        
        if failed_checks:
            self.results['overall_status'] = 'UNHEALTHY'
            self.results['recommendations'].append("Address failed health checks before proceeding")
        elif warning_checks:
            self.results['overall_status'] = 'HEALTHY_WITH_WARNINGS'
            self.results['recommendations'].append("Review warnings and consider addressing them")
        else:
            self.results['overall_status'] = 'HEALTHY'
            self.results['recommendations'].append("System is ready for normal operation")
        
        self.results['summary'] = {
            'total_checks': len(checks),
            'passed': len(passed_checks),
            'warnings': len(warning_checks),
            'failed': len(failed_checks),
            'failed_checks': failed_checks,
            'warning_checks': warning_checks
        }
    
    def _display_results(self):
        """Display comprehensive health check results"""
        print("\n" + "=" * 60)
        print("📊 SYSTEM HEALTH CHECK RESULTS")
        print("=" * 60)
        
        # Overall status
        status_emoji = {
            'HEALTHY': '✅',
            'HEALTHY_WITH_WARNINGS': '⚠️',
            'UNHEALTHY': '❌',
            'CRITICAL_ERROR': '💥'
        }
        
        print(f"\n🏥 Overall Status: {status_emoji.get(self.results['overall_status'], '❓')} {self.results['overall_status']}")
        
        # Summary
        summary = self.results['summary']
        print(f"\n📈 Summary:")
        print(f"  • Total Checks: {summary['total_checks']}")
        print(f"  • Passed: {summary['passed']}")
        print(f"  • Warnings: {summary['warnings']}")
        print(f"  • Failed: {summary['failed']}")
        
        # Recommendations
        if self.results['recommendations']:
            print(f"\n💡 Recommendations:")
            for rec in self.results['recommendations']:
                print(f"  • {rec}")
        
        # Failed checks details
        if summary['failed'] > 0:
            print(f"\n❌ Failed Checks:")
            for check_name in summary['failed_checks']:
                check = self.results['checks'][check_name]
                print(f"  • {check_name}: {check['message']}")
        
        # Warning checks details
        if summary['warnings'] > 0:
            print(f"\n⚠️ Warning Checks:")
            for check_name in summary['warning_checks']:
                check = self.results['checks'][check_name]
                print(f"  • {check_name}: {check['message']}")
        
        print(f"\n🕐 Check completed at: {self.results['timestamp']}")
        print("=" * 60)


def main():
    """Main execution function"""
    checker = SystemHealthChecker()
    results = checker.run_all_checks()
    
    # Save results to file
    results_file = f"health_check_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 Detailed results saved to: {results_file}")
    
    # Exit with appropriate code
    if results['overall_status'] in ['UNHEALTHY', 'CRITICAL_ERROR']:
        sys.exit(1)
    elif results['overall_status'] == 'HEALTHY_WITH_WARNINGS':
        sys.exit(2)
    else:
        sys.exit(0)


if __name__ == "__main__":
    main()