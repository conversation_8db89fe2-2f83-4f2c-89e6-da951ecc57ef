/* app/static/css/style.css */

/* Global Styles */
body {
    font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.footer {
    margin-top: auto;
}

/* Header and Navigation */
.navbar-brand {
    font-weight: 700;
}

/* Cards and Containers */
.card {
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}

.card-header {
    border-radius: 0.5rem 0.5rem 0 0 !important;
    font-weight: 500;
}

/* Calendar Styles */
.calendar-container {
    min-height: 600px;
}

/* Form Styles */
.form-label {
    font-weight: 500;
}

.form-control:focus, .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Button Styles */
.btn {
    border-radius: 0.25rem;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

/* Status Badges */
.status-badge {
    padding: 0.35em 0.65em;
    border-radius: 0.25rem;
    font-weight: 500;
}

/* Dashboard Stats */
.stat-card {
    transition: transform 0.3s;
}

.stat-card:hover {
    transform: translateY(-5px);
}

/* Table Styles */
.table th {
    font-weight: 600;
    background-color: #f8f9fa;
}

/* Profile Section */
.profile-header {
    padding: 2rem 0;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    margin-bottom: 2rem;
}

/* Print Styles */
@media print {
    .navbar, .footer, .btn, .no-print {
        display: none !important;
    }
    
    .container {
        width: 100%;
        max-width: 100%;
    }
    
    .card {
        border: none;
        box-shadow: none;
    }
    
    body {
        font-size: 12pt;
    }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .calendar-container {
        min-height: 400px;
    }
    
    .hide-on-small {
        display: none !important;
    }
}

/* Animation for loading states */
.spinner-grow, .spinner-border {
    vertical-align: middle;
}

/* Helper Classes */
.cursor-pointer {
    cursor: pointer;
}

.bg-light-hover:hover {
    background-color: #f8f9fa;
}

/* Appointment Status Colors */
.appointment.status-scheduled {
    background-color: #0d6efd;
    color: white;
}

.appointment.status-completed {
    background-color: #198754;
    color: white;
}

.appointment.status-cancelled {
    background-color: #dc3545;
    color: white;
    text-decoration: line-through;
}

.appointment.status-no-show {
    background-color: #fd7e14;
    color: white;
}

/* Stripe Elements Custom Styling */
.StripeElement {
    height: 40px;
    padding: 10px 12px;
    width: 100%;
    border: 1px solid #ced4da;
    border-radius: 4px;
    background-color: white;
}

.StripeElement--focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.StripeElement--invalid {
    border-color: #dc3545;
}

/* Custom Calendar Navigation */
.calendar-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.calendar-title {
    font-size: 1.25rem;
    font-weight: 500;
}

/* Animation for alerts */
.alert {
    animation: fadeIn 0.5s;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Recurrence Pattern End Options */
.recurrence-end-option {
    margin-bottom: 1.5rem;
    position: relative;
    clear: both;
}

.recurrence-end-option .form-check {
    margin-bottom: 0.5rem;
}

.recurrence-end-option .form-check-input {
    margin-top: 0.25rem;
}

.recurrence-end-option .form-check-label {
    display: block;
    margin-left: 1.5rem;
    font-weight: normal;
}

.recurrence-end-option .nested-input {
    padding-left: 1.8rem;
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
}

/* Ensure radio buttons are clearly separated */
.recurrence-end-container .form-check + .form-check {
    margin-top: 1rem;
}