{% extends "base.html" %}

{% block title %}{{ t('manager.reports.weekly_payment_report.title') }} - TutorAide Inc.{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">
            <i class="fas fa-chart-line me-2"></i>
            {{ t('manager.reports.weekly_payment_report.title') }}
        </h2>
        <p class="text-muted">{{ t('manager.reports.weekly_payment_report.subtitle') }}</p>
    </div>
    <div class="col-md-4 text-end">
        <div class="btn-group" role="group">
            <a href="{{ url_for('manager.tutor_payments_list') }}" class="btn btn-outline-primary">
                <i class="fas fa-list me-1"></i> {{ t('manager.tutor_payments.title') }}
            </a>
            <button class="btn btn-success" onclick="window.print()">
                <i class="fas fa-print me-1"></i> {{ t('base.common.print') }}
            </button>
        </div>
    </div>
</div>

<!-- Week Navigation -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-week me-2"></i>
                    {{ t('manager.reports.weekly_payment_report.week_period') }}: 
                    {{ week_start.strftime('%d %B %Y') }} - {{ week_end.strftime('%d %B %Y') }}
                </h5>
            </div>
            <div class="col-md-6 text-end">
                <div class="btn-group" role="group">
                    <a href="{{ url_for('manager.weekly_payment_report', week_offset=week_offset+1) }}" 
                       class="btn btn-outline-secondary btn-sm">
                        <i class="fas fa-chevron-left me-1"></i> {{ t('manager.reports.weekly_payment_report.previous_week') }}
                    </a>
                    {% if week_offset > 0 %}
                    <a href="{{ url_for('manager.weekly_payment_report', week_offset=week_offset-1) }}" 
                       class="btn btn-outline-secondary btn-sm">
                        {{ t('manager.reports.weekly_payment_report.next_week') }} <i class="fas fa-chevron-right ms-1"></i>
                    </a>
                    {% endif %}
                    {% if week_offset != 0 %}
                    <a href="{{ url_for('manager.weekly_payment_report') }}" 
                       class="btn btn-primary btn-sm">
                        {{ t('manager.reports.weekly_payment_report.current_week') }}
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        
        {% if not show_comparison %}
        <div class="mt-3">
            <a href="{{ url_for('manager.weekly_payment_report', week_offset=week_offset, compare='true') }}" 
               class="btn btn-outline-info btn-sm">
                <i class="fas fa-chart-bar me-1"></i> {{ t('manager.reports.weekly_payment_report.show_comparison') }}
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-primary">{{ total_sessions }}</h3>
                <p class="text-muted mb-0">{{ t('manager.reports.weekly_payment_report.total_sessions') }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-success">${{ "%.2f"|format(total_service_amount) }}</h3>
                <p class="text-muted mb-0">{{ t('manager.reports.weekly_payment_report.total_service_amount') }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-info">${{ "%.2f"|format(total_transport_amount) }}</h3>
                <p class="text-muted mb-0">{{ t('manager.reports.weekly_payment_report.total_transport_amount') }}</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-primary">${{ "%.2f"|format(total_payment_amount) }}</h3>
                <p class="text-muted mb-0">{{ t('manager.reports.weekly_payment_report.total_payment_amount') }}</p>
            </div>
        </div>
    </div>
</div>

<!-- Comparison with Previous Week -->
{% if show_comparison and previous_week_data %}
<div class="alert alert-info">
    <h5><i class="fas fa-chart-line me-2"></i>{{ t('manager.reports.weekly_payment_report.comparison_title') }}</h5>
    <div class="row">
        <div class="col-md-6">
            {% set sessions_diff = total_sessions - (previous_week_data.sessions_count or 0) %}
            <strong>{{ t('manager.reports.weekly_payment_report.sessions_comparison') }}:</strong>
            {{ total_sessions }} 
            {% if sessions_diff > 0 %}
                <span class="text-success">(+{{ sessions_diff }})</span>
            {% elif sessions_diff < 0 %}
                <span class="text-danger">({{ sessions_diff }})</span>
            {% else %}
                <span class="text-muted">({{ t('manager.reports.weekly_payment_report.no_change') }})</span>
            {% endif %}
        </div>
        <div class="col-md-6">
            {% set amount_diff = total_payment_amount - (previous_week_data.total_payment_amount|float or 0) %}
            <strong>{{ t('manager.reports.weekly_payment_report.amount_comparison') }}:</strong>
            ${{ "%.2f"|format(total_payment_amount) }}
            {% if amount_diff > 0 %}
                <span class="text-success">(+${{ "%.2f"|format(amount_diff) }})</span>
            {% elif amount_diff < 0 %}
                <span class="text-danger">(-${{ "%.2f"|format(-amount_diff) }})</span>
            {% else %}
                <span class="text-muted">({{ t('manager.reports.weekly_payment_report.no_change') }})</span>
            {% endif %}
        </div>
    </div>
</div>
{% endif %}

<!-- Tutor Payment Details Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-users me-2"></i>
            {{ t('manager.reports.weekly_payment_report.tutors_details') }}
        </h5>
    </div>
    <div class="card-body">
        {% if tutors_data %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>{{ t('manager.reports.weekly_payment_report.tutor_name') }}</th>
                        <th class="text-center">{{ t('manager.reports.weekly_payment_report.sessions_count') }}</th>
                        <th class="text-end">{{ t('manager.reports.weekly_payment_report.service_amount') }}</th>
                        <th class="text-end">{{ t('manager.reports.weekly_payment_report.transport_amount') }}</th>
                        <th class="text-end">{{ t('manager.reports.weekly_payment_report.total_amount') }}</th>
                        <th class="text-center">{{ t('manager.reports.weekly_payment_report.actions') }}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for tutor in tutors_data %}
                    <tr>
                        <td>
                            <strong>{{ tutor.last_name }}, {{ tutor.first_name }}</strong>
                        </td>
                        <td class="text-center">
                            <span class="badge bg-primary">{{ tutor.sessions_count }}</span>
                        </td>
                        <td class="text-end">
                            ${{ "%.2f"|format(tutor.total_service_amount|float) }}
                        </td>
                        <td class="text-end">
                            ${{ "%.2f"|format(tutor.total_transport_amount|float) }}
                        </td>
                        <td class="text-end">
                            <strong>${{ "%.2f"|format(tutor.total_payment_amount|float) }}</strong>
                        </td>
                        <td class="text-center">
                            <a href="{{ url_for('manager.tutor_payments_list', tutor_id=tutor.tutor_id) }}" 
                               class="btn btn-sm btn-outline-primary" 
                               title="{{ t('manager.reports.weekly_payment_report.view_payments') }}">
                                <i class="fas fa-eye"></i>
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
                <tfoot class="table-light">
                    <tr>
                        <th>{{ t('manager.reports.weekly_payment_report.totals') }}</th>
                        <th class="text-center">
                            <span class="badge bg-success">{{ total_sessions }}</span>
                        </th>
                        <th class="text-end">
                            <strong>${{ "%.2f"|format(total_service_amount) }}</strong>
                        </th>
                        <th class="text-end">
                            <strong>${{ "%.2f"|format(total_transport_amount) }}</strong>
                        </th>
                        <th class="text-end">
                            <strong>${{ "%.2f"|format(total_payment_amount) }}</strong>
                        </th>
                        <th></th>
                    </tr>
                </tfoot>
            </table>
        </div>
        
        <!-- Validation Actions -->
        <div class="mt-4 p-3 bg-light rounded">
            <h6><i class="fas fa-check-circle me-2"></i>{{ t('manager.reports.weekly_payment_report.validation_section') }}</h6>
            <p class="text-muted mb-3">{{ t('manager.reports.weekly_payment_report.validation_instructions') }}</p>
            <div class="d-flex gap-2">
                <a href="{{ url_for('manager.tutor_payments_list', status='pending') }}" 
                   class="btn btn-success">
                    <i class="fas fa-credit-card me-1"></i>
                    {{ t('manager.reports.weekly_payment_report.process_payments') }}
                </a>
                <button class="btn btn-outline-primary" onclick="window.print()">
                    <i class="fas fa-download me-1"></i>
                    {{ t('manager.reports.weekly_payment_report.export_report') }}
                </button>
            </div>
        </div>
        
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">{{ t('manager.reports.weekly_payment_report.no_sessions') }}</h5>
            <p class="text-muted">{{ t('manager.reports.weekly_payment_report.no_sessions_message') }}</p>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
@media print {
    .btn-group, .btn, .alert .btn-group {
        display: none !important;
    }
    
    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
    }
    
    .bg-light {
        background-color: #f8f9fa !important;
    }
}
</style>
{% endblock %}