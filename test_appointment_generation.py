#!/usr/bin/env python3
"""
Test script to verify appointment generation from recurring schedules
"""
from datetime import datetime, date, time
from app import create_app
from app.extensions import db
from app.models.appointment_recurring_schedule import AppointmentRecurringSchedule

def test_appointment_generation():
    app = create_app()
    with app.app_context():
        try:
            print("Testing appointment generation from recurring schedules...")
            
            # Create a weekly recurring schedule (using future dates)
            schedule = AppointmentRecurringSchedule(
                tutor_id=1,
                client_id=1,
                tutor_service_id=1,
                start_time=time(14, 30),  # 2:30 PM
                duration_minutes=90,
                frequency='weekly',
                day_of_week=2,  # Wednesday (0=Monday)
                pattern_start_date=date(2025, 7, 23),  # A Wednesday in the future
                pattern_end_date=date(2025, 10, 23),  # End date in the future
                default_status='scheduled',
                default_notes='Weekly tutoring session'
            )
            
            print("✓ Created weekly recurring schedule")
            print(f"  - Start time: {schedule.start_time}")
            print(f"  - Duration: {schedule.duration_minutes} minutes")
            print(f"  - Frequency: {schedule.frequency}")
            print(f"  - Day of week: {schedule.day_of_week} (Wednesday)")
            print(f"  - Pattern start: {schedule.pattern_start_date}")
            print(f"  - Pattern end: {schedule.pattern_end_date}")
            
            # Test next occurrence calculation
            test_dates = [
                date(2025, 7, 21),  # Monday before start
                date(2025, 7, 23),  # Start date (Wednesday)
                date(2025, 7, 26),  # Saturday after start
                date(2025, 7, 30),  # Next Wednesday
            ]
            
            print("\n--- Testing next occurrence calculation ---")
            for test_date in test_dates:
                next_occurrence = schedule.get_next_occurrence(test_date)
                print(f"  From {test_date} ({test_date.strftime('%A')}): {next_occurrence}")
            
            # Test appointment generation
            print("\n--- Testing appointment generation ---")
            
            # Generate first appointment
            appointment1 = schedule.generate_next_appointment()
                
            if appointment1:
                print(f"✓ Generated first appointment:")
                print(f"  - Start: {appointment1.start_time}")
                print(f"  - End: {appointment1.end_time}")
                print(f"  - Status: {appointment1.status}")
                print(f"  - Notes: {appointment1.notes}")
                print(f"  - Recurring schedule ID: {appointment1.recurring_schedule_id}")
                print(f"  - Is recurring: {appointment1.is_recurring}")
            else:
                print("✗ Failed to generate first appointment")
                return False
            
            # Generate second appointment
            appointment2 = schedule.generate_next_appointment()
            if appointment2:
                print(f"✓ Generated second appointment:")
                print(f"  - Start: {appointment2.start_time}")
                print(f"  - End: {appointment2.end_time}")
                print(f"  - Last generated date updated to: {schedule.last_generated_date}")
            else:
                print("✗ Failed to generate second appointment")
                return False
            
            # Test bulk generation
            print("\n--- Testing bulk appointment generation ---")
            schedule.last_generated_date = None  # Reset for bulk test
            appointments = schedule.generate_appointments_until(date(2025, 8, 15))
            print(f"✓ Generated {len(appointments)} appointments until 2025-08-15:")
            for i, apt in enumerate(appointments, 1):
                print(f"  {i}. {apt.start_time.date()} at {apt.start_time.time()}")
            
            # Test biweekly schedule
            print("\n--- Testing biweekly schedule ---")
            biweekly_schedule = AppointmentRecurringSchedule(
                tutor_id=1,
                client_id=1,
                tutor_service_id=1,
                start_time=time(10, 0),
                duration_minutes=60,
                frequency='biweekly',
                day_of_week=4,  # Friday
                pattern_start_date=date(2025, 7, 25),  # A Friday in the future
                pattern_end_date=date(2025, 10, 25),
                default_status='scheduled'
            )
            
            biweekly_appointments = biweekly_schedule.generate_appointments_until(date(2025, 9, 1))
            print(f"✓ Generated {len(biweekly_appointments)} biweekly appointments:")
            for i, apt in enumerate(biweekly_appointments, 1):
                print(f"  {i}. {apt.start_time.date()} ({apt.start_time.strftime('%A')})")
            
            # Test deactivation
            print("\n--- Testing schedule deactivation ---")
            schedule.deactivate()
            print(f"✓ Schedule deactivated: is_active = {schedule.is_active}")
            
            deactivated_appointment = schedule.generate_next_appointment()
            if deactivated_appointment is None:
                print("✓ Deactivated schedule correctly returns None for appointment generation")
            else:
                print("✗ Deactivated schedule should not generate appointments")
                return False
            
            print("\n✓ All appointment generation tests passed!")
            return True
            
        except Exception as e:
            print(f"✗ Test failed: {e}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    test_appointment_generation()