# app/services/appointment_service.py
from app.extensions import db
from app.models.appointment import Appointment
from app.models.tutor import <PERSON><PERSON>
from app.models.client import Client
from app.models.service import TutorService, Service
from app.models.tutor_payment import TutorPayment
from app.models.appointment_audit import AppointmentAudit
from sqlalchemy import and_, or_
from datetime import datetime, timedelta
from flask import request
from flask_login import current_user

class AppointmentService:

    @staticmethod
    def populate_appointment_form_choices(form):
        """Populate dropdown choices for the appointment form."""
        # Get active tutors
        tutors = Tutor.query.filter_by(is_active=True).all()
        form.tutor_id.choices = [(t.tutor_id, f"{t.first_name} {t.last_name}") for t in tutors]

        # Get clients and dependants for the dropdown
        form.client_id.choices = []

        # Add individual clients with 'c_' prefix
        clients = Client.query.filter_by(client_type='individual').all()
        for c in clients:
            form.client_id.choices.append((f"c_{c.client_id}", f"{c.first_name} {c.last_name} (Client)"))

        # Add dependants with 'd_' prefix
        from app.models.dependant import Dependant
        dependants = Dependant.query.all()
        for d in dependants:
            form.client_id.choices.append((f"d_{d.dependant_id}", f"{d.first_name} {d.last_name} (Dependant)"))

        # If tutor is selected, get their services
        if form.tutor_id.data:
            tutor_services = TutorService.query.filter_by(tutor_id=form.tutor_id.data, is_active=True).join(Service).all()
            form.tutor_service_id.choices = [(ts.tutor_service_id, f"{ts.service.name} (${ts.custom_rate or ts.service.base_rate or 0}/hr) ({ts.service.duration_minutes} minutes)") for ts in tutor_services]
        else:
            form.tutor_service_id.choices = [(-1, "Select a tutor first")]

        # Get client_id from the selected client if available
        actual_client_id = None
        dependant_id = None
        if form.client_id.data:
            selected_id = form.client_id.data

            # Handle both string and integer types
            if isinstance(selected_id, str):
                if selected_id.startswith('c_'):
                    # This is a client
                    actual_client_id = int(selected_id[2:])  # Remove 'c_' prefix
                elif selected_id.startswith('d_'):
                    # This is a dependant - need to find the associated client
                    dependant_id = int(selected_id[2:])  # Remove 'd_' prefix

                    # Find the primary client for this dependant
                    from app.models.dependant import DependantRelationship
                    primary_relationship = DependantRelationship.query.filter_by(
                        dependant_id=dependant_id,
                        is_primary=True
                    ).first()

                    if primary_relationship:
                        actual_client_id = primary_relationship.client_id
                    else:
                        # If no primary relationship, get any relationship
                        any_relationship = DependantRelationship.query.filter_by(
                            dependant_id=dependant_id
                        ).first()
                        if any_relationship:
                            actual_client_id = any_relationship.client_id
                else:
                    # Fallback for old format (just a number as string)
                    try:
                        actual_client_id = int(selected_id)
                    except ValueError:
                        # Invalid format, skip
                        pass
            elif isinstance(selected_id, int):
                # Direct integer ID - assume it's a client ID for backward compatibility
                actual_client_id = selected_id

        # Add subscriptions if client or dependant is selected
        if actual_client_id and hasattr(form, 'subscription_id'):
            from app.services.subscription_service import SubscriptionService

            if dependant_id:
                # Get subscriptions available to the dependant
                subscriptions = SubscriptionService.get_active_subscriptions_for_dependant(dependant_id)
                form.subscription_id.choices = [(0, 'No Subscription')] + [
                    (s.subscription_id, f"{s.plan.name} ({s.hours_remaining:.1f} hours remaining) - {s.client.first_name} {s.client.last_name}'s subscription")
                    for s in subscriptions
                ]
            else:
                # Get subscriptions for the client
                subscriptions = SubscriptionService.get_active_subscriptions_for_client(actual_client_id)
                form.subscription_id.choices = [(0, 'No Subscription')] + [
                    (s.subscription_id, f"{s.plan.name} ({s.hours_remaining:.1f} hours remaining)")
                    for s in subscriptions
                ]
        elif hasattr(form, 'subscription_id'):
            form.subscription_id.choices = [(0, 'Select a client or dependant first')]

    @staticmethod
    def check_availability(tutor_id, start_time, end_time, exclude_appointment_id=None):
        """Check if the tutor is available during the specified time."""
        conflicts_query = Appointment.query.filter(
            Appointment.tutor_id == tutor_id,
            Appointment.status != 'cancelled',
            or_(
                # Case 1: New appointment starts during existing appointment
                and_(
                    Appointment.start_time <= start_time,
                    Appointment.end_time > start_time
                ),
                # Case 2: New appointment ends during existing appointment
                and_(
                    Appointment.start_time < end_time,
                    Appointment.end_time >= end_time
                ),
                # Case 3: New appointment completely overlaps existing appointment
                and_(
                    Appointment.start_time >= start_time,
                    Appointment.end_time <= end_time
                )
            )
        )

        # Exclude the current appointment if editing
        if exclude_appointment_id:
            conflicts_query = conflicts_query.filter(Appointment.appointment_id != exclude_appointment_id)

        return conflicts_query.first() is None

    @staticmethod
    def create_appointment_from_form(form):
        """Create a new appointment from form data (handles both regular and recurring)."""
        if form.is_recurring.data:
            return AppointmentService.create_recurring_appointment_from_form(form)
        else:
            return AppointmentService.create_regular_appointment_from_form(form)

    @staticmethod
    def create_regular_appointment_from_form(form):
        """Create a new regular appointment from form data."""
        # Convert form fields to datetime objects
        # Handle start_time as string (from SelectField) and convert to time object
        if isinstance(form.start_time.data, str):
            start_time_obj = datetime.strptime(form.start_time.data, '%H:%M').time()
        else:
            start_time_obj = form.start_time.data

        # Convert end_time to time object if it's not already
        if isinstance(form.end_time.data, str):
            end_time_obj = datetime.strptime(form.end_time.data, '%H:%M').time()
        else:
            end_time_obj = form.end_time.data

        start_datetime = datetime.combine(form.start_date.data, start_time_obj)
        end_datetime = datetime.combine(form.start_date.data, end_time_obj)

        # Calculate duration in minutes
        duration_minutes = int((end_datetime - start_datetime).total_seconds() / 60)

        # Check availability
        if not AppointmentService.check_availability(form.tutor_id.data, start_datetime, end_datetime):
            return None

        # Handle client vs dependant selection using prefixed IDs
        selected_id = form.client_id.data
        client_id = None
        dependant_id = None

        # Handle both string and integer types
        if isinstance(selected_id, str):
            if selected_id.startswith('c_'):
                # This is a client
                client_id = int(selected_id[2:])  # Remove 'c_' prefix
            elif selected_id.startswith('d_'):
                # This is a dependant
                dependant_id = int(selected_id[2:])  # Remove 'd_' prefix

                # Find the primary client for this dependant
                from app.models.dependant import DependantRelationship
                primary_relationship = DependantRelationship.query.filter_by(
                    dependant_id=dependant_id,
                    is_primary=True
                ).first()

                if primary_relationship:
                    client_id = primary_relationship.client_id
                else:
                    # If no primary relationship, get any relationship
                    any_relationship = DependantRelationship.query.filter_by(
                        dependant_id=dependant_id
                    ).first()
                    if any_relationship:
                        client_id = any_relationship.client_id
                    else:
                        # This shouldn't happen, but handle gracefully
                        return None
            else:
                # Fallback for old format (just a number as string)
                try:
                    client_id = int(selected_id)
                except ValueError:
                    # Invalid format, return None
                    return None
        elif isinstance(selected_id, int):
            # Direct integer ID - assume it's a client ID for backward compatibility
            client_id = selected_id
        else:
            # Invalid type, return None
            return None

        # Handle subscription if applicable (both clients and dependants can use subscriptions)
        is_subscription_based = False
        subscription_id = None
        if (hasattr(form, 'is_subscription_based') and form.is_subscription_based.data and
            form.subscription_id.data and form.subscription_id.data != 0):
            is_subscription_based = True
            subscription_id = form.subscription_id.data

        # Initialize transport fee
        transport_fee = 0

        # Only apply transport fee if NOT subscription-based
        if not is_subscription_based and hasattr(form, 'apply_transport_fee') and form.apply_transport_fee.data:
            if hasattr(form, 'transport_fee') and form.transport_fee.data:
                transport_fee = form.transport_fee.data
            else:
                # Get the transport fee from the tutor_service
                tutor_service = TutorService.query.get(form.tutor_service_id.data)
                if tutor_service and tutor_service.transport_fee:
                    transport_fee = tutor_service.transport_fee

        # Create new appointment
        appointment = Appointment(
            tutor_id=form.tutor_id.data,
            client_id=client_id,
            dependant_id=dependant_id,
            tutor_service_id=form.tutor_service_id.data,
            start_time=start_datetime,
            end_time=end_datetime,
            status=form.status.data,
            notes=form.notes.data,
            transport_fee=transport_fee,
            transport_fee_for_tutor=True,
            is_subscription_based=is_subscription_based,
            subscription_id=subscription_id,
            created_by=current_user.user_id if current_user and current_user.is_authenticated else None,
            modified_by=current_user.user_id if current_user and current_user.is_authenticated else None
        )

        db.session.add(appointment)
        db.session.flush()  # Get the appointment ID

        # Create audit log entry
        if appointment.appointment_id:
            AppointmentAudit.log_action(
                appointment_id=appointment.appointment_id,
                action='create',
                user=current_user if current_user and current_user.is_authenticated else None,
                new_appointment=appointment,
                ip_address=request.remote_addr if request else None,
                user_agent=request.headers.get('User-Agent') if request else None
            )

        # Create tutor payment record
        if appointment.appointment_id:
            from app.models.tutor_payment import TutorPayment

            # Calculate service amount for tutor
            tutor_service = TutorService.query.get(form.tutor_service_id.data)
            # Use the appointment's duration_minutes, fallback to calculated duration if None
            appointment_duration = appointment.duration_minutes or appointment.get_duration_minutes()
            duration_hours = appointment_duration / 60
            service_amount = float(tutor_service.tutor_rate) * duration_hours

            # Create payment record with transport fee
            payment = TutorPayment(
                tutor_id=form.tutor_id.data,
                appointment_id=appointment.appointment_id,
                service_amount=service_amount,
                transport_amount=transport_fee,
                total_amount=service_amount + float(transport_fee)
            )

            db.session.add(payment)

        db.session.commit()

        return appointment

    @staticmethod
    def update_appointment_status(appointment_id, status, notes=None):
        """Update appointment status and handle related actions with enhanced transaction management."""
        import logging
        from sqlalchemy.exc import IntegrityError, SQLAlchemyError
        from datetime import datetime

        logger = logging.getLogger(__name__)

        try:
            # Start transaction
            appointment = Appointment.query.get_or_404(appointment_id)
            old_status = appointment.status

            # Validate status value
            valid_statuses = ['scheduled', 'completed', 'cancelled', 'no-show', 'awaiting_confirmation', 'confirmed', 'rescheduled']
            if status not in valid_statuses:
                raise ValueError(f"Invalid status '{status}'. Must be one of: {', '.join(valid_statuses)}")

            # Log the status change attempt
            logger.info(f"Updating appointment {appointment_id} status from '{old_status}' to '{status}'")

            # Store old values for audit
            old_appointment_values = {
                'status': appointment.status,
                'notes': appointment.notes,
                'tutor_id': appointment.tutor_id,
                'client_id': appointment.client_id,
                'dependant_id': appointment.dependant_id,
                'start_time': appointment.start_time,
                'end_time': appointment.end_time,
                'transport_fee': appointment.transport_fee
            }

            # Update appointment
            appointment.status = status
            appointment.modification_date = datetime.utcnow()
            appointment.modified_by = current_user.user_id if current_user and current_user.is_authenticated else None

            if notes is not None:
                appointment.notes = notes
            
            # Create audit log entry
            action = 'cancel' if status == 'cancelled' else 'update'
            AppointmentAudit.log_action(
                appointment_id=appointment_id,
                action=action,
                user=current_user if current_user and current_user.is_authenticated else None,
                old_appointment=type('obj', (object,), old_appointment_values),
                new_appointment=appointment,
                ip_address=request.remote_addr if request else None,
                user_agent=request.headers.get('User-Agent') if request else None
            )

            # If appointment is being marked as completed
            if status == 'completed' and old_status != 'completed':
                # Update tutor payment status
                payment = TutorPayment.query.filter_by(appointment_id=appointment_id).first()
                if payment:
                    # Mark payment as ready for processing
                    payment.status = 'ready'

                # Record subscription usage if this is a subscription-based appointment
                if appointment.is_subscription_based and appointment.subscription_id:
                    from app.services.subscription_service import SubscriptionService
                    SubscriptionService.record_usage(appointment_id)
                else:
                    # Generate invoice for individual appointment (within existing transaction)
                    try:
                        from app.services.invoice_service import InvoiceService
                        InvoiceService.generate_invoice_for_appointment_in_transaction(appointment_id)
                    except Exception as e:
                        # Log the error but don't fail the status update
                        import logging
                        from flask import current_app
                        current_app.logger.error(f"Error generating invoice for appointment {appointment_id}: {str(e)}")
                        # Continue with the status update even if invoice generation fails

            # Commit transaction
            db.session.commit()
            logger.info(f"Successfully updated appointment {appointment_id} status to '{status}'")

            return appointment, None

        except IntegrityError as e:
            db.session.rollback()
            error_msg = f"Database constraint violation when updating appointment {appointment_id}: {str(e)}"
            logger.error(error_msg)
            return None, error_msg

        except SQLAlchemyError as e:
            db.session.rollback()
            error_msg = f"Database error when updating appointment {appointment_id}: {str(e)}"
            logger.error(error_msg)
            return None, error_msg

        except ValueError as e:
            db.session.rollback()
            error_msg = str(e)
            logger.error(error_msg)
            return None, error_msg

        except Exception as e:
            db.session.rollback()
            error_msg = f"Unexpected error when updating appointment {appointment_id}: {str(e)}"
            logger.error(error_msg)
            return None, error_msg

    @staticmethod
    def create_recurring_appointment_from_form(form):
        """Create a new recurring appointment template from form data."""
        # Handle client vs dependant selection using prefixed IDs
        selected_id = form.client_id.data
        client_id = None
        dependant_id = None

        # Handle both string and integer types
        if isinstance(selected_id, str):
            if selected_id.startswith('c_'):
                # This is a client
                client_id = int(selected_id[2:])  # Remove 'c_' prefix
            elif selected_id.startswith('d_'):
                # This is a dependant
                dependant_id = int(selected_id[2:])  # Remove 'd_' prefix

                # Find the primary client for this dependant
                from app.models.dependant import DependantRelationship
                primary_relationship = DependantRelationship.query.filter_by(
                    dependant_id=dependant_id,
                    is_primary=True
                ).first()

                if primary_relationship:
                    client_id = primary_relationship.client_id
                else:
                    # If no primary relationship, get any relationship
                    any_relationship = DependantRelationship.query.filter_by(
                        dependant_id=dependant_id
                    ).first()
                    if any_relationship:
                        client_id = any_relationship.client_id
                    else:
                        # This shouldn't happen, but handle gracefully
                        return None, []
            else:
                # Fallback for old format (just a number as string)
                try:
                    client_id = int(selected_id)
                except ValueError:
                    # Invalid format, return error
                    return None, []
        elif isinstance(selected_id, int):
            # Direct integer ID - assume it's a client ID for backward compatibility
            client_id = selected_id
        else:
            # Invalid type, return error
            return None, []

        # Calculate transport fee
        transport_fee = 0.00
        if hasattr(form, 'apply_transport_fee') and form.apply_transport_fee.data:
            if hasattr(form, 'transport_fee') and form.transport_fee.data:
                transport_fee = float(form.transport_fee.data)
            else:
                # Get transport fee from tutor service
                tutor_service = TutorService.query.get(form.tutor_service_id.data)
                if tutor_service and tutor_service.transport_fee:
                    transport_fee = float(tutor_service.transport_fee)

        # Handle subscription
        is_subscription_based = form.is_subscription_based.data if hasattr(form, 'is_subscription_based') else False
        subscription_id = form.subscription_id.data if (hasattr(form, 'subscription_id') and form.subscription_id.data) else None

        # Get service duration
        tutor_service = TutorService.query.get(form.tutor_service_id.data)
        service = Service.query.get(tutor_service.service_id)
        duration_minutes = service.duration_minutes

        # Create a template datetime for the first occurrence
        # Handle start_time as string (from SelectField) and convert to time object
        if isinstance(form.start_time.data, str):
            start_time_obj = datetime.strptime(form.start_time.data, '%H:%M').time()
        else:
            start_time_obj = form.start_time.data

        template_start_datetime = datetime.combine(form.pattern_start_date.data, start_time_obj)
        template_end_datetime = template_start_datetime + timedelta(minutes=duration_minutes)

        # Create recurring appointment template
        recurring_template = Appointment(
            tutor_id=form.tutor_id.data,
            client_id=client_id,
            dependant_id=dependant_id,
            tutor_service_id=form.tutor_service_id.data,
            start_time=template_start_datetime,  # First occurrence datetime
            end_time=template_end_datetime,      # First occurrence end datetime
            duration_minutes=duration_minutes,   # Store duration for convenience
            status='scheduled',  # Default status for templates
            notes=form.notes.data,
            transport_fee=transport_fee,
            transport_fee_for_tutor=True,  # Always true - all transport fees go to tutor
            is_subscription_based=is_subscription_based,
            subscription_id=subscription_id,
            is_recurring=True,  # This is a recurring template
            frequency=form.frequency.data,
            day_of_week=form.day_of_week.data,
            week_of_month=form.week_of_month.data if form.frequency.data == 'monthly' else None,
            pattern_start_date=form.pattern_start_date.data,
            pattern_end_date=form.pattern_end_date.data if form.end_type.data == 'date' else None,
            pattern_occurrences=form.pattern_occurrences.data if form.end_type.data == 'occurrences' else None,
            last_generated_date=None,
            created_by=current_user.user_id if current_user and current_user.is_authenticated else None,
            modified_by=current_user.user_id if current_user and current_user.is_authenticated else None
        )

        db.session.add(recurring_template)
        db.session.flush()  # Get the template ID

        # Generate initial appointments (e.g., next 4 weeks)
        generated_appointments = []
        current_date = form.pattern_start_date.data  # Use pattern_start_date
        max_generate_date = current_date + timedelta(weeks=4)  # Generate 4 weeks ahead
        appointment_count = 0
        max_appointments = 16  # Safety limit

        while current_date <= max_generate_date and appointment_count < max_appointments:
            if recurring_template.pattern_end_date and current_date > recurring_template.pattern_end_date:
                break

            # Check if this date matches the recurring pattern
            should_create_appointment = False

            if form.frequency.data == 'weekly':
                # Check if the day of week matches
                if current_date.weekday() == form.day_of_week.data:
                    should_create_appointment = True
            elif form.frequency.data == 'biweekly':
                # Check if it's the right day and the right week (every 2 weeks from start)
                weeks_diff = (current_date - form.pattern_start_date.data).days // 7
                if current_date.weekday() == form.day_of_week.data and weeks_diff % 2 == 0:
                    should_create_appointment = True
            elif form.frequency.data == 'monthly':
                # Monthly logic - same day of week in the specified week of month
                if current_date.weekday() == form.day_of_week.data:
                    # Calculate which week of the month this is
                    week_of_month = (current_date.day - 1) // 7 + 1
                    if week_of_month == form.week_of_month.data:
                        should_create_appointment = True

            if should_create_appointment:
                # Create appointment for this date using template's time
                template_time = recurring_template.start_time.time()
                appointment_start = datetime.combine(current_date, template_time)
                appointment_end = appointment_start + timedelta(minutes=duration_minutes)

                # Check availability before adding
                if AppointmentService.check_availability(form.tutor_id.data, appointment_start, appointment_end):
                    appointment = Appointment(
                        tutor_id=form.tutor_id.data,
                        client_id=client_id,
                        dependant_id=dependant_id,
                        tutor_service_id=form.tutor_service_id.data,
                        start_time=appointment_start,
                        end_time=appointment_end,
                        duration_minutes=duration_minutes,  # Add duration for recurring instances
                        status='scheduled',
                        notes=form.notes.data,
                        transport_fee=transport_fee,
                        transport_fee_for_tutor=True,
                        is_subscription_based=is_subscription_based,
                        subscription_id=subscription_id,
                        recurring_template_id=recurring_template.id  # Link to template
                    )
                    db.session.add(appointment)
                    generated_appointments.append(appointment)
                    appointment_count += 1

            # Move to next day
            current_date += timedelta(days=1)

        db.session.commit()
        return recurring_template, generated_appointments

    @staticmethod
    def update_appointment_from_form(appointment, form):
        """Update an existing appointment from form data."""
        try:
            # Convert form fields to datetime objects
            # Handle start_time as string (from SelectField) and convert to time object
            if isinstance(form.start_time.data, str):
                start_time_obj = datetime.strptime(form.start_time.data, '%H:%M').time()
            else:
                start_time_obj = form.start_time.data

            # Convert end_time to time object if it's not already
            if isinstance(form.end_time.data, str):
                end_time_obj = datetime.strptime(form.end_time.data, '%H:%M').time()
            else:
                end_time_obj = form.end_time.data

            start_datetime = datetime.combine(form.start_date.data, start_time_obj)
            end_datetime = datetime.combine(form.start_date.data, end_time_obj)

            # Calculate duration in minutes
            duration_minutes = int((end_datetime - start_datetime).total_seconds() / 60)

            # Only check availability if time, date, or tutor is changing
            time_changed = (appointment.start_time != start_datetime or
                          appointment.end_time != end_datetime or
                          appointment.tutor_id != form.tutor_id.data)

            if time_changed:
                # Check availability (excluding this appointment)
                if not AppointmentService.check_availability(form.tutor_id.data, start_datetime, end_datetime, appointment.appointment_id):
                    return False

            # Handle client vs dependant selection using prefixed IDs (same logic as create)
            selected_id = form.client_id.data
            client_id = None
            dependant_id = None

            # Handle both string and integer types
            if isinstance(selected_id, str):
                if selected_id.startswith('c_'):
                    # This is a client
                    client_id = int(selected_id[2:])  # Remove 'c_' prefix
                elif selected_id.startswith('d_'):
                    # This is a dependant
                    dependant_id = int(selected_id[2:])  # Remove 'd_' prefix

                    # Find the primary client for this dependant
                    from app.models.dependant import DependantRelationship
                    primary_relationship = DependantRelationship.query.filter_by(
                        dependant_id=dependant_id,
                        is_primary=True
                    ).first()

                    if primary_relationship:
                        client_id = primary_relationship.client_id
                    else:
                        # If no primary relationship, get any relationship
                        any_relationship = DependantRelationship.query.filter_by(
                            dependant_id=dependant_id
                        ).first()
                        if any_relationship:
                            client_id = any_relationship.client_id
                        else:
                            # This shouldn't happen, but handle gracefully
                            return False
                else:
                    # Fallback for old format (just a number as string)
                    try:
                        client_id = int(selected_id)
                    except ValueError:
                        # Invalid format, return False
                        return False
            elif isinstance(selected_id, int):
                # Direct integer ID - assume it's a client ID for backward compatibility
                client_id = selected_id
            else:
                # Invalid type, return False
                return False

            # Store old values for audit
            old_appointment = type('obj', (object,), {
                'status': appointment.status,
                'tutor_id': appointment.tutor_id,
                'client_id': appointment.client_id,
                'dependant_id': appointment.dependant_id,
                'start_time': appointment.start_time,
                'end_time': appointment.end_time,
                'notes': appointment.notes,
                'transport_fee': appointment.transport_fee
            })

            # Update appointment
            appointment.tutor_id = form.tutor_id.data
            appointment.client_id = client_id
            appointment.dependant_id = dependant_id
            appointment.tutor_service_id = form.tutor_service_id.data
            appointment.start_time = start_datetime
            appointment.end_time = end_datetime
            appointment.duration_minutes = duration_minutes  # Update duration
            appointment.status = form.status.data
            appointment.notes = form.notes.data
            appointment.modification_date = datetime.utcnow()
            appointment.modified_by = current_user.user_id if current_user and current_user.is_authenticated else None

            # Create audit log entry
            AppointmentAudit.log_action(
                appointment_id=appointment.appointment_id,
                action='update',
                user=current_user if current_user and current_user.is_authenticated else None,
                old_appointment=old_appointment,
                new_appointment=appointment,
                ip_address=request.remote_addr if request else None,
                user_agent=request.headers.get('User-Agent') if request else None
            )

            db.session.commit()

            return True
        except Exception as e:
            db.session.rollback()
            return False

    @staticmethod
    def prepare_calendar_data(appointments, tutors, start_date, end_date, view_type):
        """Prepare appointment data for the calendar view."""
        calendar_data = {
            'view_type': view_type,
            'start_date': start_date.strftime('%Y-%m-%d'),
            'tutors': [],
            'time_slots': [],
            'appointments': []
        }

        # Add tutor data
        for tutor in tutors:
            calendar_data['tutors'].append({
                'id': tutor.tutor_id,
                'name': f"{tutor.first_name} {tutor.last_name}"
            })

        # Generate time slots based on view type
        if view_type == 'day' or view_type == 'week':
            # For day and week views, show hourly slots
            for hour in range(8, 22):  # 8 AM to 9 PM
                calendar_data['time_slots'].append({
                    'hour': hour,
                    'label': f"{hour % 12 or 12} {'AM' if hour < 12 else 'PM'}"
                })
        elif view_type == 'month':
            # For month view, show days
            current_date = start_date
            while current_date < end_date:
                calendar_data['time_slots'].append({
                    'date': current_date.strftime('%Y-%m-%d'),
                    'day': current_date.day,
                    'weekday': current_date.strftime('%a')
                })
                current_date += timedelta(days=1)

        # Add appointment data
        for appointment in appointments:
            tutor_service = appointment.tutor_service if hasattr(appointment, 'tutor_service') and appointment.tutor_service else TutorService.query.get(appointment.tutor_service_id)
            service = tutor_service.service if tutor_service else None

            calendar_data['appointments'].append({
                'id': appointment.appointment_id,
                'tutor_id': appointment.tutor_id,
                'client_name': appointment.appointment_subject_name,  # This handles both clients and dependants
                'student_name': appointment.appointment_subject_name,  # Alias for backward compatibility
                'service_name': service.name if service else "Unknown",
                'start_time': appointment.start_time.strftime('%Y-%m-%d %H:%M'),
                'end_time': appointment.end_time.strftime('%Y-%m-%d %H:%M'),
                'status': appointment.status,
                'notes': appointment.notes
            })

        return calendar_data

    @staticmethod
    def get_navigation_dates(current_date, view_type):
        """Generate previous and next dates for navigation."""
        if view_type == 'day':
            prev_date = current_date - timedelta(days=1)
            next_date = current_date + timedelta(days=1)
        elif view_type == 'week':
            prev_date = current_date - timedelta(days=7)
            next_date = current_date + timedelta(days=7)
        elif view_type == 'month':
            # Go to the same day in the previous/next month
            if current_date.month == 1:
                prev_date = current_date.replace(year=current_date.year-1, month=12)
            else:
                prev_date = current_date.replace(month=current_date.month-1)

            if current_date.month == 12:
                next_date = current_date.replace(year=current_date.year+1, month=1)
            else:
                next_date = current_date.replace(month=current_date.month+1)
        else:
            # Default to daily navigation
            prev_date = current_date - timedelta(days=1)
            next_date = current_date + timedelta(days=1)

        return prev_date, next_date

    @staticmethod
    def generate_future_appointments(days=30):
        """Generate future appointments from recurring templates.

        Args:
            days: Number of days ahead to generate appointments for

        Returns:
            int: Number of appointments created
        """
        from datetime import datetime, timedelta

        # Find all recurring templates
        recurring_templates = Appointment.query.filter(
            Appointment.is_recurring == True
        ).all()

        appointments_created = 0
        target_date = datetime.now().date() + timedelta(days=days)

        for template in recurring_templates:
            # Skip if template has ended
            if template.pattern_end_date and template.pattern_end_date < datetime.now().date():
                continue

            # Generate appointments up to target date
            current_check_date = template.last_generated_date or template.pattern_start_date

            while current_check_date <= target_date:
                next_occurrence = template.get_next_occurrence(current_check_date)

                if not next_occurrence or next_occurrence > target_date:
                    break

                # Check if appointment already exists for this date
                template_time = template.start_time.time()
                start_datetime = datetime.combine(next_occurrence, template_time)
                end_datetime = start_datetime + timedelta(minutes=template.calculated_duration_minutes)

                existing_appointment = Appointment.query.filter(
                    Appointment.recurring_template_id == template.id,
                    Appointment.start_time == start_datetime
                ).first()

                if not existing_appointment:
                    # Check tutor availability
                    if AppointmentService.check_availability(template.tutor_id, start_datetime, end_datetime):
                        # Generate the appointment
                        new_appointment = template.generate_next_appointment()
                        if new_appointment:
                            db.session.add(new_appointment)
                            appointments_created += 1

                # Update the check date
                current_check_date = next_occurrence + timedelta(days=1)

        if appointments_created > 0:
            db.session.commit()

        return appointments_created