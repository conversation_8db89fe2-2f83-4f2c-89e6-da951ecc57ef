<!-- app/templates/emails/appointment_reminder_client.html -->
<!DOCTYPE html>
<html>
<head>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333333;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #dddddd;
            border-radius: 5px;
        }
        .header {
            background-color: #0d6efd;
            padding: 10px 20px;
            color: white;
            border-radius: 5px 5px 0 0;
        }
        .content {
            padding: 20px;
        }
        .appointment-details {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .button {
            display: inline-block;
            background-color: #0d6efd;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .footer {
            font-size: 12px;
            color: #777777;
            margin-top: 20px;
            padding-top: 10px;
            border-top: 1px solid #dddddd;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>Appointment Reminder</h2>
        </div>
        <div class="content">
            <p>Dear {{ appointment.client.first_name }},</p>
            
            <p>This is a reminder that you have an upcoming tutoring appointment:</p>
            
            <div class="appointment-details">
                <p><strong>Client:</strong> {{ appointment.client.first_name }} {{ appointment.client.last_name }}</p>
                <p><strong>Tutor:</strong> {{ appointment.tutor.first_name }} {{ appointment.tutor.last_name }}</p>
                <p><strong>Service:</strong> {{ appointment.tutor_service.service.name }}</p>
                <p><strong>Date:</strong> {{ appointment.start_time.strftime('%A, %B %d, %Y') }}</p>
                <p><strong>Time:</strong> {{ appointment.start_time.strftime('%I:%M %p') }} - {{ appointment.end_time.strftime('%I:%M %p') }}</p>
            </div>
            
            <p>You can view the appointment details and make changes through your client portal:</p>
            
            <p>
                <a href="{{ url }}" class="button">View Appointment</a>
            </p>
            
            <p>If you need to cancel or reschedule, please do so at least 24 hours in advance.</p>
            
            <p>Thank you for choosing our tutoring services!</p>
            
            <p>Best regards,<br>
            TutorAide Inc.</p>
        </div>
        <div class="footer">
            <p>This is an automated message, please do not reply directly to this email.</p>
        </div>
    </div>
</body>
</html>
